<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="{{ settings.color_button }}">
    <link rel="canonical" href="{{ canonical_url }}">

    {%- if settings.favicon != blank -%}
      <link rel="shortcut icon" href="{{ settings.favicon | img_url: '32x32' }}" type="image/png">
    {%- endif -%}

    {%- assign formatted_initial_value = gift_card.initial_value | money_without_trailing_zeros: gift_card.currency -%}
    {%- assign formatted_initial_value_stripped = formatted_initial_value | strip_html -%}
    <title>{{ 'info.gift_card_title' | t: value: formatted_initial_value_stripped, shop: shop.name }}</title>

    <meta name="description" content="{{ 'info.gift_card_description' | t }}">

    {%- render 'social-meta-tags' -%}

    {%- render 'font-face' -%}
    {{ 'components.css' | asset_url | stylesheet_tag: preload: true }}
    {%- render 'css-variables' -%}

    <script>
      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
    </script>

    {{ 'vendor/qrcode.js' | shopify_asset_url | script_tag }}

    {{ content_for_header }}
  </head>

  <body
    class="template-giftcard"
    data-button_style="{{ settings.button_style }}"
    data-edges="{{ settings.roundness }}"
    data-type_header_capitalize="{{ settings.type_header_capitalize }}"
    data-swatch_style="{{ settings.swatch_style }}"
    data-grid-style="{{ settings.product_grid_style }}"
  >
    <div id="PageContainer">
      <div class="page-width">
        {%- section 'giftcard-header' -%}

        <main class="giftcard" role="main">
          {{ content_for_layout }}
        </main>

        <footer class="giftcard__footer">
          <a class="add-to-apple-wallet" href="{{ gift_card.pass_url }}">
            {% assign alt_text = 'actions.add_to_apple_wallet' | t %}
            {%- render 'image-element',
              asset: 'gift-card/add-to-apple-wallet.svg',
              type: 'asset',
              img_width: 120,
              img_height: 40,
              alt: alt_text,
              sizes: '120px',
              context: 'giftcard'
            -%}
          </a>
        </footer>
      </div>
    </div>
  </body>
</html>
