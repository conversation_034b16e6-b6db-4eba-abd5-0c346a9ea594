{%comment%}EComposer_v_1_0{%endcomment%}
{%- comment -%}
  Default EComposer theme
{%- endcomment -%}
<!doctype html>
<html class="no-js" lang="{{ shop.locale }}">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
  <link rel="canonical" href="{{ canonical_url }}">
  {%- if settings.favicon != blank -%}
    <link rel="shortcut icon" href="{{ settings.favicon | img_url: '32x32' }}" type="image/png">
  {%- endif -%}

  {%- capture seo_title -%}
    {%- if template == 'search' and search.performed == true -%}
      {{ 'general.search.heading' | t: count: search.results_count }}: {{ 'general.search.results_with_count' | t: terms: search.terms, count: search.results_count }}
    {%- else -%}
      {{ page_title }}
    {%- endif -%}

    {%- if current_tags -%}
      {%- assign meta_tags = current_tags | join: ', ' -%} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags -}}
    {%- endif -%}
    {%- if current_page != 1 -%}
      &ndash; {{ 'general.meta.page' | t: page: current_page }}
    {%- endif -%}
    {%- assign escaped_page_title = page_title | escape -%}
    {%- unless escaped_page_title contains shop.name -%}
      &ndash; {{ shop.name }}
    {%- endunless -%}
  {%- endcapture -%}
  <title>{{ seo_title | strip }}</title>

  {%- if page_description -%}
    <meta name="description" content="{{ page_description | escape }}">
  {%- endif -%}

  {%- assign og_title = page_title -%}
  {%- assign og_url = canonical_url -%}
  {%- assign og_type = 'website' -%}
  {%- assign og_description = page_description | default: shop.description | default: shop.name -%}


  {% comment %} Template specific overides {% endcomment %}
  {% if template.name == 'product' %}
    {%- assign og_title = product.title | strip_html -%}
    {%- assign og_type = 'product' -%}
    {% if product.images.size > 0 %}
      {%- capture og_image_tags -%}{% for image in product.images limit:3 -%}<meta property="og:image" content="http:{{ image.src | product_img_url: '1200x1200' }}">{% endfor -%}{% endcapture -%}
      {%- capture og_image_secure_url_tags -%}{% for image in product.images limit:3 -%}<meta property="og:image:secure_url" content="https:{{ image.src | product_img_url: '1200x1200' }}">{% endfor -%}{% endcapture -%}
    {% endif %}

  {% elsif template.name == 'article' %}
    {%- assign og_title = article.title | strip_html -%}
    {%- assign og_type = 'article' -%}
    {%- assign og_description = article.excerpt_or_content | strip_html -%}
    {% if article.image %}
      {%- capture og_image_tags -%}<meta property="og:image" content="http:{{ article.image | img_url: '1200x1200' }}">{%- endcapture -%}
      {%- capture og_image_secure_url_tags -%}<meta property="og:image:secure_url" content="https:{{ article.image | img_url: '1200x1200' }}">{%- endcapture -%}
    {% endif %}

  {% elsif template.name == 'collection' %}
    {%- assign og_title = collection.title | strip_html -%}
    {%- assign og_type = 'product.group' -%}
    {% if collection.image %}
      {%- capture og_image_tags -%}<meta property="og:image" content="http:{{ collection.image | img_url: '1200x1200' }}">{%- endcapture -%}
      {%- capture og_image_secure_url_tags -%}<meta property="og:image:secure_url" content="https:{{ collection.image | img_url: '1200x1200' }}">{%- endcapture -%}
    {% endif %}

  {% elsif template.name == 'password' %}
    {%- assign og_title = shop.name -%}
    {%- assign og_url = shop.url -%}
    {%- assign og_description = shop.description | default: shop.name -%}
  {% endif %}

  <meta property="og:site_name" content="{{ shop.name }}">
  <meta property="og:url" content="{{ og_url }}">
  <meta property="og:title" content="{{ og_title }}">
  <meta property="og:type" content="{{ og_type }}">
  <meta property="og:description" content="{{ og_description }}">
  {% if template.name == 'product' %}
    <meta property="og:price:amount" content="{{ product.price | money_without_currency | strip_html }}">
    <meta property="og:price:currency" content="{{ cart.currency.iso_code }}">
  {% endif %}
  {{ og_image_tags }}
  {{ og_image_secure_url_tags }}

  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="{{ og_title }}">
  <meta name="twitter:description" content="{{ og_description }}">
  {%- render 'ecom_header', ECOM_THEME: true -%}
   {{ content_for_header }}

</head>

<body class="template-{{ template | split: '.' | first }}">
  <div class="EComposer-theme">
    {{ content_for_layout }}
  </div>
 {%- render 'ecom_footer', ECOM_THEME: true -%}
</body>
</html>
/* 限制导航栏容器的宽度，并居中显示 */
.navbar-container {
  max-width: calc(100% - 200px); /* 减去左右两边的100px */
  margin: 0 auto; /* 居中 */
  padding: 0 100px; /* 左右内边距为100px */
  box-sizing: border-box; /* 确保padding不会影响总宽度 */
}

/* 如果你的导航栏是flex布局 */
.navbar-container {
  display: flex;
  justify-content: space-between; /* 内容均匀分布 */
  align-items: center; /* 垂直居中 */
}