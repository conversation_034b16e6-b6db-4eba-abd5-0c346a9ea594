<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}" dir="{{ settings.text_direction }}">
  <head>
 {%- render 'ecom_header' -%}
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="{{ settings.color_button }}">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    <link rel="dns-prefetch" href="https://ajax.googleapis.com">
    <link rel="dns-prefetch" href="https://maps.googleapis.com">
    <link rel="dns-prefetch" href="https://maps.gstatic.com">


    {%- if settings.favicon != blank -%}
      <link rel="shortcut icon" href="{{ settings.favicon | img_url: '32x32' }}" type="image/png">
    {%- endif -%}

    {%- render 'page-title' -%}

    {%- if page_description -%}
      <meta name="description" content="{{ page_description | escape }}">
    {%- endif -%}

    {%- render 'social-meta-tags' -%}

    {%- render 'font-face' -%} 
    {{ 'components.css' | asset_url | stylesheet_tag: preload: true }}
    {%- render 'css-variables' -%}
    {{ 'overrides.css' | asset_url | stylesheet_tag }}
    {{ 'overrides.css' | asset_url | stylesheet_tag }}

    {%- style -%}
      :root {
        --product-grid-padding: 12px;
      }
      /* @font-face {
          font-family: 'Lato-Bold';
          src: url('/cdn/shop/t/52/assets/Lato-Bold.ttf?v=127362163070977022521752065208') format('woff2'),
              url('/cdn/shop/t/52/assets/Lato-Bold.ttf?v=127362163070977022521752065208') format('woff2');
          font-weight: blod;
          font-style: normal;
        } */
        @font-face {
        font-family: "Lato";
        src: url("{{'Lato-Regular.ttf' | asset_url}}") format("woff2");
      }
      @font-face {
        font-family: "Playfair Display";
        src: url("{{'PlayfairDisplay-Regular.ttf' | asset_url}}") format("woff2");
      }
    {%- endstyle -%}
{% comment %} 
  
{{ 'Lato-Bold.ttf' | asset_url | stylesheet_tag }}
使用这个找到字体的路径
 {% endcomment %}

    {%- liquid
      assign enableSuperScript = false
      unless shop.money_format contains 'money' or shop.money_format contains '.'
        if settings.superscript_decimals
          if shop.money_format contains '{{amount}}' or shop.money_format contains '{{ amount }}'
            assign enableSuperScript = true
          elsif shop.money_format contains '{{amount_with_comma_separator}}' or shop.money_format contains '{{ amount_with_comma_separator }}'
            assign enableSuperScript = true
          endif
        endif
      endunless
    -%}

    <script>
      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');

      window.theme = window.theme || {};

      theme.settings = {
        themeName: 'Expanse',
        themeVersion: '6.1.0', // x-release-please-version
      };
    </script>

    {%- render 'import-map' -%}
    {%- render 'es-module-shims' -%}
    {%- render 'is-land' -%}
    {%- render 'preload-js' -%}
    {%- render 'theme-editor' -%}

    {%- if tinyscript -%}{{ content_for_header }}{%- else -%}{% render 'tiny-script-control' %}{%- endif -%}

    <script src="{{ 'theme.js' | asset_url }}" defer="defer"></script>

    {%- if request.page_type contains 'customers/' -%}
      <script src="{{ 'shopify_common.js' | shopify_asset_url }}" defer="defer"></script>
    {%- endif -%}

    <!-- Meta Pixel Code -->
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '1852384068306191');
fbq('track', 'PageView');
</script>
<noscript><img height="1" width="1" style="display:none"
src="https://www.facebook.com/tr?id=1852384068306191&ev=PageView&noscript=1"
/></noscript>
<!-- End Meta Pixel Code -->
    

    {% if request.design_mode %}
      <script>
        theme.settings.email = {{ shop.email | json }}
      </script>
      <script src="https://api.archetypethemes.co/design-mode.js" defer="defer"></script>
    {% endif %}
  <!-- Event snippet for Google Shopping App Purchase conversion page
In your html page, add the snippet and call gtag_report_conversion when someone clicks on the chosen link or button. -->
<script>
function gtag_report_conversion(url) {
  var callback = function () {
    if (typeof(url) != 'undefined') {
      window.location = url;
    }
  };
  gtag('event', 'conversion', {
      'send_to': 'AW-460142936/FWagCNG31vABENjytNsB',
      'value': 0.0,
      'transaction_id': '',
      'event_callback': callback
  });
  return false;
}
</script>
  </head>

  <body
    class="template-{{ template | replace: '.', ' ' | truncatewords: 1, '' | handle }}{% if request.path == '/challenge' %} template-challange{% endif %}"
    data-button_style="{{ settings.button_style }}"
    data-edges="{{ settings.roundness }}"
    data-type_header_capitalize="{{ settings.type_header_capitalize }}"
    data-swatch_style="{{ settings.swatch_style }}"
    data-grid-style="{{ settings.product_grid_style }}"
  >
    <a class="in-page-link visually-hidden skip-link" href="#MainContent">{{ 'actions.skip_to_content' | t }}</a>

    <div id="PageContainer" class="page-container">
      <div class="transition-body">
        {%- sections 'header-group' -%}
        {%- sections 'popup-group' -%}

        <main class="main-content" id="MainContent">
          {{ content_for_layout }}
        </main>

        {%- sections 'footer-group' -%}
      </div>
    </div>
 
    {%- liquid
    render 'photoswipe-template'
    render 'tool-tip'
  -%}
<div class="shopify-dynamic-checkout---buttons" style="display:none!important;">{{ content_for_additional_checkout_buttons }}</div>{%- render "ecom_footer"-%}</body>

</html>
