<!-- 第五个模块 - 产品网格 -->
<style>
    /* 基于1920×1080基准的动态缩放样式 */

    /* 设置根字体大小，用于rem计算 */
    html {
        font-size: clamp(14px, 1vw, 18px); /* 响应式根字体大小 */
    }

    /* 产品网格标题 */
    .case-product-grid-title {
        font-family: 'Playfair Display', serif;
        font-weight: 400;
        font-size: 1.35vw; /* 26px ÷ 1920 × 100 = 1.35vw */
        margin-bottom: 1.51vw; /* 29px ÷ 1920 × 100 = 1.51vw */
        margin-left: 5.21vw; /* 100px ÷ 1920 × 100 = 5.21vw */
    }

    /* 产品网格容器 */
    .case-product-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr); /* 保留网格布局 */
        gap: 1.04vw; /* 20px ÷ 1920 × 100 = 1.04vw */
        padding: 0 5.21vw; /* 100px ÷ 1920 × 100 = 5.21vw */
        margin-bottom: 2.60vw; /* 50px ÷ 1920 × 100 = 2.60vw */
    }

    /* 产品卡片 */
    .case-product-card {
        text-align: center;
        margin-bottom: 1.04vw; /* 20px ÷ 1920 × 100 = 1.04vw */
        opacity: 0; /* 保留固定值 */
        transform: translateY(1.56vw); /* 30px ÷ 1920 × 100 = 1.56vw */
        transition: opacity 0.6s ease, transform 0.6s ease; /* 保留固定动画时间 */
    }

    .case-product-card.visible {
        opacity: 1; /* 保留固定值 */
        transform: translateY(0); /* 保留固定值 */
    }

    /* 图片容器 - 固定边框大小 */
    .case-product-card .case-image-container {
        width: 17.08vw; /* 328px ÷ 1920 × 100 = 17.08vw */
        height: 17.08vw; /* 328px ÷ 1920 × 100 = 17.08vw */
        aspect-ratio: 1 / 1; /* 强制保持1:1宽高比 */
        overflow: hidden; /* 隐藏放大时超出的部分 */
        margin: 0 auto; /* 居中显示 */
        position: relative;
    }

    .case-product-card img {
        width: 100%; /* 填满容器 */
        height: 100%; /* 填满容器 */
        aspect-ratio: 1 / 1; /* 强制保持1:1宽高比 */
        object-fit: cover; /* 保留固定值 */
        transition: transform 0.3s ease, opacity 0.3s ease; /* 添加透明度过渡 */
        display: block;
    }

    /* 场景图样式 */
    .case-product-card .case-scene-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        /*transition: opacity 0.3s ease;*/
        transition: .4s ease-in-out;
        z-index: 2;
    }

    .case-product-card:hover .case-scene-image {
        opacity: 1;
    }

    .case-product-card .case-image-container:hover img {
        transform: scale(1.05); /* 只放大图片，不影响容器 */
    }

    /* 价格容器 */
    .case-price-box {
        margin: 0.52vw 0; /* 10px ÷ 1920 × 100 = 0.52vw */
        font-family: 'PingFang SC', sans-serif;
        font-weight: 500;
        font-size: 0.73vw; /* 14px ÷ 1920 × 100 = 0.73vw */
    }

    .case-price-original {
        text-decoration: line-through;
        color: #999999;
        margin-right: 0.26vw; /* 5px ÷ 1920 × 100 = 0.26vw */
    }

    .case-price-sale {
        color: #EB5E30;
    }

    .case-price-normal {
        color: #333333;
    }

    /* 购买按钮 */
    .case-shop-button {
        display: inline-block;
        width: 7.66vw; /* 147px ÷ 1920 × 100 = 7.66vw */
        height: 1.98vw; /* 38px ÷ 1920 × 100 = 1.98vw */
        border-radius: 3.13vw; /* 60px ÷ 1920 × 100 = 3.13vw */
        border: 1px solid #5C3D2E;
        background-color: white;
        color: #5C3D2E;
        cursor: pointer;
        font-family: 'PingFang SC', sans-serif;
        font-size: 0.73vw; /* 14px ÷ 1920 × 100 = 0.73vw */
        line-height: 1.98vw; /* 38px ÷ 1920 × 100 = 1.98vw */
        text-decoration: none;
        transition: all 0.3s ease; /* 保留固定动画时间 */
    }

    .case-shop-button:hover {
        background-color: #5C3D2E;
        color: white;
    }

    /* 平板端响应式设计 */
    @media (max-width: 1024px) {
        .case-product-grid-title {
            font-size: 2.34vw; /* 24px ÷ 1024 × 100 = 2.34vw */
            margin-bottom: 1.95vw; /* 20px ÷ 1024 × 100 = 1.95vw */
            margin-left: 4.88vw; /* 50px ÷ 1024 × 100 = 4.88vw */
        }

        .case-product-grid {
            grid-template-columns: repeat(4, 1fr); /* 保留网格布局 */
            padding: 0 4.88vw; /* 50px ÷ 1024 × 100 = 4.88vw */
            gap: 1.95vw; /* 20px ÷ 1024 × 100 = 1.95vw */
            margin-bottom: 3.91vw; /* 40px ÷ 1024 × 100 = 3.91vw */
        }

        .case-product-card {
            margin-bottom: 1.95vw; /* 20px ÷ 1024 × 100 = 1.95vw */
            transform: translateY(2.93vw); /* 30px ÷ 1024 × 100 = 2.93vw */
        }

        .case-product-card .case-image-container {
            width: 21.88vw; /* 224px ÷ 1024 × 100 = 21.88vw */
            height: 21.88vw; /* 224px ÷ 1024 × 100 = 21.88vw */
            aspect-ratio: 1 / 1; /* 强制保持1:1宽高比 */
        }

        .case-product-card .case-scene-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            /*transition: opacity 0.3s ease;*/
            transition: .4s ease-in-out;
            z-index: 2;
        }

        .case-product-card:hover .case-scene-image {
            opacity: 1;
        }

        .case-price-box {
            margin: 0.98vw 0; /* 10px ÷ 1024 × 100 = 0.98vw */
            font-size: 1.37vw; /* 14px ÷ 1024 × 100 = 1.37vw */
        }

        .case-price-original {
            margin-right: 0.49vw; /* 5px ÷ 1024 × 100 = 0.49vw */
        }

        .case-shop-button {
            width: 12.5vw; /* 128px ÷ 1024 × 100 = 12.5vw */
            height: 3.52vw; /* 36px ÷ 1024 × 100 = 3.52vw */
            font-size: 1.37vw; /* 14px ÷ 1024 × 100 = 1.37vw */
            line-height: 3.52vw; /* 36px ÷ 1024 × 100 = 3.52vw */
            border-radius: 5.86vw; /* 60px ÷ 1024 × 100 = 5.86vw */
        }
    }

    /* 移动端响应式设计 - 基于768px基础使用vw单位 */
    @media (max-width: 768px) {
        .case-product-grid-title {
            margin-left: 0;
            margin-top: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
            font-size: 2.86vw; /* 22px ÷ 768 × 100 = 2.86vw */
            text-align: center;
            position: relative;
            margin-bottom: 2.60vw; /* 20px ÷ 768 × 100 = 3.13vw */
        }

        .case-product-grid-title::before {
            content: '';
            position: absolute;
            top: -2.60vw; /* -20px ÷ 768 × 100 = -2.60vw */
            left: 50%; /* 保留百分比 */
            transform: translateX(-50%); /* 保留固定值 */
            width: calc(100vw - 5.21vw); /* 40px ÷ 768 × 100 = 5.21vw */
            height: 1px; /* 保留固定值 */
            border-top: 1px solid #EEEEEE;
        }

        .case-product-grid {
            grid-template-columns: repeat(2, 1fr); /* 保留网格布局 */
            padding: 0 2.08vw; /* 16px ÷ 768 × 100 = 2.08vw */
            gap: 2.60vw 1.95vw; /* 20px 15px 转换为vw (基于768px) */
            margin-bottom: 4.17vw; /* 32px ÷ 768 × 100 = 4.17vw */
        }

        .case-product-card {
            margin-bottom: 0;
            transform: translateY(3.91vw); /* 30px ÷ 768 × 100 = 3.91vw */
        }

        .case-product-card .case-image-container {
            width: 21.35vw; /* 164 ÷ 768 × 100 = 32.55vw，与高度保持一致 */
            height: 21.35vw; /* 164 ÷ 768 × 100 = 32.55vw */
            aspect-ratio: 1 / 1; /* 强制保持1:1宽高比 */
            margin: 0 auto; /* 居中显示 */
        }

        .case-product-card .case-scene-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            /*transition: opacity 0.3s ease;*/
            transition: .4s ease-in-out;
            z-index: 2;
        }

        .case-product-card:hover .case-scene-image {
            opacity: 1;
        }

        .case-price-box {
            margin: 0.78vw 0; /* 6px ÷ 768 × 100 = 0.78vw */
            font-size: 1.82vw; /* 14px ÷ 768 × 100 = 1.82vw */
        }

        .case-price-original {
            margin-right: 0.65vw; /* 5px ÷ 768 × 100 = 0.65vw */
        }

        .case-shop-button {
            width: 15.63vw; /* 120px ÷ 768 × 100 = 15.63vw */
            height: 4.17vw; /* 32px ÷ 768 × 100 = 4.17vw */
            font-size: 1.56vw; /* 12px ÷ 768 × 100 = 1.56vw */
            line-height: 4.17vw; /* 32px ÷ 768 × 100 = 4.17vw */
            border-radius: 7.81vw; /* 60px ÷ 768 × 100 = 7.81vw */
        }
    }

    /* 超小屏幕保护 - 基于375px基础使用vw单位 */
    @media (max-width: 480px) {
        .case-product-grid-title {
            margin-left: 0;
            margin-top: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
            font-size: 5.87vw; /* 22px ÷ 375 × 100 = 5.87vw */
            text-align: center;
            position: relative;
            margin-bottom: 5.33vw; /* 20px ÷ 375 × 100 = 6.4vw */
        }

        .case-product-grid-title::before {
            content: '';
            position: absolute;
            top: -5.33vw; /* -20px ÷ 375 × 100 = -5.33vw */
            left: 50%; /* 保留百分比 */
            transform: translateX(-50%); /* 保留固定值 */
            width: calc(100vw - 10.67vw); /* 40px ÷ 375 × 100 = 10.67vw */
            height: 1px; /* 保留固定值 */
            border-top: 1px solid #EEEEEE;
        }

        .case-product-grid {
            grid-template-columns: repeat(2, 1fr); /* 保留网格布局 */
            padding: 0 4.27vw; /* 16px ÷ 375 × 100 = 4.27vw */
            gap: 5.33vw 4vw; /* 20px 15px 转换为vw (基于375px) */
            margin-bottom: 8.53vw; /* 32px ÷ 375 × 100 = 8.53vw */
        }

        .case-product-card {
            margin-bottom: 0;
            transform: translateY(8vw); /* 30px ÷ 375 × 100 = 8vw */
        }

        .case-product-card .case-image-container {
            width: 43.73vw; /* 164 ÷ 375 × 100 = 53.33vw，适合小屏幕 */
            height: 43.73vw; /* 164 ÷ 375 × 100 = 53.33vw */
            aspect-ratio: 1 / 1; /* 强制保持1:1宽高比 */
            margin: 0 auto; /* 居中显示 */
        }

        .case-product-card .case-scene-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            /*transition: opacity 0.3s ease;*/
            transition: .4s ease-in-out;
            z-index: 2;
        }

        .case-product-card:hover .case-scene-image {
            opacity: 1;
        }

        .case-price-box {
            margin: 1.6vw 0; /* 6px ÷ 375 × 100 = 1.6vw */
            font-size: 3.73vw; /* 14px ÷ 375 × 100 = 3.73vw */
        }

        .case-price-original {
            margin-right: 1.33vw; /* 5px ÷ 375 × 100 = 1.33vw */
        }

        .case-shop-button {
            width: 32vw; /* 120px ÷ 375 × 100 = 32vw */
            height: 8.53vw; /* 32px ÷ 375 × 100 = 8.53vw */
            font-size: 3.2vw; /* 12px ÷ 375 × 100 = 3.2vw */
            line-height: 8.53vw; /* 32px ÷ 375 × 100 = 8.53vw */
            border-radius: 16vw; /* 60px ÷ 375 × 100 = 16vw */
        }
    }

    /* 超大屏幕优化 - 防止元素过大 */
    @media (min-width: 2560px) {
        .case-product-grid-title {
            font-size: clamp(26px, 1.35vw, 36px); /* 限制最大字体 */
            margin-bottom: clamp(29px, 1.51vw, 40px);
            margin-left: clamp(100px, 5.21vw, 150px);
        }

        .case-product-grid {
            gap: clamp(20px, 1.04vw, 30px); /* 限制最大间距 */
            padding: 0 clamp(90px, 4.69vw, 140px);
            margin-bottom: clamp(50px, 2.60vw, 80px);
        }

        .case-product-card {
            margin-bottom: clamp(20px, 1.04vw, 30px);
            transform: translateY(clamp(30px, 1.56vw, 40px));
        }

        .case-product-card .case-image-container {
            width: clamp(328px, 17.08vw, 400px); /* 限制最大尺寸 */
            height: clamp(328px, 17.08vw, 400px);
            aspect-ratio: 1 / 1; /* 强制保持1:1宽高比 */
        }

        .case-product-card .case-scene-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            /*transition: opacity 0.3s ease;*/
            transition: .4s ease-in-out;
            z-index: 2;
        }

        .case-product-card:hover .case-scene-image {
            opacity: 1;
        }

        .case-price-box {
            margin: clamp(10px, 0.52vw, 15px) 0;
            font-size: clamp(14px, 0.73vw, 18px);
        }

        .case-price-original {
            margin-right: clamp(5px, 0.26vw, 8px);
        }

        .case-shop-button {
            width: clamp(147px, 7.66vw, 180px);
            height: clamp(38px, 1.98vw, 48px);
            font-size: clamp(14px, 0.73vw, 18px);
            line-height: clamp(38px, 1.98vw, 48px);
            border-radius: clamp(60px, 3.13vw, 80px);
        }
    }

    /* 高分辨率屏幕优化 */
    @media (min-width: 1920px) and (max-width: 2559px) {
        .case-product-grid-title {
            font-size: clamp(24px, 1.35vw, 30px); /* 在标准和大屏之间平滑过渡 */
        }

        .case-product-card .case-image-container {
            width: clamp(300px, 17.08vw, 360px);
            height: clamp(300px, 17.08vw, 360px);
            aspect-ratio: 1 / 1; /* 强制保持1:1宽高比 */
        }

        .case-product-card .case-scene-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            /*transition: opacity 0.3s ease;*/
            transition: .4s ease-in-out;
            z-index: 2;
        }

        .case-product-card:hover .case-scene-image {
            opacity: 1;
        }

        .case-shop-button {
            font-size: clamp(13px, 0.73vw, 16px);
        }
    }

    /* 中等屏幕优化 */
    @media (min-width: 1025px) and (max-width: 1366px) {
        .case-product-grid {
            gap: 1.5vw; /* 稍微减少间距 */
        }

        .case-product-card .case-image-container {
            width: 16vw; /* 适中的尺寸 */
            height: 16vw;
            aspect-ratio: 1 / 1; /* 强制保持1:1宽高比 */
        }

        .case-product-card .case-scene-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            /*transition: opacity 0.3s ease;*/
            transition: .4s ease-in-out;
            z-index: 2;
        }

        .case-product-card:hover .case-scene-image {
            opacity: 1;
        }

        .case-shop-button {
            width: 7vw;
            height: 1.8vw;
            font-size: 0.7vw;
            line-height: 1.8vw;
        }
    }
</style>

<div class="case-product-grid-module">
    <!-- 产品网格标题 -->
    <h2 class="case-product-grid-title">{{ section.settings.grid_title }}</h2>

    <!-- 产品网格区域 -->
    <div class="case-product-grid">
        {% for product in collections[section.settings.featured_collection].products limit: 10 %}
            <div class="case-product-card">
                <div class="case-image-container">
                    <!-- 主图 -->
                    <img src="{{ product.featured_image | img_url: '400x500' }}" alt="{{ product.title }}" />

                    <!-- 场景图 - 优先使用alt包含scene/lifestyle的图片，否则使用第二张图片 -->
                    {% assign scene_image = null %}
                    {% for image in product.images %}
                        {% assign alt_lower = image.alt | downcase %}
                        {% if alt_lower contains 'scene' or alt_lower contains 'lifestyle' or alt_lower contains '场景' %}
                            {% assign scene_image = image %}
                            {% break %}
                        {% endif %}
                    {% endfor %}

                    {% unless scene_image %}
                        {% if product.images.size > 1 %}
                            {% assign scene_image = product.images[1] %}
                        {% endif %}
                    {% endunless %}

                    {% if scene_image %}
                        <img class="case-scene-image" src="{{ scene_image | img_url: '400x500' }}" alt="{{ product.title }} - Scene" />
                    {% endif %}
                </div>
                <div class="case-price-box">
                    {% if product.compare_at_price > product.price %}
                        <span class="case-price-original">{{ product.compare_at_price | money }}</span>
                        <span class="case-price-sale">{{ product.price | money }}</span>
                    {% else %}
                        <span class="case-price-normal">{{ product.price | money }}</span>
                    {% endif %}
                </div>
                <button class="case-shop-button" onclick="window.location.href='{{ product.url }}'">Shop Now</button>
            </div>
        {% endfor %}
    </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    // 添加滚动动画效果
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, observerOptions);

    // 为产品卡片添加动画
    const productCards = document.querySelectorAll('.case-product-card');
    productCards.forEach((card, index) => {
      card.style.transitionDelay = `${index * 0.1}s`;
      observer.observe(card);
    });

    // 移动端触摸支持 - 点击切换场景图
    if ('ontouchstart' in window) {
      productCards.forEach(card => {
        const sceneImage = card.querySelector('.case-scene-image');
        if (sceneImage) {
          let isSceneVisible = false;

          card.addEventListener('touchstart', function(e) {
            e.preventDefault();
            isSceneVisible = !isSceneVisible;
            sceneImage.style.opacity = isSceneVisible ? '1' : '0';
          });

          // 点击其他地方时隐藏场景图
          document.addEventListener('touchstart', function(e) {
            if (!card.contains(e.target)) {
              isSceneVisible = false;
              sceneImage.style.opacity = '0';
            }
          });
        }
      });
    }
  });
</script>

{% schema %}
{
  "name": "Product Grid Module",
  "settings": [
    {
      "type": "text",
      "id": "grid_title",
      "label": "Grid Title",
      "default": "TOP 10"
    },
    {
      "type": "collection",
      "id": "featured_collection",
      "label": "Featured Collection"
    }
  ],
  "presets": [
    {
      "name": "Product Grid Module",
      "category": "Custom"
    }
  ]
}
{% endschema %}