<style>
    /* 基于1920×1080基准的动态缩放样式 */

    /* 设置根字体大小，用于rem计算 */
    html {
        font-size: clamp(14px, 1vw, 18px); /* 响应式根字体大小 */
    }

    /* 面包屑导航 - 桌面端使用vw，移动端使用rem */
    .case-breadcrumb {
        padding: 0 0 1.04vw 5.21vw; /* 0 0 20px 100px 转换为vw */
        font-size: 0.83vw; /* 16px ÷ 1920 × 100 = 0.83vw */
        background-color: white; /* 保留固定颜色 */
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 100%; /* 保留百分比 */
        letter-spacing: 0px; /* 保留固定值 */
    }

    .case-breadcrumb a {
        color: #999999; /* 保留固定颜色 */
        text-decoration: none; /* 保留固定值 */
        transition: all 0.3s ease; /* 添加过渡动画 */
    }

    .case-breadcrumb a:hover {
        text-decoration: underline; /* 保留固定值 */
        color: #666666; /* 悬停时颜色稍深 */
    }

    .case-breadcrumb .separator {
        color: #999999; /* 保留固定颜色 */
        margin: 0 0.42vw; /* 8px ÷ 1920 × 100 = 0.42vw */
    }

    .case-breadcrumb .current {
        color: #666666; /* 保留固定颜色 */
        font-weight: 500; /* 当前页面稍微加粗 */
    }

    /* 平板端响应式设计 */
    @media (max-width: 1024px) {
        .case-breadcrumb {
            padding: 0 0 1.95vw 4.88vw; /* 0 0 20px 50px 转换为平板vw */
            font-size: 1.56vw; /* 16px ÷ 1024 × 100 = 1.56vw */
        }

        .case-breadcrumb .separator {
            margin: 0 0.78vw; /* 8px ÷ 1024 × 100 = 0.78vw */
        }
    }

    /* 移动端响应式设计 - 基于768px基础使用vw单位 */
    @media (max-width: 768px) {
        .case-breadcrumb {
            padding: 1.30vw 0 1.30vw 2.08vw; /* 10px 0 10px 16px 转换为vw (基于768px) */
            font-size: 1.56vw; /* 12px ÷ 768 × 100 = 1.56vw */
            text-align: left; /* 改为左对齐 */
          border-top: 1px solid #EEEEEE;
        }

        .case-breadcrumb .separator {
            margin: 0 1.04vw; /* 8px ÷ 768 × 100 = 1.04vw */
        }
      .swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
        bottom: 0.39vw !important; /* 3px ÷ 768 × 100 = 1.04vw */
      }
    }

    /* 超小屏幕保护 - 基于375px基础使用vw单位 */
    @media (max-width: 480px) {
        .case-breadcrumb {
            padding: 2.67vw 0 2.67vw 4.27vw; /* 10px 0 10px 16px 转换为vw (基于375px) */
            font-size: 3.2vw; /* 12px ÷ 375 × 100 = 3.2vw */
          line-height: 3.47vw; /* 13*/
          border-top: 1px solid #EEEEEE;
        }

        .case-breadcrumb .separator {
            margin: 0 2.13vw; /* 8px ÷ 375 × 100 = 2.13vw */
        }

        /* 超小屏幕时可能需要换行显示 */
        .case-breadcrumb {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
      .swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
        bottom: 0.8vw !important; /* 3px ÷ 375 × 100 = 1.04vw */
      }
    }

    /* 超大屏幕优化 - 防止元素过大 */
    @media (min-width: 2560px) {
        .case-breadcrumb {
            padding: 0 0 clamp(20px, 1.04vw, 30px) clamp(100px, 5.21vw, 150px); /* 限制最大内边距 */
            font-size: clamp(16px, 0.83vw, 20px); /* 限制最大字体 */
        }

        .case-breadcrumb .separator {
            margin: 0 clamp(8px, 0.42vw, 12px); /* 限制最大间距 */
        }
    }

    /* 高分辨率屏幕优化 */
    @media (min-width: 1920px) and (max-width: 2559px) {
        .case-breadcrumb {
            font-size: clamp(15px, 0.83vw, 18px); /* 在标准和大屏之间平滑过渡 */
        }
    }

    /* 中等屏幕优化 */
    @media (min-width: 1025px) and (max-width: 1366px) {
        .case-breadcrumb {
            padding: 0 0 1.5vw 4vw; /* 适中的内边距 */
            font-size: 1.2vw; /* 适中的字体大小 */
        }

        .case-breadcrumb .separator {
            margin: 0 0.6vw; /* 适中的间距 */
        }
    }

    /* 增强可访问性 */
    .case-breadcrumb {
        position: relative;
    }

    .case-breadcrumb::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }

    /* 焦点状态优化 */
    .case-breadcrumb a:focus {
        outline: 2px solid #007acc;
        outline-offset: 2px;
        border-radius: 2px;
    }

    /* 打印样式优化 */
    @media print {
        .case-breadcrumb {
            padding: 0 0 10px 20px;
            font-size: 12px;
            color: #000 !important;
        }

        .case-breadcrumb a,
        .case-breadcrumb .separator,
        .case-breadcrumb .current {
            color: #000 !important;
        }
    }
</style>

<!-- 面包屑导航 -->
<nav class="case-breadcrumb">
    <a href="{{ routes.root_url }}">{{ section.settings.home_text | default: 'Home' }}</a>
    <span class="separator">{{ section.settings.separator | default: '·' }}</span>
    <span class="current">
        {% if section.settings.current_text != blank %}
            {{ section.settings.current_text }}
        {% else %}
            {{ collection.title | default: 'Savanna Collection' }}
        {% endif %}
    </span>
</nav>

{% schema %}
{
  "name": "Breadcrumb Navigation",
  "settings": [
    {
      "type": "text",
      "id": "home_text",
      "label": "Home Text",
      "default": "Home"
    },
    {
      "type": "text",
      "id": "separator",
      "label": "Separator",
      "default": "·"
    },
    {
      "type": "text",
      "id": "current_text",
      "label": "Current Page Text",
      "info": "Savanna Collection"
    }
  ],
  "presets": [
    {
      "name": "Breadcrumb Navigation",
      "category": "Navigation"
    }
  ]
}
{% endschema %}