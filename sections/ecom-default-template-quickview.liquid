{% comment %}
 EComposer (https://ecomposer.io)
 You SHOULD NOT modify source code in this page because
 It is automatically generated from EComposer
 At 2024-04-01 10:29:50
 {% endcomment %}

 {% assign ecom_root_url = routes.root_url %}
 {% if ecom_root_url != '/'%}
 {% assign ecom_root_url = routes.root_url | append: '/' %}
 {% endif %}
 <link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-modal_css" href="https://cdn.ecomposer.app/vendors/css/ecom_modal.css" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.6" /><noscript><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-modal_css" href="https://cdn.ecomposer.app/vendors/css/ecom_modal.css" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.6" /></noscript><script type="text/javascript" asyc="async" id="ecom-vendors-slider_js" src="https://cdn.ecomposer.app/vendors/js/<EMAIL>" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-modal_js" src="https://cdn.ecomposer.app/vendors/js/ecom_modal.js" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-zoom_js" src="https://cdn.ecomposer.app/vendors/js/ecom_zoom.js" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-shopify_option_selection_js" src="https://cdn.ecomposer.app/vendors/js/ecomposer_option_selection.js" ></script>
{%capture section_id %}ecom-default-template{% endcapture%}{% if section and section_id == section.id and headless == true %}
{{ content_for_header }}
{% render 'ecom_header', ECOM_THEME: true %}{% endif %}<link href="https://fonts.googleapis.com/css?family=Plus+Jakarta+Sans:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Albert+Sans:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Jost:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/>
{{'https://nhocleo.myshopify.com/cdn/shop/t/41/assets/ecom-65d80a3f5c865bdfa4074a12.css?v=108684635481550204061711967394' | stylesheet_tag }}
<script src="https://nhocleo.myshopify.com/cdn/shop/t/41/assets/ecom-65d80a3f5c865bdfa4074a12.js?v=73947026943843446861711967393" defer="defer"></script>
<script type="text/javascript" class="ecom-page-info">
 window.EComposer = window.EComposer || {};
 window.EComposer.TEMPLATE_ID="65d80a3f5c865bdfa4074a12";
 window.EComposer.TEMPLATE = {"template_id":"65d80a3f5c865bdfa4074a12","title":"Untitled","type":"quickview","slug":"ecom-65d80a3f5c865bdfa4074a12","plan_id":7};
 </script>
<div class="ecom-builder" id="ecom-default-template"><div class="ecom-sections" data-section-id="{{section.id}}">
 {% comment %}
 // Reactive
 * this.data.settings.product
 {% endcomment %}
 {% liquid
 if request.page_type != 'product' or product == blank
 assign selected_product = 'fine-knit-cashmere-sweater'
 if selected_product == blank or selected_product == '' or all_products[selected_product] == blank
 assign product = collections['all'].products.first
 else
 assign product = all_products[selected_product]
 if product.id == blank
 assign product = collections['all'].products.last
 endif
 endif
 endif
 %}
 {%- if product != blank -%}
 {%- capture ec_form_id -%}product_form_{{ product.id }}_ecom-v2xxl78d4e{%- endcapture -%}
 {%- form 'product', product, id: ec_form_id, class: "ecom-product-form ecom-product-form--single ecom-click", product_id: product.id, data-product_id: product.id,data-product-id: product.id, data-handle: product.handle-%}
 <section class="ecom-row ecom-core ecom-section ecom-v2xxl78d4e" data-id="ecom-v2xxl78d4e" style="z-index: inherit;"><div data-deep="1" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-7q52pwo5djb"><div class="core__column--wrapper" style=""><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-wvw58p0f3t" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-s2zrkweba2" data-id="ecom-s2zrkweba2" style="z-index: inherit;"><div data-deep="2" class="core__row--columns"><div class="ecom-column ecom-core core__column--first ecom-x2io97b7rhi"><div class="core__column--wrapper" style=""><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-usorwbs45b" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__media ecom-position-sticky" deep="2"><div class="ecom-product-single__media-wrapper
 {% if product.media.size == 1 %}
 ecom-dont-has-many-images
 {% endif %}
 "><div class="ecom-product-single__media-container ecom-product-single__media--slider ecom-product-single__media--horizontal ecom-product-single__media-tablet--horizontal ecom-product-single__media-mobile--horizontal"><div class="ecom-product-single__media--featured ecom-swiper-container" data-breakpoints="{&quot;0&quot;:{&quot;__screen_name&quot;:&quot;mobile&quot;},&quot;768&quot;:{&quot;__screen_name&quot;:&quot;tablet&quot;},&quot;1025&quot;:{&quot;__screen_name&quot;:&quot;desktop&quot;,&quot;slidesPerView&quot;:1}}" data-priority="variant">
 {% comment %}
 Reactivity
 *this.data.settings.image_action
 *this.data.settings.enable_zoom
 {% endcomment %}

 {%- liquid
 if product.has_only_default_variant
 assign target = product
 else
 assign target = product.selected_or_first_available_variant
 endif
 -%}



 {% assign img_padding = 1 | times: 1 %}
 {% assign img_padding__tablet = 1 | times: 1 %}
 {% assign img_padding__mobile = 1 | times: 1 %}
 <div class="ecom-swiper-wrapper ecom-product-single__media--images ecom-swiper-wrapper ecom-product-single__media--images-layout__slider">
 {% for image in product.images %}
 {% assign fetchpriority = 'low'%}
 {% if forloop.first %}
 {% assign fetchpriority = 'high'%}
 {% endif %}
 {% assign img_ration = 1 | divided_by: image.aspect_ratio | times: 100 %}

 <div class="ecom-product-single__media--image ecom-swiper-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-product-single__media--image" data-index="{{forloop.index0}}" data-variant_id="{{ image.variants | map:'id' | join: ',' }}" style="--img_padding: {{ img_ration | divided_by: img_padding }}%;--img_padding__tablet: {{ img_ration | divided_by: img_padding__tablet }}%;--img_padding__mobile: {{ img_ration | divided_by: img_padding__mobile }}%;" data-half-width="{% assign img_ration_half = 1 | divided_by: image.aspect_ratio | times: 50 %}{{img_ration_half}}" data-full-width="{% assign img_ration_full = 1 | divided_by: image.aspect_ratio | times: 100 %}{{img_ration_full}}">

 {%- assign img_master = image | img_url: 'master' -%}
 {{ image | image_url: width: 1946 | image_tag:
 sizes: sizes,
 widths: '246, 493, 600, 713, 823, 990, 1100, 1206, 1346, 1426, 1646, 1946',
 class: 'ecom-image-default'

 ,loading: 'lazy'
 ,fetchpriority: fetchpriority
 ,alt: image.alt | escape
 }}

 </div>
 {% endfor %}
 {%- for media in product.media -%}
 {% assign media_ration = 1 | divided_by: media.aspect_ratio | times: 100 %}
 {% case media.media_type %}
 {% when 'image' %}
 {% continue %}
 {% when 'external_video'%}
 <div class="ecom-product-single__media--image ecom-swiper-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-product-single__media---external-video ecom-product-single__media--full" data-position="{{media.position}}" style="--img_padding: {{ media_ration | divided_by: img_padding }}%;--img_padding__tablet: {{ media_ration | divided_by: img_padding__tablet }}%;--img_padding__mobile: {{ media_ration | divided_by: img_padding__mobile }}%;">
 {{ media | external_video_tag: image_size:'master' }}
 </div>
 {% when 'video' %}
 <div data-stopdrag="true" class="ecom-product-single__media--image ecom-swiper-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-product-single__media--video ecom-product-single__media--full ecom-swiper-no-swiping" data-position="{{media.position}}" style="--img_padding: {{ media_ration | divided_by: img_padding }}%;--img_padding__tablet: {{ media_ration | divided_by: img_padding__tablet }}%;--img_padding__mobile: {{ media_ration | divided_by: img_padding__mobile }}%;">
 {{ media | video_tag: image_size:'master', class: 'ecom-media-video', controls: undefined, autoplay: undefined, mute: undefined,loop: undefined }}

 <button class="ecom-product-single__media--play-control" type="button">
 <span class="ecom-product-single__media--play-control-wrapper">
 <span class="visually-hidden">Play video</span>

 </span>
 </button>

 </div>
 {% when 'model' %}
 <div class="ecom-product-single__media--image ecom-swiper-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-swiper-no-swiping ecom-product-single__media--model ecom-product-single__media--full" data-stopdrag="true" data-position="{{media.position}}">
 <div class="ecom-product-single__media--model-wrapper" style="padding-top: 100%">
 {{ media | model_viewer_tag: image_size:'master', reveal: 'interaction', toggleable: true, data-model-id: media.id }}
 </div>
 </div>
 {% else %}
 <div data-media-type="{{media.media_type}}" class="ecom-product-single__media--image ecom-swiper-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-swiper-no-swiping ecom-product-single__media--full" data-position="{{media.position}}">
 <div class="ecom-product-single__media" style="--img_padding: {{ media_ration | divided_by: img_padding }}%;--img_padding__tablet: {{ media_ration | divided_by: img_padding__tablet }}%;--img_padding__mobile: {{ media_ration | divided_by: img_padding__mobile }}%;">
 {{ media | media_tag: image_size:'master', class: 'ecom-product-single__media--item' }}
 </div>
 </div>
 {% endcase %}
 {%- endfor -%}
 </div>

 <div class="ecom-swiper-pagination"></div>
 <div class="ecom-swiper-button-next ecom-swiper-controls" style="display: flex">
 <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
 </svg>
 </div>
 <div class="ecom-swiper-button-prev ecom-swiper-controls" style="display: flex">
 <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
 </svg>
 </div>

 </div><div class="ecom-product-single__media--thumbs ecom-product-single__init-thumb-hidden ecom-swiper-container" data-breakpoints="{&quot;0&quot;:{&quot;__screen_name&quot;:&quot;mobile&quot;,&quot;slidesPerView&quot;:4,&quot;thumbnail_position&quot;:&quot;column&quot;},&quot;768&quot;:{&quot;__screen_name&quot;:&quot;tablet&quot;,&quot;slidesPerView&quot;:4,&quot;spaceBetween&quot;:15,&quot;thumbnail_position&quot;:&quot;column&quot;},&quot;1025&quot;:{&quot;__screen_name&quot;:&quot;desktop&quot;,&quot;slidesPerView&quot;:4,&quot;spaceBetween&quot;:10,&quot;thumbnail_position&quot;:&quot;column&quot;}}">
 {%- if product.media.size > 1 -%}
 <div class="ecom-swiper-wrapper">
 {% for image in product.images%}
 {% assign fetchpriority = 'low'%}
 {% if forloop.first %}
 {% assign fetchpriority = 'high'%}
 {% endif %}
 <div class="ecom-product-single__media--thumbnail ecom-swiper-slide" data-variant_id="{{ image.variants | map:'id' | join:',' }}">
 <img loading="lazy"
 class="ecom-product-thumbnail"
 src="{{ image | img_url: '350x350' , crop: 'center' }}"
 alt="{{ image.alt }}",
 fetchpriority="{{ fetchpriority }}"
 ,loading="lazy"
 />
 </div>
 {% endfor %}
 {%- for media in product.media -%}
 {% if media.media_type != 'image' %}
 <div class="ecom-product-single__media--thumbnail ecom-swiper-slide">
 <div class="ecom-product-single__media--thumbnail--icon">
 {% if media.media_type == 'model' %}

 {% else %}

 {% endif %}
 </div>
 <img src="{{ media.preview_image | img_url: '350x350' , crop: 'center' }}" alt="{{ media.alt }}" loading="lazy"/>
 </div>
 {% endif %}
 {%- endfor -%}
 </div>

 <div class="ecom-swiper-button-next ecom-swiper-controls-thumb" style="display: none">
 <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
 </svg>
 </div>
 <div class="ecom-swiper-button-prev ecom-swiper-controls-thumb" style="display: none">
 <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
 </svg>
 </div>

 {%- endif -%}
 </div></div></div><div class="ecom-hidden-content">
 <script type="application/json" id="Product-model-{{ product.id }}">
 {{ product.media | where: 'media_type', 'model' | json }}
 </script>
 </div></div></div> </div></div></div><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__column--resize" style="display: none;"></div></div> <div class="ecom-column ecom-core core__column--last ecom-fxp6ollqnzt"><div class="core__column--wrapper" style=""><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block ecom-5qhzvstbvpc" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__title" deep="2"><div class="ecom-product-single__title-wrapper"><div class="ecom-product-single__title-container"><h2 class="ecom-product__heading" href="{{ product.url }}" title="{{ product.title }}">{{ product.title }}</h2></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-e9htia8ha2c" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__rating" deep="2"><div class="ecom-product-single__rating-wrapper" data-review-platform="
 {%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}
 {{review_platform}}
 "><div class="ecom-product-single__rating-container">
 {%- if review_platform -%}
 {%- if EComBuilderMode == true -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <div class="ecom-app-extension-info">
 <span class="ecom-theme-app-extension-icon">
 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M364.2 267.2l-104.8-15.28L212.6 156.7C208.7 148.8 200.7 143.9 191.9 143.9C183.2 143.8 175.1 148.8 171.4 156.7L124.5 251.1L19.71 267.2C.9566 269.9-6.618 293.1 7.007 306.5l75.87 74l-17.1 104.6c-1.25 6.75 .5878 13.51 4.963 18.63C74.22 509 80.64 512 87.52 512c3.625 0 7.436-.9554 10.69-2.705l93.79-49.38l93.74 49.38c3.25 1.625 6.991 2.675 10.62 2.675c6.75 0 13.23-2.97 17.61-8.22c4.25-5.125 6.273-11.98 5.148-18.61L301.2 380.5l75.92-74C390.6 293 383.1 269.1 364.2 267.2zM266.7 369.3l17.62 103.1l-92.5-48.75l-92.5 48.75l17.62-103.1L42.22 296.1l103.4-15l46.38-93.88l46.13 93.88l103.5 15L266.7 369.3zM256 112h64v64C320 184.8 327.2 192 336 192S352 184.8 352 176v-64h64c8.844 0 16-7.156 16-16s-7.154-15.1-15.1-15.1L352 80v-64C352 7.156 344.8 0 336 0S320 7.156 320 16v64l-63.99 .0022C247.2 80 240 87.16 240 96S247.2 112 256 112zM496 208h-32v-32c0-8.844-7.155-15.1-15.1-15.1S432 167.2 432 176v32h-32c-8.844 0-15.1 7.162-15.1 16.01S391.2 240 400 240h32v32c0 8.844 7.158 16.01 16 16.01S464 280.8 464 272v-32h32c8.844 0 16-7.151 16-15.99S504.8 208 496 208z"/></svg>
 </span>
 <div class="ecom-theme-app-extension-content">
 <span class="ecom-theme-app-extension-title">{{review_platform | replace: '_', ' ' | replace: '-', ' ' | capitalize}}</span>
 <div class="ecom-placeholder-on-builder-mode" data-ecom-placeholder="The rating element will display when published to Online store"></div>
 </div>
 </div>
 </div>
 {%- else -%}
 {%- case review_platform -%}
 {%- when 'none' -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <p>Please select the rating platform in settings</p>
 </div>
 {%- when 'ali-reviews' -%}
 <div product-id="{{ product.id }}" product-handle="{{ product.handle }}" class="alireviews-review-star-rating"></div>
 {%- when 'vital-reviews'-%}
 <div class="bundle-aggregated_reviews"></div>
 {%- when 'opinew-reviews' -%}
 <div id='opinew-stars-plugin-product'>{% render 'opinew_review_stars_product' product:product %}</div>
 {%- when 'judgeme' -%}
 <div style='{{ jm_style }}' class='jdgm-widget jdgm-preview-badge' data-id='{{ product.id }}'>
 {{ product.metafields.judgeme.badge }}
 </div>
 {%- when 'product-reviews-addon' -%}
 <span class="stamped-product-reviews-badge stamped-main-badge" data-id="{{ product.id }}" data-product-sku="{{ product.handle }}" style="display: inline-block;">{{- product.metafields.stamped.badge -}}</span>
 {%- when 'areviews-aliexpress'-%}
 <div class="areviews_header_stars"></div>
 {%- when 'loox'-%}
 <a href="#looxReviews">
 <div class="loox-rating" data-id="{{ product.id }}" data-rating="{{ product.metafields.loox.avg_rating }}" data-raters="{{ product.metafields.loox.num_reviews }}"></div>
 </a>
 {% when 'ryviu'%}
 {%- if EComBuilderMode == true -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <div class="ecom-app-extension-info">
 <span class="ecom-theme-app-extension-icon">
 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M364.2 267.2l-104.8-15.28L212.6 156.7C208.7 148.8 200.7 143.9 191.9 143.9C183.2 143.8 175.1 148.8 171.4 156.7L124.5 251.1L19.71 267.2C.9566 269.9-6.618 293.1 7.007 306.5l75.87 74l-17.1 104.6c-1.25 6.75 .5878 13.51 4.963 18.63C74.22 509 80.64 512 87.52 512c3.625 0 7.436-.9554 10.69-2.705l93.79-49.38l93.74 49.38c3.25 1.625 6.991 2.675 10.62 2.675c6.75 0 13.23-2.97 17.61-8.22c4.25-5.125 6.273-11.98 5.148-18.61L301.2 380.5l75.92-74C390.6 293 383.1 269.1 364.2 267.2zM266.7 369.3l17.62 103.1l-92.5-48.75l-92.5 48.75l17.62-103.1L42.22 296.1l103.4-15l46.38-93.88l46.13 93.88l103.5 15L266.7 369.3zM256 112h64v64C320 184.8 327.2 192 336 192S352 184.8 352 176v-64h64c8.844 0 16-7.156 16-16s-7.154-15.1-15.1-15.1L352 80v-64C352 7.156 344.8 0 336 0S320 7.156 320 16v64l-63.99 .0022C247.2 80 240 87.16 240 96S247.2 112 256 112zM496 208h-32v-32c0-8.844-7.155-15.1-15.1-15.1S432 167.2 432 176v32h-32c-8.844 0-15.1 7.162-15.1 16.01S391.2 240 400 240h32v32c0 8.844 7.158 16.01 16 16.01S464 280.8 464 272v-32h32c8.844 0 16-7.151 16-15.99S504.8 208 496 208z"/></svg>
 </span>
 <div class="ecom-theme-app-extension-content">
 <span class="ecom-theme-app-extension-title">Ryviu</span>
 <div class="ecom-placeholder-on-builder-mode" data-ecom-placeholder="The rating element will display when published to Online store"></div>
 </div>
 </div>
 {%- else -%}
 <div class="review-widget">
 <ryviu-widget-total
 reviews_data="{{product.metafields.ryviu.product_reviews_info | escape }}"
 product_id="{{product.id}}" handle="{{product.handle}}">
 </ryviu-widget-total>
 </div>
 {%- endif -%}
 {%- when 'yotpo-social-reviews' -%}
 <div class="yotpo bottomLine" style="display:inline-block" data-product-id="{{ product.id }}"> </div>
 {%- when 'aliexpress-reviews-importer'-%}
 <div id="shop_booster_rate_6" product-id="{{ product.id }}"></div>
 {%- when 'rivyo-product-review'-%}
 <div class="wc_product_review_badge" data-handle="{{ product.handle }}" data-product_id="{{ product.id }}"></div>
 {%-when 'growave' -%}
 {% capture the_snippet_review_avg %}{% render 'ssw-widget-avg-rate-listing', product: product %}{% endcapture %}
 {% unless the_snippet_review_avg contains 'Liquid error' %}
 {{ the_snippet_review_avg }}
 {% endunless %}
 {%- when 'smart-aliexpress-reviews'-%}
 <a href="#scm-reviews-importer">
 <div id="scm-product-detail-rate" class="scm-reviews-rate" data-rate-version2= {{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo | json}}>
 </div>
 </a>
 {%- when 'photo-reviews' -%}
 <div id='opinew-stars-plugin-product'>{% render 'opinew_review_stars_product' product:product %}</div>
 {%- when 'lai-reviews' -%}
 <div class="scm-reviews-rate" data-rate-version2="{{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo | json | escape }}" data-product-id="{{ product.id }}"></div>
 {%- when 'product-reviews' -%}
 <span class="shopify-product-reviews-badge" data-id="{{ product.id }}"></span>
 {%- when 'rivyo' -%}
 <div class="wc_product_review_badge" data-handle="{{ product.handle }}" data-product_id="{{ product.id }}"></div>
 {%- when 'sealapps-product-review' -%}
 <div class="custom-vstar-rating-widget" product-id="{{ product.id }}"></div>
 {%- when 'ali-product-review' -%}
 <div product-id="{{ product.id }}" class="alr-display-review-badge"></div>
 {%- when 'klaviyo-reviews' -%}
 <div class="klaviyo-star-rating-widget" data-id="{{product.id}}" data-product-title="{{product.title}}" data-product-type="{{product.type}}"></div>
 {%- when 'air-reviews' -%}
 <div class="AirReviews-Widget AirReviews-Widget--Stars" data-review-avg="{{ product.metafields.air_reviews_product.review_avg }}" data-review-count="{{ product.metafields.air_reviews_product.review_count }}"></div>
 {% else %}
 <p>The rating platform not supported</p>
 {%-endcase-%}
 {% endif %}
 {%- else -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <p>Please select the rating platform in settings</p>
 </div>
 {%- endif -%}
 </div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-7iwzweue0ul" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__price" deep="2"><div class="ecom-product-single__price-wrapper"><div class="ecom-product-single__price-container"><div class="ecom-product-single__price-container-grid"><div class="ecom-product-single ecom-product-single__price--prices">
 {%- liquid
 if product.has_only_default_variant
 assign target = product
 else
 assign target = product.selected_or_first_available_variant
 endif
 -%}
 <div class="ecom-product-single__price--sale {% if target.compare_at_price == nil or target.compare_at_price < target.price %} ecom-product-single__price-normal {% endif %}" data-price="{{target.price}}">
 {%- if settings.currency_code_enabled -%}
 {{target.price | money_with_currency}}
 {%- else -%}
 {{target.price| money }}
 {%- endif -%}
 </div>

 <div class="ecom-product-single__price--regular" {%- if target.compare_at_price == nil or target.compare_at_price < target.price -%} style="display:none" {% endif %}>
 {%- if settings.currency_code_enabled -%}
 {{target.compare_at_price | money_with_currency}}
 {%- else -%}
 {{target.compare_at_price | money }}
 {%- endif -%}
 </div>

 {%- capture taxes_text -%}{%- endcapture -%}
 {%- if cart.taxes_included or shop.shipping_policy.body != blank -%}
 {% if taxes_text %}
 <div class="ec-product__tax caption rte">
 {%- if shop.taxes_included -%}

 {%- endif -%}
 {%- if shop.shipping_policy.body != blank -%}

 {%- endif -%}
 </div>
 {%- endif -%}
 {%- endif -%}
 </div><div class="ecom-product-single__price--badges-wrapper">
 <div class="ecom-product-single__price--badges">
 {%- assign savings = target.compare_at_price | minus: target.price | times: 100.0 | divided_by: target.compare_at_price | round -%}

 {%- if target.compare_at_price != nil and target.compare_at_price > target.price and target.available == true -%}
 <span class="ecom-product-single__price--badges-sale" data-text=" {price}% OFF" data-sale="{{savings}}" data-type="percent">
 {price}% OFF
 </span>
 {% endif %}
 <span class="ecom-product-single__price--badges-sold-out" {% if target.available %} style="display:none" {% endif %}>
 Sold out
 </span>
 </div>
 </div></div><div class="ecom-product-single-afterpay">

 </div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-66mdb3czs6k" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__description" deep="2"><div class="ecom-product-single__description-wrapper"><div class="ecom-product-single__description-container" data-show-type="short"><div class="ecom-product-single__description--paragraph"><div>
 <div>{{ product.description | strip_html | truncatewords: 30 }}</div>
 </div></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-cqzwkzvo0ci" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__inventory" deep="2"><div class="ecom-product-single__inventory-wrapper"><div class="ecom-product-single__inventory-container">
 {%-liquid
 if use_variant
 assign target = product.selected_or_first_available_variant
 else
 assign target = product
 endif
 assign stock_class = 'ecom-product-single-status--instock'
 if target.available == false
 assign stock_class = 'ecom-product-single-status--outstock'
 endif
 -%}
 <p
 class="ecom-product-single__inventory-status {{stock_class}}"
 data-product_id = "{{product.id}}"
 data-hide_day_of_the_week="undefined"
 data-format="undefined"
 data-variant="{{ product.selected_or_first_available_variant.id }}"
 data-instock-text="In stock"
 data-outstock-text="Outstock"
 data-inventory-text="{quantity} items left in stock"
 data-show-less-than="100"
 data-preorder-text="Preorder text"
 data-show-next-incoming-date="true"
 data-unavailable-text="Unavailable"
 data-incoming-date-text="Variant is out of stock. The product will available at: {next_incoming_date}"
 >
 In stock
 </p>
 </div><div class="ecom-product-single__inventory-progress-bar"><div class="ecom-product-single__inventory-progress-bar--timer"></div></div></div></div></div> <div class="ecom-block ecom-core core__block ecom-yi00rqsw3aq" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__variant-picker" deep="2"><div class="ecom-product-single__variant-picker-wrapper ecom_not_hide_dropdown_arrow"><div class="ecom-product-single__variant-picker-container {% if product.has_only_default_variant%} ecom-product-single__variant-picker--only-default{% endif%}" data-picker-type="color" data-ecom-placeholder="">
 {% assign ecom_has_variant_picker = true%}
 {% assign variant_selected = product.selected_or_first_available_variant%}
 <div class="ecom-product-single__variant-picker--main">
 {%- capture swatch_option_temp -%}Color{%- endcapture-%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {%- assign swatch_option = swatch_option | split: ',' -%}
 {%liquid
 assign colors = shop.metafields.ecomposer.colors
 %}
 {% unless product.has_only_default_variant %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-product-single__picker-main ecom-product-single__picker-option-{{option.name | handleize }}">
 <span class="ecom-product-single__picker-main-label ecom-product-single__picker--option-label" data-option-index="{{current_option.position | minus: 1}}">
 <span class="ecom-product-variant--option-label-text">{{ current_option.name }}</span>
 </span>
 <ul class="ecom-product-single__picker-colors-list">
 {%- assign index = current_option.position | prepend: 'option' -%}
 {% assign value_key_selected = variant_selected[index] | downcase %}
 {%- for value in current_option.values -%}
 {% assign value_key = value | downcase | strip %}
 <li data-option-index="{{ option_index }}" class="ecom-product-single__swatch-item ecom-product-single__picker-colors-item {% if value_key == value_key_selected %}ecom-box-active{% endif %}" data-value="{{ value | escape }}">
 <span {% if colors and colors.value[value_key] != blank %} style="{{colors.value[value_key]}}"{% else %} class="ecom-product-single__picker-colors--no-color" {% endif %}>
 </span>
 </li>
 {%- endfor -%}
 </ul>
 </div>
 {% continue%}
 {%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-product-single__picker-option-{{option.name | handleize }}">
 <span class="ecom-product-single__picker-radio-label ecom-product-single__picker--option-label" data-option-index="{{option_index}}">
 <span class="ecom-product-variant--option-label-text">{{option.name}}</span>
 </span>

 <ul class="ecom-product-single__picker-radio-list">
 {% for value in option.values %}
 <li class="ecom-product-single__swatch-item ecom-product-single__picker-radio-list-item {% if value == variant_selected[index] %}ecom-button-active{% endif %}" data-option-index="{{ option_index }}" data-value="{{ value | escape }}">
 {{value}}
 </li>
 {% endfor %}
 </ul>

 </div>
 {%- endfor -%}
 {% endunless %}
 </div><div class="ecom-product-single__variant-picker--options">
 <select name="id" class="ecom-product-single-select-id {% if product.has_only_default_variant %} ecom-product-single__picker-default-variant {% endif %}" data-product-id="{{product.id}}" data-json-product="product-json-{{product.id}}-ecom-yi00rqsw3aq" id="ecom-variant-selector-{{product.id}}-ecom-yi00rqsw3aq">
 {% for variant in product.variants%}
 <option value="{{variant.id}}" {% if variant_selected.id == variant.id %} selected="selected" {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div><div class="ecom-product-single__variant-picker--json">
 <script type="application/json" id="product-json-{{product.id}}-ecom-yi00rqsw3aq">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div></div></div></div></div> <div class="ecom-block ecom-core core__block ecom-07z8zzjef1qr" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__quantity" deep="2"><div class="ecom-product-single__quantity-wrapper"><div class="ecom-product-single__quantity-container"><label class="ecom-product-single__quantity-container-label" for="quantity"></label><div class="ecom-product-single__quantity-controls"><button type="button" class="ecom-product-single__quantity-controls-button ecom-product-single__quantity-controls-minus" aria-label="control minus"><span class="w-5 ecom-product-single__quantity-controls-icon"><svg class="inline-flex w-5 " viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 10a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 0 1.5h-6.5a.75.75 0 0 1-.75-.75Z" fill="currentColor"></path></svg></span></button><label for="quantity" style="display: none;">Quantity</label><input name="quantity" type="number" id="quantity" max="9999" min="1" class="ecom-product-single__quantity-input" required=""><button type="button" class="ecom-product-single__quantity-controls-button ecom-product-single__quantity-controls-plus" aria-label="control plus"><span class="w-5 ecom-product-single__quantity-controls-icon"><svg class="inline-flex w-5 " viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.75 6.75a.75.75 0 0 0-1.5 0v2.5h-2.5a.75.75 0 0 0 0 1.5h2.5v2.5a.75.75 0 0 0 1.5 0v-2.5h2.5a.75.75 0 0 0 0-1.5h-2.5v-2.5Z" fill="currentColor"></path></svg></span></button></div></div></div></div></div> <div class="ecom-block ecom-core core__block ecom-mwx0o77333i ec-button-action" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__add-to-cart" deep="2"><div class="ecom-product-single__add-to-cart-wrapper"><div class="ecom-product-single__add-to-cart-container"><div class="ecom-product-single__add-to-cart-buttons"><div class="ecom-product-single__add-to-cart-buttons-wrapper"><button type="submit" class="ecom-product-single__add-to-cart--submit ecom-flex animated" data-text-add-cart="Add to cart" data-text-pre-order="Pre Order" data-text-unavailable="Unavailable" data-text-outstock="Out stock" data-text-added-cart="Added to cart" data-message-added-cart="Added to cart" data-href="#" data-target="" data-action="popup" data-variant-id="{{product.first_available_variant.id}}"><span class="ecom-add-to-cart-text">Add to cart</span><span class="ecom__element--button-icon"></span></button></div></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-xpx9pwax29l" data-core-is="block"><div class="ecom-row ecom-core core__block ecom-0b2x5d0mugqo" data-id="ecom-0b2x5d0mugqo" style="z-index: inherit;"><div data-deep="3" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-6890bz4xgc6"><div class="core__column--wrapper" style=""><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-1dy0c7bmq6o" data-core-is="block"><div class="ecom__element element__heading ecom-element ecom-html" data-stopdrag="true" deep="3"><h3 class="ecom__heading ecom-db"><b>Guarantee safe</b> <i>&amp; secure checkout</i></h3></div></div> <div class="ecom-block ecom-core core__block ecom-b0tj80po3oh" data-core-is="block"><div class="ecom-element ecom-base-image" deep="3"><figure><div class="ecom-container-image ecom-image-align" data-stopdrag=""><div data-stopdrag="" class="ecom-image-content-position ecom-image-default ecom-base-image-container-overlay" style=""><picture class="ecom-image-picture"><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0726/1221/7115/files/trus.svg?v=1678270241" alt="trus" style=""></picture></div></div></figure></div></div> </div></div></div><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__column--resize" style="display: none;"></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-b96utpn9o9" data-core-is="block"><div class="ecom__element element__heading ecom-element ecom-html" data-stopdrag="true" deep="2"><h3 class="ecom__heading ecom-db">Share:</h3></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-q1h83mc4ctb" data-core-is="block"><div class="ecom__element ecom-element element__social" deep="2"><div class="ecom__element-social facebook" style=""><a href="https://www.facebook.com/sharer/sharer.php?u={current-link}" alt="facebook" class="element-social-link" target="_blank"><div class="social-icon"><span><svg version="1.1" id="lni_lni-facebook-filled" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 64 64" style="enable-background:new 0 0 64 64;" xml:space="preserve">
 <path d="M47.4,25.8L47.4,25.8h-5.6h-2v-2v-6.2v-2h2H46c1.1,0,2-0.8,2-2V3c0-1.1-0.8-2-2-2h-7.3c-7.9,0-13.4,5.6-13.4,13.9v8.7v2h-2
 h-6.8c-1.4,0-2.7,1.1-2.7,2.7v7.2c0,1.4,1.1,2.7,2.7,2.7h6.6h2v2v20.1c0,1.4,1.1,2.7,2.7,2.7h9.4c0.6,0,1.1-0.3,1.5-0.7
 s0.7-1.1,0.7-1.7l0,0l0,0V40.3v-2h2.1H46c1.3,0,2.3-0.8,2.5-2v-0.1v-0.1l1.4-6.9c0.1-0.7,0-1.5-0.6-2.3
 C49.1,26.4,48.2,25.9,47.4,25.8z"></path>
 </svg></span></div></a></div><div class="ecom__element-social twitter" style=""><a href="https://twitter.com/intent/tweet?url={current-link}" alt="twitter" class="element-social-link" target="_blank"><div class="social-icon"><span><svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 512 512"><path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"></path></svg></span></div></a></div><div class="ecom__element-social linkedIn" style=""><a href="https://www.linkedin.com/shareArticle?mini=true&amp;url={current-link}" alt="linkedIn" class="element-social-link" target="_blank"><div class="social-icon"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M100.28 448H7.4V148.9h92.88zM53.79 108.1C24.09 108.1 0 83.5 0 53.8a53.79 53.79 0 0 1 107.58 0c0 29.7-24.1 54.3-53.79 54.3zM447.9 448h-92.68V302.4c0-34.7-.7-79.2-48.29-79.2-48.29 0-55.69 37.7-55.69 76.7V448h-92.78V148.9h89.08v40.8h1.3c12.4-23.5 42.69-48.3 87.88-48.3 94 0 111.28 61.9 111.28 142.3V448z"></path></svg></span></div></a></div></div></div> </div></div></div><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__column--resize" style="display: none;"></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> </div></div></div><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__column--resize" style="display: none;"></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
 {% unless ecom_has_variant_picker %}
 <input type="hidden" name="id" value="{{product.selected_or_first_available_variant.id}}" />
 <script type="application/json" id="product-json-{{product.id}}-ecom-v2xxl78d4e">
 {%- render "ecom_product_json", product: product -%}
 </script>
 {% endunless %}
 {%- endform -%}
 {% assign ecom_has_variant_picker = false %}
 {%- endif -%}

</div></div>
{% schema %}
{
 "name": "Untitled",
 "locales": {},
 "settings": [
 {
 "type": "header",
 "content": "The section was generated by [Ecomposer](https:\/\/ecomposer.io).",
 "info": "\n EComposer is a Visual Page Builder app on Shopify AppStore.\n It provides 100+ pre-built templates in library,\n so you can build unlimited pages that you can imagine with it.\n "
 },
 {
 "type": "header",
 "content": "[EDIT WITH EComposer APP \u2192](https:\/\/ecomposer.app\/shop?open_builder=1&page=quickview&id=65d80a3f5c865bdfa4074a12&utm_source=theme-customize)",
 "info": "(*You must install the app to start editing this section [Learn more](https:\/\/help.ecomposer.io\/docs\/getting-started\/installation\/))"
 }
 ]
}
{% endschema %}