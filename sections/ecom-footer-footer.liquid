{% comment %}
 EComposer (https://ecomposer.io)
 You SHOULD NOT modify source code in this page because
 It is automatically generated from EComposer
 At 2025-07-02 10:43:54
 {% endcomment %} 
 
 {% assign ecom_root_url = routes.root_url %}
 
 {% if ecom_root_url != '/'%}
 {% assign ecom_root_url = routes.root_url | append: '/' %}
 {% endif %}
 <link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-modal_css" href="https://cdn.ecomposer.app/vendors/css/ecom_modal.css" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.9" /><noscript><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-modal_css" href="https://cdn.ecomposer.app/vendors/css/ecom_modal.css" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.9" /></noscript><script type="text/javascript" asyc="async" id="ecom-vendors-modal_js" src="https://cdn.ecomposer.app/vendors/js/ecom_modal_new.js" ></script>
{%capture section_id %}ecom-footer-footer{% endcapture%}{% if section and section_id == section.id and headless == true and false%}
{{ content_for_header }}
{% render 'ecom_header', ECOM_THEME: true %}{% endif %}<link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/>
{{'ecom-6784ca5f1ff512529601ad24.css' | asset_url | stylesheet_tag }}
<script src="{{'ecom-6784ca5f1ff512529601ad24.js' | asset_url }}" defer="defer"></script>
<script type="text/javascript" class="ecom-page-info">
 window.EComposer = window.EComposer || {};
 window.EComposer.TEMPLATE_ID="6784ca5f1ff512529601ad24";
 window.EComposer.FOOTER = {"template_id":"6784ca5f1ff512529601ad24","title":"footer","type":"footer","slug":"ecom-footer","plan_id":4};
 </script>
<div class="ecom-builder" id="ecom-footer"><div class="ecom-sections" data-section-id="{{section.id}}"><section class="ecom-row ecom-core ecom-section ecom-nisoq47o8e" data-id="ecom-nisoq47o8e" style="z-index: inherit;"><div data-deep="1" class="core__row--columns core__row--full"><div class="ecom-column ecom-core ecom-y5hp0i44wkm"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-mv5lj4xc2yq" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-57e6ldsefus" data-id="ecom-57e6ldsefus" style="z-index: inherit;"><div data-deep="2" class="core__row--columns core__row--full"><div class="ecom-column ecom-core ecom-0t95g0bjigxe"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-hhxc588vpeb" data-core-is="block"><div class="ecom-element ecom-base-image" deep="2"><figure><div class="ecom-container-image ecom-image-align" data-stopdrag=""><div data-stopdrag="" class="ecom-image-content-position ecom-image-default ecom-base-image-container-overlay" style=""><a href="https://www.sicotas.com/" class="ecom-image-picture-link ecom-image-picture ecom-base-image-container-overlay ecom-flex"><picture><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/logo_af50637a-c0ed-4b4b-85d2-dba49d27166b.png?v=1736761411" alt="logo_af50637a-c0ed-4b4b-85d2-dba49d27166b" width="1658" height="259"></picture></a></div></div></figure></div></div> <div class="ecom-block ecom-core core__block ecom-987xrjowdd9" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed"><div>Email:&nbsp; <a href="https://www.sicotas.com/pages/email"><b><EMAIL></b></a> </div><div>Phone: <b>(*************</b></div></div></div></div> <div class="ecom-block ecom-core core__block ecom-4gsdm3mr20l" data-core-is="block"><div class="ecom__element ecom-element element__social" deep="2"><div class="ecom__element-social custom" style=""><a href="https://www.instagram.com/sicotas/" alt="custom" aria-label="custom" class="element-social-link element-social-link__custom" target="_blank"><div class="social-icon"><span><svg version="1.1" id="lni_lni-instagram-original" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 64 64" style="enable-background:new 0 0 64 64;" xml:space="preserve" fill="currentColor"><g><path d="M62.9,19.2c-0.1-3.2-0.7-5.5-1.4-7.6S59.7,7.8,58,6.1s-3.4-2.7-5.4-3.5c-2-0.8-4.2-1.3-7.6-1.4C41.5,1,40.5,1,32,1s-9.4,0-12.8,0.1s-5.5,0.7-7.6,1.4S7.8,4.4,6.1,6.1s-2.8,3.4-3.5,5.5c-0.8,2-1.3,4.2-1.4,7.6S1,23.5,1,32s0,9.4,0.1,12.8c0.1,3.4,0.7,5.5,1.4,7.6c0.7,2.1,1.8,3.8,3.5,5.5s3.5,2.8,5.5,3.5c2,0.7,4.2,1.3,7.6,1.4C22.5,63,23.4,63,31.9,63s9.4,0,12.8-0.1s5.5-0.7,7.6-1.4c2.1-0.7,3.8-1.8,5.5-3.5s2.8-3.5,3.5-5.5c0.7-2,1.3-4.2,1.4-7.6c0.1-3.2,0.1-4.2,0.1-12.7S63,22.6,62.9,19.2z M57.3,44.5c-0.1,3-0.7,4.6-1.1,5.8c-0.6,1.4-1.3,2.5-2.4,3.5c-1.1,1.1-2.1,1.7-3.5,2.4c-1.1,0.4-2.7,1-5.8,1.1c-3.2,0-4.2,0-12.4,0s-9.3,0-12.5-0.1c-3-0.1-4.6-0.7-5.8-1.1c-1.4-0.6-2.5-1.3-3.5-2.4c-1.1-1.1-1.7-2.1-2.4-3.5c-0.4-1.1-1-2.7-1.1-5.8c0-3.1,0-4.1,0-12.4s0-9.3,0.1-12.5c0.1-3,0.7-4.6,1.1-5.8c0.6-1.4,1.3-2.5,2.3-3.5c1.1-1.1,2.1-1.7,3.5-2.3c1.1-0.4,2.7-1,5.8-1.1c3.2-0.1,4.2-0.1,12.5-0.1s9.3,0,12.5,0.1c3,0.1,4.6,0.7,5.8,1.1c1.4,0.6,2.5,1.3,3.5,2.3c1.1,1.1,1.7,2.1,2.4,3.5c0.4,1.1,1,2.7,1.1,5.8c0.1,3.2,0.1,4.2,0.1,12.5S57.4,41.3,57.3,44.5z"></path><path d="M32,16.1c-8.9,0-15.9,7.2-15.9,15.9c0,8.9,7.2,15.9,15.9,15.9S48,40.9,48,32S40.9,16.1,32,16.1z M32,42.4c-5.8,0-10.4-4.7-10.4-10.4S26.3,21.6,32,21.6c5.8,0,10.4,4.6,10.4,10.4S37.8,42.4,32,42.4z"></path><ellipse cx="48.7" cy="15.4" rx="3.7" ry="3.7"></ellipse></g></svg></span></div></a></div><div class="ecom__element-social custom" style=""><a href="https://www.facebook.com/profile.php?id=100063806004977" alt="custom" aria-label="custom" class="element-social-link element-social-link__custom" target="_blank"><div class="social-icon"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor"><path d="M 19.253906 2 C 15.311906 2 13 4.0821719 13 8.8261719 L 13 13 L 8 13 L 8 18 L 13 18 L 13 30 L 18 30 L 18 18 L 22 18 L 23 13 L 18 13 L 18 9.671875 C 18 7.884875 18.582766 7 20.259766 7 L 23 7 L 23 2.2050781 C 22.526 2.1410781 21.144906 2 19.253906 2 z"></path></svg></span></div></a></div><div class="ecom__element-social custom" style=""><a href="https://www.youtube.com/channel/UCCn0Sd5HymHpHL64UNhYK3w" alt="custom" aria-label="custom" class="element-social-link element-social-link__custom" target="_blank"><div class="social-icon"><span><svg version="1.1" id="lni_lni-youtube" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 64 64" style="enable-background:new 0 0 64 64;" xml:space="preserve" fill="currentColor"><path d="M61.7,17.1c-0.7-2.7-2.8-4.8-5.5-5.5C51.4,10.3,32,10.3,32,10.3s-19.4,0-24.2,1.3c-2.7,0.7-4.8,2.8-5.5,5.5C1,22,1,32,1,32s0,10.1,1.3,14.9c0.7,2.7,2.8,4.8,5.5,5.5c4.8,1.3,24.2,1.3,24.2,1.3s19.4,0,24.2-1.3c2.7-0.7,4.8-2.8,5.5-5.5C63,42.1,63,32,63,32S63,22,61.7,17.1z M25.8,41.3V22.7L41.9,32L25.8,41.3z"></path></svg></span></div></a></div><div class="ecom__element-social custom" style=""><a href="https://www.pinterest.com/Sicotasofficial/" alt="custom" aria-label="custom" class="element-social-link element-social-link__custom" target="_blank"><div class="social-icon"><span><svg version="1.1" id="lni_lni-pinterest" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 64 64" style="enable-background:new 0 0 64 64;" xml:space="preserve" fill="currentColor"><path d="M1.8,32.1C2,41.4,6.3,50.7,13.6,56.3c2.3,1.7,4.8,2.7,7.4,3.8c-1.1-7.1,1.6-14.2,3.1-21.2c0.2-0.7,0.3-1.5,0.3-2.3c0-1.1-0.4-2.2-0.7-3.3c-0.3-1.8-0.1-3.7,0.7-5.4c1.1-2.3,3.7-4.1,6-3.2c2.1,0.8,2.9,3.6,2.5,5.8c-0.4,2.3-1.6,4.3-2.2,6.5c-0.7,2.2-0.6,4.9,1,6.4c1.5,1.4,3.9,1.5,5.8,0.7c2.8-1.2,4.6-4,5.7-6.8c2-5.2,1.6-11.8-2.5-15.6C39,20,36.6,18.9,34,18.5c-4.4-0.7-9.2,0.6-12.3,3.8s-4.5,8.1-3.2,12.3c0.4,1.4,1.2,2.8,1.5,4.2s0.2,3.2-0.8,4.2c-0.1,0.1-0.2,0.2-0.4,0.3c-0.2,0.1-0.5-0.1-0.7-0.2c-1.9-1.2-3.4-3.1-4.3-5.1c-2.8-6.1-1.4-13.7,3-18.7s11.5-7.4,18.1-6.5c6.2,0.8,12.3,4.5,14.9,10.2c1.6,3.4,1.9,7.3,1.3,11c-0.6,3.8-2.1,7.4-4.6,10.3s-6.1,4.9-9.9,5.1c-3.1,0.2-6.4-0.9-8-3.5c-1,5.4-2.9,10.7-5.7,15.4c-0.1,0.2,6.4,1.6,7,1.6c7.4,0.6,15.3-2.3,21.1-6.9C67,43.3,65.3,19,49.5,7c-8.2-6.3-17.9-7.5-27.5-4.1c-2.9,1-5.6,2.7-8.1,4.5c-4,3-7.2,6.9-9.3,11.4C2.5,22.9,1.7,27.5,1.8,32.1z"></path></svg></span></div></a></div><div class="ecom__element-social custom" style=""><a href="https://www.tiktok.com/@sicotas" alt="custom" aria-label="custom" class="element-social-link element-social-link__custom" target="_blank"><div class="social-icon"><span><svg width="64" height="64" viewBox="0 0 64 64" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M55.619 1H8.38095C4.3111 1 1 4.3111 1 8.38095V55.619C1 59.6889 4.3111 63 8.38095 63H55.619C59.6889 63 63 59.6889 63 55.619V8.38095C63 4.3111 59.6889 1 55.619 1ZM49.7231 28.0482C49.3846 28.0811 49.0447 28.0984 48.7046 28.0999C46.8681 28.1002 45.0604 27.6431 43.4448 26.7699C41.8291 25.8968 40.4563 24.635 39.4503 23.0986V40.1294C39.4503 42.6189 38.7121 45.0526 37.329 47.1226C35.9458 49.1926 33.9799 50.806 31.6799 51.7587C29.3798 52.7114 26.8489 52.9607 24.4072 52.475C21.9654 51.9893 19.7226 50.7905 17.9622 49.0301C16.2018 47.2697 15.0029 45.0268 14.5172 42.5851C14.0316 40.1433 14.2808 37.6124 15.2335 35.3124C16.1863 33.0123 17.7996 31.0464 19.8696 29.6633C21.9396 28.2801 24.3733 27.5419 26.8629 27.5419C27.1256 27.5419 27.3825 27.5655 27.6408 27.5818V33.7847C27.3825 33.7537 27.1286 33.7065 26.8629 33.7065C25.159 33.7065 23.5249 34.3833 22.3201 35.5881C21.1153 36.7929 20.4385 38.427 20.4385 40.1309C20.4385 41.8347 21.1153 43.4688 22.3201 44.6736C23.5249 45.8784 25.159 46.5552 26.8629 46.5552C30.4116 46.5552 33.5456 43.7593 33.5456 40.2106L33.6076 11.2861H39.5419C39.813 13.8645 40.9818 16.2656 42.844 18.0696C44.7062 19.8735 47.1433 20.9654 49.729 21.1544V28.0482" fill="black"></path></svg></span></div></a></div></div></div> </div></div></div></div> <div class="ecom-column ecom-core ecom-cx9x7zak5bb"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-ddjtnxv8n8d" data-core-is="block"><div class="ecom__element element__heading ecom-element ecom-html" data-stopdrag="true" deep="2"><h6 class="ecom__heading ecom-db">About us</h6></div></div> <div class="ecom-block ecom-core core__block ecom-j75l8k7v0j" data-core-is="block"><div class="ecom-element ecom-shopify ecom-shopify__menu" deep="2"><div class="ecom-element ecom-shopify ecom-shopify__menu-wrapper" data-show-all="false"><div class="ecom-element ecom-shopify ecom-shopify__menu-container">
 {%- capture link_handle -%}my-account{%- endcapture -%}
 {%- unless link_handle == empty -%}
 {%- assign menu = linklists[link_handle] -%}
 {% if menu == blank %}
 {%- assign menu = linklists['main-menu'] -%}
 {% endif %}
 {%- capture show_humber -%}false{%- endcapture -%}
 {%- capture icon_menu -%}{%- endcapture -%}
 {%- capture icon_menu_active -%}{%- endcapture -%}
 {%- if show_humber == 'true' -%}
 <div class="ecom-menu__icon-humber--wrapper">
 <div class="ecom-menu__icon-humber">
 
 </div>
 </div>
 <div class="ecom-shopify__menu-list--mobile--wrapper">
 <ul class="ecom-shopify__menu-list--mobile" data-show-humber="undefined">
 <div class="modal-header">
 <span class="ecom-menu-collapse-close--mobile">
 <svg
 xmlns="http://www.w3.org/2000/svg"
 class="w-6 h-6"
 fill="none"
 viewBox="0 0 24 24"
 stroke="currentColor"
 >
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
 </svg>
 </span>
 </div>
 {% if menu and menu.links.size %}
 {% for link in menu.links %}
 {% assign child_link = link.links %}
 <li class="ecom-shopify__menu-item ecom-shopify__menu-item-type--{{link.type}} {% if child_link.size > 0 %}ecom-shopify__menu-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %} ">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title {% if link.active or link.child_active %} ecom-shopify__menu-item--active ecom-text-active {% endif %}"
 href="{{link.url}}"
 title="{{link.title | escape }}"
 target="_self"
 >
 {{ link.title }}
 {%- if link.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if child_link.size > 0 %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{child_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for child in child_link %}
 {% assign grand_link = child.links %}
 <li class="ecom-shopify__menu-child-link-item ecom-shopify__menu-child-link-item-type--{{child.type}} {% if grand_link.size > 0 %}ecom-shopify__menu-child-link-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %} {% if child.active or child.child_active%} ecom-shopify__menu-child-link-item--active ecom-text-active{% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{child.url}}"
 title="{{child.title | escape }}"
 target="_self"
 >
 {{ child.title }}
 {%- if child.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if grand_link.size > 0 %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{grand_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for grand in grand_link %}
 <li class="ecom-shopify__menu-grand-link-item ecom-menu_item ecom-items ecom-shopify__menu-grand-link-item-type--{{grand.type}} {% if grand.active or grand.child_active %}ecom-shopify__menu-grand-link-item--active ecom-text-active{% endif %}">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 </li>
 {% endfor %}
 </ul>
 {%- endif -%}
 </li>
 {% endfor %}
 </ul>
 {% endif %}
 </li>
 {% endfor %}
 {% endif %}
 </ul>
 </div>
 {%- endif -%}
 <div class="ecom-shopify__menu-list--wrapper ecom-flex">
 <ul class="ecom-shopify__menu-list" data-menu-layout="vertical">
 {% if menu and menu.links.size %}
 {% for link in menu.links %}
 {% assign child_link = link.links %}
 <li class="ecom-shopify__menu-item ecom-shopify__menu-item-type--{{link.type}} {% if child_link.size > 0 %}ecom-shopify__menu-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title {% if link.active or link.child_active %} ecom-shopify__menu-item--active ecom-text-active {% endif %}"
 href="{{link.url}}"
 title="{{link.title | escape }}"
 target="_self"
 >
 {{ link.title }}
 {%- if link.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if child_link.size > 0 %}
 {%- capture layout -%}
 vertical
 {%- endcapture -%}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{child_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for child in child_link %}
 {% assign grand_link = child.links %}
 <li class="ecom-shopify__menu-child-link-item ecom-shopify__menu-child-link-item-type--{{child.type}} {% if grand_link.size > 0 %}ecom-shopify__menu-child-link-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{child.url}}"
 title="{{child.title | escape }}"
 target="_self"
 >
 {{ child.title }}
 {%- if child.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 {% if grand_link.size > 0 and layout == 'horizontal' %}
 {% for grand in grand_link %}
 <a
 class="ecom-menu_title ecom-element--menu_title {% if grand.active %} ecom-shopify__menu-child-link-item--active ecom-text-active{% endif %}"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 {% endfor %}
 {% endif %}
 </div>
 {% if grand_link.size > 0 and layout == 'vertical' %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{grand_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for grand in grand_link %}
 <li class="ecom-shopify__menu-grand-link-item ecom-menu_item ecom-items ecom-shopify__menu-grand-link-item-type--{{grand.type}} {% if grand.active or grand.child_active %}ecom-shopify__menu-grand-link-item--active ecom-text-active{% endif %}">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 </li>
 {% endfor %}
 </ul>
 {%- endif -%}
 </li>
 {% endfor %}
 </ul>
 {% endif %}
 </li>
 {% endfor %}
 {% endif %}
 </ul>
 </div>
 {% else %}
 <p>Select a menu to show</p>
 {%- endunless -%}
 </div></div></div></div> </div></div></div></div> <div class="ecom-column ecom-core ecom-vh3xq8usfo8"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="2"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-5xpaqhdjx6n" data-core-is="block"><div class="ecom__element element__heading ecom-element ecom-html" data-stopdrag="true" deep="2"><h6 class="ecom__heading ecom-db">Account</h6></div></div> <div class="ecom-block ecom-core core__block ecom-tlewd5fi80k" data-core-is="block"><div class="ecom-element ecom-shopify ecom-shopify__menu" deep="2"><div class="ecom-element ecom-shopify ecom-shopify__menu-wrapper" data-show-all="false"><div class="ecom-element ecom-shopify ecom-shopify__menu-container">
 {%- capture link_handle -%}account{%- endcapture -%}
 {%- unless link_handle == empty -%}
 {%- assign menu = linklists[link_handle] -%}
 {% if menu == blank %}
 {%- assign menu = linklists['main-menu'] -%}
 {% endif %}
 {%- capture show_humber -%}false{%- endcapture -%}
 {%- capture icon_menu -%}{%- endcapture -%}
 {%- capture icon_menu_active -%}{%- endcapture -%}
 {%- if show_humber == 'true' -%}
 <div class="ecom-menu__icon-humber--wrapper">
 <div class="ecom-menu__icon-humber">
 
 </div>
 </div>
 <div class="ecom-shopify__menu-list--mobile--wrapper">
 <ul class="ecom-shopify__menu-list--mobile" data-show-humber="undefined">
 <div class="modal-header">
 <span class="ecom-menu-collapse-close--mobile">
 <svg
 xmlns="http://www.w3.org/2000/svg"
 class="w-6 h-6"
 fill="none"
 viewBox="0 0 24 24"
 stroke="currentColor"
 >
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
 </svg>
 </span>
 </div>
 {% if menu and menu.links.size %}
 {% for link in menu.links %}
 {% assign child_link = link.links %}
 <li class="ecom-shopify__menu-item ecom-shopify__menu-item-type--{{link.type}} {% if child_link.size > 0 %}ecom-shopify__menu-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %} ">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title {% if link.active or link.child_active %} ecom-shopify__menu-item--active ecom-text-active {% endif %}"
 href="{{link.url}}"
 title="{{link.title | escape }}"
 target="_self"
 >
 {{ link.title }}
 {%- if link.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if child_link.size > 0 %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{child_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for child in child_link %}
 {% assign grand_link = child.links %}
 <li class="ecom-shopify__menu-child-link-item ecom-shopify__menu-child-link-item-type--{{child.type}} {% if grand_link.size > 0 %}ecom-shopify__menu-child-link-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %} {% if child.active or child.child_active%} ecom-shopify__menu-child-link-item--active ecom-text-active{% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{child.url}}"
 title="{{child.title | escape }}"
 target="_self"
 >
 {{ child.title }}
 {%- if child.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if grand_link.size > 0 %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{grand_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for grand in grand_link %}
 <li class="ecom-shopify__menu-grand-link-item ecom-menu_item ecom-items ecom-shopify__menu-grand-link-item-type--{{grand.type}} {% if grand.active or grand.child_active %}ecom-shopify__menu-grand-link-item--active ecom-text-active{% endif %}">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 </li>
 {% endfor %}
 </ul>
 {%- endif -%}
 </li>
 {% endfor %}
 </ul>
 {% endif %}
 </li>
 {% endfor %}
 {% endif %}
 </ul>
 </div>
 {%- endif -%}
 <div class="ecom-shopify__menu-list--wrapper ecom-flex">
 <ul class="ecom-shopify__menu-list" data-menu-layout="vertical">
 {% if menu and menu.links.size %}
 {% for link in menu.links %}
 {% assign child_link = link.links %}
 <li class="ecom-shopify__menu-item ecom-shopify__menu-item-type--{{link.type}} {% if child_link.size > 0 %}ecom-shopify__menu-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title {% if link.active or link.child_active %} ecom-shopify__menu-item--active ecom-text-active {% endif %}"
 href="{{link.url}}"
 title="{{link.title | escape }}"
 target="_self"
 >
 {{ link.title }}
 {%- if link.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if child_link.size > 0 %}
 {%- capture layout -%}
 vertical
 {%- endcapture -%}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{child_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for child in child_link %}
 {% assign grand_link = child.links %}
 <li class="ecom-shopify__menu-child-link-item ecom-shopify__menu-child-link-item-type--{{child.type}} {% if grand_link.size > 0 %}ecom-shopify__menu-child-link-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{child.url}}"
 title="{{child.title | escape }}"
 target="_self"
 >
 {{ child.title }}
 {%- if child.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 {% if grand_link.size > 0 and layout == 'horizontal' %}
 {% for grand in grand_link %}
 <a
 class="ecom-menu_title ecom-element--menu_title {% if grand.active %} ecom-shopify__menu-child-link-item--active ecom-text-active{% endif %}"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 {% endfor %}
 {% endif %}
 </div>
 {% if grand_link.size > 0 and layout == 'vertical' %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{grand_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for grand in grand_link %}
 <li class="ecom-shopify__menu-grand-link-item ecom-menu_item ecom-items ecom-shopify__menu-grand-link-item-type--{{grand.type}} {% if grand.active or grand.child_active %}ecom-shopify__menu-grand-link-item--active ecom-text-active{% endif %}">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 </li>
 {% endfor %}
 </ul>
 {%- endif -%}
 </li>
 {% endfor %}
 </ul>
 {% endif %}
 </li>
 {% endfor %}
 {% endif %}
 </ul>
 </div>
 {% else %}
 <p>Select a menu to show</p>
 {%- endunless -%}
 </div></div></div></div> </div></div></div></div> <div class="ecom-column ecom-core ecom-hj4gfzz236u"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="3"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-4ok7jo5au8p" data-core-is="block"><div class="ecom__element element__heading ecom-element ecom-html" data-stopdrag="true" deep="2"><h6 class="ecom__heading ecom-db">Customer Service</h6></div></div> <div class="ecom-block ecom-core core__block ecom-swnshbp4zx" data-core-is="block"><div class="ecom-element ecom-shopify ecom-shopify__menu" deep="2"><div class="ecom-element ecom-shopify ecom-shopify__menu-wrapper" data-show-all="false"><div class="ecom-element ecom-shopify ecom-shopify__menu-container">
 {%- capture link_handle -%}footer{%- endcapture -%}
 {%- unless link_handle == empty -%}
 {%- assign menu = linklists[link_handle] -%}
 {% if menu == blank %}
 {%- assign menu = linklists['main-menu'] -%}
 {% endif %}
 {%- capture show_humber -%}false{%- endcapture -%}
 {%- capture icon_menu -%}{%- endcapture -%}
 {%- capture icon_menu_active -%}{%- endcapture -%}
 {%- if show_humber == 'true' -%}
 <div class="ecom-menu__icon-humber--wrapper">
 <div class="ecom-menu__icon-humber">
 
 </div>
 </div>
 <div class="ecom-shopify__menu-list--mobile--wrapper">
 <ul class="ecom-shopify__menu-list--mobile" data-show-humber="undefined">
 <div class="modal-header">
 <span class="ecom-menu-collapse-close--mobile">
 <svg
 xmlns="http://www.w3.org/2000/svg"
 class="w-6 h-6"
 fill="none"
 viewBox="0 0 24 24"
 stroke="currentColor"
 >
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
 </svg>
 </span>
 </div>
 {% if menu and menu.links.size %}
 {% for link in menu.links %}
 {% assign child_link = link.links %}
 <li class="ecom-shopify__menu-item ecom-shopify__menu-item-type--{{link.type}} {% if child_link.size > 0 %}ecom-shopify__menu-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %} ">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title {% if link.active or link.child_active %} ecom-shopify__menu-item--active ecom-text-active {% endif %}"
 href="{{link.url}}"
 title="{{link.title | escape }}"
 target="_self"
 >
 {{ link.title }}
 {%- if link.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if child_link.size > 0 %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{child_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for child in child_link %}
 {% assign grand_link = child.links %}
 <li class="ecom-shopify__menu-child-link-item ecom-shopify__menu-child-link-item-type--{{child.type}} {% if grand_link.size > 0 %}ecom-shopify__menu-child-link-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %} {% if child.active or child.child_active%} ecom-shopify__menu-child-link-item--active ecom-text-active{% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{child.url}}"
 title="{{child.title | escape }}"
 target="_self"
 >
 {{ child.title }}
 {%- if child.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if grand_link.size > 0 %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{grand_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for grand in grand_link %}
 <li class="ecom-shopify__menu-grand-link-item ecom-menu_item ecom-items ecom-shopify__menu-grand-link-item-type--{{grand.type}} {% if grand.active or grand.child_active %}ecom-shopify__menu-grand-link-item--active ecom-text-active{% endif %}">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 </li>
 {% endfor %}
 </ul>
 {%- endif -%}
 </li>
 {% endfor %}
 </ul>
 {% endif %}
 </li>
 {% endfor %}
 {% endif %}
 </ul>
 </div>
 {%- endif -%}
 <div class="ecom-shopify__menu-list--wrapper ecom-flex">
 <ul class="ecom-shopify__menu-list" data-menu-layout="vertical">
 {% if menu and menu.links.size %}
 {% for link in menu.links %}
 {% assign child_link = link.links %}
 <li class="ecom-shopify__menu-item ecom-shopify__menu-item-type--{{link.type}} {% if child_link.size > 0 %}ecom-shopify__menu-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title {% if link.active or link.child_active %} ecom-shopify__menu-item--active ecom-text-active {% endif %}"
 href="{{link.url}}"
 title="{{link.title | escape }}"
 target="_self"
 >
 {{ link.title }}
 {%- if link.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if child_link.size > 0 %}
 {%- capture layout -%}
 vertical
 {%- endcapture -%}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{child_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for child in child_link %}
 {% assign grand_link = child.links %}
 <li class="ecom-shopify__menu-child-link-item ecom-shopify__menu-child-link-item-type--{{child.type}} {% if grand_link.size > 0 %}ecom-shopify__menu-child-link-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{child.url}}"
 title="{{child.title | escape }}"
 target="_self"
 >
 {{ child.title }}
 {%- if child.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 {% if grand_link.size > 0 and layout == 'horizontal' %}
 {% for grand in grand_link %}
 <a
 class="ecom-menu_title ecom-element--menu_title {% if grand.active %} ecom-shopify__menu-child-link-item--active ecom-text-active{% endif %}"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 {% endfor %}
 {% endif %}
 </div>
 {% if grand_link.size > 0 and layout == 'vertical' %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{grand_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for grand in grand_link %}
 <li class="ecom-shopify__menu-grand-link-item ecom-menu_item ecom-items ecom-shopify__menu-grand-link-item-type--{{grand.type}} {% if grand.active or grand.child_active %}ecom-shopify__menu-grand-link-item--active ecom-text-active{% endif %}">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 </li>
 {% endfor %}
 </ul>
 {%- endif -%}
 </li>
 {% endfor %}
 </ul>
 {% endif %}
 </li>
 {% endfor %}
 {% endif %}
 </ul>
 </div>
 {% else %}
 <p>Select a menu to show</p>
 {%- endunless -%}
 </div></div></div></div> </div></div></div></div> <div class="ecom-column ecom-core ecom-qub9v2haqhj"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="4"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-rdzov9cl4af" data-core-is="block"><div class="ecom__element element__heading ecom-element ecom-html" data-stopdrag="true" deep="2"><h6 class="ecom__heading ecom-db">B2B program</h6></div></div> <div class="ecom-block ecom-core core__block ecom-7pt0e3ygo7n" data-core-is="block"><div class="ecom-element ecom-shopify ecom-shopify__menu" deep="2"><div class="ecom-element ecom-shopify ecom-shopify__menu-wrapper" data-show-all="false"><div class="ecom-element ecom-shopify ecom-shopify__menu-container">
 {%- capture link_handle -%}b2b-program{%- endcapture -%}
 {%- unless link_handle == empty -%}
 {%- assign menu = linklists[link_handle] -%}
 {% if menu == blank %}
 {%- assign menu = linklists['main-menu'] -%}
 {% endif %}
 {%- capture show_humber -%}false{%- endcapture -%}
 {%- capture icon_menu -%}{%- endcapture -%}
 {%- capture icon_menu_active -%}{%- endcapture -%}
 {%- if show_humber == 'true' -%}
 <div class="ecom-menu__icon-humber--wrapper">
 <div class="ecom-menu__icon-humber">
 
 </div>
 </div>
 <div class="ecom-shopify__menu-list--mobile--wrapper">
 <ul class="ecom-shopify__menu-list--mobile" data-show-humber="undefined">
 <div class="modal-header">
 <span class="ecom-menu-collapse-close--mobile">
 <svg
 xmlns="http://www.w3.org/2000/svg"
 class="w-6 h-6"
 fill="none"
 viewBox="0 0 24 24"
 stroke="currentColor"
 >
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
 </svg>
 </span>
 </div>
 {% if menu and menu.links.size %}
 {% for link in menu.links %}
 {% assign child_link = link.links %}
 <li class="ecom-shopify__menu-item ecom-shopify__menu-item-type--{{link.type}} {% if child_link.size > 0 %}ecom-shopify__menu-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %} ">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title {% if link.active or link.child_active %} ecom-shopify__menu-item--active ecom-text-active {% endif %}"
 href="{{link.url}}"
 title="{{link.title | escape }}"
 target="_self"
 >
 {{ link.title }}
 {%- if link.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if child_link.size > 0 %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{child_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for child in child_link %}
 {% assign grand_link = child.links %}
 <li class="ecom-shopify__menu-child-link-item ecom-shopify__menu-child-link-item-type--{{child.type}} {% if grand_link.size > 0 %}ecom-shopify__menu-child-link-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %} {% if child.active or child.child_active%} ecom-shopify__menu-child-link-item--active ecom-text-active{% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{child.url}}"
 title="{{child.title | escape }}"
 target="_self"
 >
 {{ child.title }}
 {%- if child.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if grand_link.size > 0 %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{grand_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for grand in grand_link %}
 <li class="ecom-shopify__menu-grand-link-item ecom-menu_item ecom-items ecom-shopify__menu-grand-link-item-type--{{grand.type}} {% if grand.active or grand.child_active %}ecom-shopify__menu-grand-link-item--active ecom-text-active{% endif %}">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 </li>
 {% endfor %}
 </ul>
 {%- endif -%}
 </li>
 {% endfor %}
 </ul>
 {% endif %}
 </li>
 {% endfor %}
 {% endif %}
 </ul>
 </div>
 {%- endif -%}
 <div class="ecom-shopify__menu-list--wrapper ecom-flex">
 <ul class="ecom-shopify__menu-list" data-menu-layout="vertical">
 {% if menu and menu.links.size %}
 {% for link in menu.links %}
 {% assign child_link = link.links %}
 <li class="ecom-shopify__menu-item ecom-shopify__menu-item-type--{{link.type}} {% if child_link.size > 0 %}ecom-shopify__menu-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title {% if link.active or link.child_active %} ecom-shopify__menu-item--active ecom-text-active {% endif %}"
 href="{{link.url}}"
 title="{{link.title | escape }}"
 target="_self"
 >
 {{ link.title }}
 {%- if link.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 </div>
 {% if child_link.size > 0 %}
 {%- capture layout -%}
 vertical
 {%- endcapture -%}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{child_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for child in child_link %}
 {% assign grand_link = child.links %}
 <li class="ecom-shopify__menu-child-link-item ecom-shopify__menu-child-link-item-type--{{child.type}} {% if grand_link.size > 0 %}ecom-shopify__menu-child-link-item--has-children {% else %} ecom-shopify__menu-item--link {% endif %}">
 <div class="ecom-menu_item ecom-items">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{child.url}}"
 title="{{child.title | escape }}"
 target="_self"
 >
 {{ child.title }}
 {%- if child.levels > 0 -%}
 <div class="ecom-element--menu_icon">
 <div class="ecom-items--icon ecom-element--menu_icon--normal">
 {{ icon_menu }}
 </div>
 <div class="ecom-items--icon ecom-element--menu_icon--active">
 {{ icon_menu_active }}
 </div>
 </div>
 {%- endif -%}
 </a>
 {% if grand_link.size > 0 and layout == 'horizontal' %}
 {% for grand in grand_link %}
 <a
 class="ecom-menu_title ecom-element--menu_title {% if grand.active %} ecom-shopify__menu-child-link-item--active ecom-text-active{% endif %}"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 {% endfor %}
 {% endif %}
 </div>
 {% if grand_link.size > 0 and layout == 'vertical' %}
 <ul
 class="ecom-shopify__menu-sub-menu"
 data-menu-size="{{grand_link.size}}"
 data-menu-level="{{child_link.level}}"
 >
 {% for grand in grand_link %}
 <li class="ecom-shopify__menu-grand-link-item ecom-menu_item ecom-items ecom-shopify__menu-grand-link-item-type--{{grand.type}} {% if grand.active or grand.child_active %}ecom-shopify__menu-grand-link-item--active ecom-text-active{% endif %}">
 <a
 class="ecom-menu_title ecom-element--menu_title"
 href="{{grand.url}}"
 title="{{grand.title | escape }}"
 target="_self"
 >
 {{ grand.title }}
 </a>
 </li>
 {% endfor %}
 </ul>
 {%- endif -%}
 </li>
 {% endfor %}
 </ul>
 {% endif %}
 </li>
 {% endfor %}
 {% endif %}
 </ul>
 </div>
 {% else %}
 <p>Select a menu to show</p>
 {%- endunless -%}
 </div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-nq0ocqe8ygg" data-core-is="block"><div class="ecom__element ecom-element element__divi" deep="1"><div class="ecom__element-divi" style="--divi-line-height: 1px;"><div class="divi-line divi-style" style="display: block;"></div><div class="divi-cont" style="--flex-desktop: 0.5; --flex-tablet: 0.5; --flex-mobile: 0.5; --divider-width: auto; --divider-width-tablet: auto; --divider-width-mobile: auto; display: none;"><div class="divi-cont-before divi-style" style="display: block;"></div><div class="divi-cont-after divi-style" style="display: block;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-svtngnfik6" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-xx0iyiy3f4n" data-id="ecom-xx0iyiy3f4n" style="z-index: inherit;"><div data-deep="2" class="core__row--columns core__row--full"><div class="ecom-column ecom-core ecom-gzlnx0rdiq"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-tn4uigxdta" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed">© 2025 SICOTAS Copyright Powered by Sicotas</div></div></div> </div></div></div></div> <div class="ecom-column ecom-core ecom-0aoh8pn9eysf"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-cuyc4zjrs7v" data-core-is="block"><div class="ecom-element ecom-base-image" deep="2"><figure><div class="ecom-container-image ecom-image-align" data-stopdrag=""><div data-stopdrag="" class="ecom-image-content-position ecom-image-default ecom-base-image-container-overlay" style=""><picture class="ecom-image-picture"><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/122222.png?v=1736760438" alt="122222" width="1690" height="100"></picture></div></div></figure></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
</div></div>
{% schema %}
{
 "name": "footer",
 "settings": [
 {
 "type": "header",
 "content": "The section was generated by [Ecomposer](https:\/\/ecomposer.io).",
 "info": "\n EComposer is a Visual Page Builder app on Shopify AppStore.\n It provides 100+ pre-built templates in library,\n so you can build unlimited pages that you can imagine with it.\n Please don't add Shopify sections to EComposer's page in the theme customize. If you do this, Shopify sections will be deleted when you republish your page in EComposer\n "
 },
 {
 "type": "header",
 "content": "[EDIT WITH EComposer APP \u2192](https:\/\/ecomposer.app\/shop?open_builder=1&page=footer&id=6784ca5f1ff512529601ad24&utm_source=theme-customize)",
 "info": "(*You must install the app to start editing this section [Learn more](https:\/\/help.ecomposer.io\/docs\/getting-started\/installation\/))"
 }
 ],
 "locales": {}
}
{% endschema %}