{% comment %}
 EComposer (https://ecomposer.io)
 You SHOULD NOT modify source code in this page because
 It is automatically generated from EComposer
 At 2025-01-24 03:04:31
 {% endcomment %} 
 
 {% assign ecom_root_url = routes.root_url %}
 {% if ecom_root_url != '/'%}
 {% assign ecom_root_url = routes.root_url | append: '/' %}
 {% endif %}
 <link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.6" /><noscript><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.6" /></noscript><script type="text/javascript" asyc="async" id="ecom-vendors-slider_js" src="https://cdn.ecomposer.app/vendors/js/<EMAIL>" ></script>
{%capture section_id %}ecom-blog-article-template-article{% endcapture%}{% if section and section_id == section.id and headless == true %}
{{ content_for_header }}
{% render 'ecom_header', ECOM_THEME: true %}{% endif %}<link href="https://fonts.googleapis.com/css?family=Inter:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/>
{{'ecom-678777d2c0e0f140c60a1002.css' | asset_url | stylesheet_tag }}
<script src="{{'ecom-678777d2c0e0f140c60a1002.js' | asset_url }}" defer="defer"></script>
<script type="text/javascript" class="ecom-page-info">
 window.EComposer = window.EComposer || {};
 window.EComposer.TEMPLATE_ID="678777d2c0e0f140c60a1002";
 window.EComposer.TEMPLATE = {"template_id":"678777d2c0e0f140c60a1002","title":"Blog article template","type":"article","slug":"ecom-blog-article-template","plan_id":2};
 </script>
<div class="ecom-builder" id="ecom-blog-article-template"><div class="ecom-sections" data-section-id="{{section.id}}">
 {% unless tmp_block %}{% assign tmp_block = blog %}{% endunless %}
 {%- capture article_selected -%}blogs-sicotas-brand-story/transform-your-home-for-thanksgiving-with-these-furniture-essentials{%- endcapture-%}
 {% assign article_selected = article_selected | strip %}
 {%- if request.page_type != 'article' and article_selected != blank -%}
 {%- assign article = articles[article_selected]-%}
 {% assign blog_handle = article_selected | split: '/' | first %}
 {% assign blog = blogs[blog_handle] %}
 {%- endif -%}
 <section class="ecom-row ecom-core ecom-section ecom-w5sd25et7am" data-id="ecom-w5sd25et7am" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="1" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-sp7f6mu1vel"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-6sz94jy4ikx" data-core-is="block"><div class="ecom-element ecom-base ecom-base__breadcrumbs--wrapper" deep="1">
 {%- if EComBuilderMode -%}
 {% capture page_type %}article{% endcapture %}
 {%- else -%}
 {%- assign page_type = request.page_type %}
 {%- endif %}
 {%- capture icon_parent -%}
 
 {%- endcapture -%}
 {%- capture icon_breadcrumbs -%}
 <span class="ecom-base__breadcrumbs--icon">
 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor"><path d="M312.2 2.281c-7.469-4.484-17.34-2.125-21.94 5.484l-288 480c-4.562 7.578-2.094 17.41 5.5 21.95C10.34 511.3 13.19 512 16 512c5.406 0 10.72-2.766 13.72-7.766l288-480C322.3 16.66 319.8 6.828 312.2 2.281z"></path></svg>
 </span>
 
 {%- endcapture -%}
 {%- unless page_type == 'index' or page_type == 'list-collections' or page_type == '404' -%}
 <nav class="ecom-base__breadcrumbs ecom-flex" role="navigation" aria-label="breadcrumbs">
 <div class="ecom-base__breadcrumbs__list">
 {{ icon_parent }}<a class="ecom-base__breadcrumbs__link" href="/">Home</a>
 {%- case page_type -%}
 {%- when 'page' -%}
 {{ icon_breadcrumbs }}
 {%- if EComBuilderMode -%}
 <a class="ecom-base__breadcrumbs__link" href="#" aria-current="page">Untitled</a>
 {%- else -%}
 <a class="ecom-base__breadcrumbs__link" href="{{ page.url }}" aria-current="page">{{ page.title }}</a>
 {%- endif -%}
 {%- when 'cart' -%}
 {{ icon_breadcrumbs }}
 <a class="ecom-base__breadcrumbs__link" href="{{ routes.cart_url }}" aria-current="cart"></a>
 {%- when 'product' -%}
 {%- if collection.url -%}
 {{ icon_breadcrumbs }}{{ collection.title | link_to: collection.url }}
 {%- else -%}
 {% for collection in product.collections %}
 {% if collection.handle == 'all' or collection.handle == 'frontpage' or collection.title == 'All' %}
 {% continue%}
 {% else %}
 {{ icon_breadcrumbs }}{{ collection.title | link_to: collection.url }}
 {% break %}
 {% endif%}
 {% endfor %}
 {%- endif -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ product.url }}" aria-current="page">{{ product.title }}</a>
 {%- when 'collection' and collection.handle -%}
 {%- if current_tags -%}
 {{ icon_breadcrumbs }}{{ collection.title | link_to: collection.url }}
 {%- capture tag_url -%}{{ collection.url }}/{{ current_tags | join: "+"}}{%- endcapture -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: " + "}}</a>
 {%- else -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ collection.url }}" aria-current="page">{{ collection.title }}</a>
 {%- endif -%}
 {%- when 'blog' -%}
 {%- if current_tags -%}
 {{ icon_breadcrumbs }}{{ blog.title | link_to: blog.url }}
 {%- capture tag_url -%}{{blog.url}}/tagged/{{ current_tags | join: "+" }}{%- endcapture -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: " + " }}</a>
 {%- else -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ blog.url }}" aria-current="page">{{ blog.title }}</a>
 {%- endif -%}
 {%- when 'article' -%}
 {{ icon_breadcrumbs }}{{ blog.title | link_to: blog.url }}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ article.url }}" aria-current="page">{{ article.title }}</a>
 {%- else -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ request.path }}" aria-current="page">{{ page_title }}</a>
 {%- endcase -%}
 </div>
 </nav>
 {%- endunless -%}{%- assign page_type = request.page_type %}
 {%- if page_type == 'index' -%}
 {{ icon_parent }}
 {%- if EComBuilderMode -%}
 <a class="ecom-base__breadcrumbs__link" href="#" aria-current="index">Home</a>
 {%- else -%}
 <a class="ecom-base__breadcrumbs__link" href="/" aria-current="index">Home</a>
 {%- endif -%}
 {%- endif -%}
 </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-61odzf44nn5" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article--tags" deep="1"><div class="ecom-shopify__article--tags-wrapper ecom-flex ecom-column"><div class="ecom-shopify__article--tags-inner">
 {%- capture limit -%}10{%-endcapture-%}
 {% assign limit = limit | plus: 0 %}
 {% if article and article.tags %}
 <ul class="ecom-shopify__article--tags-items ecom-flex ecom-wrap">
 {% for tag in article.tags limit: limit %}
 <li class="ecom-shopify__article--tags-item">
 <a href="{{ecom_root_url}}blogs/{{blog.handle}}/tagged/{{tag | handleize }}">{{ tag }}</a>
 </li>
 {% endfor %}
 </ul>
 {% else %}
 
 {% endif %}
 </div></div></div></div> <div class="ecom-block ecom-core core__block ecom-3hmfffd2q0b" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article-title" deep="1"><div class="ecom-shopify__article--title-wrapper"><div class="ecom-shopify__article--title-container">
 {% if article != blank %}
 <h2 itemprop="headline" class="ecom-shopify__article--title-text" href="{{article.url}}" title="{{article.title | escape }}">{{article.title | escape }}</h2>
 {% else %}
 
 {% endif %}
 </div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-urrlgcek1e" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-e68avtvotgk" data-id="ecom-e68avtvotgk" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="2" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-ezb854bsq8"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-exqew0qea6u" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article-author" deep="2"><div class="ecom-shopify__article-author--wrapper"><div class="ecom-shopify__article-author--container ecom-flex"><div class="ecom-shopify__article-author--avatar">
 
 {%- if article.user.image -%}
 <div class="ecom-image-default">
 <img loading="lazy" src="{{ article.user.image | img_url: 'master' }}" alt="{{ article.author }}">
 </div>
 {%- else -%}
 <div class="ecom-image-default">
 
 <img loading="lazy" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/1_1logo_dc3c0cf3-4122-4578-9765-d04bc967406a.jpg?v=1736938058" alt="{{ article.author }}"/>
 
 </div>
 {%- endif -%}
 
 </div><div class="ecom-shopify__article-author-information ecom-flex ecom-column"><div class="ecom-shopify__article-author--text">
 {% if article and article.author != blank %}
 {{ article.author }}
 {% else %}
 
 {% endif %}
 </div></div></div></div></div></div> <div class="ecom-block ecom-core core__block ecom-k22yvvojr2e" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article-date" deep="2"><div class="ecom-shopify__article-date--wrapper"><div class="ecom-shopify__article-date--container ecom-flex ecom-al_center"><div class="ecom-shopify__article-date-icon ecom-flex ecom-al_center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M280 248.7L339.1 338.7C347.3 349.7 344.3 364.6 333.3 371.1C322.3 379.3 307.4 376.3 300 365.3L236 269.3C233.4 265.4 232 260.7 232 256V120C232 106.7 242.7 96 256 96C269.3 96 280 106.7 280 120L280 248.7zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 48C141.1 48 48 141.1 48 256C48 370.9 141.1 464 256 464C370.9 464 464 370.9 464 256C464 141.1 370.9 48 256 48z"></path></svg></div><div class="ecom-shopify__article-date--date">
 {% if article and article.created_at != blank %}
 <div class="ecom-shopify__article-date-text">
 {{article.created_at | time_tag: format: 'date' }}
 </div>
 {% else %}
 
 {% endif %}
 </div></div></div></div></div> <div class="ecom-block ecom-core core__block ecom-km7illfasf" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article-comments-count" deep="2"><div class="ecom-shopify__article-comments-count--wrapper"><div class="ecom-shopify__article-comments-count--container ecom-flex ecom-al_center"><div class="ecom-shopify__article-comments-count-icon ecom-flex ecom-al_center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor"><path d="M 3 5 L 3 23 L 8 23 L 8 28.078125 L 14.351563 23 L 29 23 L 29 5 Z M 5 7 L 27 7 L 27 21 L 13.648438 21 L 10 23.917969 L 10 21 L 5 21 Z"></path></svg></div><div class="ecom-shopify__article-comments-count--count">
 {% if article != blank %}
 {% if article.comments_enabled? %}
 {% assign comments_count = article.comments_count %}
 {% if article.comments_count == 1%}
 <p class="ecom-shopify__article-comments-count--container-counter">
 {{comment_count}} comment
 </p>
 {% else %}
 <p class="ecom-shopify__article-comments-count--container-counter">
 {{comments_count}} comments
 </p>
 {% endif %}
 {% endif %}
 {% else %}
 
 {% endif %}
 </div></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-wx2in4yiere" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article--image" deep="1"><div class="ecom-shopify__article--image-wrapper">
 {% if article != blank %}
 {% if article.image %}
 <div class="ecom-shopify__article-hero-container ecom-image-align">
 <div class="ecom-shopify__article-hero-image ecom-image-default"
 itemprop="image"
 >
 <img srcset="
 {% if article.image.width >= 350 %}{{ article.image | img_url: '350x' }} 350w,{% endif %}
 {% if article.image.width >= 700 %}{{ article.image | img_url: '700x' }} 700w,{% endif %}
 {% if article.image.width >= 749 %}{{ article.image | img_url: '749x' }} 749w,{% endif %}
 {% if article.image.width >= 1498 %}{{ article.image | img_url: '1498x' }} 1498w,{% endif %}
 {% if article.image.width >= 1100 %}{{ article.image | img_url: '1100x' }} 1100w,{% endif %}
 {% if article.image.width >= 2200 %}{{ article.image | img_url: '2200x' }} 2200w,{% endif %}"
 sizes="(min-width: 1200px) 1100px, (min-width: 750px) calc(100vw - 10rem), 100vw"
 src="{{ article.image | img_url: '1100x' }}"
 loading="lazy"
 width="{{ article.image.width }}"
 height="{{ article.image.height }}"
 alt="{{ article.image.alt | escape }}">
 </div>
 </div>
 {% endif %}
 {% else %}
 <div>Please select the article in settings</div>
 {% endif %}
 </div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-cjd8u5cjvk6" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-0bra9a58qmdk" data-id="ecom-0bra9a58qmdk" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="2" class="core__row--columns"><div class="ecom-column ecom-core core__column--first ecom-l0es8yu8and"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-8l4rx7e0iux" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article--description" deep="2"><div class="ecom-shopify__article__description-wrapper"><div class="ecom-shopify__article__description-container" content_type="html"><div class="ecom-shopify__article__description--paragraph">
 {%- if article != blank -%}
 {{article.content}}
 {%- else -%}
 <div>Please select the article in settings</div>
 {%- endif -%}
 </div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-cc1frl5xe57" data-core-is="block"><div class="ecom__element ecom-element element__divi" deep="2"><div class="ecom__element-divi" style="--divi-line-height: 1px;"><div class="divi-line divi-style" style="display: block;"></div><div class="divi-cont" style="--flex-desktop: 0.5; --flex-tablet: 0.5; --flex-mobile: 0.5; --divider-width: auto; --divider-width-tablet: auto; --divider-width-mobile: auto; display: none;"><div class="divi-cont-before divi-style" style="display: block;"></div><div class="divi-cont-after divi-style" style="display: block;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-5ehjy4p3hqn" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article-prev_next" deep="2"><div class="ecom-shopify__article--prev_next-wrapper"><div class="ecom-shopify__article--prev_next-container ecom-flex ecom-fl_between"><div class="ecom-shopify__article--prev">
 {%- liquid
 assign prev_article = blog.previous_article
 if EComBuilderMode
 assign prev_article = blog.articles.first
 endif
 -%}
 {%- if prev_article -%}
 <div class="ecom-shopify__article--prev-content ecom-flex ecom-column">
 
 <div class="ecom-shopify__article--item-heading ecom-shopify__article--item-heading-prev ecom-flex ecom-al_center">
 
 <a href="{{prev_article.url}}" alt="{{prev_article.title}}">Previous Post</a>
 </div>
 
 <div class="ecom-shopify__article--item-body ecom-flex">
 
 {%- if prev_article.image -%}
 <div class="ecom-shopify__article--item-image ecom-col-auto">
 <div class="ecom-image-default">
 <img loading="lazy" src="{{ prev_article.image | img_url: '360x'}}" alt="{{ prev_article.image.src.alt | escape }}" />
 </div>
 </div>
 {%- endif -%}
 
 
 <div class="ecom-shopify__article--item-content ecom-flex ecom-column ecom-col">
 
 <div class="ecom-shopify__article--item-title ecom-shopify__article--item-title-prev">
 <a href="{{prev_article.url}}" alt="{{prev_article.title}}">{{ prev_article.title }}</a>
 </div>
 
 
 <div class="ecom-shopify__article--item-time ecom-flex ecom-al_center">
 
 <span class="ecom-shopify__article--item-icon ecom-flex ecom-al_center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M272 251.2L333.3 343.1C338.2 350.5 336.2 360.4 328.9 365.3C321.5 370.2 311.6 368.2 306.7 360.9L242.7 264.9C240.9 262.2 240 259.2 240 255.1V111.1C240 103.2 247.2 95.1 256 95.1C264.8 95.1 272 103.2 272 111.1L272 251.2zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 32C132.3 32 32 132.3 32 256C32 379.7 132.3 480 256 480C379.7 480 480 379.7 480 256C480 132.3 379.7 32 256 32z"></path></svg></span>
 
 <span>{{ prev_article.published_at | time_tag: format: 'abbreviated_date' }}</span>
 </div>
 
 </div>
 
 </div>
 </div>
 {%- endif -%}
 </div><div class="ecom-shopify__article--next">
 {%- liquid
 assign next_article = blog.next_article
 if EComBuilderMode
 assign next_article = blog.articles.last
 endif
 -%}
 {%- if next_article -%}
 <div class="ecom-shopify__article--next-content ecom-flex ecom-column">
 
 <div class="ecom-shopify__article--item-heading ecom-shopify__article--item-heading-next ecom-flex ecom-al_center ecom-fl_right">
 
 <a href="{{next_article.url}}" alt="{{next_article.title}}">Next Post</a>
 </div>
 
 <div class="ecom-shopify__article--item-body ecom-flex">
 
 {%- if next_article.image -%}
 <div class="ecom-shopify__article--item-image ecom-col-auto">
 <div class="ecom-image-default">
 <img loading="lazy" src="{{ next_article.image | img_url: '360x'}}" alt="{{ next_article.image.src.alt | escape }}" />
 </div>
 </div>
 {%- endif -%}
 
 
 <div class="ecom-shopify__article--item-content ecom-flex ecom-column ecom-col">
 
 <div class="ecom-shopify__article--item-title ecom-shopify__article--item-title-next">
 <a href="{{next_article.url}}" alt="{{next_article.title}}">{{ next_article.title }}</a>
 </div>
 
 
 <div class="ecom-shopify__article--item-time ecom-flex ecom-al_center ecom-fl_right">
 
 <span class="ecom-shopify__article--item-icon ecom-flex ecom-al_center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M272 251.2L333.3 343.1C338.2 350.5 336.2 360.4 328.9 365.3C321.5 370.2 311.6 368.2 306.7 360.9L242.7 264.9C240.9 262.2 240 259.2 240 255.1V111.1C240 103.2 247.2 95.1 256 95.1C264.8 95.1 272 103.2 272 111.1L272 251.2zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 32C132.3 32 32 132.3 32 256C32 379.7 132.3 480 256 480C379.7 480 480 379.7 480 256C480 132.3 379.7 32 256 32z"></path></svg></span>
 
 <span>{{ next_article.published_at | time_tag: format: 'abbreviated_date' }}</span>
 </div>
 
 </div>
 
 </div>
 </div>
 {%- endif -%}
 </div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-pxn1hhhtpmo" data-core-is="block"><div class="ecom__element ecom-element element__social" deep="2"><div class="ecom__element-social facebook icon-border" style=""><a href="https://www.facebook.com/sharer/sharer.php?u={current-link}" alt="facebook" aria-label="facebook" class="element-social-link" target="_blank"><div class="social-icon"><span><svg version="1.1" id="lni_lni-facebook-filled" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 64 64" style="enable-background:new 0 0 64 64;" xml:space="preserve">
 <path d="M47.4,25.8L47.4,25.8h-5.6h-2v-2v-6.2v-2h2H46c1.1,0,2-0.8,2-2V3c0-1.1-0.8-2-2-2h-7.3c-7.9,0-13.4,5.6-13.4,13.9v8.7v2h-2
 h-6.8c-1.4,0-2.7,1.1-2.7,2.7v7.2c0,1.4,1.1,2.7,2.7,2.7h6.6h2v2v20.1c0,1.4,1.1,2.7,2.7,2.7h9.4c0.6,0,1.1-0.3,1.5-0.7
 s0.7-1.1,0.7-1.7l0,0l0,0V40.3v-2h2.1H46c1.3,0,2.3-0.8,2.5-2v-0.1v-0.1l1.4-6.9c0.1-0.7,0-1.5-0.6-2.3
 C49.1,26.4,48.2,25.9,47.4,25.8z"></path>
 </svg></span></div><span class="element-social-label">Facebook</span></a></div><div class="ecom__element-social twitter icon-border" style=""><a href="https://twitter.com/intent/tweet?url={current-link}" alt="twitter" aria-label="twitter" class="element-social-link" target="_blank"><div class="social-icon"><span><svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 512 512"><path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"></path></svg></span></div><span class="element-social-label">Twitter</span></a></div><div class="ecom__element-social pinterest icon-border" style=""><a href="https://pinterest.com/pin/create/button/?url={current-link}" alt="pinterest" aria-label="pinterest" class="element-social-link" target="_blank"><div class="social-icon"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M 16.09375 4 C 11.01675 4 6 7.3833281 6 12.861328 C 6 16.344328 7.9584844 18.324219 9.1464844 18.324219 C 9.6364844 18.324219 9.9199219 16.958266 9.9199219 16.572266 C 9.9199219 16.112266 8.7460938 15.131797 8.7460938 13.216797 C 8.7460938 9.2387969 11.774359 6.4199219 15.693359 6.4199219 C 19.063359 6.4199219 21.556641 8.3335625 21.556641 11.851562 C 21.556641 14.478563 20.501891 19.40625 17.087891 19.40625 C 15.855891 19.40625 14.802734 18.516234 14.802734 17.240234 C 14.802734 15.370234 16 13.558906 16 11.628906 C 16 8.3529063 11.462891 8.94725 11.462891 12.90625 C 11.462891 13.73725 11.5665 14.657063 11.9375 15.414062 C 11.2555 18.353063 10 23.037406 10 26.066406 C 10 27.001406 10.133656 27.921422 10.222656 28.857422 C 10.390656 29.045422 10.307453 29.025641 10.564453 28.931641 C 13.058453 25.517641 12.827078 24.544172 13.955078 20.076172 C 14.564078 21.234172 16.137766 21.857422 17.384766 21.857422 C 22.639766 21.857422 25 16.736141 25 12.119141 C 25 7.2061406 20.75475 4 16.09375 4 z"></path></svg></span></div><span class="element-social-label">Pinterest</span></a></div><div class="ecom__element-social linkedIn icon-border" style=""><a href="https://www.linkedin.com/shareArticle?mini=true&amp;url={current-link}" alt="linkedIn" aria-label="linkedIn" class="element-social-link" target="_blank"><div class="social-icon"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M100.28 448H7.4V148.9h92.88zM53.79 108.1C24.09 108.1 0 83.5 0 53.8a53.79 53.79 0 0 1 107.58 0c0 29.7-24.1 54.3-53.79 54.3zM447.9 448h-92.68V302.4c0-34.7-.7-79.2-48.29-79.2-48.29 0-55.69 37.7-55.69 76.7V448h-92.78V148.9h89.08v40.8h1.3c12.4-23.5 42.69-48.3 87.88-48.3 94 0 111.28 61.9 111.28 142.3V448z"></path></svg></span></div><span class="element-social-label">LinkedIn</span></a></div><div class="ecom__element-social email icon-border" style=""><a href="mailto:?&amp;subject=&amp;cc=&amp;bcc=&amp;body={current-link}" alt="email" aria-label="email" class="element-social-link" target="_blank"><div class="social-icon"><span><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Capa_1" x="0px" y="0px" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve">
 <g>
 <path d="M485.743,85.333H26.257C11.815,85.333,0,97.148,0,111.589V400.41c0,14.44,11.815,26.257,26.257,26.257h459.487 c14.44,0,26.257-11.815,26.257-26.257V111.589C512,97.148,500.185,85.333,485.743,85.333z M475.89,105.024L271.104,258.626 c-3.682,2.802-9.334,4.555-15.105,4.529c-5.77,0.026-11.421-1.727-15.104-4.529L36.109,105.024H475.89z M366.5,268.761 l111.59,137.847c0.112,0.138,0.249,0.243,0.368,0.368H33.542c0.118-0.131,0.256-0.23,0.368-0.368L145.5,268.761 c3.419-4.227,2.771-10.424-1.464-13.851c-4.227-3.419-10.424-2.771-13.844,1.457l-110.5,136.501V117.332l209.394,157.046 c7.871,5.862,17.447,8.442,26.912,8.468c9.452-0.02,19.036-2.6,26.912-8.468l209.394-157.046v275.534L381.807,256.367 c-3.42-4.227-9.623-4.877-13.844-1.457C363.729,258.329,363.079,264.534,366.5,268.761z"></path>
 </g>
 </svg></span></div><span class="element-social-label">Email</span></a></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-dcglnk3ksae" data-core-is="block"><div class="ecom__element ecom-element element__divi" deep="2"><div class="ecom__element-divi" style="--divi-line-height: 1px;"><div class="divi-line divi-style" style="display: block;"></div><div class="divi-cont" style="--flex-desktop: 0.5; --flex-tablet: 0.5; --flex-mobile: 0.5; --divider-width: auto; --divider-width-tablet: auto; --divider-width-mobile: auto; display: none;"><div class="divi-cont-before divi-style" style="display: block;"></div><div class="divi-cont-after divi-style" style="display: block;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-dulgikmx789" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed">Comments</div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-bqmn985pf8e" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article-comments" deep="2"><div class="ecom-shopify__article-comments--wrapper"><div class="ecom-shopify__article--comments--container" data-ecom-placeholder="">
 {%- if article != blank -%}
 {% if article.comments_enabled? %}
 <div class="ecom-shopify__article-comment-wrapper">
 <div id="comments" class="ecom-shopify__article-comments">
 {%- if article.comments_count == 0 -%}
 <p>No comments</p>
 {%- endif -%}
 {%- if article.comments_count > 0 -%}
 {%- assign anchorId = '#comments-' | append: article.id -%}
 {% paginate article.comments by 3 %}
 <div class="ecom-shopify__article-comments">
 {%- if comment.status == 'pending' and comment.content -%}
 <article id="{{ comment.id }}" class="ecom-shopify__article-comments-comment ecom-flex ecom-column">
 
 {%- if comment.email -%}
 <div class="ecom-shopify__comment-author-image ecom-col-auto">
 <div class="ecom-image-default">
 <img loading="lazy" alt="{{ comment.author }}" src="https://www.gravatar.com/avatar/{{ comment.email | downcase | md5 }}" />
 </div>
 </div>
 {%- endif -%}
 
 <div class="ecom-shopify__comment-content ecom-col">
 {{ comment.content }}
 <footer class="right">
 <div class="ecom-flex ecom-al_center ecom-shopify__article-comments-meta">
 
 <span class="ecom-shopify__article-circle-divider ecom-shopify__article-caption-with-letter-spacing">{{ comment.author }}</span>
 </div>
 </footer>
 </div>
 </article>
 {%- endif -%}
 {%- for comment in article.comments -%}
 <article id="{{ comment.id }}" class="ecom-shopify__article-comments-comment ecom-flex">
 
 {%- if comment.email -%}
 <div class="ecom-shopify__comment-author-image ecom-col-auto">
 <div class="ecom-image-default">
 <img loading="lazy" alt="{{ comment.author }}" src="https://www.gravatar.com/avatar/{{ comment.email | downcase | md5 }}" />
 </div>
 </div>
 {%- endif -%}
 
 <div class="ecom-shopify__comment-content ecom-col">
 {{ comment.content }}
 <footer class="ecom-flex">
 <div class="ecom-flex ecom-al_center">
 
 <span class="ecom-shopify__article-circle-divider ecom-shopify__article-caption-with-letter-spacing">{{ comment.author }}</span>
 </div>
 <div class="ecom-flex ecom-al_center ecom-shopify__article-comments-meta">
 
 <span class="ecom-shopify__article-comments-icon ecom-flex ecom-al_center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M280 248.7L339.1 338.7C347.3 349.7 344.3 364.6 333.3 371.1C322.3 379.3 307.4 376.3 300 365.3L236 269.3C233.4 265.4 232 260.7 232 256V120C232 106.7 242.7 96 256 96C269.3 96 280 106.7 280 120L280 248.7zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 48C141.1 48 48 141.1 48 256C48 370.9 141.1 464 256 464C370.9 464 464 370.9 464 256C464 141.1 370.9 48 256 48z"></path></svg></span>
 
 <span class="caption-with-letter-spacing">{{ comment.created_at | time_tag: format: "abbreviated_date" }}</span>
 </div>
 </footer>
 </div>
 </article>
 {%- endfor -%}
 {%- if paginate.pages > 1 -%}
 <nav role="navigation">
 <ol class="ecom-pagination-navigation ecom-collection__pagination-navigation">
 {%- if paginate.previous -%}
 <li class="ecom-pagination-item ecom-prev" style="">
 <a class="ecom-paginate-action ecom-button-default" href="{{ paginate.previous.url }}">
 
 Previous
 </a>
 </li>
 {%- else -%}
 <li class="ecom-pagination-item ecom-prev ecom-paginate-action ecom-disabled"
 style=""
 >
 
 Previous
 </li>
 {%- endif -%}
 {%- for part in paginate.parts -%}
 {%- if part.is_link -%}
 <li class="ecom-pagination-item">
 <a href="{{ part.url }}" title="{{ part.title }}">
 {{ part.title }}
 </a>
 </li>
 {%- else -%}
 {%- if part.title == paginate.current_page -%}
 <li class="ecom-pagination-item ecom-button-active" aria-current="page">
 {{ part.title }}
 </li>
 {%- else -%}
 <li class="ecom-pagination-item">
 {{ part.title }}
 </li>
 {%- endif -%}
 {%- endif -%}
 {%- endfor -%}
 {%- if paginate.next -%}
 <li class="ecom-pagination-item ecom-next" style="">
 <a class="ecom-paginate-action" href="{{ paginate.next.url }}">
 Next
 
 </a>
 </li>
 {%- else -%}
 <li class="ecom-pagination-item ecom-next ecom-paginate-action ecom-collection__pagination--disabled"
 style="">
 Next
 
 </li>
 {%- endif -%}
 </ol>
 </nav>
 {%- endif -%}
 </div>
 {% endpaginate %}
 {%- endif -%}
 </div>
 </div>
 {% endif %}
 {%- else -%}
 
 {% endif %}
 </div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-zqp2k92k2eo" data-core-is="block"><div class="ecom__element ecom-element element__divi" deep="2"><div class="ecom__element-divi" style="--divi-line-height: 1px;"><div class="divi-line divi-style" style="display: block;"></div><div class="divi-cont" style="--flex-desktop: 0.5; --flex-tablet: 0.5; --flex-mobile: 0.5; --divider-width: auto; --divider-width-tablet: auto; --divider-width-mobile: auto; display: none;"><div class="divi-cont-before divi-style" style="display: block;"></div><div class="divi-cont-after divi-style" style="display: block;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-cljs5o0g1cv" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed">Leave a comment</div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-ffxzsptjug" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed">Your Email Address Will Not Be Published. Required Fields Are Marked *</div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-rgds68kzy1f" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__article-comments-count" deep="2"><div class="ecom-shopify__article-comments-count--wrapper"><div class="ecom-shopify__article-comments-count--container" data-ecom-placeholder="">
 
 {%- if article != blank -%}
 {% capture layout %}horizontial{% endcapture %}
 {%- if article.comments_enabled? -%}
 {% capture icon_success %}
 <svg aria-hidden="true" focusable="false" role="presentation" class="icon ecom-shopify__article--icon-success" viewBox="0 0 13 13">
 <path d="M6.5 12.35C9.73087 12.35 12.35 9.73086 12.35 6.5C12.35 3.26913 9.73087 0.65 6.5 0.65C3.26913 0.65 0.65 3.26913 0.65 6.5C0.65 9.73086 3.26913 12.35 6.5 12.35Z" fill="#428445" stroke="white" stroke-width="0.7"/>
 <path d="M5.53271 8.66357L9.25213 4.68197" stroke="white"/>
 <path d="M4.10645 6.7688L6.13766 8.62553" stroke="white">
 </svg>
 {% endcapture %}
 {% capture icon_error%}
 <svg aria-hidden="true" focusable="false" role="presentation" class="icon ecom-shopify__article--icon-error" viewBox="0 0 13 13">
 <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
 <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
 <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
 <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
 </svg>
 {% endcapture %}
 {% if article != empty and article.id %}
 <div>
 {% form 'new_comment', article %}
 {% capture post_message %}
 Comment success
 {% endcapture %}
 {%- if article.moderated? and comment.status == 'unapproved' -%}
 {% capture post_message %}
 Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.
 {% endcapture %}
 {% endif %}
 {%- if form.errors or EComBuilderMode == true -%}
 <div class="ecom-shopify__article-form__message ecom-shopify__article-form__message--error" role="alert">
 <h3 class="ecom-shopify__article-form-status ecom-shopify__article-caption-large" tabindex="-1">
 
 </h3>
 <ul class="ecom-shopify__article-form-status-list ecom-shopify__article-caption-large">
 {%- for field in form.errors -%}
 <li>
 <a href="#CommentForm-{{ field }}" class="link">
 {%- if form.errors.translated_fields[field] contains 'author' -%}
 
 {%- elsif form.errors.translated_fields[field] contains 'body'-%}
 
 {%- else -%}
 {{ form.errors.translated_fields[field] }}
 {%- endif -%}
 {{ form.errors.messages[field] }}
 </a>
 </li>
 {%- endfor -%}
 </ul>
 </div>
 {%- endif -%}
 {%- if form.posted_successfully? or EComBuilderMode == true -%}
 <div class="ecom-shopify__article-form__message ecom-shopify__article-form__message--success" role="status">
 <h3 class="ecom-shopify__article-form-status" tabindex="-1">{{ post_message }}</h3>
 </div>
 {%- endif -%}
 <div class="ecom-shopify__article-comment-fields ecom-shopify__article-comment-{{layout}} {% if article.moderated? == false %}ecom-shopify__article-comments-fields{% endif %}">
 {%- if layout contains 'horizontial' -%}
 <div class="ecom-flex ecom-shopify__article-comment-fields-horizontial">
 {%- endif -%}
 <div class="ecom-shopify__article-comment-field ecom-shopify__article-field--with-error">
 
 <input
 type="text"
 name="comment[author]"
 id="CommentForm-author"
 class="ecom-shopify__article-comment-field__input"
 autocomplete="name"
 value="{{ form.author }}"
 aria-required="true"
 {% if form.errors contains 'author' %}
 aria-invalid="true"
 aria-describedby="CommentForm-author-error"
 {% endif %}
 placeholder="Your name"
 >
 {%- if form.errors contains 'author' -%}
 <small id="CommentForm-author-error">
 <span class="ecom-shopify__article-comment-form-message">{{ icon_error}} {{ form.errors.messages['author'] }}.</span>
 </small>
 {%- endif -%}
 </div>
 <div class="ecom-shopify__article-comment-field ecom-shopify__article-comment-field--with-error">
 
 <input
 type="email"
 name="comment[email]"
 id="CommentForm-email"
 autocomplete="email"
 class="ecom-shopify__article-comment-field__input"
 value="{{ form.email }}"
 autocorrect="off"
 autocapitalize="off"
 aria-required="true"
 {% if form.errors contains 'email' %}
 aria-invalid="true"
 aria-describedby="CommentForm-email-error"
 {% endif %}
 placeholder="Your email"
 >
 {%- if form.errors contains 'email' -%}
 <small id="CommentForm-email-error">
 <span class="ecom-shopify__article-comment-form-message">{{ icon_error}} {{ form.errors.messages['email'] }}.</span>
 </small>
 {%- endif -%}
 </div>
 {%- if layout contains 'horizontial' -%}
 </div>
 {%- endif -%}
 <div class="ecom-w-full ecom-shopify__article-comment-field ecom-shopify__article-comment-field--with-error">
 
 <textarea
 rows="5"
 name="comment[body]"
 id="CommentForm-body"
 class="ecom-shopify__article-comment-field-text-area ecom-shopify__article-comment-field__input"
 aria-required="true"
 {% if form.errors contains 'body' %}
 aria-invalid="true"
 aria-describedby="CommentForm-body-error"
 {% endif %}
 placeholder="Leave a comment"
 >{{ form.body }}</textarea>
 </div>
 <div class="element__button ecom-button-default">
 <button type="submit" class="ecom-shopify__article-comment-field-button">
 Post comment
 </button>
 </div>
 {%- if form.errors contains 'body' -%}
 <small id="CommentForm-body-error">
 <span class="ecom-shopify__article-comment-form--message">{{ icon_error}} {{ form.errors.messages['body'] }}.</span>
 </small>
 {%- endif -%}
 </div>
 {%- if article.moderated? -%}
 <p class="ecom-shopify__article-comment-warning ecom-shopify__article-comment-caption"></p>
 {%- endif -%}
 {% endform %}
 </div>
 {% endif %}
 {%- endif -%}
 {% else %}
 
 {% endif %}
 {% assign EComBuilderMode = true %}
 </div></div></div></div> </div></div></div></div> <div class="ecom-column ecom-core core__column--last ecom-syemwzw9cpr"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-boyztm2kl7t" data-core-is="block"><div class="ecom-element ecom-shopify ecom-shopify__search" deep="2"><div class="ecom-element ecom-shopify ecom-shopify__search-wrapper" data-settings="[{&quot;params&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;name&quot;:&quot;placeholder&quot;,&quot;label&quot;:&quot;Placeholder text&quot;,&quot;options&quot;:{&quot;toolbar&quot;:false}},{&quot;type&quot;:&quot;text&quot;,&quot;name&quot;:&quot;search_button_title&quot;,&quot;label&quot;:&quot;Button text&quot;,&quot;options&quot;:{&quot;toolbar&quot;:false}},{&quot;type&quot;:&quot;choose&quot;,&quot;label&quot;:&quot;Button position&quot;,&quot;name&quot;:&quot;btn_position&quot;,&quot;options&quot;:{&quot;type&quot;:&quot;align-x&quot;,&quot;values&quot;:[-1,1],&quot;visible&quot;:{&quot;keep_data&quot;:false}},&quot;css&quot;:{&quot;selector&quot;:&quot; .ecom-search-btn-submit&quot;,&quot;properties&quot;:{&quot;order&quot;:&quot;&quot;}}},{&quot;type&quot;:&quot;picker&quot;,&quot;label&quot;:&quot;Icon&quot;,&quot;name&quot;:&quot;icon&quot;,&quot;options&quot;:{&quot;type&quot;:&quot;icon&quot;,&quot;layout&quot;:&quot;grid&quot;,&quot;output&quot;:&quot;value&quot;,&quot;multiple&quot;:false,&quot;simple&quot;:false}},{&quot;type&quot;:&quot;choose&quot;,&quot;label&quot;:&quot;Icon position&quot;,&quot;name&quot;:&quot;icon_position&quot;,&quot;options&quot;:{&quot;type&quot;:&quot;align-x&quot;,&quot;values&quot;:[-1,1],&quot;visible&quot;:{&quot;keep_data&quot;:false}},&quot;css&quot;:{&quot;selector&quot;:&quot; .ecom__element--button-icon&quot;,&quot;properties&quot;:{&quot;order&quot;:&quot;&quot;}}},{&quot;type&quot;:&quot;number&quot;,&quot;label&quot;:&quot;Icon spacing&quot;,&quot;name&quot;:&quot;spacing&quot;,&quot;options&quot;:{&quot;units&quot;:{&quot;px&quot;:{&quot;min&quot;:0,&quot;max&quot;:200}},&quot;visible&quot;:{&quot;keep_data&quot;:false}},&quot;css&quot;:{&quot;selector&quot;:&quot; .ecom-search-btn-submit&quot;,&quot;properties&quot;:{&quot;gap&quot;:&quot;&quot;}}},{&quot;type&quot;:&quot;line&quot;},{&quot;type&quot;:&quot;checkbox&quot;,&quot;name&quot;:&quot;types&quot;,&quot;label&quot;:&quot;Specifies the type of results requested&quot;,&quot;options&quot;:{&quot;preview&quot;:&quot;title&quot;,&quot;values&quot;:{&quot;product&quot;:&quot;Product&quot;,&quot;page&quot;:&quot;Page&quot;,&quot;article&quot;:&quot;Article&quot;}}},{&quot;type&quot;:&quot;dropdown&quot;,&quot;name&quot;:&quot;prefix&quot;,&quot;label&quot;:&quot;Partial search&quot;,&quot;options&quot;:{&quot;preview&quot;:&quot;title&quot;,&quot;default&quot;:false,&quot;values&quot;:{&quot;none&quot;:&quot;No partial search&quot;,&quot;show&quot;:&quot;For last search term&quot;,&quot;last&quot;:&quot;Show last&quot;}},&quot;description&quot;:&quot;For example, if \&quot;winter snow\&quot; is used as a search query, a search will find all applicable resources that contain both \&quot;winter\&quot; and any term that starts with \&quot;snow\&quot;. This could be terms like \&quot;snow\&quot;, \&quot;snowshoe\&quot;, or \&quot;snowboard\&quot;.&quot;},{&quot;type&quot;:&quot;dropdown&quot;,&quot;name&quot;:&quot;unavailable_products&quot;,&quot;label&quot;:&quot;Unavailable products display.&quot;,&quot;options&quot;:{&quot;preview&quot;:&quot;title&quot;,&quot;default&quot;:false,&quot;values&quot;:{&quot;last&quot;:&quot;Show last&quot;,&quot;hide&quot;:&quot;Hide&quot;,&quot;show&quot;:&quot;Show&quot;},&quot;visible&quot;:{}},&quot;description&quot;:&quot;Specifies whether to display results for unavailable products or variants in filtered results&quot;},{&quot;type&quot;:&quot;dropdown&quot;,&quot;name&quot;:&quot;sort_by&quot;,&quot;label&quot;:&quot;Sort by&quot;,&quot;options&quot;:{&quot;preview&quot;:&quot;title&quot;,&quot;default&quot;:false,&quot;values&quot;:{&quot;relevance&quot;:&quot;Relevance&quot;,&quot;price-ascending&quot;:&quot;Price ascending&quot;,&quot;price-descending&quot;:&quot;Price descending&quot;},&quot;visible&quot;:{}},&quot;description&quot;:&quot;Specifies the sort order of the results&quot;},{&quot;type&quot;:&quot;line&quot;},{&quot;type&quot;:&quot;switch&quot;,&quot;label&quot;:&quot;Enable search suggestions&quot;,&quot;name&quot;:&quot;enable_predictive_search&quot;,&quot;options&quot;:{&quot;oneline&quot;:true,&quot;values&quot;:{&quot;on&quot;:{&quot;label&quot;:&quot;Yes&quot;,&quot;value&quot;:true},&quot;off&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:false}}}},{&quot;type&quot;:&quot;toggle&quot;,&quot;label&quot;:&quot;Query suggestions&quot;,&quot;name&quot;:&quot;predictive_search_query&quot;,&quot;options&quot;:{&quot;oneline&quot;:true,&quot;values&quot;:{&quot;on&quot;:{&quot;label&quot;:&quot;Yes&quot;,&quot;value&quot;:true},&quot;off&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:false}},&quot;visible&quot;:{&quot;name&quot;:&quot;enable_predictive_search&quot;,&quot;value&quot;:true}}},{&quot;type&quot;:&quot;toggle&quot;,&quot;label&quot;:&quot;Collection suggestions&quot;,&quot;name&quot;:&quot;predictive_search_collection&quot;,&quot;options&quot;:{&quot;oneline&quot;:true,&quot;values&quot;:{&quot;on&quot;:{&quot;label&quot;:&quot;Yes&quot;,&quot;value&quot;:true},&quot;off&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:false}},&quot;visible&quot;:{&quot;name&quot;:&quot;enable_predictive_search&quot;,&quot;value&quot;:true}}},{&quot;type&quot;:&quot;toggle&quot;,&quot;label&quot;:&quot;Page suggestions&quot;,&quot;name&quot;:&quot;predictive_search_page&quot;,&quot;options&quot;:{&quot;oneline&quot;:true,&quot;values&quot;:{&quot;on&quot;:{&quot;label&quot;:&quot;Yes&quot;,&quot;value&quot;:true},&quot;off&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:false}},&quot;visible&quot;:{&quot;name&quot;:&quot;enable_predictive_search&quot;,&quot;value&quot;:true}}},{&quot;type&quot;:&quot;toggle&quot;,&quot;label&quot;:&quot;Article suggestions&quot;,&quot;name&quot;:&quot;predictive_search_article&quot;,&quot;options&quot;:{&quot;oneline&quot;:true,&quot;values&quot;:{&quot;on&quot;:{&quot;label&quot;:&quot;Yes&quot;,&quot;value&quot;:true},&quot;off&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;value&quot;:false}},&quot;visible&quot;:{&quot;name&quot;:&quot;enable_predictive_search&quot;,&quot;value&quot;:true}}},{&quot;type&quot;:&quot;text&quot;,&quot;label&quot;:&quot;'No product found' text&quot;,&quot;name&quot;:&quot;no_product_found_text&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;label&quot;:&quot;'Suggestions' text&quot;,&quot;name&quot;:&quot;suggestions_text&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;label&quot;:&quot;'Pages' text&quot;,&quot;name&quot;:&quot;pages_text&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;label&quot;:&quot;'Articles' text&quot;,&quot;name&quot;:&quot;articles_text&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;label&quot;:&quot;'Product' text&quot;,&quot;name&quot;:&quot;products_text&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;label&quot;:&quot;'Search for' text&quot;,&quot;name&quot;:&quot;search_for_text&quot;}]}]" data-no-product-found="No product found" data-suggestions="Suggestion" data-pages="Pages" data-articles="Articles" data-products="Products" data-search-for="Search for"><div class="ecom-element ecom-shopify ecom-shopify__search-container">
 <form action="{{ routes.search_url }}" method="GET" class="ecom-shopify__search-form ecom-flex ecom-al_center">
 <input type="text"
 class="ecom-shopify__search-input"
 placeholder="Search for anything..."
 name="q"
 value="{{ search.terms | escape }}"
 
 />
 <input type="hidden" name="type" value="page,article" />
 <input type="hidden" name="options[unavailable_products]" value="last" />
 <input type="hidden" name="options[prefix]" value="none" />
 <input type="hidden" name="sort_by" value="relevance" />
 
 <button type="submit" class="ecom-flex ecom-al_center ecom-fl_center ecom-search-btn-submit">
 Search
 <span class="ecom__element--button-icon ecom-flex ecom-al_center ecom-fl_center"><svg version="1.1" id="lni_lni-search-alt" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 64 64" style="enable-background:new 0 0 64 64;" xml:space="preserve" fill="currentColor">
<path d="M62.1,57L44.6,42.8c3.2-4.2,5-9.3,5-14.7c0-6.5-2.5-12.5-7.1-17.1v0c-9.4-9.4-24.7-9.4-34.2,0C3.8,15.5,1.3,21.6,1.3,28
	c0,6.5,2.5,12.5,7.1,17.1c4.7,4.7,10.9,7.1,17.1,7.1c6.1,0,12.1-2.3,16.8-6.8l17.7,14.3c0.3,0.3,0.7,0.4,1.1,0.4
	c0.5,0,1-0.2,1.4-0.6C63,58.7,62.9,57.6,62.1,57z M10.8,42.7C6.9,38.8,4.8,33.6,4.8,28s2.1-10.7,6.1-14.6c4-4,9.3-6,14.6-6
	c5.3,0,10.6,2,14.6,6c3.9,3.9,6.1,9.1,6.1,14.6S43.9,38.8,40,42.7C32,50.7,18.9,50.7,10.8,42.7z"></path>
</svg></span>
 </button>
 
 </form>
 </div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-qgn0epwueh" data-core-is="block"><div class="ecom-row ecom-core core__block ecom-bifep9ilod6" data-id="ecom-bifep9ilod6" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="3" class="core__row--columns"></div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-3r3ocq1lx5s" data-core-is="block"><div class="ecom-row ecom-core core__block ecom-6zxiicol8w" data-id="ecom-6zxiicol8w" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="3" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-gmyp7tnd7g4"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-ifukipavuxt" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="3"><div class="text-content ecom-html has-drop-cap-view-framed">Popular Posts</div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-99fhgjwd0fi" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__blog" deep="3"><div class="ecom-shopify__blog-wrapper"><div class="ecom-shopify__blog-container" data-items-on-row="4">
 {% unless tmp_block == nil and tmp_block == blank %}
 {% assign tmp_block = blog %}
 {% endunless %}
 {%- capture stringBlog -%}{%- endcapture-%}
 {% assign blog_handles = stringBlog | split: ',' %}
 <div class="ecom-grid ecom-shopify__blog--posts" style="display:grid">
 {% assign count = 1 %}
 
 {% if blog != null %}
 {%- capture limit -%}4{%-endcapture-%}
 {% assign limit = limit | plus: 0 %}
 {% assign tmp_article = article %}
 
 
 {% for article in blog.articles %}
 {% if count > limit %}{% break%}{% endif %}
 {% assign count = count | plus: 1%}
 
 <div class="ecom-shopify__blog--post ecom-shopify__blog-vertical">
 
 <div class="ecom-shopify__blog--post-thumbnail--img">
 <div class="ecom-shopify__blog--post-thumbnail ecom-image-align {% if article.image == blank %} ecom-shopify__blog--post-no-thumbnail{% endif %}">
 {%- if article.image -%}
 {%- liquid
 assign featured_media_aspect_ratio = article.featured_media.aspect_ratio
 if article.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <a style="text-decoration:none" href="{{article.url}}" class="ecom-shopify__blog--post-link ecom-image-default" data-blog-handle="{{article.handle}}">
 <img srcset="
 {%- if article.image.src.width >= 165 -%}{{ article.image.src | img_url: '165x' }} 165w,{%- endif -%}
 {%- if article.image.src.width >= 360 -%}{{ article.image.src | img_url: '360x' }} 360w,{%- endif -%}
 {%- if article.image.src.width >= 533 -%}{{ article.image.src | img_url: '533x' }} 533w,{%- endif -%}
 {%- if article.image.src.width >= 720 -%}{{ article.image.src | img_url: '720x' }} 720w,{%- endif -%}
 {%- if article.image.src.width >= 940 -%}{{ article.image.src | img_url: '940x' }} 940w,{%- endif -%}
 {%- if article.image.src.width >= 1066 -%}{{ article.image.src | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ article.image.src | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ article.image.src.alt | escape }}"
 width="{{ article.image.width }}"
 height="{{ article.image.height }}"
 loading="lazy"
 >
 </a>
 {%- endif -%}
 </div>
 </div>
 <div class="ecom-shopify__blog--posts-details">
 
 <a href="{{article.url }}" class="ecom-shopify__blog--post-link" data-blog-handle="{{article.handle}}"><h2 class="ecom-shopify__blog--post-title">{{article.title}}</h2></a>
 <div class="ecom-shopify__blog--post-group-4 ecom-flex ecom-al_center ecom-fl_left">
 
 
 <div class="ecom-shopify__blog--post-published-at ecom-shopify__blog--post-informations">
 {% assign published_at = article.published_at | time_tag: format: 'abbreviated_date' %}
 
 <span class="ecom-shopify__blog--post-informations-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M280 248.7L339.1 338.7C347.3 349.7 344.3 364.6 333.3 371.1C322.3 379.3 307.4 376.3 300 365.3L236 269.3C233.4 265.4 232 260.7 232 256V120C232 106.7 242.7 96 256 96C269.3 96 280 106.7 280 120L280 248.7zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 48C141.1 48 48 141.1 48 256C48 370.9 141.1 464 256 464C370.9 464 464 370.9 464 256C464 141.1 370.9 48 256 48z"></path></svg></span>
 
 <span>{{published_at}}</span>
 </div>
 </div>
 
 </div>
 </div>
 {% endfor %}
 
 {% else %}
 <div>No posts yet!</div>
 {% endif %}
 
 </div>
 
 {% unless tmp_block == nil and tmp_block == blank %}
 {% assign blog = tmp_block %}
 {% endunless %}
 {% unless tmp_article == nil and tmp_article == blank %}
 {% assign article = tmp_article %}
 {% endunless %}
 </div><div class="ecom-collection__product-loading ecom-dn"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="48px" height="48px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" style="margin: auto; background: none; display: block; shape-rendering: auto;"><path d="M10 50A40 40 0 0 0 90 50A40 42 0 0 1 10 50" fill="#0a0a0a" stroke="none"><animateTransform attributeName="transform" type="rotate" dur="0.5434782608695652s" repeatCount="indefinite" keyTimes="0;1" values="0 50 51;360 50 51"></animateTransform></path></svg></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-4mbarz0rtp5" data-core-is="block"><div class="ecom-row ecom-core core__block ecom-2pgt7zbzss" data-id="ecom-2pgt7zbzss" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="3" class="core__row--columns"></div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-bwdkoi40vyt" data-core-is="block"><div class="ecom-row ecom-core core__block ecom-48rjna2fugc" data-id="ecom-48rjna2fugc" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="3" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-b2oc43k5s0t"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-drqa9b7u8iv" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="3"><div class="text-content ecom-html has-drop-cap-view-framed">Subscribe Us</div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-uvfnkz9w9vb" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="3"><div class="text-content ecom-html has-drop-cap-view-framed">Subscribe to our newsletter and receive a selection of cool articles every weeks</div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-06gzt1d7pj7i" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__newsletter-form" deep="3"><div class="ecom-shopify__newsletter-form-wrapper ecom-w__full"><div class="ecom-shopify__newsletter-form-container ecom-newsletter-vertical"><div>
 {% form 'customer', action:'/contact', class: 'ecom-shopify__newsletter-form' ,id:'newsletter-form-ecom-06gzt1d7pj7i' %}
 <input type="hidden" name="contact[tags]" value="newsletter" />
 <div class="ecom-newsletter-form-content-wrap">
 <div class="ecom-newsletter-form-content ">
 
 <input
 id="ECom-NewsletterForm-ecom-06gzt1d7pj7i"
 type="email"
 name="contact[email]"
 class="ecom-shopify__newsletter-form-field--input"
 value="{{ form.email }}"
 aria-required="true"
 autocorrect="off"
 autocapitalize="off"
 autocomplete="email"
 {% if form.errors %}
 aria-invalid="true"
 {% endif %}
 placeholder="Enter your email"
 required
 >
 
 
 <div class="ecome-shopify__newsletter_button-submit-wrapper">
 <button type="submit" class="ecome-shopify__newsletter__button" name="commit">
 <span> Subscribe </span>
 <span class="ecom-shopify__newsletter__button-icon"> </span>
 </button>
 </div>
 </div>
 
 </div>
 
 {%- if form.posted_successfully? or EComBuilderMode == true and false -%}
 <div class="ecom-shopify__newsletter-form-success" {% unless EComBuilderMode %}tabindex="-1"{% endunless %}>
 Subscribed successfully!
 </div>
 {%- endif -%}
 {%- if form.errors or EComBuilderMode == true and false -%}
 <div class="ecom-shopify__newsletter-form-message">
 <h2 class="ecom-shopify__newsletter-form-status-error" role="alert" {% unless EComBuilderMode %}tabindex="-1"{% endunless %}>
 Opps!. An error has occurred
 </h2>
 <p class="ecom-shopify__newsletter-form-detail-error">
 {% if EComBuilderMode %}
 This is example error text when missing email field
 {% endif %}
 {{ form.errors.translated_fields['email'] | capitalize }} {{ form.errors.messages['email'] }}
 </p>
 </div>
 {%- endif -%}
 <div class="ecom-shopify__newsletter-form-message ecom-dn">
 <p class="ecom-shopify__newsletter-form-detail-error">
 Email have already subscribed!
 </p>
 </div>
 {% endform %}
 </div></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-75sbdd1jo4e" data-core-is="block"><div class="ecom-row ecom-core core__block ecom-3v17aqhpi92" data-id="ecom-3v17aqhpi92" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="3" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-mylfeza3pfb"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-pfph02cxp" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="3"><div class="text-content ecom-html has-drop-cap-view-framed">Related articles</div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-dxi817jf1bn" data-core-is="block"><div class="ecom-element ecom-shopify-elements ecom-shopify__blog" deep="3"><div class="ecom-shopify__blog-wrapper"><div class="ecom-shopify__blog-container" data-items-on-row="4">
 {% unless tmp_block == nil and tmp_block == blank %}
 {% assign tmp_block = blog %}
 {% endunless %}
 {%- capture stringBlog -%}{%- endcapture-%}
 {% assign blog_handles = stringBlog | split: ',' %}
 <div class="ecom-grid ecom-shopify__blog--posts" style="display:grid">
 {% assign count = 1 %}
 
 {% if blog != null %}
 {%- capture limit -%}4{%-endcapture-%}
 {% assign limit = limit | plus: 0 %}
 {% assign tmp_article = article %}
 
 
 {% for article in blog.articles %}
 {% if count > limit %}{% break%}{% endif %}
 {% assign count = count | plus: 1%}
 
 <div class="ecom-shopify__blog--post ecom-shopify__blog-vertical">
 
 <div class="ecom-shopify__blog--post-thumbnail--img">
 <div class="ecom-shopify__blog--post-thumbnail ecom-image-align {% if article.image == blank %} ecom-shopify__blog--post-no-thumbnail{% endif %}">
 {%- if article.image -%}
 {%- liquid
 assign featured_media_aspect_ratio = article.featured_media.aspect_ratio
 if article.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <a style="text-decoration:none" href="{{article.url}}" class="ecom-shopify__blog--post-link ecom-image-default" data-blog-handle="{{article.handle}}">
 <img srcset="
 {%- if article.image.src.width >= 165 -%}{{ article.image.src | img_url: '165x' }} 165w,{%- endif -%}
 {%- if article.image.src.width >= 360 -%}{{ article.image.src | img_url: '360x' }} 360w,{%- endif -%}
 {%- if article.image.src.width >= 533 -%}{{ article.image.src | img_url: '533x' }} 533w,{%- endif -%}
 {%- if article.image.src.width >= 720 -%}{{ article.image.src | img_url: '720x' }} 720w,{%- endif -%}
 {%- if article.image.src.width >= 940 -%}{{ article.image.src | img_url: '940x' }} 940w,{%- endif -%}
 {%- if article.image.src.width >= 1066 -%}{{ article.image.src | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ article.image.src | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ article.image.src.alt | escape }}"
 width="{{ article.image.width }}"
 height="{{ article.image.height }}"
 loading="lazy"
 >
 </a>
 {%- endif -%}
 </div>
 </div>
 <div class="ecom-shopify__blog--posts-details">
 
 <a href="{{article.url }}" class="ecom-shopify__blog--post-link" data-blog-handle="{{article.handle}}"><h2 class="ecom-shopify__blog--post-title">{{article.title}}</h2></a>
 <div class="ecom-shopify__blog--post-group-4 ecom-flex ecom-al_center ecom-fl_left">
 
 
 <div class="ecom-shopify__blog--post-published-at ecom-shopify__blog--post-informations">
 {% assign published_at = article.published_at | time_tag: format: 'abbreviated_date' %}
 
 <span class="ecom-shopify__blog--post-informations-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M280 248.7L339.1 338.7C347.3 349.7 344.3 364.6 333.3 371.1C322.3 379.3 307.4 376.3 300 365.3L236 269.3C233.4 265.4 232 260.7 232 256V120C232 106.7 242.7 96 256 96C269.3 96 280 106.7 280 120L280 248.7zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 48C141.1 48 48 141.1 48 256C48 370.9 141.1 464 256 464C370.9 464 464 370.9 464 256C464 141.1 370.9 48 256 48z"></path></svg></span>
 
 <span>{{published_at}}</span>
 </div>
 </div>
 
 </div>
 </div>
 {% endfor %}
 
 {% else %}
 <div>No posts yet!</div>
 {% endif %}
 
 </div>
 
 {% unless tmp_block == nil and tmp_block == blank %}
 {% assign blog = tmp_block %}
 {% endunless %}
 {% unless tmp_article == nil and tmp_article == blank %}
 {% assign article = tmp_article %}
 {% endunless %}
 </div><div class="ecom-collection__product-loading ecom-dn"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="48px" height="48px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" style="margin: auto; background: none; display: block; shape-rendering: auto;"><path d="M10 50A40 40 0 0 0 90 50A40 42 0 0 1 10 50" fill="#0a0a0a" stroke="none"><animateTransform attributeName="transform" type="rotate" dur="0.5434782608695652s" repeatCount="indefinite" keyTimes="0;1" values="0 50 51;360 50 51"></animateTransform></path></svg></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
</div></div>
{% schema %}
{
 "name": "Blog article template",
 "locales": {},
 "settings": [
 {
 "type": "header",
 "content": "The section was generated by [Ecomposer](https:\/\/ecomposer.io).",
 "info": "\n EComposer is a Visual Page Builder app on Shopify AppStore.\n It provides 100+ pre-built templates in library,\n so you can build unlimited pages that you can imagine with it.\n Please don't add Shopify sections to EComposer's page in the theme customize. If you do this, Shopify sections will be deleted when you republish your page in EComposer\n "
 },
 {
 "type": "header",
 "content": "[EDIT WITH EComposer APP \u2192](https:\/\/ecomposer.app\/shop?open_builder=1&page=article&id=678777d2c0e0f140c60a1002&utm_source=theme-customize)",
 "info": "(*You must install the app to start editing this section [Learn more](https:\/\/help.ecomposer.io\/docs\/getting-started\/installation\/))"
 }
 ]
}
{% endschema %}