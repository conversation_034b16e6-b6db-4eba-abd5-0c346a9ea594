<style>
    /* 内容区域 - 基础样式，基于1920px基础使用vw单位 */
    .content-1 {
        padding: 3.65vw 15.71vw 4.69vw 17.60vw;
        color: {{ section.settings.text_color | default: '#6D4C41' }};
        background: {{ section.settings.background_color | default: '#FCF7F2' }};
    }

    .content-logo-1 {
        margin-bottom: 1.042vw;  /* 20 ÷ 1920 × 100 = 1.042 */
    }
    .logo-text-1 {
        color: {{ section.settings.text_color | default: '#6D4C41' }};
        font-family: Playfair Display, serif;
        font-weight: 400;
        font-size: 1.67vw; /* 32px ÷ 1920 × 100 = 1.67vw */
        letter-spacing: 0px;
    }

    .content-box-1 {
        flex: 1;
    }

    .content-line-1 {
        background: {{ section.settings.line_color | default: '#6D4C41' }};
        height: 2px; /* 2px ÷ 1920 × 100 = 0.10vw */
        width: 4.17vw; /* 80px ÷ 1920 × 100 = 4.17vw */
        margin-bottom: 1.04vw; /* 20px ÷ 1920 × 100 = 1.04vw */
    }

    .content-text-1 {
        font-family: PingFang SC, sans-serif;
        font-weight: 400;
        font-size: 0.83vw; /* 16px ÷ 1920 × 100 = 0.83vw */
        line-height: 1.56vw; /* 30px ÷ 1920 × 100 = 1.56vw */
        letter-spacing: 0px;
        color: {{ section.settings.text_color | default: '#6D4C41' }};
        /* web端显示全部内容，不需要行数限制 */
        display: block;
        -webkit-line-clamp: unset;
        -webkit-box-orient: unset;
        overflow: visible;
    }

    /* web端隐藏Read More按钮 */
    .read-more-btn {
        display: none;
    }

    /* 桌面端样式 - 基于1920px基础使用vw单位 */
    @media (min-width: 768px) {
        .content-1 {
            padding: 3.65vw 15.71vw 4.69vw 17.60vw; /* 70px 340px 90px 338px 转换为vw (基于1920px) */
        }

        .logo-text-1 {
            font-size: 2.40vw; /* 46px ÷ 1920 × 100 = 2.40vw */
        }

        /* 桌面端确保显示全部内容，不需要Read More功能 */
        .content-text-1 {
            display: block !important;
            -webkit-line-clamp: unset !important;
            -webkit-box-orient: unset !important;
            overflow: visible !important;
        }

        /* 桌面端隐藏Read More按钮 */
        .read-more-btn {
            display: none !important;
        }
    }

    /* 移动端调整 - 基于768px基础使用vw单位 */
    @media (max-width: 768px) {
        .content-1 {
            padding: 2.60vw 1.17vw 3.13vw 2.08vw; /* 20px 9px 24px 16px 转换为vw (基于768px) */
        }

        .logo-text-1 {
            font-size: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
            margin-bottom: 1.30vw; /* 10px ÷ 768 × 100 = 1.30vw */
        }

        .content-line-1 {
            margin-bottom: 1.82vw; /* 14px ÷ 768 × 100 = 1.82vw */
            width: 4.22vw; /* 32px ÷ 768 × 100 = 1.82vw */
        }

        .content-text-1 {
            font-size: 1.82vw; /* 14px ÷ 768 × 100 = 1.82vw */
            line-height: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
            display: -webkit-box;
            -webkit-line-clamp: 3; /* 默认显示3行 */
            -webkit-box-orient: vertical;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .content-text-1.expanded {
            display: block;
            -webkit-line-clamp: unset;
        }

        .read-more-btn {
            font-family: PingFang SC;
            font-weight: 400;
            font-style: normal;
            font-size: 1.82vw; /* 14px ÷ 768 × 100 = 1.82vw */
            line-height: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
            letter-spacing: 0px;
            text-decoration: underline;
            text-decoration-style: solid;
            color: inherit;
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            margin-top: 1.30vw; /* 10px ÷ 768 × 100 = 1.30vw */
            transition: opacity 0.3s ease;
        }

        .read-more-btn:hover {
            opacity: 0.7;
        }
    }

    /* 超小屏幕调整 - 基于375px基础使用vw单位 */
    @media (max-width: 480px) {
        .content-1 {
            padding: 5.33vw 2.4vw 6.4vw 4.27vw; /* 20px 9px 24px 16px 转换为vw (基于375px) */
        }

        .logo-text-1 {
            font-size: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
            margin-bottom: 2.67vw; /* 10px ÷ 375 × 100 = 2.67vw */
        }

        .content-line-1 {
            margin-bottom: 3.73vw; /* 14px ÷ 375 × 100 = 3.73vw */
            width: 10.67vw; /* 40 ÷ 375 × 100 = 3.73vw */
        }

        .content-text-1 {
            font-size: 3.73vw; /* 14px ÷ 375 × 100 = 3.73vw */
            line-height: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
            display: -webkit-box;
            -webkit-line-clamp: 3; /* 默认显示3行 */
            -webkit-box-orient: vertical;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .content-text-1.expanded {
            display: block;
            -webkit-line-clamp: unset;
        }

        .read-more-btn {
            font-family: PingFang SC;
            font-weight: 400;
            font-style: normal;
            font-size: 3.73vw; /* 14px ÷ 375 × 100 = 3.73vw */
            line-height: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
            letter-spacing: 0px;
            text-decoration: underline;
            text-decoration-style: solid;
            color: inherit;
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            margin-top: 2.67vw; /* 10px ÷ 375 × 100 = 2.67vw */
            transition: opacity 0.3s ease;
        }

        .read-more-btn:hover {
            opacity: 0.7;
        }
    }
</style>

<section class="content-1">
    <div class="content-logo-1">
        <div class="logo-text-1" id="logoText">{{ section.settings.logo_text | default: 'Savanna' }}</div>
    </div>
    <div class="content-box-1">
        <div class="content-line-1"></div>
        <div class="content-text-1" id="caseContentTopMainDescription">
            {% if section.settings.description_text != blank %}
                {{ section.settings.description_text }}
            {% else %}
                <p>对于那些希望在室内引入更多纹理和视觉趣味的人来说，Cas系列以其波浪形面板而闻名，为任何空间增添深度和层次感。虽然波浪图案可能会产生大胆的视觉效果，但家具采用微妙的冷色调木材，从白色到深灰色桤木，让人感觉相当宁静。该系列以仙后座命名，这是一个代表好运、愿望实现、善良和纯洁的星座——所有这些都是您想要融入家中的元素！</p>
            {% endif %}
        </div>
        <button class="read-more-btn" id="caseContentTopReadMoreBtn" onclick="toggleReadMoreCaseContentTop()">Read More</button>
    </div>
</section>

<script>
function toggleReadMoreCaseContentTop() {
    const viewportWidth = window.innerWidth;

    // 只在移动端（768px以下）执行Read More功能
    if (viewportWidth <= 768) {
        const textElement = document.getElementById('caseContentTopMainDescription');
        const btnElement = document.getElementById('caseContentTopReadMoreBtn');

        if (textElement && btnElement) {
            if (textElement.classList.contains('expanded')) {
                // 收起内容
                textElement.classList.remove('expanded');
                btnElement.textContent = 'Read More';
            } else {
                // 展开内容
                textElement.classList.add('expanded');
                btnElement.textContent = 'Read Less';
            }
        }
    }
    // web端不执行任何操作
}

// 只在移动端检查是否需要显示Read More按钮
document.addEventListener('DOMContentLoaded', function() {
    const viewportWidth = window.innerWidth;

    // 只在移动端（768px以下）执行Read More功能
    if (viewportWidth <= 768) {
        const textElement = document.getElementById('caseContentTopMainDescription');
        const btnElement = document.getElementById('caseContentTopReadMoreBtn');

        if (textElement && btnElement) {
            // 检查文本是否超过3行
            const lineHeight = parseInt(window.getComputedStyle(textElement).lineHeight);
            const maxHeight = lineHeight * 3;

            // 临时展开以检查实际高度
            textElement.style.display = 'block';
            textElement.style.webkitLineClamp = 'unset';
            const actualHeight = textElement.scrollHeight;

            // 恢复原始状态
            textElement.style.display = '';
            textElement.style.webkitLineClamp = '';

            // 如果内容不超过3行，隐藏Read More按钮
            if (actualHeight <= maxHeight) {
                btnElement.style.display = 'none';
            } else {
                btnElement.style.display = 'block';
            }
        }
    } else {
        // web端隐藏Read More按钮并显示全部内容
        const textElement = document.getElementById('caseContentTopMainDescription');
        const btnElement = document.getElementById('caseContentTopReadMoreBtn');

        if (textElement) {
            textElement.style.display = 'block';
            textElement.style.webkitLineClamp = 'unset';
            textElement.style.webkitBoxOrient = 'unset';
            textElement.style.overflow = 'visible';
        }

        if (btnElement) {
            btnElement.style.display = 'none';
        }
    }
});
</script>

{% schema %}
{
  "name": "Content Section Top",
  "settings": [
    {
      "type": "text",
      "id": "logo_text",
      "label": "Logo Text",
      "default": "Savanna"
    },
    {
      "type": "richtext",
      "id": "description_text",
      "label": "Description Text"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#FCF7F2"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#6D4C41"
    },
    {
      "type": "color",
      "id": "line_color",
      "label": "Decorative Line Color",
      "default": "#6D4C41"
    }
  ],
  "presets": [
    {
      "name": "Content Section Top",
      "category": "Custom"
    }
  ]
}
{% endschema %}