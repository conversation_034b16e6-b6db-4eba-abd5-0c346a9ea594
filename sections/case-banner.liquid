<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />

<style>
    /* 基于1920×1080基准的动态缩放样式 */

    /* 设置根字体大小，用于rem计算 */
    html {
        font-size: clamp(14px, 1vw, 18px); /* 响应式根字体大小 */
    }

    .case-banner-swiper {
        width: 100%; /* 保留百分比 */
        height: 39.58vw; /* 760px ÷ 1920 × 100 = 39.58vw */
    }

    .swiper-slide {
        text-align: center;
        font-size: 0.94vw; /* 18px ÷ 1920 × 100 = 0.94vw */
        background: #444; /* 保留固定颜色 */
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .swiper-slide img {
        display: block;
        width: 100%; /* 保留百分比 */
        height: 100%; /* 保留百分比 */
        object-fit: cover; /* 保留固定值 */
        position: absolute;
        top: 0;
        left: 0;
    }

    /* 默认显示Web端图片，隐藏移动端图片 */
    .swiper-slide .case-banner-web-image {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
    }

    .swiper-slide .case-banner-mobile-image {
        display: none;
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
    }

    .swiper-slide video {
        width: 100%; /* 保留百分比 */
        height: 100%; /* 保留百分比 */
        object-fit: cover; /* 保留固定值 */
    }

    /* 幻灯片内容区域样式 */
    .case-banner-slide-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.5); /* 保留固定颜色 */
        padding: 1.04vw; /* 20px ÷ 1920 × 100 = 1.04vw */
        text-align: left;
        color: white; /* 保留固定颜色 */
    }

    /* 幻灯片标题样式 */
    .case-banner-slide-title {
        font-size: 1.25vw; /* 24px ÷ 1920 × 100 = 1.25vw */
        margin-bottom: 0.52vw; /* 10px ÷ 1920 × 100 = 0.52vw */
    }

    /* 分页器样式 */
    .swiper-pagination .swiper-pagination-bullet-active {
        background: #6D4C41 !important;
    }
    .swiper-pagination .swiper-pagination-bullet {
        background: #ffffff ;
    }
    .swiper-pagination .swiper-pagination-bullet-active,
    .swiper-pagination .swiper-pagination-bullet {
        display: inline-block;
        width: 0.73vw; /* 14px ÷ 1920 × 100 = 0.73vw */
        height: 0.73vw; /* 14px ÷ 1920 × 100 = 0.73vw */
    }
    .swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
        bottom: 0.78vw; /* 15 ÷ 1920 × 100 = 1.04vw */
    }

    /* 导航按钮基础样式 */
    .swiper-button-next, .swiper-button-prev {
        width: 2.08vw; /* 40px ÷ 1920 × 100 = 2.08vw */
        height: 2.08vw; /* 40px ÷ 1920 × 100 = 2.08vw */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 确保SVG不变形 */
    .swiper-button-next svg, .swiper-button-prev svg {
        width: 100%;
        height: 100%;
    }

    /* 导航按钮SVG样式 */
    /* 默认状态（能点击） */
    .swiper-button-next svg circle,
    .swiper-button-prev svg circle {
        fill: #ffffff; /* 背景颜色 */
        fill-opacity: 1; /* 确保不透明 */
        stroke: none; /* 无边框 */
        transition: fill 0.3s ease;
    }

    .swiper-button-next svg path,
    .swiper-button-prev svg path {
        stroke: #6D4C41; /* 箭头颜色 */
        transition: stroke 0.3s ease;
    }
    /* 禁用状态（不能点击） */
    .swiper-button-next.swiper-button-disabled svg circle,
    .swiper-button-prev.swiper-button-disabled svg circle {
        fill: #ffffff !important; /* 背景颜色，确保不透明 */
        fill-opacity: 1 !important; /* 确保完全不透明 */
    }

    .swiper-button-next.swiper-button-disabled svg path,
    .swiper-button-prev.swiper-button-disabled svg path {
        stroke: #CCCCCC; /* 箭头颜色 */
    }

    /* 悬停状态（鼠标放上去） */
    .swiper-button-next:not(.swiper-button-disabled):hover svg circle,
    .swiper-button-prev:not(.swiper-button-disabled):hover svg circle {
        fill: #6D4C41; /* 背景颜色 */
        fill-opacity: 1; /* 确保不透明 */
    }

    .swiper-button-next:not(.swiper-button-disabled):hover svg path,
    .swiper-button-prev:not(.swiper-button-disabled):hover svg path {
        stroke: #ffffff; /* 箭头颜色 */
    }
    .swiper-button-next.swiper-button-disabled,
    .swiper-button-prev.swiper-button-disabled {
        opacity: 1;
    }

    /* 平板端响应式设计 */
    @media (max-width: 1024px) {
        .case-banner-swiper {
            height: 35.16vw; /* 360px ÷ 1024 × 100 = 35.16vw */
        }

        .swiper-slide {
            font-size: 1.76vw; /* 18px ÷ 1024 × 100 = 1.76vw */
        }

        .case-banner-slide-content {
            padding: 1.95vw; /* 20px ÷ 1024 × 100 = 1.95vw */
        }

        .case-banner-slide-title {
            font-size: 2.34vw; /* 24px ÷ 1024 × 100 = 2.34vw */
            margin-bottom: 0.98vw; /* 10px ÷ 1024 × 100 = 0.98vw */
        }

        .swiper-pagination .swiper-pagination-bullet-active,
        .swiper-pagination .swiper-pagination-bullet {
            width: 1.37vw; /* 14px ÷ 1024 × 100 = 1.37vw */
            height: 1.37vw; /* 14px ÷ 1024 × 100 = 1.37vw */
        }

        .swiper-button-next, .swiper-button-prev {
            width: 3.91vw; /* 40px ÷ 1024 × 100 = 3.91vw */
            height: 3.91vw; /* 40px ÷ 1024 × 100 = 3.91vw */
        }

        /* 平板端SVG尺寸控制 */
        .swiper-button-next svg, .swiper-button-prev svg {
            /*max-width: 40px;*/
            /*max-height: 40px;*/
        }
    }

    /* 移动端响应式设计 - 基于768px基础使用vw单位 */
    @media (max-width: 768px) {
        .case-banner-swiper {
            height: 26.04vw; /* 200px ÷ 768 × 100 = 26.04vw */
        }

        .swiper-slide {
            font-size: 2.34vw; /* 18px ÷ 768 × 100 = 2.34vw */
        }

        .case-banner-slide-content {
            padding: 1.95vw; /* 15px ÷ 768 × 100 = 1.95vw */
        }

        .case-banner-slide-title {
            font-size: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
            margin-bottom: 1.30vw; /* 10px ÷ 768 × 100 = 1.30vw */
        }

        .swiper-pagination .swiper-pagination-bullet-active,
        .swiper-pagination .swiper-pagination-bullet {
            width: 1.04vw; /* 8px ÷ 768 × 100 = 1.04vw */
            height: 1.04vw; /* 8px ÷ 768 × 100 = 1.04vw */
        }

        /* 移动端图片显示控制 */
        .swiper-slide .case-banner-web-image {
            display: none; /* 隐藏Web端图片 */
        }

        .swiper-slide .case-banner-mobile-image {
            display: block; /* 显示移动端图片 */
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 移动端每个幻灯片都会显示图片，不再需要隐藏逻辑 */

        /* 隐藏移动端的左右点击图标 */
        .swiper-button-next,
        .swiper-button-prev {
            display: none; /* 完全隐藏左右点击图标 */
        }
    }

    /* 超小屏幕保护 - 基于375px基础确保最小可用性 */
    @media (max-width: 480px) {
        .case-banner-swiper {
            height: 53.33vw; /* 200px ÷ 375 × 100 = 53.33vw，适合小屏幕 */
        }

        .swiper-slide {
            font-size: 4.8vw; /* 18px ÷ 375 × 100 = 4.8vw */
        }

        .case-banner-slide-content {
            padding: 4vw; /* 15px ÷ 375 × 100 = 4vw */
        }

        .case-banner-slide-title {
            font-size: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
            margin-bottom: 2.67vw; /* 10px ÷ 375 × 100 = 2.67vw */
        }

        .swiper-pagination .swiper-pagination-bullet-active,
        .swiper-pagination .swiper-pagination-bullet {
            width: 2.13vw; /* 8px ÷ 375 × 100 = 2.13vw */
            height: 2.13vw; /* 8px ÷ 375 × 100 = 2.13vw */
        }

        /* 移动端图片显示控制 */
        .swiper-slide .case-banner-web-image {
            display: none; /* 隐藏Web端图片 */
        }

        .swiper-slide .case-banner-mobile-image {
            display: block; /* 显示移动端图片 */
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 移动端每个幻灯片都会显示图片，不再需要隐藏逻辑 */

        /* 确保左右点击图标在超小屏幕上也隐藏 */
        .swiper-button-next,
        .swiper-button-prev {
            display: none; /* 完全隐藏左右点击图标 */
        }
    }

    /* 超大屏幕优化 - 防止元素过大 */
    @media (min-width: 2560px) {
        .case-banner-swiper {
            height: clamp(760px, 39.58vw, 1000px); /* 限制最大高度 */
        }

        .swiper-slide {
            font-size: clamp(18px, 0.94vw, 24px); /* 限制最大字体 */
        }

        .case-banner-slide-content {
            padding: clamp(20px, 1.04vw, 30px); /* 限制最大内边距 */
        }

        .case-banner-slide-title {
            font-size: clamp(24px, 1.25vw, 32px); /* 限制最大字体 */
            margin-bottom: clamp(10px, 0.52vw, 15px);
        }

        .swiper-pagination .swiper-pagination-bullet-active,
        .swiper-pagination .swiper-pagination-bullet {
            width: clamp(14px, 0.73vw, 18px);
            height: clamp(14px, 0.73vw, 18px);
        }

        .swiper-button-next, .swiper-button-prev {
            width: clamp(40px, 2.08vw, 50px);
            height: clamp(40px, 2.08vw, 50px);
        }

        /* 超大屏幕SVG尺寸控制 */
        .swiper-button-next svg, .swiper-button-prev svg {
            /*max-width: 50px;*/
            /*max-height: 50px;*/
        }
    }

    /* 高分辨率屏幕优化 */
    @media (min-width: 1920px) and (max-width: 2559px) {
        .case-banner-swiper {
            height: clamp(700px, 39.58vw, 800px); /* 在标准和大屏之间平滑过渡 */
        }

        .case-banner-slide-title {
            font-size: clamp(22px, 1.25vw, 28px);
        }
    }

    /* 中等屏幕优化 */
    @media (min-width: 1025px) and (max-width: 1366px) {
        .case-banner-swiper {
            height: 36vw; /* 适中的高度 */
        }

        .swiper-slide {
            font-size: 1.3vw; /* 适中的字体大小 */
        }

        .case-banner-slide-title {
            font-size: 1.8vw; /* 适中的标题字体 */
        }
    }
</style>

<!-- 移动端图片逻辑已简化，不再需要全局检查 -->

<!-- 横幅区域 -->
<div class="swiper case-banner-swiper case-banner-mySwiper" >
    <div class="swiper-wrapper">
        {% for block in section.blocks %}
            {% case block.type %}
                {% when 'slide' %}
                    <div class="swiper-slide" {{ block.shopify_attributes }}>
                        {% if block.settings.type == "image" %}
                            <!-- Web端图片 (桌面端和平板端) -->
                            <img class="case-banner-web-image" src="{{ block.settings.image | img_url: '1200x800' }}" alt="{{ block.settings.title }}">
                            <!-- 移动端图片逻辑 -->
                            {% if block.settings.mobile_image != blank %}
                                <img class="case-banner-mobile-image" src="{{ block.settings.mobile_image | img_url: 'master' }}" alt="{{ block.settings.title }}" loading="lazy">
                            {% elsif block.settings.image != blank %}
                                <img class="case-banner-mobile-image" src="{{ block.settings.image | img_url: 'master' }}" alt="{{ block.settings.title }}" loading="lazy">
                            {% endif %}
                        {% else %}
                            <video controls playsinline poster="{{ block.settings.poster | img_url: '1200x800' }}">
                                <source src="{{ block.settings.video }}" type="video/mp4">
                            </video>
                        {% endif %}

                        {% if block.settings.title != blank or block.settings.description != blank %}
                            <div class="case-banner-slide-content">
                                {% if block.settings.title != blank %}
                                    <h3 class="case-banner-slide-title">{{ block.settings.title }}</h3>
                                {% endif %}
                                {% if block.settings.description != blank %}
                                    <p>{{ block.settings.description }}</p>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
            {% endcase %}
        {% endfor %}
    </div>

    <!-- 导航按钮 -->
    <div class="swiper-button-next">
         <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none" preserveAspectRatio="xMidYMid meet">
            <circle cx="20" cy="20" r="20" fill="white"/>
            <path d="M25 20.0034H15" stroke="#6D4C41" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M20 15L25 20L20 25" stroke="#6D4C41" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    </div>
    <div class="swiper-button-prev">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none" preserveAspectRatio="xMidYMid meet">
            <circle cx="20" cy="20" r="20" fill="white"/>
            <path d="M15 19.9965H25" stroke="#6D4C41" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M20 25L15 20L20 15" stroke="#6D4C41" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    </div>

    <!-- 分页器 -->
    <div class="swiper-pagination"></div>
</div>

<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script>
  // 轮播图配置
  const initSwiper = () => {
    // 移动端图片显示逻辑已简化，每个幻灯片都会有移动端图片显示
    // 不再需要特殊的JavaScript处理逻辑

    const swiperConfig = {
      spaceBetween: 30,
      centeredSlides: true,
      mousewheel: false,
      // loop: true, // 启用无限循环切换
      autoplay: {
        delay: {{ section.settings.autoplay_delay }} * 50,
        disableOnInteraction: false,
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      on: {
        init: function () {
          const videos = document.querySelectorAll('.swiper-slide video');
          videos.forEach(video => {
            video.pause();
          });
        },
        slideChange: function () {
          const videos = document.querySelectorAll('.swiper-slide video');
          videos.forEach(video => {
            video.pause();
          });

          const activeSlide = this.slides[this.activeIndex];
          const video = activeSlide.querySelector('video');
          if (video) {
            video.currentTime = 0;
            video.play().catch(e => console.log('视频自动播放被阻止:', e));
          }
        }
      }
    };

    new Swiper(".case-banner-mySwiper", swiperConfig);
  };

  // Shopify专用初始化
  document.addEventListener('DOMContentLoaded', initSwiper);
  document.addEventListener('shopify:section:load', initSwiper);

  // ,
  // {
  //   "type": "range",
  //   "id": "slider_height",
  //   "label": "Slider Height (px)",
  //   "min": 760,
  //   "max": 760,
  //   "step": 10,
  //   "default": 760,
  //   "unit": "px"
  // }
</script>

{% schema %}
{
  "name": "Banner Slider",
  "settings": [
    {
      "type": "range",
      "id": "autoplay_delay",
      "label": "切换时长 (秒)",
      "min": 1,
      "max": 100,
      "step": 1,
      "default": 5,
      "unit": "s"
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "Slide",
      "settings": [
        {
          "type": "select",
          "id": "type",
          "label": "Slide Type",
          "options": [
            {
              "value": "image",
              "label": "Image"
            },
            {
              "value": "video",
              "label": "Video"
            }
          ],
          "default": "image"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Web端图片",
          "info": "桌面端使用的图片"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "移动端图片",
          "info": "移动端专用图片"
        },
        {
          "type": "text",
          "id": "video",
          "label": "Video URL",
          "info": "Required for video slides"
        },
        {
          "type": "image_picker",
          "id": "poster",
          "label": "Video Poster",
          "info": "Optional for video slides"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Slide Title"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Slide Description"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Banner Slider"

    }
  ]
}
{% endschema %}