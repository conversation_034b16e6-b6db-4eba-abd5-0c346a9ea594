/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "header",
  "name": "t:labels.header_group",
  "sections": {
    "toolbar": {
      "type": "toolbar",
      "blocks": {
        "toolbar-0": {
          "type": "announcement",
          "settings": {
            "richtext": "<p>Welcome Offer: Enjoy 10%+ Off and Free Delivery—No Code Needed!</p>",
            "richtext_mobile": ""
          }
        },
        "toolbar-1": {
          "type": "announcement",
          "disabled": true,
          "settings": {
            "richtext": "<p><strong>Get 12% Off Sitewide for All the Orders with Free Shipping！</strong></p>",
            "richtext_mobile": ""
          }
        }
      },
      "block_order": [
        "toolbar-0",
        "toolbar-1"
      ],
      "settings": {
        "announcement_center": true,
        "toolbar_social": false,
        "show_locale_selector": true,
        "show_currency_selector": true,
        "show_currency_flags": true
      }
    },
    "header": {
      "type": "header",
      "blocks": {
        "erlink_YAi4Eh": {
          "type": "erlink",
          "settings": {
            "pare_name": "Home",
            "link_list": "footer",
            "er_url": "",
            "pare_name2": "",
            "hottext": "",
            "hotc1": "#ffffff",
            "hotbg1": "#cdb399",
            "pare_name3": "",
            "hottext2": "",
            "hotc2": "#ffffff",
            "hotbg2": "#cdb399",
            "pare_name4": "",
            "hottext3": "",
            "hotc3": "#ffffff",
            "hotbg3": "#cdb399"
          }
        },
        "erlink_zz97Ud": {
          "type": "erlink",
          "settings": {
            "pare_name": "Home",
            "link_list": "customer-account-main-menu",
            "er_url": "",
            "pare_name2": "Shop",
            "hottext": "hot",
            "hotc1": "#ffffff",
            "hotbg1": "#cdb399",
            "pare_name3": "",
            "hottext2": "",
            "hotc2": "#ffffff",
            "hotbg2": "#cdb399",
            "pare_name4": "",
            "hottext3": "",
            "hotc3": "#ffffff",
            "hotbg3": "#cdb399"
          }
        },
        "danimage_3LmxK4": {
          "type": "danimage",
          "settings": {
            "pare_name": "Home",
            "slider_image": "shopify://shop_images/image_7_46a26dfd-bd53-4011-81a0-b2abb6d78aa8.png",
            "text": "Savanna Collection",
            "texturl": "shopify://products/rivet-bar-stools-tufted-back"
          }
        },
        "danimage_NmzHpy": {
          "type": "danimage",
          "settings": {
            "pare_name": "Home",
            "slider_image": "shopify://shop_images/image_8_a1af1fd7-01e2-485b-bea0-d3aee0d11253.png",
            "text": "Nightstands",
            "texturl": "shopify://pages/about-us"
          }
        },
        "collectionlist_xWxW9j": {
          "type": "collectionlist",
          "settings": {
            "pare_name": "Home",
            "collection_list": [
              "all-bedroom",
              "andy-collections-1",
              "sicotas-leather-counter-velvet-bar-stool-for-club-and-kitchen",
              "bar-wine-cabinet",
              "armoires-wardrobes",
              "stools",
              "bed-room-furniture"
            ]
          }
        },
        "erlink_yp368B": {
          "type": "erlink",
          "settings": {
            "pare_name": "order",
            "link_list": "text",
            "er_url": "",
            "pare_name2": "",
            "hottext": "",
            "hotc1": "#ffffff",
            "hotbg1": "#cdb399",
            "pare_name3": "",
            "hottext2": "",
            "hotc2": "#ffffff",
            "hotbg2": "#cdb399",
            "pare_name4": "",
            "hottext3": "",
            "hotc3": "#ffffff",
            "hotbg3": "#cdb399"
          }
        },
        "logo_VNUqDC": {
          "type": "logo",
          "settings": {
            "logo": "shopify://shop_images/logo_5d5becb0-ccad-495a-80af-35321f83556f.png",
            "logo-inverted": "shopify://shop_images/logo_5d5becb0-ccad-495a-80af-35321f83556f.png",
            "desktop_logo_width": 170,
            "mobile_logo_width": 110
          }
        },
        "mbblock_KbApQA": {
          "type": "mbblock",
          "settings": {
            "oneclass": "Collections",
            "oneurl": "",
            "collection_select": "all",
            "menu_link1": "text",
            "collection_select2": "andy-collections-1",
            "menu_link2": "text",
            "menu_link3": "text2"
          }
        },
        "mbblock_nAeLQB": {
          "type": "mbblock",
          "settings": {
            "oneclass": "New",
            "oneurl": "shopify://blogs/blogs-sicotas-brand-story",
            "collection_select": "",
            "menu_link1": "",
            "collection_select2": "",
            "menu_link2": "",
            "menu_link3": ""
          }
        },
        "mbblock_QzjGRh": {
          "type": "mbblock",
          "settings": {
            "oneclass": "All",
            "oneurl": "shopify://collections/all",
            "collection_select": "",
            "menu_link1": "",
            "collection_select2": "",
            "menu_link2": "",
            "menu_link3": ""
          }
        },
        "mbblocktwo_UDFP3r": {
          "type": "mbblocktwo",
          "settings": {
            "one_picker": "shopify://shop_images/fcd648d2002d7413dc2f5018775a421c_2130eb31-64f0-4f0f-a96a-5fc626dddc16.jpg",
            "oneclass": "Living Room",
            "oneurl": "",
            "collection_select": "storage-furniture",
            "menu_link1": "main-menu",
            "collection_select2": "",
            "menu_link2": "",
            "menu_link3": "text2"
          }
        }
      },
      "block_order": [
        "erlink_YAi4Eh",
        "erlink_zz97Ud",
        "danimage_3LmxK4",
        "danimage_NmzHpy",
        "collectionlist_xWxW9j",
        "erlink_yp368B",
        "logo_VNUqDC",
        "mbblock_KbApQA",
        "mbblock_nAeLQB",
        "mbblock_QzjGRh",
        "mbblocktwo_UDFP3r"
      ],
      "settings": {
        "main_menu_link_list": "text",
        "hover_menu": true,
        "main_menu_alignment": "left",
        "mega_products": true,
        "header_sticky": true,
        "header_footer_menu": true,
        "sticky_index": false,
        "sticky_collection": false
      }
    }
  },
  "order": [
    "toolbar",
    "header"
  ]
}
