<style>
    .social-img-{{section.id}}{
        padding:3.33vw 5.21vw 3.39vw 5.21vw

    }
.social-img-{{section.id}} .image-social-centent{
    display: flex;
    gap: 20px;
    margin-top: 32px;
}
.social-img-{{section.id}} h2.social-title{
    font-family: Playfair Display;
font-weight: 400;
font-style: Regular;
font-size: 26px;
leading-trim: NONE;
line-height: 1.35;
letter-spacing: 0px;

}
.image-social-centent .social-img-container{
    position: relative;
}
.image-social-centent .social-img-text{
    position: absolute;
    display: flex;
    gap: 16.4px;
    bottom: 0;
    width: 100%;
    justify-content: center;
    align-items: center;
    height: 70px;
    angle: 0 deg;
    opacity: 1;
    
    background: linear-gradient(360deg, #6D4C41 0%, rgba(105, 90, 85, 0.6) 49.52%, rgba(102, 102, 102, 0) 100%);

}
.image-social-centent .social-img img.mb{
    display: none;
}
.social-img-text .socialtitle{
    font-family: PingFang SC;
    font-weight: 500;
    font-style: Medium;
    font-size: 24px;
    leading-trim: NONE;
    line-height: 1.4;
    letter-spacing: 0px;
    color: #fff;
}
.social-img-text .socitext{
    font-family: PingFang SC;
    font-weight: 400;
    font-style: Regular;
    font-size: 20px;
    leading-trim: NONE;
    line-height: 1.4;
    letter-spacing: 0px;
    margin: 0;
    padding: 0;
    color: #fff;
}
@media (max-width: 1150px) and (min-width: 769px) {
    
    .image-social-centent .social-img-text{
        flex-direction: column;
        gap: 0;
    }
}
@media only screen and (max-width: 768px) {
    .social-img-{{section.id}} h2.social-title{
      
    font-weight: 400;
    line-height: 1;
    font-size: 26px;


    }
    .social-img-{{section.id}}{
        padding:  0 16px;
    }
    .social-img-{{section.id}} .image-social-centent{
        flex-direction: column;
        gap: 15px;
        margin-top: 14px;
    }
    .image-social-centent .social-img-text{
        align-items: flex-start;
        flex-direction: column;
        gap: 0;
    }
    .social-img-text .socialtitle{
        font-size: 18px;
        margin-left: 16px;
    }
    .social-img-text .socitext{
        font-size: 14px;
        margin-left: 16px;
    }
    .image-social-centent .social-img img.mb{
        display: block;
    }
    .image-social-centent .social-img img.pc{
        display: none;
    }
}

</style>

<div class="page-width page-content social-img-{{section.id}}">
    <h2 class="social-title">{{section.settings.title}}</h2>
    <div class="image-social-centent">
        {% for block in section.blocks %}
        <div class="social-img-container">
            <div class="social-img">
              <a> 
                {% if block.settings.pcimage != blank %} 
                    <img src="{{block.settings.pcimage | image_url }}" alt="{{block.settings.pcimage.alt}}" class="pc">
                {% endif %}

                {% if block.settings.mbimage != blank %} 
                    <img src="{{block.settings.mbimage | image_url }}" alt="{{block.settings.mbimage.alt}}" class="mb">
                {% else %}
                 <img src="{{block.settings.pcimage | image_url }}" alt="{{block.settings.pcimage.alt}}" class="mb">
            {% endif %}
              </a> 
            </div>
            <div class="social-img-text">
              {% if block.settings.title != blank %}  <h3 class="socialtitle">{{block.settings.title}}</h3> {% endif %}
              {% if block.settings.text != blank %} <p class="socitext">{{block.settings.text}}</p>{% endif %}
            </div>
        </div>
        {% endfor %}
    
    </div>
</div>


{% schema %}
    {
      "name": "社媒图片",
      "max_blocks": 4,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "标题"
        }
      ],
      "blocks": [
    {
      "type": "svg",
      "name": "图文",
      "settings": [
        {
          "type": "image_picker",
          "id": "pcimage",
          "label": "pcimage"
        },
        {
          "type": "image_picker",
          "id": "mbimage",
          "label": "mbimage"
        },
        {
          "type": "text",
          "id": "title",
          "label": "标题"
        },
      
        {
          "type": "text",
          "id": "text",
          "label": "文本"
        },
        {
          "type": "url",
          "id": "link",
          "label": "链接"
        }
        
      ]
    }
  ], 

      "presets": [
        {
          "name": "社媒图片"
        }
      ]
    }
  {% endschema %}