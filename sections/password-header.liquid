{%- render 'section-password-header' -%}

{% schema %}
{
  "name": "t:labels.header",
  "class": "password-header-section",
  "settings": [
    {
      "type": "checkbox",
      "id": "overlay_header",
      "label": "t:labels.overlay_header",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:labels.logo_image"
    },
    {
      "type": "range",
      "id": "desktop_logo_height",
      "label": "t:labels.desktop_logo_height",
      "default": 50,
      "min": 20,
      "max": 120,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "mobile_logo_height",
      "label": "t:labels.mobile_logo_height",
      "default": 50,
      "min": 20,
      "max": 120,
      "unit": "px"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
