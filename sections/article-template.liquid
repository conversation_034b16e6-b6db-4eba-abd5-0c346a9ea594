{%- render 'section-article-template' -%}

{% schema %}
{
  "name": "t:labels.article_pages",
  "class": "blog-layout__main",
  "settings": [
    {
      "type": "checkbox",
      "id": "blog_capitalize_first",
      "label": "t:actions.capitalize_first_letter",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_tags",
      "label": "t:actions.show_tags",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_date",
      "label": "t:actions.show_date",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_comments",
      "label": "t:actions.show_comment_count",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_author",
      "label": "t:actions.show_author"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
