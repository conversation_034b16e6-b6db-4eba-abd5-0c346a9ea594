{%- render 'section-image-compare' -%}

{% schema %}
{
  "name": "t:labels.image_comparison",
  "class": "image-comparison-section",
  "max_blocks": 2,
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "t:labels.heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:labels.heading_size",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "t:labels.sizes.small"
        },
        {
          "value": "h2",
          "label": "t:labels.sizes.medium"
        },
        {
          "value": "h1",
          "label": "t:labels.sizes.large"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_position",
      "label": "t:labels.heading_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "center",
          "label": "t:labels.alignments.center"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "fullwidth",
      "label": "t:labels.full_page_width",
      "default": true
    },
    {
      "type": "select",
      "id": "slider_style",
      "label": "t:labels.slider_style",
      "default": "classic",
      "options": [
        {
          "value": "classic",
          "label": "t:labels.classic"
        },
        {
          "value": "minimal",
          "label": "t:labels.minimal"
        }
      ]
    },
    {
      "type": "range",
      "id": "height",
      "label": "t:labels.height",
      "default": 600,
      "min": 400,
      "max": 900,
      "step": 10,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:labels.colors"
    },
    {
      "type": "color",
      "id": "color",
      "label": "t:labels.button",
      "default": "#FFFFFF"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "t:labels.image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:labels.image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:labels.image_comparison",
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
