<style>
    /* 内容区域 - Web端基于1920px计算vw */
    .content {
        padding: 4.01vw 17.71vw 5.16vw 17.12vw; /* 77px 340px 99px 338px */
        display: flex;
        align-items: center;
        gap: 3.65vw; /* 70px */
        color: {{ section.settings.text_color | default: '#8F8884' }};
        background: {{ section.settings.background_color | default: 'transparent' }};
    }

    .content-logo {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .logo-text {
        font-weight: 100;
        color: {{ section.settings.text_color | default: '#8F8884' }};
      font-family: Lato;
      font-style: Hairline;
      font-size: 5.208vw; /* 80px */
      leading-trim: NONE;
      line-height: 100%;
      letter-spacing: 0px;

    }

    .content-box {
        flex: 1;
    }

    .content-line {
        background: {{ section.settings.line_color | default: section.settings.text_color | default: '#8F8884' }};
        height: 2px; /* 2px */
        width: 4.17vw; /* 80px */
        margin-bottom: 1.04vw; /* 20px */
    }

    .content-text {
        font-size: 0.83vw; /* 16px */
        font-weight: 400;
        line-height: 1.56vw; /* 30px */
        color: {{ section.settings.text_color | default: '#8F8884' }};
        /* web端显示全部内容，不需要行数限制 */
        display: block;
        -webkit-line-clamp: unset;
        -webkit-box-orient: unset;
        overflow: visible;
    }

    /* web端隐藏Read More按钮 */
    .read-more-btn {
        display: none;
    }

    /* 桌面端样式 - 基于1920px基础使用vw单位 */
    @media (min-width: 769px) {
        /* 桌面端确保显示全部内容，不需要Read More功能 */
        .content-text {
            display: block !important;
            -webkit-line-clamp: unset !important;
            -webkit-box-orient: unset !important;
            overflow: visible !important;
        }

        /* 桌面端隐藏Read More按钮 */
        .read-more-btn {
            display: none !important;
        }
    }

    /* 响应式设计 - 平板设备 */
    @media (max-width: 1024px) {
        .content {
            padding: 2.08vw 5.21vw; /* 40px 100px */
            gap: 2.08vw; /* 40px */
        }
    }

    /* 响应式设计 - 手机设备 基于768px计算vw */
    @media (max-width: 768px) {
        .content {
            flex-direction: column;
            padding: 2.99vw 0.91vw 3.9vw 2.08vw; /* 23px 7px 30 16px */
            gap: 1.3vw; /* 10px */
        }

        .content-logo {
            width: 100%;
            justify-content: flex-start;
          font-weight: 300;
          font-style: Light;
          font-size: 2.6vw;  /* 20px */
          leading-trim: NONE;
          line-height: 100%;
          letter-spacing: 0px;

        }

        .content-line {
            margin-bottom: 1.82vw; /* 14px */
          width: 3.9vw; /* 30 */
        }

        .logo-text {
            font-family: Lato;
            font-weight: 100;
            font-style: Light;
            font-size: 2.6vw; /* 20px */
            leading-trim: NONE;
            line-height: 100%;
            letter-spacing: 0px;
        }

        .content-text {
            font-family: PingFang SC;
            font-weight: 400;
            font-style: Regular;
            font-size: 1.82vw; /* 14px */
            leading-trim: NONE;
            line-height: 2.6vw; /* 20px */
            letter-spacing: 0px;
            display: -webkit-box;
            -webkit-line-clamp: 3; /* 默认显示3行 */
            -webkit-box-orient: vertical;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .content-text.expanded {
            display: block;
            -webkit-line-clamp: unset;
        }

        .read-more-btn {
            font-family: PingFang SC;
            font-weight: 400;
            font-style: normal;
            font-size: 1.82vw; /* 14px ÷ 768 × 100 = 1.82vw */
            line-height: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
            letter-spacing: 0px;
            text-decoration: underline;
            text-decoration-style: solid;
            color: inherit;
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            margin-top: 1.30vw; /* 10px ÷ 768 × 100 = 1.30vw */
            transition: opacity 0.3s ease;
            display: block;
        }

        .read-more-btn:hover {
            opacity: 0.7;
        }
    }

    /* 响应式设计 - 小屏手机设备 基于375px计算vw */
    @media (max-width: 480px) {
        .content {
            flex-direction: column;
            padding: 6.13vw 1.87vw 8vw 4.27vw; /* 23px 7px 30px 16px */
            gap: 2.67vw; /* 10px */
        }

        .content-logo {
            width: 100%;
            justify-content: flex-start;
          font-weight: 300;
          font-style: Light;
          font-size: 5.33vw;  /* 20px */
          leading-trim: NONE;
          line-height: 100%;
          letter-spacing: 0px;
        }

        .content-line {
            margin-bottom: 3.73vw; /* 14px */
          width: 8vw; /* 30 */
        }

        .logo-text {
            font-family: Lato;
            font-weight: 100;
            font-style: Light;
            font-size: 5.33vw; /* 20px */
            leading-trim: NONE;
            line-height: 100%;
            letter-spacing: 0px;
        }

        .content-text {
            font-family: PingFang SC;
            font-weight: 400;
            font-style: Regular;
            font-size: 3.73vw; /* 14px */
            leading-trim: NONE;
            line-height: 5.33vw; /* 20px */
            letter-spacing: 0px;
            display: -webkit-box;
            -webkit-line-clamp: 3; /* 默认显示3行 */
            -webkit-box-orient: vertical;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .content-text.expanded {
            display: block;
            -webkit-line-clamp: unset;
        }

        .read-more-btn {
            font-family: PingFang SC;
            font-weight: 400;
            font-style: normal;
            font-size: 3.73vw; /* 14px ÷ 375 × 100 = 3.73vw */
            line-height: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
            letter-spacing: 0px;
            text-decoration: underline;
            text-decoration-style: solid;
            color: inherit;
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            margin-top: 2.67vw; /* 10px ÷ 375 × 100 = 2.67vw */
            transition: opacity 0.3s ease;
            display: block;
        }

        .read-more-btn:hover {
            opacity: 0.7;
        }
    }
</style>

<!-- 内容区域 -->
<section class="content">
    <div class="content-logo">
        <div class="logo-text">{{ section.settings.logo_text }}</div>
    </div>
    <div class="content-box">
        <div class="content-line"></div>
        <div class="content-text" id="caseGraphicMainDescription">
            <p>{{ section.settings.main_description }}</p>
        </div>
        <button class="read-more-btn" id="caseGraphicReadMoreBtn" onclick="toggleReadMoreCaseGraphic()">Read More</button>
    </div>
</section>

<script>
function toggleReadMoreCaseGraphic() {
    const viewportWidth = window.innerWidth;

    // 只在移动端（768px以下）执行Read More功能
    if (viewportWidth <= 768) {
        const textElement = document.getElementById('caseGraphicMainDescription');
        const btnElement = document.getElementById('caseGraphicReadMoreBtn');

        if (textElement && btnElement) {
            if (textElement.classList.contains('expanded')) {
                // 收起内容
                textElement.classList.remove('expanded');
                btnElement.textContent = 'Read More';
            } else {
                // 展开内容
                textElement.classList.add('expanded');
                btnElement.textContent = 'Read Less';
            }
        }
    }
    // web端不执行任何操作
}

// 只在移动端检查是否需要显示Read More按钮
document.addEventListener('DOMContentLoaded', function() {
    const viewportWidth = window.innerWidth;

    // 只在移动端（768px以下）执行Read More功能
    if (viewportWidth <= 768) {
        const textElement = document.getElementById('caseGraphicMainDescription');
        const btnElement = document.getElementById('caseGraphicReadMoreBtn');

        if (textElement && btnElement) {
            // 检查文本是否超过3行
            const lineHeight = parseInt(window.getComputedStyle(textElement).lineHeight);
            const maxHeight = lineHeight * 3;

            // 临时展开以检查实际高度
            textElement.style.display = 'block';
            textElement.style.webkitLineClamp = 'unset';
            const actualHeight = textElement.scrollHeight;

            // 恢复原始状态
            textElement.style.display = '';
            textElement.style.webkitLineClamp = '';

            // 如果内容不超过3行，隐藏Read More按钮
            if (actualHeight <= maxHeight) {
                btnElement.style.display = 'none';
            } else {
                btnElement.style.display = 'block';
            }
        }
    } else {
        // web端隐藏Read More按钮并显示全部内容
        const textElement = document.getElementById('caseGraphicMainDescription');
        const btnElement = document.getElementById('caseGraphicReadMoreBtn');

        if (textElement) {
            textElement.style.display = 'block';
            textElement.style.webkitLineClamp = 'unset';
            textElement.style.webkitBoxOrient = 'unset';
            textElement.style.overflow = 'visible';
        }

        if (btnElement) {
            btnElement.style.display = 'none';
        }
    }
});
</script>

{% schema %}
{
  "name": "Content Section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "logo_text",
      "label": "Logo Text",
      "default": "Cas"
    },
    {
      "type": "textarea",
      "id": "main_description",
      "label": "Main Description",
      "default": "对于那些希望在室内引入更多纹理和视觉趣味的人来说，Cas系列以其波浪形面板而闻名，为任何空间增添深度和层次感。虽然波浪图案可能会产生大胆的视觉效果，但家具采用微妙的冷色调木材，从白色到深灰色桤木，让人感觉相当宁静。该系列以仙后座命名，这是一个代表好运、愿望实现、善良和纯洁的星座——所有这些都是您想要融入家中的元素！"
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "range",
      "id": "logo_font_size",
      "label": "Logo Font Size (px)",
      "min": 50,
      "max": 150,
      "step": 5,
      "default": 100
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#8F8884"
    },
    {
      "type": "color",
      "id": "line_color",
      "label": "Decorative Line Color",
      "default": "#8F8884"
    }
  ],
  "presets": [
    {
      "name": "Content Section",
      "category": "Text"
    }
  ]
}
{% endschema %}