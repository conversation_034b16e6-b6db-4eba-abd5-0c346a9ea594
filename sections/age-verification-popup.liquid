{%- render 'section-age-verification-popup' -%}

{% schema %}
{
  "name": "t:labels.age_verification_popup",
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_test_mode",
      "label": "t:actions.enable_test_mode",
      "info": "t:info.forces_the_age_verification",
      "default": false
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:labels.color_scheme",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "t:labels.none"
        },
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:labels.background_image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:labels.image",
      "info": "t:info.2000x800px_recommended"
    },
    {
      "type": "checkbox",
      "id": "blur_image",
      "label": "t:actions.blur_the_image",
      "default": false
    },
    {
      "type": "header",
      "content": "t:labels.age_verification_question"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:labels.heading",
      "default": "Confirm your age"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "t:labels.age_verification_question",
      "default": "<p>Are you 18 years old or older?</p>"
    },
    {
      "type": "text",
      "id": "decline_button_label",
      "label": "t:actions.decline_button_text",
      "default": "No I'm not"
    },
    {
      "type": "text",
      "id": "approve_button_label",
      "label": "t:actions.approve_button_text",
      "default": "Yes I am"
    },
    {
      "type": "header",
      "content": "t:labels.declined"
    },
    {
      "type": "paragraph",
      "content": "t:info.content_for_age_verification_failure"
    },
    {
      "type": "text",
      "id": "decline_heading",
      "label": "t:labels.heading",
      "default": "Come back when you're older"
    },
    {
      "type": "richtext",
      "id": "decline_text",
      "label": "t:labels.text",
      "default": "<p>Sorry, the content of this store can't be seen by a younger audience. Come back when you're older.</p>"
    },
    {
      "type": "text",
      "id": "return_button_label",
      "label": "t:actions.return_button_text",
      "default": "Oops, I entered incorrectly"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header"]
  }
}
{% endschema %}
