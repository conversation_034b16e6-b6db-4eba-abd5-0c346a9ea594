<style>
    .text-icon-{{section.id}}{
        background: #FCF7F2;
        padding: 47px 0;
    }
    .page-content__inner_text{
        display: flex;
        justify-content: space-around;
        align-items: center;
        /* gap: 89px; */
        width: calc(100% - 288px);
        margin: 0 auto;
    }
    .page-content__inner_text .text-icon-flex{
        display: flex;
    align-items: center;
    gap: 20px;
    }
    .page-content__inner_text .text-icon__text .text-icon__title{
        font-family: PingFang SC;
        font-weight: 600;
        font-style: Semibold;
        font-size: 16px;
        leading-trim: NONE;
        line-height: 1.4;
        letter-spacing: 0px;
        color: #6D4C41;
    }
    .page-content__inner_text .text-icon__text .text-icon__title.mb{
            display: none;
        }
    .page-content__inner_text .text-icon__text p{
        font-family: PingFang SC;
        font-weight: 400;
        font-style: Regular;
        font-size: 14px;
        leading-trim: NONE;
        line-height: 1.4;
        letter-spacing: 0px;
        color: #6D4C41;
    }
    .page-content__inner_text .text-icon-flex.hr:last-child{
        display: none;
    }
    @media (max-width: 1400px) and (min-width: 769px) {
        .page-content__inner_text{
            width: 100%;
            gap: 20px;
        }
        .page-content__inner_text .text-icon-flex{
            flex-direction: column;
        }
        .page-content__inner_text .text-icon__text{
            text-align: center;
        }
    }
      
    @media only screen and (max-width: 768px)  {
        .text-icon-{{section.id}}{
    
            padding: 17px 25px 12px;
        }
        .page-content__inner_text{
            width: 100%;
        }
        .page-content__inner_text .text-icon-flex{
            flex-direction: column;
            justify-content: center;
            gap: unset;
            row-gap: 11px;
        }
        .page-content__inner_text .text-icon__text p,.page-content__inner_text .text-icon-flex.hr{
            display: none;
        }
        .page-content__inner_text .text-icon__text .text-icon__title{
            font-weight: 400;
            font-style: Regular;
            font-size: 14px;
            leading-trim: NONE;
            line-height: 18px;
            letter-spacing: 0px;
            text-align: center; 
           
        } 
        .page-content__inner_text .text-icon__text .text-icon__title.mb{
            display: block !important;
        }
        .page-content__inner_text .text-icon__text .text-icon__title.pc{
            display: none;
        }
        .text-icon__icon .shipping-icon{
            width: 26px;
            height: 17px;
        }
        .text-icon__icon .evenodd-icon{
            width: 21px;
            height: 15px;
        }
        .text-icon__icon .return-icon{
            width: 18px;
            height: 18px;
        }
        .text-icon__icon .day-icon{
            width: 17px;
            height: 19px;
        }
    }
</style>
<div class="page-width page-content text-icon-{{section.id}}">
    <div class="page-content__inner_text ">
        
    {% for block in section.blocks %}
        
            <div class="text-icon-flex">
                {% if block.settings.svg != blank %}
                <div class="text-icon__icon">
                    {{block.settings.svg}}
                </div>
                {% endif %}
                <div class="text-icon__text">
                <a href="{{block.settings.link}}"> 
                 {% if block.settings.pc_title != blank %}<h3 class="text-icon__title pc">{{block.settings.pc_title}}</h3> {% endif %}
                </a>

            {% if block.settings.mb_title != blank %}
                <a href="{{block.settings.link}}">   <h3 class="text-icon__title mb">{{block.settings.mb_title}}</h3></a>
                {% else %}
                <a href="{{block.settings.link}}">  <h3 class="text-icon__title mb">{{block.settings.pc_title}}</h3></a>
            {% endif %}
            <a href="{{block.settings.link}}">   <p>{{block.settings.text}}</p> </a>
                </div>
            </div>
            <div class="text-icon-flex hr {% if forloop.index >= 1 %} is_show {% endif} %}">
                <svg width="1" height="67" viewBox="0 0 1 67" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.20373" d="M0.5 0.5V66.5" stroke="#CDB399" stroke-linecap="square"/>
                </svg>
            </div>
        
      {% endfor %}
         
     
    </div>
</div>


{% schema %}
    {
      "name": "Text Icon",
      "max_blocks": 4,
      "settings": [
      ],
      "blocks": [
    {
      "type": "svg",
      "name": "文本和svg",
      "settings": [
        {
          "type": "textarea",
          "id": "svg",
          "label": "svg"
        },
        {
          "type": "text",
          "id": "pc_title",
          "label": "电脑端标题"
        },
        {
          "type": "text",
          "id": "mb_title",
          "label": "手机端标题"
        },
        {
          "type": "text",
          "id": "text",
          "label": "文本"
        },
        {
          "type": "url",
          "id": "link",
          "label": "链接"
        }
        
      ]
    }
  ], 

      "presets": [
        {
          "name": "Text Icon"
        }
      ]
    }
  {% endschema %}