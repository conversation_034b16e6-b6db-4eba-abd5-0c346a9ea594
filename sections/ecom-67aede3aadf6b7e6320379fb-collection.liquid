{% comment %}
 EComposer (https://ecomposer.io)
 You SHOULD NOT modify source code in this page because
 It is automatically generated from EComposer
 At 2025-02-26 02:00:46
 {% endcomment %} 
 
 {% assign ecom_root_url = routes.root_url %}
 {% if ecom_root_url != '/'%}
 {% assign ecom_root_url = routes.root_url | append: '/' %}
 {% endif %}
 <link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.7" /><noscript><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.7" /></noscript><script type="text/javascript" asyc="async" id="ecom-vendors-slider_js" src="https://cdn.ecomposer.app/vendors/js/<EMAIL>" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-shopify_option_selection_js" src="https://cdn.ecomposer.app/vendors/js/ecomposer_option_selection.js" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-countdown_js" src="https://cdn.ecomposer.app/vendors/js/ecom-countdown.js?ver=2.0" ></script>
{%capture section_id %}ecom-67aede3aadf6b7e6320379fb-collection{% endcapture%}{% if section and section_id == section.id and headless == true %}
{{ content_for_header }}
{% render 'ecom_header', ECOM_THEME: true %}{% endif %}<link href="https://fonts.googleapis.com/css?family=Jost:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Roboto:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/>
{{'ecom-67aede3aadf6b7e6320379fb.css' | asset_url | stylesheet_tag }}
<script src="{{'ecom-67aede3aadf6b7e6320379fb.js' | asset_url }}" defer="defer"></script>
<script type="text/javascript" class="ecom-page-info">
 window.EComposer = window.EComposer || {};
 window.EComposer.TEMPLATE_ID="67aede3aadf6b7e6320379fb";
 window.EComposer.TEMPLATE = {"template_id":"67aede3aadf6b7e6320379fb","title":"Entryway","type":"collection","slug":"ecom-67aede3aadf6b7e6320379fb","plan_id":4};
 </script>
<div class="ecom-builder" id="ecom-67aede3aadf6b7e6320379fb"><div class="ecom-sections" data-section-id="{{section.id}}">
 {% comment %}
 // Reactive
 * this.data.settings.collection
 {% endcomment %}
 {% liquid
 if request.page_type != 'collection' or collection == blank
 assign selected_collection = 'entryway'
 if selected_collection == blank or selected_collection == '' or collections[selected_collection] == blank
 assign collection = collections.all
 else
 assign collection = collections[selected_collection]
 if collection.id == blank
 assign collection = collections.all
 endif
 endif
 endif
 %}
 <section class="ecom-row ecom-core ecom-section ecom-gbjndpwh0u" data-id="ecom-gbjndpwh0u" data-notice-content="" style="z-index: inherit;"><div data-deep="1" class="core__row--columns core__row--full"><div class="ecom-column ecom-core core__column--first core__column--last ecom-g0neuzxx9od"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-vqgm1c3ro3e" data-core-is="block"><div class="ecom-element ecom-base ecom-base__breadcrumbs--wrapper" deep="1">
 {%- if EComBuilderMode -%}
 {% capture page_type %}collection{% endcapture %}
 {%- else -%}
 {%- assign page_type = request.page_type %}
 {%- endif %}
 {%- capture icon_parent -%}
 
 {%- endcapture -%}
 {%- capture icon_breadcrumbs -%}
 <span class="ecom-base__breadcrumbs--icon">
 <svg width="8" height="8" viewBox="0 0 8 8" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><circle cx="4" cy="4" r="4"></circle></svg>
 </span>
 
 {%- endcapture -%}
 {%- unless page_type == 'index' or page_type == 'list-collections' or page_type == '404' -%}
 <nav class="ecom-base__breadcrumbs ecom-flex" role="navigation" aria-label="breadcrumbs">
 <div class="ecom-base__breadcrumbs__list">
 {{ icon_parent }}<a class="ecom-base__breadcrumbs__link" href="#">Home</a>
 {%- case page_type -%}
 {%- when 'page' -%}
 {{ icon_breadcrumbs }}
 {%- if EComBuilderMode -%}
 <a class="ecom-base__breadcrumbs__link" href="#" aria-current="page">Untitled</a>
 {%- else -%}
 <a class="ecom-base__breadcrumbs__link" href="{{ page.url }}" aria-current="page">{{ page.title }}</a>
 {%- endif -%}
 {%- when 'cart' -%}
 {{ icon_breadcrumbs }}
 <a class="ecom-base__breadcrumbs__link" href="{{ routes.cart_url }}" aria-current="cart">cart</a>
 {%- when 'product' -%}
 {%- if collection.url -%}
 {{ icon_breadcrumbs }}{{ collection.title | link_to: collection.url }}
 {%- else -%}
 {% for collection in product.collections %}
 {% if collection.handle == 'all' or collection.handle == 'frontpage' or collection.title == 'All' %}
 {% continue%}
 {% else %}
 {{ icon_breadcrumbs }}{{ collection.title | link_to: collection.url }}
 {% break %}
 {% endif%}
 {% endfor %}
 {%- endif -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ product.url }}" aria-current="page">{{ product.title }}</a>
 {%- when 'collection' and collection.handle -%}
 {%- if current_tags -%}
 {{ icon_breadcrumbs }}{{ collection.title | link_to: collection.url }}
 {%- capture tag_url -%}{{ collection.url }}/{{ current_tags | join: "+"}}{%- endcapture -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: " + "}}</a>
 {%- else -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ collection.url }}" aria-current="page">{{ collection.title }}</a>
 {%- endif -%}
 {%- when 'blog' -%}
 {%- if current_tags -%}
 {{ icon_breadcrumbs }}{{ blog.title | link_to: blog.url }}
 {%- capture tag_url -%}{{blog.url}}/tagged/{{ current_tags | join: "+" }}{%- endcapture -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: " + " }}</a>
 {%- else -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ blog.url }}" aria-current="page">{{ blog.title }}</a>
 {%- endif -%}
 {%- when 'article' -%}
 {{ icon_breadcrumbs }}{{ blog.title | link_to: blog.url }}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ article.url }}" aria-current="page">{{ article.title }}</a>
 {%- else -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ request.path }}" aria-current="page">{{ page_title }}</a>
 {%- endcase -%}
 </div>
 </nav>
 {%- endunless -%}{%- assign page_type = request.page_type %}
 {%- if page_type == 'index' -%}
 {{ icon_parent }}
 {%- if EComBuilderMode -%}
 <a class="ecom-base__breadcrumbs__link" href="#" aria-current="index">Home</a>
 {%- else -%}
 <a class="ecom-base__breadcrumbs__link" href="/" aria-current="index">Home</a>
 {%- endif -%}
 {%- endif -%}
 </div></div> <div class="ecom-animation ecom-block ecom-core core__block elmspace ecom-acyp6k8frke" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__title" deep="1"><div class="ecom-element ecom-collection ecom-collection__title-wrapper"><div class="ecom-element ecom-collection ecom-collection__title-container"><h2 class="ecom-collection__title--headline ecom__heading">{{collection.title}}</h2></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
 <section class="ecom-row ecom-core ecom-section ecom-a5l8jzswfcu" data-id="ecom-a5l8jzswfcu" data-notice-content="" style="z-index: inherit;"><div data-deep="1" class="core__row--columns core__row--full"><div class="ecom-column ecom-core core__column--first core__column--last ecom-5eyb95fuoy"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-0yb2qnqrm8v" data-core-is="group"><div class="ecom-elements ecom-base-element ecom-elements-slider"><div class="ecom-core ecom-group core__block ecom-re9omgql1si"> <div class="core__group--wrapper" data-r="" style=""><div class="ecom-slider--container"><div class="ecom-element ecom-slider slider__body ecom-swiper-container ecom-slider-not-custom-height" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;loop&quot;:false,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1.6,&quot;spaceBetween&quot;:15},&quot;768&quot;:{&quot;slidesPerView&quot;:3,&quot;spaceBetween&quot;:15},&quot;1025&quot;:{&quot;slidesPerView&quot;:5,&quot;spaceBetween&quot;:30}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:6000,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:500,&quot;effect&quot;:&quot;slide&quot;,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;},&quot;slidesPerGroup&quot;:1}"><div class="ecom-swiper-wrapper"><div class="ecom-swiper-slide"><div class="ecom-content-item"><div class="ecom-content-item__container ecom-transition-none"><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-yjno0uv04ge" data-core-is="block"><div class="ecom__element ecom-element__banner" deep="1"><div class="ecom-banner__hzoom ecom-masonry__nt_promotion ecom-image-default"><a href="{{ecom_root_url}}collections/entryway-savanna-collection" class="ecom-banner__picture-link"><picture class="ecom-banner__image-picture"><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_34.jpg?v=1739514721" name="111_1_34" ext="jpg" thumbnail="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_34.jpg?v=1739514721" alt="cat-s3" width="1000" height="1170" id="HgqZF3ul" style=""></picture></a><div class="ecom-masonry__nt_promotion_html ecom-t__0 ecom-l__0 ecom-r__0 ecom-b__0 ecom-flex ecom-w__100" style="align-items: flex-end;"><div class="ecom-banner_wrap_html_block ecom-flex ecom-column" style="margin: 0px auto;"><h3 class="ecom-banner__subheading">Savanna Collection</h3><div class="ecom-banner__content"><br></div><a class="ecom-banner__button ecom__element--button pe_auto ecom-flex ecom-fl_center ecom-al_center" href="#"><span class="ecom-banner__button--text"></span></a></div></div></div></div></div> </div></div></div></div></div><div class="ecom-swiper-slide"><div class="ecom-content-item"><div class="ecom-content-item__container ecom-transition-none"><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-4973sxs2z75" data-core-is="block"><div class="ecom__element ecom-element__banner" deep="1"><div class="ecom-banner__hzoom ecom-masonry__nt_promotion ecom-image-default"><a href="{{ecom_root_url}}collections/entryway-cas-collection" class="ecom-banner__picture-link"><picture class="ecom-banner__image-picture"><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_22.jpg?v=1739503817" name="111_1_22" ext="jpg" thumbnail="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_22.jpg?v=1739503817" alt="cat-s3" width="1000" height="1170" id="BH6SKKpE" style=""></picture></a><div class="ecom-masonry__nt_promotion_html ecom-t__0 ecom-l__0 ecom-r__0 ecom-b__0 ecom-flex ecom-w__100" style="align-items: flex-end;"><div class="ecom-banner_wrap_html_block ecom-flex ecom-column" style="margin: 0px auto;"><h3 class="ecom-banner__heading"><br></h3><h3 class="ecom-banner__subheading">Cas Collection</h3><div class="ecom-banner__content"><br></div><a class="ecom-banner__button ecom__element--button pe_auto ecom-flex ecom-fl_center ecom-al_center" href="#"><span class="ecom-banner__button--text"></span></a></div></div></div></div></div> </div></div></div></div></div><div class="ecom-swiper-slide"><div class="ecom-content-item"><div class="ecom-content-item__container ecom-transition-none"><div class="core__blocks" index="2"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-utjxi5cu3zl" data-core-is="block"><div class="ecom__element ecom-element__banner" deep="1"><div class="ecom-banner__hzoom ecom-masonry__nt_promotion ecom-image-default"><a href="{{ecom_root_url}}collections/entryway-helio-collection" class="ecom-banner__picture-link"><picture class="ecom-banner__image-picture"><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_30.jpg?v=1739512007" name="111_1_30" ext="jpg" thumbnail="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_30.jpg?v=1739512007" alt="cat-s3" width="1000" height="1170" id="K1rvdVpz" style=""></picture></a><div class="ecom-masonry__nt_promotion_html ecom-t__0 ecom-l__0 ecom-r__0 ecom-b__0 ecom-flex ecom-w__100" style="align-items: flex-end;"><div class="ecom-banner_wrap_html_block ecom-flex ecom-column" style="margin: 0px auto;"><h3 class="ecom-banner__heading"><br></h3><h3 class="ecom-banner__subheading">Helio collection</h3><div class="ecom-banner__content"><br></div><a class="ecom-banner__button ecom__element--button pe_auto ecom-flex ecom-fl_center ecom-al_center" href="#"><span class="ecom-banner__button--text"></span></a></div></div></div></div></div> </div></div></div></div></div><div class="ecom-swiper-slide"><div class="ecom-content-item"><div class="ecom-content-item__container ecom-transition-none"><div class="core__blocks" index="3"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-pv3d3fci9km" data-core-is="block"><div class="ecom__element ecom-element__banner" deep="1"><div class="ecom-banner__hzoom ecom-masonry__nt_promotion ecom-image-default"><a href="{{ecom_root_url}}collections/entryway-opus-collection" class="ecom-banner__picture-link"><picture class="ecom-banner__image-picture"><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_32.jpg?v=1739513251" name="111_1_32" ext="jpg" thumbnail="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_32.jpg?v=1739513251" alt="cat-s3" width="1000" height="1170" id="fgwJIjS0" style=""></picture></a><div class="ecom-masonry__nt_promotion_html ecom-t__0 ecom-l__0 ecom-r__0 ecom-b__0 ecom-flex ecom-w__100" style="align-items: flex-end;"><div class="ecom-banner_wrap_html_block ecom-flex ecom-column" style="margin: 0px auto;"><h3 class="ecom-banner__heading"><br></h3><h3 class="ecom-banner__subheading">Opus Collection</h3><div class="ecom-banner__content"><br></div><a class="ecom-banner__button ecom__element--button pe_auto ecom-flex ecom-fl_center ecom-al_center" href="#"><span class="ecom-banner__button--text"></span></a></div></div></div></div></div> </div></div></div></div></div><div class="ecom-swiper-slide"><div class="ecom-content-item"><div class="ecom-content-item__container ecom-transition-none"><div class="core__blocks" index="4"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-a464r2emaab" data-core-is="block"><div class="ecom__element ecom-element__banner" deep="1"><div class="ecom-banner__hzoom ecom-masonry__nt_promotion ecom-image-default"><a href="{{ecom_root_url}}collections/entryway-terra" class="ecom-banner__picture-link"><picture class="ecom-banner__image-picture"><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_33.jpg?v=1739513251" name="111_1_33" ext="jpg" thumbnail="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111_1_33.jpg?v=1739513251" alt="cat-s3" width="1000" height="1170" id="mjWcGXvN" style=""></picture></a><div class="ecom-masonry__nt_promotion_html ecom-t__0 ecom-l__0 ecom-r__0 ecom-b__0 ecom-flex ecom-w__100" style="align-items: flex-end;"><div class="ecom-banner_wrap_html_block ecom-flex ecom-column" style="margin: 0px auto;"><h3 class="ecom-banner__heading"><br></h3><h3 class="ecom-banner__subheading">Terra Collection</h3><div class="ecom-banner__content"><br></div><a class="ecom-banner__button ecom__element--button pe_auto ecom-flex ecom-fl_center ecom-al_center" href="#"><span class="ecom-banner__button--text"></span></a></div></div></div></div></div> </div></div></div></div></div></div></div><div class="ecom-swiper-navigation-position" data-position="center"><button type="button" class="ecom-swiper-button ecom-swiper-button-prev" data-ecnav-id="ecom-re9omgql1si" style="--ecom-position: absolute; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http: //www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-left"><polyline points="15 18 9 12 15 6"></polyline></svg></button><button type="button" class="ecom-swiper-button ecom-swiper-button-next" data-ecnav-id="ecom-re9omgql1si" style="--ecom-position: absolute; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-right"><polyline points="9 18 15 12 9 6"></polyline></svg></button></div><div data-ecnav-id="ecom-re9omgql1si" class="ecom-swiper-pagination-position ecom-swiper-pagination" style="display: none;"></div></div></div> </div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
 {% comment %}
 // Reactive
 * this.data.settings.collection
 {% endcomment %}
 {% liquid
 if request.page_type != 'collection' or collection == blank
 assign selected_collection = 'entryway'
 if selected_collection == blank or selected_collection == '' or collections[selected_collection] == blank
 assign collection = collections.all
 else
 assign collection = collections[selected_collection]
 if collection.id == blank
 assign collection = collections.all
 endif
 endif
 endif
 %}
 <section class="ecom-row ecom-core ecom-section ecom-r39gsccmabf" data-id="ecom-r39gsccmabf" data-notice-content="" style="z-index: inherit;"><div data-deep="1" class="core__row--columns core__row--full"><div class="ecom-column ecom-core core__column--first core__column--last ecom-2uxpfzmd"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-dudtfsd3hhi" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-iqzw6e0xzbs" data-id="ecom-iqzw6e0xzbs" data-notice-content="" style="z-index: inherit;"><div data-deep="2" class="core__row--columns"><div class="ecom-column ecom-core core__column--first ecom-dse3p9tbixp"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-jdeikfz8yin" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__filters" deep="2"><div class="ecom-collection__filters-wrapper" data-page="collection"><div class="ecom-collection__filters-container ecom-collection__filters-dropdown"><button id="button_menu_block" class="ecom-button-filter-collapse button_menu_block--mobile" type="button"><span class="ecom-filter-collapse-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor"><path d="M 5 4 L 5 6.34375 L 5.21875 6.625 L 13 16.34375 L 13 28 L 14.59375 26.8125 L 18.59375 23.8125 L 19 23.5 L 19 16.34375 L 26.78125 6.625 L 27 6.34375 L 27 4 Z M 7.28125 6 L 24.71875 6 L 17.53125 15 L 14.46875 15 Z M 15 17 L 17 17 L 17 22.5 L 15 24 Z"></path></svg></span><span>Filter </span></button><div id="ecom-modal-block" class="ecom-modal-block--mobile"><div class="ecom-modal-content--mobile ecom-scroll_bar ecom-modal-content"><div class="modal-header"><span id="ecom-modal-close" class="ecom-collapse-close ecom-collapse-close--mobile"><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></span></div><span class="ecom-collection__filters-heading">Filter </span><form method="GET" class="ecom-collection__filters-form ecom-click" data-stopdrag="true">
 
 {% if template contains '.' %}
 <input type="hidden" name="view" value="{{template | split: '.' | last }}" />
 {% endif %}
 {% if request.page_type == 'search' %}
 {% assign results = search %}
 {% else %}
 {% assign results = collection %}
 {% endif %}
 {%- assign sort_by = results.sort_by | default: results.default_sort_by -%}
 {% assign color_option_name = 'Color' | handleize %}
 {% liquid
 assign colors = shop.metafields.ecomposer.colors
 %}
 {% capture icon_caret%}
 <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-caret" viewBox="0 0 10 6">
 <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 00-.708 0L5 4.293 1.354.646a.5.5 0 00-.708.708l4 4a.5.5 0 00.708 0l4-4a.5.5 0 000-.708z" fill="currentColor">
 </svg>
 {% endcapture%}
 {% capture close_icon %}
 <span class="ecom-colletion-filters--close-icon">
 <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" role="presentation" fill="currentColor" viewBox="0 0 18 17">
 <path d="M.865 15.978a.5.5 0 00.707.707l7.433-7.431 7.579 7.282a.501.501 0 00.846-.37.5.5 0 00-.153-.351L9.712 8.546l7.417-7.416a.5.5 0 10-.707-.708L8.991 7.853 1.413.573a.5.5 0 10-.693.72l7.563 7.268-7.418 7.417z" fill="currentColor"/>
 </svg>
 </span>
 {% endcapture %}
 
 <div class="ecom-container-filter-list--wrapper " >
 <div class="ecom-container-filter-list-wrapper">
 
 <div class="ecom-container-filter-list" >
 
 {%- for filter in results.filters -%}
 {%- assign total_active_values = total_active_values | plus: filter.active_values.size -%}
 {% assign presentation = filter.presentation | default: "text" %}
 {% case filter.type %}
 {% when 'list' %}
 {% assign size = filter.values | size %}
 {% assign settings_size = undefined %}
 {%- if size > 0 -%}
 <div
 data-name="{{filter.param_name}}"
 open 
 class="ecom-js-filter ecom-collection__filters-group-dropdown ecom-collection__filters-group ecom-collection__filters-group-lists ecom-d-none" data-index="{{ forloop.index }}" data-attrs-max="5">
 <summary class="ecom-collection__filters-group-summary">
 <div class="ecom-collection__filters-group-header">
 <span class="ecom-collection__filters-group-summary--title">{{ filter.label | escape }}</span>
 <div class="ecom-icon-filter-close"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><path d="M432 256C432 269.3 421.3 280 408 280h-160v160c0 13.25-10.75 24.01-24 24.01S200 453.3 200 440v-160h-160c-13.25 0-24-10.74-24-23.99C16 242.8 26.75 232 40 232h160v-160c0-13.25 10.75-23.99 24-23.99S248 58.75 248 72v160h160C421.3 232 432 242.8 432 256z"></path></svg></div>
 <div class="ecom-icon-filter-open"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><path d="M432 256C432 269.3 421.3 280 408 280H40c-13.25 0-24-10.74-24-23.99C16 242.8 26.75 232 40 232h368C421.3 232 432 242.8 432 256z"></path></svg></div>
 </div>
 </summary>
 <div class="ecom-collection__filters-group--display ">
 
 <div class="ecom-collection__filters-group--header">
 <span class="ecom-collection__filters-group--selected no-js-hidden">
 {% assign count = filter.active_values.size %}
 {% if count == 1%}
 {{count}} selected
 {% else %}
 {{count}} selected
 {% endif %}
 </span>
 <a href="{{ filter.url_to_remove }}" class="ecom-collection__filters-group-reset-filter" >Reset</a>
 </div>
 {% assign check_is_filter_color = false %}
 
 <ul
 class="ecom-collection__filters-group-list ecom-scroll_bar{% if check_is_filter_color %} ecom-collection__filters-enable-colors{% endif %}" data-param-name="{{filter.param_name}}" role="list" data-index="{{ forloop.index }}"
 >
 {%- for value in filter.values -%}
 {%- if false == false or value.count > 0 and false == true -%}
 <li class="ecom-collection__filters-group-list-item ecom-filter-hide-checkbox" >
 
 <label for="ecom-filter-{{ filter.label | escape | strip }}-{{ forloop.index }}" class="ecom-collection__filters-group-checkbox{% if value.count == 0 and value.active == false %} ecom-collection__filters-group-checkbox--disabled{% endif %}">
 <input type="checkbox"
 class="ecom-collection__filters-group-checkbox--input "
 name="{{ value.param_name }}"
 value="{{ value.value }}"
 id="ecom-filter-{{ filter.label | escape | strip }}-{{ forloop.index }}"
 {% if value.active %}checked{% endif %}
 {% if value.count == 0 and value.active == false %}disabled{% endif %}
 >
 <span class="ecom-collection__filters-group-checkbox-label ecom-flex ecom-al_center">
 {% if check_is_filter_color %}
 {% assign value_key = value.value | downcase %}
 {% if colors and colors.value[value_key] != blank %}
 <span class="ecom-collection__filters--color-wrapper">
 <span class="ecom-collection__filters--color" style="{{colors.value[value_key]}}" data-ecom-tooltip="{{value.label}}"></span>
 </span>
 {% else %}
 <span class="ecom-collection__filters--color-wrapper">
 <span class="ecom-collection__filters--color ecom-collection__filters--no-color" data-ecom-tooltip="{{value.label}}"></span>
 </span>
 {% endif %}
 {% endif%}
 {% if presentation == 'swatch' %}
 {% assign swatch = value.swatch %}
 {%- liquid
 assign swatch_value = null
 if swatch.image
 assign image_url = swatch.image | image_url: width: 50
 assign swatch_value = 'url(' | append: image_url | append: ')'
 assign swatch_focal_point = swatch.image.presentation.focal_point
 elsif swatch.color
 assign swatch_value = 'rgb(' | append: swatch.color.rgb | append: ')'
 endif
 -%}
 {% if swatch_value %}
 <span class="ecom-collection__filters--color-wrapper">
 <span class="ecom-collection__filters--color" style="background:{{ swatch_value }};{% if swatch_focal_point %} --swatch-focal-point: {{ swatch_focal_point }};{% endif %}" data-ecom-tooltip="{{value.label}}"></span>
 </span>
 {% else %}
 <span class="ecom-collection__filters--color-wrapper">
 <span class="ecom-collection__filters--color ecom-shopify-color-unavailable"></span>
 </span>
 {% endif %}
 {% endif %}
 {{ value.label | escape }}
 <span class="ecom-collection__filters--count">({{ value.count }})</span>
 </span>
 </label>
 </li>
 {%- endif -%}
 {%- endfor -%}
 
 </ul>
 </div>
 </div>
 {%- endif -%}
 {% when 'price_range' %}
 {% liquid
 assign currencies_using_comma_decimals = 'ANG,ARS,BRL,BYN,BYR,CLF,CLP,COP,CRC,CZK,DKK,EUR,HRK,HUF,IDR,ISK,MZN,NOK,PLN,RON,RUB,SEK,TRY,UYU,VES,VND' | split: ','
 assign uses_comma_decimals = false
 if currencies_using_comma_decimals contains cart.currency.iso_code
 assign uses_comma_decimals = true
 endif
 %}
 <div
 open 
 class="ecom-collection__filters-group ecom-collection__filters-group-dropdown ecom-collection__filters-group-price-range" data-index="{{ forloop.index }}">
 <summary class="ecom-collection__filters-group-summary">
 <div class="ecom-collection__filters-group-header">
 <span class="ecom-collection__filters-group-summary--title">{{ filter.label | escape }}</span>
 <span class="ecom-collection__filters-group-count-bubble{%- if filter.min_value.value or filter.max_value.value -%}{{ filter.active_values.size }} count-bubble--dot{% endif %}"></span>
 <div class="ecom-icon-filter-close"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><path d="M432 256C432 269.3 421.3 280 408 280h-160v160c0 13.25-10.75 24.01-24 24.01S200 453.3 200 440v-160h-160c-13.25 0-24-10.74-24-23.99C16 242.8 26.75 232 40 232h160v-160c0-13.25 10.75-23.99 24-23.99S248 58.75 248 72v160h160C421.3 232 432 242.8 432 256z"></path></svg></div>
 <div class="ecom-icon-filter-open"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor"><path d="M432 256C432 269.3 421.3 280 408 280H40c-13.25 0-24-10.74-24-23.99C16 242.8 26.75 232 40 232h368C421.3 232 432 242.8 432 256z"></path></svg></div>
 </div>
 </summary>
 <div class="ecom-collection__filters-group--display ">
 {%- comment -%}
 <div class="ecom-collection__filters-group--header">
 {%- assign max_price_amount = filter.range_max | money -%}
 <span class="ecom-collection__filters-group--selected">Max price {{max_price_amount}}</span>
 <a href="{{ filter.url_to_remove }}" class="ecom-collection__filters-group-reset-filter" >Reset</a>
 </div>
 {%- endcomment -%}
 {%- if false-%}
 {%- assign max_price_for_step = filter.range_max -%}
 {% assign price_step = 0 | times: 100 %}
 {%- assign checked = "" -%}
 {%- assign filter_max_value = filter.max_value.value -%}
 {% assign loop_time = max_price_for_step | divided_by: price_step %}
 {% if loop_time < 1 %}
 {%- assign loop_time = 1 -%}
 {%- endif -%}
 {% if loop_time > 50 %}
 {% if EComBuilderMode %}<span class="ecom-collection__filters-notice-label">Notice: Too many options, please enter a value greater than.</span>{%- endif -%}
 {%- else -%}
 <div class="ecom-collection-filters--price-step">
 <ul class ="ecom-collection__filters-group-list ecom-scroll_bar">
 {% for i in (0..loop_time) %}
 {% if i < loop_time %}
 {% assign price_range = i | times: price_step %}
 {% assign price_range_step = price_range | plus: price_step %}
 <li class="ecom-collection__filters-group-list-item">
 <label class = "ecom-collection__filters-group-radio ecom-flex ecom-al_center" >
 {% if price_range_step < max_price_for_step %}
 <input class="ecom-collection__filters-group-radio--input" type="radio" name="{{ filter.min_value.param_name}}" value="{{price_range | money_without_currency}}" {% if price_range_step == filter_max_value %}checked{% endif %}>
 <input class="ecom-collection__filters-radio--input-hidden" type="radio" name="{{ filter.max_value.param_name}}" value="{{price_range_step | money_without_currency}}">
 <span class="ecom-collection__filters-group-radio-label" ecom-flex ecom-al_center>{{price_range| money}} <span class="ecom-collection-filters--seperate">-</span> {{price_range_step| money}}</span>
 {%- else -%}
 {%- assign price_range_step = max_price_for_step %}
 <input class="ecom-collection__filters-group-radio--input" type="radio" name="{{ filter.min_value.param_name}}" value="{{price_range | money_without_currency}}" {% if price_range_step == filter_max_value %}checked{% endif %}>
 <input class="ecom-collection__filters-radio--input-hidden" type="radio" name="{{ filter.max_value.param_name}}" value="{{price_range_step | money_without_currency}}">
 <span class="ecom-collection__filters-group-radio-label" ecom-flex ecom-al_center>{{price_range| money}} <span class="ecom-collection-filters--seperate">-</span> {{price_range_step| money}}</span>
 {% endif %}
 </label>
 </li>
 {% endif %}
 {% if i == loop_time and price_range < max_price_for_step %}
 {% assign price_range = i | times: price_step %}
 {% if price_range < max_price_for_step %}
 <li class="ecom-collection__filters-group-list-item">
 <label class = "ecom-collection__filters-group-radio ecom-flex ecom-al_center" >
 <input class="ecom-collection__filters-group-radio--input" type="radio" name="{{filter.min_value.param_name}}" value="{{price_range | money_without_currency}}" {% if max_price_for_step == filter_max_value %}checked{% endif %}>
 <input class="ecom-collection__filters-radio--input-hidden" type="radio" name="{{ filter.max_value.param_name}}" value="{{max_price_for_step | money_without_currency}}">
 <span class="ecom-collection__filters-group-radio-label" ecom-flex ecom-al_center>{{price_range | money}} <span class="ecom-collection-filters--seperate">-</span> {{max_price_for_step | money}}</span>
 </label>
 </li>
 {% endif %}
 {% endif %}
 {% endfor %}
 </ul>
 </div>
 {%- endif -%}
 {%- elsif false -%}
 {%- assign max_price_for_step = filter.range_max -%}
 {% assign segment_step = 0 | floor
 %}
 {%- assign price_step = max_price_for_step |times: 1.00 | divided_by: segment_step -%}
 {%- assign checked = "" -%}
 {%- assign filter_max_value = filter.max_value.value -%}
 {% assign loop_time = segment_step %}
 <div class="ecom-collection-filters--price-step">
 <ul class ="ecom-collection__filters-group-list ecom-scroll_bar">
 {% for i in (0..loop_time) %}
 {% if i < loop_time %}
 {% assign price_range = i | times: price_step %}
 {% assign price_range_step = price_range | plus: price_step | round %}
 <li class="ecom-collection__filters-group-list-item">
 <label class = "ecom-collection__filters-group-radio ecom-flex ecom-al_center" >
 <input class="ecom-collection__filters-group-radio--input" type="radio" name="{{ filter.min_value.param_name}}" value="{{price_range | money_without_currency}}" {% if price_range_step == filter_max_value %}checked{% endif %}>
 <input class="ecom-collection__filters-radio--input-hidden" type="radio" name="{{ filter.max_value.param_name}}" value="{{price_range_step | money_without_currency}}">
 <span class="ecom-collection__filters-group-radio-label" ecom-flex ecom-al_center>{{price_range| money}} <span class="ecom-collection-filters--seperate">-</span> {{price_range_step| money}}</span>
 </label>
 </li>
 {% endif %}
 {% endfor %}
 </ul>
 </div>
 {%- else -%}
 <price-range class="ecom-collection__filters-group-price">
 <div class="ecom-collection__filters-group-field">
 <label class="ecom-collection__filters-group-field--label" for="Search-In-Modal">From</label>
 <span class="ecom-collection__filters-group-field--currency">{{ cart.currency.symbol }}</span>
 <input class="ecom-collection__filters-group-field--input ecom-collection__filters-price-range-min"
 name="{{ filter.min_value.param_name }}"
 id="ecom-filter-{{ filter.label | escape | strip }}-{{ forloop.index }}"
 {%- if filter.min_value.value -%}
 {%- if uses_comma_decimals -%}
 value="{{ filter.min_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
 {%- else -%}
 value="{{ filter.min_value.value | money_without_currency | replace: ',', '' }}"
 {% endif %}
 {%- endif -%}
 type="number"
 placeholder="0"
 min="0"
 max="{{ filter.range_max | divided_by: 100.00}}">
 </input>
 </div>
 <div class="ecom-collection__filters-group-field">
 <label class="ecom-collection__filters-group-field--label" for="Search-In-Modal">To</label>
 <span class="ecom-collection__filters-group-field--currency">{{ cart.currency.symbol }}</span>
 <input class="ecom-collection__filters-group-field--input ecom-collection__filters-price-range-max"
 name="{{ filter.max_value.param_name }}"
 id="ecom-Filter-{{ filter.label | escape }}-{{ forloop.index }}"
 {%- if filter.max_value.value -%}
 {%- if uses_comma_decimals -%}
 value="{{ filter.max_value.value | money_without_currency | replace: '.', '' | replace: ',', '.' }}"
 {%- else -%}
 value="{{ filter.max_value.value | money_without_currency | replace: ',', '' }}"
 {% endif %}
 {%- endif -%}
 type="number"
 placeholder="{{ filter.range_max | money_without_currency | replace: ',', '' }}"
 min="0"
 max="{{ filter.range_max | divided_by: 100.00 }}">
 </input>
 </div>
 </price-range>
 <div class="ecom-collection-filters--price-range">
 <div class="ecom-collection-filters--prices">
 {%- assign max_price = filter.max_value.value -%}
 {%- unless max_price -%}
 {%- assign max_price = filter.range_max -%}
 {%- endunless -%}
 {%- assign min_price = filter.min_value.value -%}
 {%- unless min_price -%}
 {%- assign min_price = 0 -%}
 {%- endunless -%}
 <span id="ecom-collection-filters--price-from" class="ecom-collection-filters--price">{{ min_price | money }}</span>
 <span class="ecom-collection-filters--seperate">-</span>
 <span id="ecom-collection-filters--price-to" class="ecom-collection-filters--price">{{max_price | money }}</span>
 </div>
 {%- assign per_min = 0 -%}
 {%- assign per_max = 100 -%}
 {%- if filter.min_value.value -%}
 {%- assign per_min = filter.min_value.value | times: 1.00 | divided_by: filter.range_max | times: 100 -%}
 {%- endif -%}
 {%- if filter.max_value.value -%}
 {%- assign per_max = filter.max_value.value | times: 1.00 | divided_by: filter.range_max | times: 100 -%}
 {%- endif -%}
 <div class="ecom-collection-filters--multi-range">
 <input id="ecom-collection-filters--input-min" type="range" min="0" max="100" value="{{per_min}}" step="0.01" />
 <input id="ecom-collection-filters--input-max" type="range" min="0" max="100" value="{{per_max}}" step="0.01" />
 </div>
 </div>
 {%- endif -%}
 </div>
 </div>
 {% endcase %}
 {%- endfor -%}
 </div>
 </div>
 </div>
 
 </form></div></div></div></div></div></div> </div></div></div></div> <div class="ecom-column ecom-core core__column--last ecom-ibkte42j5g"><div class="core__column--wrapper ecom__column-full-height"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-p56rj9zscmq" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__sorting" deep="2"><div class="ecom-collection__sorting-wrapper" data-page="collection"><div class="ecom-collection__sorting-container"><div class="ecom-collection__filters-heading"> </div><select name="sort_by" class="ecom-collection__sorting-select" aria-describedby="a11y-refresh-page-message">
 {% if request.page_type == 'search' %}
 {% assign results = search %}
 {% else %}
 {% assign results = collection %}
 {% endif %}
 {%- assign sort_by = results.sort_by | default: results.default_sort_by -%}
 {%- for option in results.sort_options -%}
 <option value="{{ option.value | escape }}"{% if option.value == sort_by %} selected="selected"{% endif %}>{{ option.name | escape }}</option>
 {%- endfor -%}
 </select></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-udd2l26wals" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__product" deep="1"><div class="ecom-collection__product-wrapper"><div class="ecom-collection__product-container ecom-collection__product-container_collection"><div class="ecom-collection__product-main ecom-collection_product_template_collection" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1},&quot;768&quot;:{&quot;slidesPerView&quot;:3},&quot;1025&quot;:{&quot;slidesPerView&quot;:4}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:0,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:200,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;},&quot;spaceBetween&quot;:10}" data-pagination="default" data-week="[%-W] week%!W" data-day="[%-d] day%!D" data-hour="[%-H] hr%!H" data-minute="[%-M] min%!M" data-second="[%-S] sec%!S" data-sale="-{{sale}}%" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{-review_platform-}}" data-countdown-shows="day,hour,minute,second" data-translate="false">
 {%- capture badge_tags -%}Hot{%- endcapture -%}
 {%- liquid
 assign colors = shop.metafields.ecomposer.colors
 assign badge_tags = badge_tags | strip | split: ','
 assign tmp_collection = collection
 assign tmp_product = product
 assign enable_hook = shop.metafields.ecomposer.enable_hook.value
 -%}
 
 
 {%- capture limit-%}12{% endcapture %}
 {%- if limit == blank -%}
 {% assign limit = 12 %}
 {% else %}
 {% assign limit = limit | plus: 0 %}
 {%- endif-%}
 
 {%- if collection != blank -%}
 {%- paginate collection.products by limit -%}
 {% assign products = collection.products %}
 {% assign is_collection = true %}
 
 {% if false and collection == product %}
 {% if true %}
 {% assign products = blank %}
 {% endif %}
 {%- if recommendations.performed? and recommendations.products_count > 0 -%}
 {% assign products = recommendations.products %}
 {%- endif -%}
 {% endif %}
 {% capture quickshop_layout%}lite{% endcapture %}
 {% capture product_style%}vertical{% endcapture%}
 {%- assign view_more_only = undefined -%}
 {% capture product_layout %}grid{% endcapture %}
 {% assign check_min = 0%}
 {% if products and products.size > check_min and products != blank %}
 <div class="
 ecom-collection__product--wrapper-items ecom-collection-product__layout-grid
 "
 data-grid-column="4"
 data-grid-column-tablet="3"
 data-grid-column-mobile="1"
 
 
 >
 {% assign ecom_count = 0 %}
 {% for p in products %}
 {% assign product = p %}
 {% if p.handle %}
 {% assign product = p %}
 {% else %}
 {% assign product = all_products[p] %}
 {% endif %}
 {% if product.url == blank %} {% continue %} {% endif %}
 {% if ecom_count >= limit %}{% break %}{% endif %}
 
 
 {% assign ecom_count = ecom_count | plus: 1 %}
 {%- capture swatch_option -%}Color{%- endcapture -%}
 {% assign hide_quickshop = true %}
 {% assign other_option_layout = 'dropdown' %}
 {% capture product_picker%}
 
 {%- if product.has_only_default_variant == false-%}
 {% if quickshop_layout != 'full' and product.options.size > 1%}
 {% assign hide_quickshop = false %}
 {% endif %}
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <div class="ecom-collection__product-variants" data-picker-type="image">
 <button class="ecom-collection__product-close"></button>
 <form class="ecom-collection__product-form ecom-product-form" product_id="{{product.id}}" data-product_id="{{product.id}}" data-product-id="{{product.id}}" data-handle="{{product.handle}}">
 <div class="ecom-child-element" >
 {% assign variant_selected = product.first_available_variant%}
 
 {%- capture swatch_option_temp -%}Color{%- endcapture -%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {% assign swatch_option = swatch_option | split: ',' %}
 {% assign option_index = current_option.position | minus: 1 %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-main ecom-collection__product-picker-option-{{current_option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-collection__product-swatch-item ecom-collection__product-picker-images-item {%comment%}{% if option_value == variant_selected[index] %}ecom-product-swatch-item--active ecom-button-active{% endif %}{%endcomment%}" data-value="{{ option_value | escape }}">
 <span class="ecom-collection__product-swatch-item--wrapper"></span>
 <img src="{{ variant | img_url: "120x120", crop: 'center' }}" alt=" {{ option_value }}" loading='lazy'/>
 </li>
 {%- endfor -%}
 </ul>
 
 </div>
 {% endif %}
 {% endfor %}
 {%- if other_option_layout != 'hide' -%}
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}{% continue%}{%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-other ecom-collection__product-picker-option-{{option.name | handleize }}">
 
 
 <select class="ecom-collection__product-swatch-select ecom-collection__product-picker-dropdown-list" data-option-index="{{ option_index }}">
 {% for value in option.values %}
 <option value="{{value | escape }}" {% if value == variant_selected[index] %}selected="selected"{% endif %}>
 {{value}}
 </option>
 {% endfor %}
 </select>
 
 </div>
 {%- endfor -%}
 </div>
 {%- endif -%}
 
 
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 <div class="ecom-collection__product-picker-selection" style="display:none;">
 <select name="variant_id" data-product-id="{{product.id}}" id="ecom-variant-selector-{{product.id}}-ecom-udd2l26wals">
 {% for variant in product.variants %}
 <option value="{{variant.id}}">{{variant.title}}</option>
 {% endfor %}
 </select>
 </div>
 </div>
 </div>
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 {%- else -%}
 <div class="ecom-collection__product-quick-shop-add-to-cart-wrapper {% if hide_quickshop or view_more_only %} ecom-collection__product-quick-shop--force-show{% endif %} ">
 <div class="ecom-collection__product-form__actions ">
 
 <button type="button" class="ecom-child-element ecom-collection__product-submit ecom-ajax-cart-submit ecom-collection__product-simple-add-to-cart
 ecom-collection__product-add-cart-icon-before"
 data-text-add-cart="Add to cart"
 data-text-unavailable="Unavailable"
 data-text-sold-out="outstock"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 data-text-pre-order="Pre order"
 
 >
 
 <span class="ecom-add-to-cart-text">
 Add to cart
 </span>
 </button>
 </div>
 
 </div>
 {%- endif -%}
 </form> {% comment %} End form {% endcomment %}
 <script class="product-json" type="application/json">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% endif %}
 
 {% endcapture%}
 {% capture product_actions%}
 <div class="ecom-collection__product--actions" data-layout="{{quickshop_layout}}">
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions "
 
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% if view_more_only %}
 
 {% elsif product.has_only_default_variant == true and product.available == false %}
 
 {% elsif product.has_only_default_variant %}
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 {%- else -%}
 
 
 <a data-no-instant href="{{ecom_root_url}}cart/add?id={{ product.variants.first.id }}&quantity=1"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 class="ecom-collection__product-submit ecom-collection__product-form__actions--add ecom-collection__product-simple-add-to-cart ecom-ajax-cart-simple ecom-collection__product-add-cart-icon-before ecom-child-element" 
 data-id="{{ product.variants.first.id }}"
 data-handle="{{ product.handle }}" data-pid="{{ product.id }}" title="{{ product.title | escape }}">
 
 <span class="ecom-add-to-cart-text">
 {%- if product.variants.first.inventory_management and product.variants.first.inventory_quantity <= 0 and product.variants.first.inventory_policy == 'continue' -%}
 Pre order
 {%-else-%}
 Add to cart
 {%- endif -%}
 </span>
 </a>
 
 {%- endif -%}
 {% else %}
 
 
 
 {% endif %}
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 </div>
 {% endcapture %}
 <div class="ecom-collection__product-item " data-product-handle="{{product.handle}}" data-style="{{product_style}}"{% unless product.has_only_default_variant %} ec-variant-init{% endunless %}>
 <div
 class="ecom-collection__product-item--wrapper {% if product_style == 'horizontal'%} ecom-d-flex {% else %} ecom-flex-column {% endif %}">
 <div class="ecom-collection__product-media-wrapper ecom-enable-hover--mobile {% if product_style == 'horizontal'%} ecom-d-flex{% endif %} "
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <a href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}" target="" title="{{product.title | escape }}" class="ecom-collection__product-item--inner ecom-image-default">
 {%- if product.featured_media -%}
 {%- liquid
 assign featured_media_aspect_ratio = product.featured_media.aspect_ratio
 if product.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <div class="ecom-collection__product-media--container">
 <div
 class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt
 {% if product.media[1] != nil %}ecom-collection__product-media--hover-effect{% endif %}"
 style="padding-bottom: {{ 1 | divided_by: featured_media_aspect_ratio | times: 100 }}%;"
 
 >
 <img srcset="{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.featured_media | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.featured_media.alt | escape }}"
 loading='lazy'
 class="ecom-collection__product-media-image"
 width="{{ product.featured_media.width }}"
 height="{{ product.featured_media.height }}"
 />
 
 {%- if product.media[1] != nil -%}
 <img srcset="{%- if product.media[1].width >= 533 -%}{{ product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.media[1].width >= 720 -%}{{ product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.media[1].width >= 940 -%}{{ product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.media[1].width >= 1066 -%}{{ product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.media[1] | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.media[1].alt | escape }}"
 loading='lazy'
 class="ecom-collection__product-secondary-media"
 width="{{ product.media[1].width }}"
 height="{{ product.media[1].height }}"
 />
 {%- endif -%}
 
 </div>
 </div>
 {% else %}
 <div class="ecom-collection__product-media--container">
 <div class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt"
 
 style="padding-bottom: 85%;"
 >
 {{ 'product-1' | placeholder_svg_tag: 'ecom-colection__product-svg-placeholder' }}
 </div>
 </div>
 {% endif %}
 
 <div class="ecom-collection__product-badge">
 
 
 <span class="ecom-collection__product-badge--sold-out" aria-hidden="true" style="{%- if product.available == true -%}display: none; {%- endif -%}">
 Sold
 </span>
 <span class="ecom-collection__product-badge--sale" aria-hidden="true" style="{%- unless product.compare_at_price > product.price and product.available -%}display: none;{%- endunless -%}">
 Sale
 </span>
 {% if badge_tags %}
 {% for badge in badge_tags %}
 {% for original_tag in product.tags %}
 {% assign tag = original_tag | replace: ' ', ' ' %}
 {% if tag == badge %}
 <span class="ecom-collection__product-badge--custom ecom-collection__product-badge--{{ badge | handleize}}" aria-hidden="true">
 {{ badge }}
 </span>
 {% endif %}
 {% endfor %}
 {%- assign bad = badge | strip -%}
 {% endfor %}
 {% endif %}
 
 {%- if product.has_only_default_variant -%}
 {%- if product.compare_at_price != null and product.compare_at_price > product.price -%}
 {%- assign sale = product.compare_at_price | minus: product.price | times: 100.0 | divided_by: product.compare_at_price | round -%}
 <span class="ecom-collection__product-price--bage-sale">
 -{{sale}}%
 </span>
 {%- endif -%}
 {%- else -%}
 {%- if product.selected_or_first_available_variant.compare_at_price != null and product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
 {%- assign sale = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | times: 100.0 | divided_by: product.selected_or_first_available_variant.compare_at_price | round -%}
 {%else%}
 {%- assign sale = null %}
 {%- endif -%}
 <span class="ecom-collection__product-price--bage-sale" style="{% unless sale %}display:none{% endunless %}">
 -{{sale}}%
 </span>
 {%- endif -%}
 </div>
 </a>
 
 <div class="ecom-collection__product-group-button-action " style="justify-content: start">
 
 
 </div>
 </div>
 <div class="ecom-collection__product-item--information">
 <div class="ecom-collection__product-item--information-wrapper ecom-flex ecom-column">
 {% if product_style == 'absolute' %}
 {{product_actions}}
 {{ product_picker}}
 {% endif %}
 
 
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <h3
 class="ecom-collection__product-title-tag {% if review_platform and review_platform == 'vital-reviews' %}card__heading{% endif %} ecom-child-element"
 
 >
 <a
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{product.title | escape }}"
 target=""
 class="ecom-collection__product-item-information-title "
 >
 {{ product.title | strip_html}}
 </a>
 </h3>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 
 <div
 class="ecom-collection__product-rating-wrapper ecom-child-element"
 
 data-rating-platform="{{review_platform}}"
 >
 {%- if review_platform -%}
 {%- case review_platform -%}
 {%- when 'none' -%}
 {%- when 'ali-reviews' -%}
 <div product-id="{{ product.id }}" product-handle="{{ product.handle }}" class="alireviews-review-star-rating"></div>
 {%- when 'opinew-reviews' -%}
 <div class='opinew-stars-plugin-product-list'>{% render 'opinew_review_stars_lists' product:product %}</div>
 {%- when 'judgeme' -%}
 <div style='{{ jm_style }}' class='jdgm-widget jdgm-preview-badge' data-id='{{ product.id }}'>
 {{ product.metafields.judgeme.badge }}
 </div>
 {%- when 'product-reviews-addon' -%}
 <span class=" stamped-product-reviews-badge" data-product-sku="{{ product.handle }}" data-id="{{ product.id }}" style="display:block;">{{- product.metafields.stamped.badge -}}</span>
 {%- when 'areviews-aliexpress'-%}
 <div class="areviews_product_item areviews_stars{{ product.id }}" data-product-id="{{ product.id }}"></div>
 {%- when 'loox'-%}
 <div class="loox-rating" data-id="{{ product.id }}" data-rating="{{ product.metafields.loox.avg_rating }}" data-raters="{{ product.metafields.loox.num_reviews }}"></div>
 {% when 'ryviu'%}
 <div class="ryviu-collection">
 <ryviu-widget-total collection=1
 reviews_data="{{product.metafields.ryviu.product_reviews_info | escape }}"
 product_id="{{product.id}}" handle="{{product.handle}}">
 </ryviu-widget-total>
 </div>
 {%- when 'yotpo-social-reviews' -%}
 <div class="yotpo bottomLine" style="display:inline-block" data-product-id="{{ product.id }}"> </div>
 {%- when 'vital-reviews' -%}
 <div></div>
 {%- when 'aliexpress-reviews-importer'-%}
 <div class="shop-booster-content shop-booster-col-rat" id="shop-booster-pid-d-{{ product.id }}" ></div>
 {%- when 'rivyo-product-review'-%}
 <div class="wc_product_review_badge" data-handle="{{ product.handle }}" data-product_id="{{ product.id }}"></div>
 {%-when 'growave' -%}
 {% capture the_snippet_review_avg %}{% render 'ssw-widget-avg-rate-listing', product: product %}{% endcapture %}
 {% unless the_snippet_review_avg contains 'Liquid error' %}
 {{ the_snippet_review_avg }}
 {% endunless %}
 {%- when 'smart-aliexpress-reviews'-%}
 <div class="scm-reviews-rate" data-rate-version2= {{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo | json}}>
 </div>
 {%- when 'photo-reviews' -%}
 <div class='opinew-stars-plugin-product-list'>{% render 'opinew_review_stars_lists' product:product %}</div>
 {%- when 'product-reviews' -%}
 <span class="shopify-product-reviews-badge" data-id="{{ product.id }}"></span>
 {%- when 'lai-reviews' -%}
 {%- if EComBuilderMode -%}
 <div class="ecom-placeholder-on-builder-mode" data-ecom-placeholder="Lai star rating"></div>
 {%- endif -%}
 <div class="scm-reviews-rate" data-rate-version2="{{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo | json | escape }}" data-product-id="{{ product.id }}"></div>
 {%- when 'sealapps-product-review' -%}
 <div class="ecom-star-rating-sealapp" product-id="{{ product.id }}"></div>
 {%- when 'rivyo' -%}
 <div class="wc_product_review_badge" data-handle="{{ product.handle }}" data-product_id="{{ product.id }}"></div>
 {%- when 'klaviyo-reviews' -%}
 <div class="klaviyo-star-rating-widget" data-id="{{product.id}}" data-product-title="{{product.title}}" data-product-type="{{product.type}}"></div>
 {%- when 'air-reviews' -%}
 <div class="AirReviews-Widget AirReviews-Widget--Stars" data-review-avg="{{ product.metafields.air_reviews_product.review_avg }}" data-review-count="{{ product.metafields.air_reviews_product.review_count }}"></div>
 {%- when 'ait-product-reviews' -%}
 <span class="egg-product-reviews-rating" data-id="{{ product.id }}" id="{{ product.id }}"></span>
 {%- when 'trustify-reviews' -%}
 <div
 class="trustify-review-stars-collection"
 data-review-type="collection"
 data-review-avg="{{ product.metafields.tr_reviews_product.review_avg }}"
 data-review-count="{{ product.metafields.tr_reviews_product.review_count }}"
 >
 </div>
 {% else %}
 <p>The rating platform not supported</p>
 {%-endcase-%}
 {%- else -%}
 <p>Please select the rating platform in settings</p>
 {%- endif -%}
 </div>
 
 
 {% assign showPriceWhenNotLogin = true %}
 {% if undefined and customer == blank%}
 {% assign showPriceWhenNotLogin = false %}
 {% endif %}
 {% if showPriceWhenNotLogin == false and undefined %}
 {% assign showOnLive = true %}
 {% else %}
 {% assign showOnLive = false %}
 {% endif %}
 {% assign showPreview = false %}
 {% if showOnLive or showPreview %}
 <div class="ecom-collection__product-login-to-see">
 undefined
 </div>
 {% endif %}
 {% if true and showPriceWhenNotLogin %}
 <div class="ecom-collection__product-prices ecom-child-element" >
 {% capture ground_price %}
 
 {% endcapture %}
 {%- assign target = product.selected_or_first_available_variant -%}
 
 <div class="ecom-collection__product-price-wrapper">
 <span
 class="ecom-collection__product-price--regular ecom-collection__product--compare-at-price"
 {%- if product.compare_at_price == nil or product.compare_at_price <= product.price -%} style="display:none" {% endif %}
 >
 {% if settings.currency_code_enabled == true %} {{ target.compare_at_price | money_with_currency }} {% else %} {{ target.compare_at_price | money }} {% endif %}
 </span>
 <span class="ecom-collection__product-price{% if product.compare_at_price > product.price %} ecom-collection__product-price--sale{% endif %}">{% if settings.currency_code_enabled == true %} {{target.price | money_with_currency }} {% else %} {{target.price | money }} {% endif %}</span>
 </div>
 {{ ground_price }}
 
 </div>
 {% endif %}
 
 {% if product_style != 'absolute'%}
 {{ product_picker }}
 {% endif %}
 {% if product_style == 'horizontal' or product_style == 'vertical' or product_layout == 'list' %}
 {{product_actions}}
 {% endif %}
 
 {% if product.available and product.metafields.ecomposer.countdown %}
 <div
 class="ecom-collection__product-countdown ecom-child-element" 
 >
 <div
 class="ecom-collection__product-countdown-wrapper"
 >
 <div class="ecom-collection__product-countdown-wrapper--title">Hurry up! The sale will end on</div>
 <div class="ecom-product-single__countdown-container" >
 {%- assign countdown_from = product.metafields.ecomposer.countdown_from -%}
 <div data-product-id="{{product.id}}" class="ecom-collection__product-countdown-time ecom-collection__product-countdown-time--metafields" data-ecom-countdown-from="{{ countdown_from }}" data-ecom-countdown="{{product.metafields.ecomposer.countdown}}"></div>
 </div>
 
 {% if countdown_from%}
 <div class="ecom-collection__product-countdown-progress-bar">
 <div class="ecom-collection__product-countdown-progress-bar--wrap">
 <div class="ecom-collection__product-countdown-progress-bar--timer ecom-product-single__countdown-progress-bar--timer"></div>
 </div>
 </div>
 {% endif %}
 
 </div>
 </div>
 {% endif %}
 
 </div>
 </div>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 {% endfor %}
 </div>
 {% else %}
 <div class="ecom-collection__product--wrapper-items ecom-collection__product--no-item ecom-collection-product__layout-grid" >
 <div class="ecom-collection__product-item" >
 There are no product yet!
 </div>
 </div>
 {% endif %}
 
 
 
 {%- if paginate.pages > 1 -%}
 
 
 <nav role="navigation" ecom-child-element" 
 <ol class="ecom-pagination-navigation ecom-collection__pagination-navigation">
 {%- if paginate.previous -%}
 <li class="ecom-prev" style="">
 <a class="ecom-pagination-item ecom-paginate-action" href="{{ paginate.previous.url }}">
 <span class="ecom-paginate-action--icon"><svg width="15" height="11" viewBox="0 0 15 11" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M8.25 10.5V6H15V4.5H8.25V0L0 5.25L8.25 10.5ZM6.75 7.7625L2.79375 5.25L6.75 2.7375V7.7625Z"></path></svg></span>
 
 </a>
 </li>
 {%- else -%}
 <li class="ecom-pagination-item ecom-prev ecom-paginate-action disabled" style="">
 <span class="ecom-paginate-action--icon"><svg width="15" height="11" viewBox="0 0 15 11" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M8.25 10.5V6H15V4.5H8.25V0L0 5.25L8.25 10.5ZM6.75 7.7625L2.79375 5.25L6.75 2.7375V7.7625Z"></path></svg></span>
 
 </li>
 {%- endif -%}
 {%- for part in paginate.parts -%}
 {%- if part.is_link -%}
 <li>
 <a class="ecom-pagination-item" href="{{ part.url }}" title="{{ part.title }}">
 {{ part.title }}
 </a>
 </li>
 {%- else -%}
 {%- if part.title == paginate.current_page -%}
 <li class="ecom-pagination-item ecom-button-active" aria-current="page">
 {{ part.title }}
 </li>
 {%- else -%}
 <li class="ecom-pagination-item">
 {{ part.title }}
 </li>
 {%- endif -%}
 {%- endif -%}
 {%- endfor -%}
 {%- if paginate.next -%}
 <li class="ecom-next" style="">
 <a class="ecom-paginate-action ecom-pagination-item" href="{{ paginate.next.url }}">
 
 <span class="ecom-paginate-action--icon"><svg width="15" height="11" viewBox="0 0 15 11" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M6.75 10.5V6H0V4.5H6.75V0L15 5.25L6.75 10.5ZM8.25 7.7625L12.2062 5.25L8.25 2.7375V7.7625Z"></path></svg></span>
 </a>
 </li>
 {%- else -%}
 <li class="ecom-pagination-item ecom-next ecom-paginate-action ecom-collection__pagination--disabled" style="">
 
 <span class="ecom-paginate-action--icon"><svg width="15" height="11" viewBox="0 0 15 11" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M6.75 10.5V6H0V4.5H6.75V0L15 5.25L6.75 10.5ZM8.25 7.7625L12.2062 5.25L8.25 2.7375V7.7625Z"></path></svg></span>
 </li>
 {%- endif -%}
 </ol>
 </nav>
 
 {%- endif -%}
 
 {% endpaginate %}
 {% endif %}
 
 {% assign collection = tmp_collection %}
 {% assign product = tmp_product %}
 </div></div><div class="ecom-collection__product-loading ecom-dn"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="48px" height="48px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" style="margin: auto; background: none; display: block; shape-rendering: auto;"><path d="M10 50A40 40 0 0 0 90 50A40 42 0 0 1 10 50" fill="#0a0a0a" stroke="none"><animateTransform attributeName="transform" type="rotate" dur="0.5434782608695652s" repeatCount="indefinite" keyTimes="0;1" values="0 50 51;360 50 51"></animateTransform></path></svg></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
 
</div></div>
{% schema %}
{
 "name": "Entryway",
 "locales": {},
 "settings": [
 {
 "type": "header",
 "content": "The section was generated by [Ecomposer](https:\/\/ecomposer.io).",
 "info": "\n EComposer is a Visual Page Builder app on Shopify AppStore.\n It provides 100+ pre-built templates in library,\n so you can build unlimited pages that you can imagine with it.\n Please don't add Shopify sections to EComposer's page in the theme customize. If you do this, Shopify sections will be deleted when you republish your page in EComposer\n "
 },
 {
 "type": "header",
 "content": "[EDIT WITH EComposer APP \u2192](https:\/\/ecomposer.app\/shop?open_builder=1&page=collection&id=67aede3aadf6b7e6320379fb&utm_source=theme-customize)",
 "info": "(*You must install the app to start editing this section [Learn more](https:\/\/help.ecomposer.io\/docs\/getting-started\/installation\/))"
 }
 ]
}
{% endschema %}