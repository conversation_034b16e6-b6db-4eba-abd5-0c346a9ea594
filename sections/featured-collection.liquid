{%- render 'section-featured-collection' -%}

{% schema %}
{
  "name": "t:labels.featured_collection",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:labels.heading",
      "default": "Featured collection"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:labels.heading_size",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "t:labels.sizes.small"
        },
        {
          "value": "h2",
          "label": "t:labels.sizes.medium"
        },
        {
          "value": "h1",
          "label": "t:labels.sizes.large"
        },
        {
          "value": "h0",
          "label": "t:labels.sizes.extra_large"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_position",
      "label": "t:labels.heading_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "center",
          "label": "t:labels.alignments.center"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    },
    {
      "type": "collection",
      "id": "home_featured_products",
      "label": "t:labels.collection"
    },
    {
      "type": "range",
      "id": "count",
      "label": "t:labels.products",
      "default": 6,
      "min": 5,
      "max": 15,
      "step": 1
    },
    {
      "type": "range",
      "id": "products_per_row",
      "label": "t:labels.desktop_products_per",
      "default": 3,
      "min": 2,
      "max": 5,
      "step": 1
    },
    {
      "type": "checkbox",
      "id": "mobile_scrollable",
      "label": "t:actions.enable_swipe_on",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "view_all",
      "label": "t:actions.show_view_all_link",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:actions.show_section_divider",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:labels.featured_collection"
    }
  ],
  "blocks": [],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
