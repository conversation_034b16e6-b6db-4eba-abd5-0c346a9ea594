
{%- render 'section-giftcard-header' -%}

{% schema %}
{
  "name": "t:labels.header",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:labels.logo"
    },
    {
      "type": "range",
      "id": "desktop_logo_width",
      "label": "t:labels.desktop_logo_width",
      "default": 200,
      "min": 100,
      "max": 400,
      "step": 10,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "mobile_logo_width",
      "label": "t:labels.mobile_logo_width",
      "default": 140,
      "min": 60,
      "max": 200,
      "step": 10,
      "unit": "px",
      "info": "t:info.set_as_max_width"
    }
  ],
  "default": {
    "settings": {}
  },
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
