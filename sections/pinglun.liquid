
<script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"> </script>
<link rel="stylesheet" href="https://unpkg.com/swiper@8/swiper-bundle.min.css">    
{% comment %} {{ 'swiper-bundle.min.css' | asset_url | stylesheet_tag }}
<script src="{{ 'swiper-bundle.min.js' | asset_url }}" ></script> {% endcomment %}
<style>
    .pinglun-{{section.id}}{
        background: linear-gradient(90deg, #866E59 0.57%, #BAAEA5 101.43%);
        padding: 60px 0 90px;
    }
    .pinglun-{{section.id}} .header-top{
        margin-left: 100px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 54px;
        color: #fff; 
    }
    .pinglun-{{section.id}} .header-top h2{
        font-family: Playfair Display;
        font-weight: 400;
        font-style: Regular;
        font-size: 26px;
        leading-trim: NONE;
        line-height: 1.4;
        letter-spacing: 0px;
    }
    .pinglun-{{section.id}} .header-top a{
        color: #fff;
        margin-right: 261px;
        font-family: PingFang SC;
        font-weight: 600;
        font-style: Semibold;
        font-size: 16px;
        leading-trim: NONE;
        line-height: 1.4;
        letter-spacing: 0px;
        text-align: center;
        padding: 8px 40px; 
        border: 1px solid;
        border-radius: 30px; 
    }
    .pinglun-{{section.id}} .header-top a:hover{
        color: #6D4C41;
        background: #fff;
        border-color: #fff;
    }
    .pinglun-{{section.id}} .header-top a svg{
        display:none
    }
    .mySwiper-{{section.id}} {
        width: 100%;
        height: 100%;
        position: relative;
       

      }
      .pinglun-{{section.id}} .swiper-wrapper{
        padding-left:100px
    }
      .pingl-swiper {
        text-align: center;
        font-size: 18px;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        flex-direction: column;
        row-gap: 30px;
        padding: 30px 43px 36px 30px;
        background: #FCF7F2;
        {% comment %} flex:1; {% endcomment %}
      }
      .pingl-swiper .content-top{
        display: flex;
        column-gap: 20px;
      }
      .pingl-swiper .content-top .text-content{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
  
      .pingl-swiper .content-top img {
        display: block;
        width: 56px;
        height: 56px;
        object-fit: cover;
      }
      .pingl-swiper .content-top .text-name{
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .pingl-swiper .content-top .text-name span{
        font-family: PingFang SC;
        font-weight: 500;
        font-style: Medium;
        font-size: 18px;
        leading-trim: NONE;
        line-height: 1.4;
        letter-spacing: 0px;
    }
    .pingl-swiper .content-top .content-start{
        display: flex;
    }
    .pingl-swiper .content-content{
        width: 100%;
    }
    .pingl-swiper .content-content .content-hr svg{
        width: 100%;
    }
     
    .pingl-swiper .content-content p{
        font-family: PingFang SC;
        margin-bottom:34px;
        font-weight: 400;
        min-height:156px;
        font-style: Regular;
        font-size: 16px;
        leading-trim: NONE;
        line-height: 1.4;
        letter-spacing: 0px;
        text-align: left;
    }

    .pingl-swiper .content-product{
        justify-content: flex-start;
        align-items: center;
        display: flex;
        gap: 25px;
    }
    .pingl-swiper .content-product a{
        text-align: left;
        font-family: PingFang SC;
        font-weight: 400;
        font-style: Regular;
        font-size: 16px;
        leading-trim: NONE;
        line-height: 22px;
        letter-spacing: 0px;
        vertical-align: middle;

    }
    .product-p-image img{
        display: block;
        width: 100px;
        height: 100px;
        object-fit: cover;
    }
    .left-right{
        position: absolute;
        top: 47%;
        display: flex;
        justify-content: space-between;
        z-index: 9999;
        align-items: center;
    }
    .start_button{
        position: absolute;
        top: 45%;
        
    }
     .start_button:not(.swiper-button-disabled):hover svg circle{
        fill:#6D4C41
    }
     .start_button.left-button{
        left:140px;
        transform: rotate(180deg);
    } 
    .start_button.right-button{
       right:140px
    }
    .pinglun_content .start_button{
        z-index: 0;
    }
    .pinglun_content:hover .start_button{
        z-index: 99999;
    }
    @media only screen and (max-width: 768px)  {
        .pinglun-{{section.id}}{
            padding: 24px 0 30px;
        }
        .pinglun-{{section.id}} .header-top{
            flex-direction: column;
            align-content: flex-start;
            align-items: unset;
            margin-bottom: 20px;
        }
        .pinglun-{{section.id}} .header-top h2{
            font-size: 20px;
            margin-bottom: 10px;
           
        }
        .pinglun-{{section.id}} .header-top a{
            padding:0px;
            border:none;
            font-weight: 400;
            font-style: Regular;
            font-size: 14px;
           margin-right:unset;
           text-align: left;
           display: flex;
           gap: 6px;
        }
        .pinglun-{{section.id}} .header-top a svg{
            display:block
        }
        .pinglun-{{section.id}} .swiper-wrapper{
            padding-left:16px
        }
        .pinglun-{{section.id}} .header-top{
            margin-left:16px
        } 
        .pingl-swiper .content-top{
        gap:10px
        }
        .pingl-swiper .content-top img{
            width: 36px;
            height: 36px;
        }
        
        .pingl-swiper{
            padding: 16px 13px 19px 12px;
            gap:16px
        }
        .pingl-swiper .content-top .text-name span{
            font-size: 12px;
        }
        .pingl-swiper .content-top .text-name svg{
            width:12px;
            height:12px

        }
        .pingl-swiper .content-top .content-start svg{
            width:16px;
            height:16px
        }
        .pingl-swiper .content-product{
            gap:12px;
        
        }
        .pingl-swiper .content-content p{
            font-size: 12px;
            margin-bottom: 16px;
            line-height: 18px;
            height:162px
        }
        
        .product-p-image img{
            width:60px;
            height:58px
        }
        .pingl-swiper .content-product a{
            font-size: 12px;
           
            line-height: 16px;
            letter-spacing: 0px;
        }
    
    }

</style>

<div class="page-width page-content pinglun-{{section.id}}">
    <div class="header-top">
       {% if section.settings.title != blank%} <h2>{{section.settings.title}}</h2>{% endif%}
       {% if section.settings.title != blank%} 
       <a href="{{section.settings.link}}">
            <span>{{section.settings.button}}</span>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="10" cy="10" r="9.5" stroke="white"/>
                <path d="M14 10.0028H6" stroke="white" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M10 6L14 10L10 14" stroke="white" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
        </a>
    {% endif%}
    </div>
    <div class="pinglun_content">
        <div class="swiper mySwiper-{{section.id}}">
            <div class="swiper-wrapper">

                {% for block in section.blocks%}

                <div class="swiper-slide pingl-swiper">
                    <div class="content-top">
                        {% if block.settings.image != blank%}
                        <img src="{{block.settings.image | image_url}}" alt="{{block.settings.image.alt}}">
                        {% endif %}
                        <div class="text-content">
                            <div class="text-name">
                                {% if block.settings.is_true%}
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8 0C12.4308 0 16 3.56923 16 8C16 12.4308 12.4308 16 8 16C3.56923 16 0 12.4308 0 8C0 3.56923 3.56923 0 8 0ZM12 5.47656C11.7539 5.23069 11.2618 5.23077 11.0156 5.47656L7.26172 9.23047L5.10742 7.07715C4.86126 6.83102 4.36919 6.831 4.12305 7.07715C3.87703 7.32334 3.87696 7.8154 4.12305 8.06152L6.76953 10.708C6.89261 10.8309 7.07726 10.8926 7.26172 10.8926C7.44621 10.8925 7.63087 10.831 7.75391 10.708L12 6.46191C12.3077 6.21576 12.3077 5.72272 12 5.47656Z" fill="#7CD363"/>
                                    </svg>
                                {% endif %}
                                {% if block.settings.name != blank%}
                                <span>{{block.settings.name}}</span>   
                            {% endif %}                             
                            </div>
                            <div class="content-start">
                              
                                {%- if block.settings.icon == '5-stars' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                              {%- elsif block.settings.icon == '4-stars' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                              {%- elsif block.settings.icon == '3-stars' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                              {%- elsif block.settings.icon == '2-stars' -%}
                                    {%- render 'start' -%}
                                    {%- render 'start' -%}
                                   
                              {%- elsif block.settings.icon == '1-star' -%}
                                     {%- render 'start' -%}
                              {%- endif -%}
                                    
                            </div>
                        </div>
                    </div>
                    {%- if block.settings.content != blank  -%}
                    <div class="content-content">
                        <p>
                          {{block.settings.content}}
                        </p>
                        <div class="content-hr">
                            <svg width="416" height="1" viewBox="0 0 416 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line y1="0.5" x2="416" y2="0.5" stroke="#EEE2D8"/>
                            </svg></div>
                    </div>
                {%- endif -%}
                    {% if block.settings.product != blank%}
                    <div class="content-product">
                        <div class="product-p-image">
                            <a href="{{section.settings.product.url}}"><img src="{{block.settings.product.featured_image | image_url}}" alt="{{block.settings.product.featured_image.alt}}"></a>
                        </div>
                        <a href="{{section.settings.product.url}}">{{block.settings.product.title}}</a>
                    </div>
                {% endif %}
                </div>

                {% endfor%}
                <div class="swiper-slide pingl-swiper" style="background:none"></div>
                
            </div>
           
                <div class="left-button start_button">
                    <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="30" cy="30" r="30" fill="#6D4C4199" fill-opacity="0.85"/>
                        <path d="M37.5 30.0054H22.5" stroke="white" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M30 22.5L37.5 30L30 37.5" stroke="white" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="right-button start_button">
                    <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="30" cy="30" r="30" fill="#6D4C4199" fill-opacity="0.85"/>
                        <path d="M37.5 30.0054H22.5" stroke="white" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M30 22.5L37.5 30L30 37.5" stroke="white" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                
                </div>
           
        </div>
    </div>

</div>


<script>
    document.addEventListener("DOMContentLoaded", function () {
      new Swiper(".mySwiper-{{ section.id }}", {
         {% comment %} slidesPerView: 3.68151,
            spaceBetween: 40,  {% endcomment %}
            navigation: {
                nextEl: ".left-button",
                prevEl: ".right-button",
              },
        breakpoints: {
          // >= 1024px
          1024: {
            slidesPerView: 3.68151,
            spaceBetween: 40,
          
          },

          // >= 768px (平板)
          768: {
            slidesPerView: 2.5,
            spaceBetween: 25,
            navigation: {
                nextEl: ".left-button",
                prevEl: ".right-button",
              },
          },
          // < 768px (手机)
          0: {
            slidesPerView: 1.527,
            spaceBetween: 16,
          }
        } 
      });
    }); 
  </script>

 
{% schema %}
    {
      "name": "评论轮播", 
     
      "settings": [
        {
            "type": "text",
            "id": "title",
            "label": "标题",
            "default":"Reviews from Our Customers"
          },
          {
            "type": "text",
            "id": "button",
            "label": "按钮文本",
            "default":"Read More"
          },
          {
            "type": "url",
            "id": "link",
            "label": "按钮链接"
          },


      ],
      "blocks": [
    {
      "type": "pinlin",
      "name": "评论",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "img"
        },
        {
          "type": "checkbox",
          "id": "is_true",
          "label": "显示通过",
          "default":true
        },
        {
          "type": "text",
          "id": "name",
          "label": "名称"
        },
        {
            "type": "select",
            "id": "icon",
            "label": "t:labels.icon",
            "default": "5-stars",
            "options": [
              {
                "value": "none",
                "label": "t:labels.none"
              },
             
              {
                "value": "5-stars",
                "label": "t:labels.stars.5_stars"
              },
              {
                "value": "4-stars",
                "label": "t:labels.stars.4_stars"
              },
              {
                "value": "3-stars",
                "label": "t:labels.stars.3_stars"
              },
              {
                "value": "2-stars",
                "label": "t:labels.stars.2_stars"
              },
              {
                "value": "1-star",
                "label": "t:labels.stars.1_star"
              }
            ]
          },
        {
          "type": "textarea",
          "id": "content",
          "label": "文本"
        },
        {
          "type": "product",
          "id": "product",
          "label": "产品"
        }
        
      ]
    }
  ], 

      "presets": [
        {
          "name": "评论轮播"
        }
      ]
    }
  {% endschema %}