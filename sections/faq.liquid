{%- render 'section-faq' -%}

{% schema %}
{
  "name": "t:labels.faq",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:labels.heading",
      "default": "FAQs"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:labels.heading_size",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "t:labels.sizes.small"
        },
        {
          "value": "h2",
          "label": "t:labels.sizes.medium"
        },
        {
          "value": "h1",
          "label": "t:labels.sizes.large"
        },
        {
          "value": "h0",
          "label": "t:labels.sizes.extra_large"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_position",
      "label": "t:labels.heading_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "center",
          "label": "t:labels.alignments.center"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "rich-text",
      "name": "t:labels.rich_text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "t:labels.title",
          "default": "Rich text"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "t:labels.text",
          "default": "<p>Use this section for any descriptive text you need to fill out your pages or to add introductory headings between other blocks.</p>"
        },
        {
          "type": "select",
          "id": "align_text",
          "label": "t:labels.text_alignment",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:labels.alignments.left"
            },
            {
              "value": "center",
              "label": "t:labels.alignments.centered"
            },
            {
              "value": "right",
              "label": "t:labels.alignments.right"
            }
          ]
        }
      ]
    },
    {
      "type": "question",
      "name": "t:labels.question",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "t:labels.question",
          "default": "Frequently asked question"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "t:labels.text",
          "default": "<p>Use this text to answer questions in as much detail as possible for your customers.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:labels.faq",
      "blocks": [
        {
          "type": "question"
        },
        {
          "type": "question"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
