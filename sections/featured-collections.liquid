{%- render 'section-featured-collections' -%}

{% schema %}
{
  "name": "t:labels.collection_list",
  "class": "index-section",
  "max_blocks": 18,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:labels.heading",
      "default": "Popular categories"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:labels.heading_size",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "t:labels.sizes.small"
        },
        {
          "value": "h2",
          "label": "t:labels.sizes.medium"
        },
        {
          "value": "h1",
          "label": "t:labels.sizes.large"
        },
        {
          "value": "h0",
          "label": "t:labels.sizes.extra_large"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_position",
      "label": "t:labels.heading_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "center",
          "label": "t:labels.alignments.center"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:actions.show_section_divider",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "t:labels.collection",
      "settings": [
        {
          "id": "collection",
          "type": "collection",
          "label": "t:labels.collection",
          "info": "t:info.image_source_adjusted_theme_settings"
        },
        {
          "type": "text",
          "id": "title",
          "label": "t:labels.title",
          "info": "t:info.defaults_to_collection_title"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:labels.collection_list",
      "blocks": [
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
