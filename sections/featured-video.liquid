{%- render 'section-featured-video' -%}

{% schema %}
{
  "name": "t:labels.video",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:labels.heading"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:labels.video",
      "info": "t:info.overrides_video_url_if_both_set"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "label": "t:labels.video_url",
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
      "accept": ["youtube", "vimeo"]
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:actions.show_section_divider",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:labels.video"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
