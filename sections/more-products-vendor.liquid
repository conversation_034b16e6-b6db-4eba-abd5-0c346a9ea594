{%- render 'section-more-products-vendor' -%}

{% schema %}
{
  "name": "t:labels.more_from_vendor",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:labels.more_from_vendor"
    },
    {
      "type": "range",
      "id": "products_per_row",
      "label": "t:labels.desktop_products_per",
      "default": 3,
      "min": 2,
      "max": 5,
      "step": 1
    },
    {
      "type": "range",
      "id": "max_products",
      "label": "t:labels.maximum_products_to",
      "default": 5,
      "min": 2,
      "max": 10,
      "step": 1
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
