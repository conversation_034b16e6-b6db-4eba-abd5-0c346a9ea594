{% comment %}
 EComposer (https://ecomposer.io)
 You SHOULD NOT modify source code in this page because
 It is automatically generated from EComposer
 At 2025-05-29 10:36:56
 {% endcomment %} 
 
 {% assign ecom_root_url = routes.root_url %}
 
 {% if ecom_root_url != '/'%}
 {% assign ecom_root_url = routes.root_url | append: '/' %}
 {% endif %}
 <link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-simple_slider_css" href="https://cdn.ecomposer.app/vendors/css/ec_splide.min.css?ver=3.0" /><link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-modal_css" href="https://cdn.ecomposer.app/vendors/css/ecom_modal.css" /><link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.9" /><noscript><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-simple_slider_css" href="https://cdn.ecomposer.app/vendors/css/ec_splide.min.css?ver=3.0" /><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-modal_css" href="https://cdn.ecomposer.app/vendors/css/ecom_modal.css" /><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.9" /></noscript><script type="text/javascript" asyc="async" id="ecom-vendors-simple_slider_js" src="https://cdn.ecomposer.app/vendors/js/ec-splide.min.js?ver=3.0" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-modal_js" src="https://cdn.ecomposer.app/vendors/js/ecom_modal_new.js" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-zoom_js" src="https://cdn.ecomposer.app/vendors/js/ecom_zoom.js" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-light_galerry" src="https://cdn.ecomposer.app/vendors/js/ecom-light-galerry.js" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-shopify_option_selection_js" src="https://cdn.ecomposer.app/vendors/js/ecomposer_option_selection.js" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-countdown_js" src="https://cdn.ecomposer.app/vendors/js/ecom-countdown.js?ver=2.0" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-slider_js" src="https://cdn.ecomposer.app/vendors/js/<EMAIL>" ></script>
{%- assign headless = true -%}{%- render "ecom_theme_helper" -%}{%capture section_id %}ecom-preview-product-product{% endcapture%}{% if section and section_id == section.id and headless == true %}
{{ content_for_header }}
{% render 'ecom_header', ECOM_THEME: true %}{% endif %}<link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Roboto:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Jost:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Nunito:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Noto+Sans+TC:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Plus+Jakarta+Sans:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Inter:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Young+Serif:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/>
{{'ecom-preview-template-product.css' | asset_url | stylesheet_tag }}
<script src="{{'ecom-preview-template-product.js' | asset_url }}" defer="defer"></script>
<script type="text/javascript" class="ecom-page-info">
 window.EComposer = window.EComposer || {};
 window.EComposer.TEMPLATE_ID="preview-template-product";
 window.EComposer.TEMPLATE = {"template_id":"preview-template-product","title":"xiangqing","type":"product","slug":"ecom-preview-product","plan_id":4};
 </script>
<div class="ecom-builder" id="ecom-preview-product"><div class="ecom-sections" data-section-id="{{section.id}}">
 {% comment %}
 // Reactive
 * this.data.settings.collection
 {% endcomment %}
 {% liquid
 if request.page_type != 'collection' or collection == blank
 assign selected_collection = 'women'
 if selected_collection == blank or selected_collection == '' or collections[selected_collection] == blank
 assign collection = collections.all
 else
 assign collection = collections[selected_collection]
 if collection.id == blank
 assign collection = collections.all
 endif
 endif
 endif
 %}
 <section class="ecom-row ecom-core ecom-section ecom-arwo46l9sgs" data-id="ecom-arwo46l9sgs" style="z-index: inherit;"><div data-deep="1" class="core__row--columns core__row--full"><div class="ecom-column ecom-core ecom-2h87927t5ci"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-90yorf3g1dl" data-core-is="block"><div class="ecom-element ecom-base ecom-base__breadcrumbs--wrapper" deep="1">
 {%- if EComBuilderMode -%}
 {% capture page_type %}product{% endcapture %}
 {%- else -%}
 {%- assign page_type = request.page_type %}
 {%- endif %}
 {%- capture icon_parent -%}
 
 {%- endcapture -%}
 {%- capture icon_breadcrumbs -%}
 <span class="ecom-base__breadcrumbs--icon">
 <svg width="8" height="8" viewBox="0 0 8 8" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><circle cx="4" cy="4" r="4"></circle></svg>
 </span>
 
 {%- endcapture -%}
 {%- unless page_type == 'index' or page_type == 'list-collections' or page_type == '404' -%}
 <nav class="ecom-base__breadcrumbs ecom-flex" role="navigation" aria-label="breadcrumbs">
 <div class="ecom-base__breadcrumbs__list">
 {{ icon_parent }}<a class="ecom-base__breadcrumbs__link" href="#">Home</a>
 {%- case page_type -%}
 {%- when 'page' -%}
 {{ icon_breadcrumbs }}
 {%- if EComBuilderMode -%}
 <a class="ecom-base__breadcrumbs__link" href="#" aria-current="page">Untitled</a>
 {%- else -%}
 <a class="ecom-base__breadcrumbs__link" href="{{ page.url }}" aria-current="page">{{ page.title }}</a>
 {%- endif -%}
 {%- when 'cart' -%}
 {{ icon_breadcrumbs }}
 <a class="ecom-base__breadcrumbs__link" href="{{ routes.cart_url }}" aria-current="cart">cart</a>
 {%- when 'product' -%}
 {%- if collection.url -%}
 {{ icon_breadcrumbs }}{{ collection.title | link_to: collection.url }}
 
 {%- endif -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ product.url }}" aria-current="page">{{ product.title }}</a>
 {%- when 'collection' and collection.handle -%}
 {%- if current_tags -%}
 {{ icon_breadcrumbs }}{{ collection.title | link_to: collection.url }}
 {%- capture tag_url -%}{{ collection.url }}/{{ current_tags | join: "+"}}{%- endcapture -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: " + "}}</a>
 {%- else -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ collection.url }}" aria-current="page">{{ collection.title }}</a>
 {%- endif -%}
 {%- when 'blog' -%}
 {%- if current_tags -%}
 {{ icon_breadcrumbs }}{{ blog.title | link_to: blog.url }}
 {%- capture tag_url -%}{{blog.url}}/tagged/{{ current_tags | join: "+" }}{%- endcapture -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ tag_url }}" aria-current="page">{{ current_tags | join: " + " }}</a>
 {%- else -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ blog.url }}" aria-current="page">{{ blog.title }}</a>
 {%- endif -%}
 {%- when 'article' -%}
 {{ icon_breadcrumbs }}{{ blog.title | link_to: blog.url }}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ article.url }}" aria-current="page">{{ article.title }}</a>
 {%- else -%}
 {{ icon_breadcrumbs }}<a class="ecom-base__breadcrumbs__link" href="{{ request.path }}" aria-current="page">{{ page_title }}</a>
 {%- endcase -%}
 </div>
 </nav>
 {%- endunless -%}{%- assign page_type = request.page_type %}
 {%- if page_type == 'index' -%}
 {{ icon_parent }}
 {%- if EComBuilderMode -%}
 <a class="ecom-base__breadcrumbs__link" href="#" aria-current="index">Home</a>
 {%- else -%}
 <a class="ecom-base__breadcrumbs__link" href="/" aria-current="index">Home</a>
 {%- endif -%}
 {%- endif -%}
 </div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
 
 {% comment %}
 // Reactive
 * this.data.settings.product
 {% endcomment %}
 {% liquid
 unless recommendations.performed
 if request.page_type != 'product' or product == blank
 assign selected_product = ''
 if selected_product == blank or selected_product == '' or all_products[selected_product] == blank
 assign product = collections['all'].products.first
 else
 assign product = all_products[selected_product]
 if product.id == blank
 assign product = collections['all'].products.last
 endif
 endif
 endif
 endunless
 %}
 {%- if product != blank -%}
 {%- capture ec_form_id -%}product_form_{{ product.id }}_ecom-jsuaa3ojd6{%- endcapture -%}
 {%- form 'product', product, id: ec_form_id, class: "ecom-product-form ecom-product-form--single ecom-click", product_id: product.id, data-product_id: product.id,data-product-id: product.id, data-handle: product.handle-%}
 <section class="ecom-row ecom-core ecom-section ecom-jsuaa3ojd6" data-id="ecom-jsuaa3ojd6" style="z-index: inherit;"><div data-deep="1" class="core__row--columns core__row--full"><div class="ecom-column ecom-core ecom-pcaej127whi"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-xu7p3deoum" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-gw5f5afqed7" data-id="ecom-gw5f5afqed7" style="z-index: inherit;"><div data-deep="2" class="core__row--columns core__row--full"><div class="ecom-column ecom-core ecom-pc38tfr8j6j"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-8fvlz46y338" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__media ecom-position-sticky" deep="2"><div class="ecom-product-single__media-wrapper 
 {% if product.media.size == 1 %}
 ecom-dont-has-many-images
 {% endif %}
 "><div class="ecom-product-single__media-container ecom-product-single__media--slider ecom-product-single__media--vertical ecom-product-single__media-tablet--horizontal ecom-product-single__media-mobile--horizontal"><div class="ec_splide ecom-product-single__media--featured ecom-swiper-container" data-direction="ltr" data-breakpoints="{&quot;0&quot;:{},&quot;768&quot;:{},&quot;1025&quot;:{}}" data-priority="featured"><div class="ec_splide__track">
 {% comment %}
 Reactivity
 *this.data.settings.image_action
 *this.data.settings.enable_zoom
 *this.data.settings.grid_advance_number_images
 {% endcomment %}
 
 {%- liquid
 if product.has_only_default_variant
 assign target = product
 else
 assign target = product.selected_or_first_available_variant
 endif
 -%}
 
 
 
 {% assign img_padding = 1 | times: 1 %}
 {% assign img_padding__tablet = 1 | times: 1 %}
 {% assign img_padding__mobile = 1 | times: 1 %}
 <div class="ec_splide__list ecom-swiper-wrapper ecom-product-single__media--images ecom-swiper-wrapper ecom-product-single__media--images-layout__slider">
 {% assign use_limit = undefined %}
 {% assign limit_images = undefined %}
 {% assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src' %}
 {% for media in product.media %}
 {% assign media_ration = 1 | divided_by: media.aspect_ratio | times: 100 %}
 {% assign image = media.preview_image %}
 {% case media.media_type %}
 {% when 'image' %}
 {%if use_limit and forloop.index > limit_images%}{% break %}{% endif %}
 {% assign fetchpriority = 'low'%}
 {% assign lazyload = 'low'%}
 {% if forloop.first %}
 {% assign fetchpriority = 'high'%}
 {% assign lazyload = 'eager'%}
 {% endif %}
 {% assign img_ration = 1 | divided_by: media.aspect_ratio | times: 100 %}
 
 <div class="ec_splide__slide ec_splide__slide ecom-product-single__media--image ecom-splide-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-product-single__media--image" data-index="{{forloop.index0}}" {% if variant_images contains media.src %} data-variant_id="{{ product.images | where: 'src', image.src | map: 'variants' | map: 'id' | join: ',' }}"{% endif %} style="--img_padding: {{ img_ration | divided_by: img_padding }}%;--img_padding__tablet: {{ img_ration | divided_by: img_padding__tablet }}%;--img_padding__mobile: {{ img_ration | divided_by: img_padding__mobile }}%;" data-half-width="{% assign img_ration_half = 1 | divided_by: image.aspect_ratio | times: 50 %}{{img_ration_half}}" data-full-width="{% assign img_ration_full = 1 | divided_by: media.aspect_ratio | times: 100 %}{{img_ration_full}}">
 
 
 {%- assign img_master = media | img_url: 'master' -%}
 {{ media | image_url: width: 1946 | image_tag:
 sizes: sizes,
 widths: '246, 493, 600, 713, 823, 990, 1100, 1206, 1346, 1426, 1646, 1946',
 class: 'ecom-image-default'
 
 ,loading: 'eager'
 ,fetchpriority: fetchpriority
 ,alt: media.alt | escape
 }}
 
 
 </div>
 {% when 'external_video'%}
 <div class="ec_splide__slide ecom-product-single__media--image ecom-splide-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-product-single__media---external-video ecom-product-single__media--full" data-position="{{media.position}}" style="--img_padding: {{ media_ration | divided_by: img_padding }}%;--img_padding__tablet: {{ media_ration | divided_by: img_padding__tablet }}%;--img_padding__mobile: {{ media_ration | divided_by: img_padding__mobile }}%;" >
 {{ media | external_video_tag: image_size:'master' }}
 </div>
 {% when 'video' %}
 {% assign videoUrl = '' %}
 {% for source in media.sources %}
 {% if source.format == "mp4" %}
 {% assign videoUrl = source.url %}
 {% break %}
 {% endif %}
 {% endfor %}
 <div data-stopdrag="true" class="ec_splide__slide ecom-product-single__media--image ecom-splide-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-product-single__media--video ecom-product-single__media--full ecom-swiper-no-swiping" data-position="{{media.position}}" style="--img_padding: {{ media_ration | divided_by: img_padding }}%;--img_padding__tablet: {{ media_ration | divided_by: img_padding__tablet }}%;--img_padding__mobile: {{ media_ration | divided_by: img_padding__mobile }}%;" >
 {{ media | video_tag: image_size:'master', class: 'ecom-media-video', controls: false, autoplay: false, muted: undefined,loop: false }}
 
 <button class="ecom-product-single__media--play-control" type="button">
 <span class="ecom-product-single__media--play-control-wrapper">
 <span class="visually-hidden">Play video</span>
 
 </span>
 </button>
 
 </div>
 {% when 'model' %}
 <div class="ec_splide__slide ecom-product-single__media--image ecom-splide-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-swiper-no-swiping ecom-product-single__media--model ecom-product-single__media--full" data-stopdrag="true" data-position="{{media.position}}">
 <div class="ecom-product-single__media--model-wrapper">
 {{ media | model_viewer_tag: image_size:'master', reveal: 'interaction', toggleable: true, data-model-id: media.id }}
 </div>
 </div>
 {% else %}
 <div data-media-type="{{media.media_type}}" class="ec_splide__slide ecom-product-single__media--image ecom-splide-slide ecom-flex ecom-image-align- ecom-image-align---tablet ecom-image-align---mobile ecom-swiper-no-swiping ecom-product-single__media--full" data-position="{{media.position}}">
 <div class="ecom-product-single__media" style="--img_padding: {{ media_ration | divided_by: img_padding }}%;--img_padding__tablet: {{ media_ration | divided_by: img_padding__tablet }}%;--img_padding__mobile: {{ media_ration | divided_by: img_padding__mobile }}%;">
 {{ media | media_tag: image_size:'master', class: 'ecom-product-single__media--item' }}
 </div>
 </div>
 {% endcase %}
 {% endfor %}
 </div>
 </div><div class="ec_splide__arrows"><button type="button" class="ec_splide__arrow ec_splide__arrow--next ecom-swiper-button ecom-swiper-button-next ecom-swiper-controls"><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
 </svg></button><button type="button" class="ec_splide__arrow ec_splide__arrow--prev ecom-swiper-button ecom-swiper-button-prev ecom-swiper-controls"><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
 </svg></button></div><ul class="ec_splide__pagination ecom-swiper-pagination"></ul></div><div class="ec_splide ecom-product-single__media--thumbs ecom-product-single__init-thumb-hidden ecom-swiper-container" data-direction="ltr" data-breakpoints="{&quot;0&quot;:{&quot;perPage&quot;:5,&quot;gap&quot;:8,&quot;thumbnail_position&quot;:&quot;column&quot;},&quot;768&quot;:{&quot;perPage&quot;:4,&quot;gap&quot;:8,&quot;thumbnail_position&quot;:&quot;column&quot;},&quot;1025&quot;:{&quot;perPage&quot;:1,&quot;gap&quot;:12,&quot;thumbnail_position&quot;:&quot;row-reverse&quot;}}"><div class="ec_splide__track">
 {%- if product.media.size > 1 -%}
 <div class="ec_splide__list ecom-swiper-wrapper">
 {% assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src' %}
 {% for media in product.media %}
 {% if media.media_type == 'image' %}
 {% assign image = media.preview_image %}
 {% assign fetchpriority = 'low'%}
 {% if forloop.first %}
 {% assign fetchpriority = 'high'%}
 {% endif %}
 <div class="ec_splide__slide ecom-product-single__media--thumbnail ecom-splide-slide" {% if variant_images contains media.src %} data-variant_id="{{ product.images | where: 'src', image.src | map: 'variants' | map: 'id' | join: ',' }}"{% endif %}>
 <img
 class="ecom-product-thumbnail"
 src="{{ media | img_url: '100x100' }}"
 alt="{{ media.alt }}",
 fetchpriority="{{ fetchpriority }}"
 
 />
 </div>
 {% else %}
 <div class="ec_splide__slide ecom-product-single__media--thumbnail ecom-splide-slide">
 <div class="ecom-product-single__media--thumbnail--icon">
 {% if media.media_type == 'model' %}
 
 {% else %}
 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM176 168V344C176 352.7 180.7 360.7 188.3 364.9C195.8 369.2 205.1 369 212.5 364.5L356.5 276.5C363.6 272.1 368 264.4 368 256C368 247.6 363.6 239.9 356.5 235.5L212.5 147.5C205.1 142.1 195.8 142.8 188.3 147.1C180.7 151.3 176 159.3 176 168V168z"></path></svg>
 {% endif %}
 </div>
 <img src="{{ media.preview_image | img_url: '100x100' }}" alt="{{ media.alt }}" />
 </div>
 {% endif %}
 {% endfor %}
 </div>
 {%- endif -%}
 </div><div class="ec_splide__arrows"><button type="button" class="ec_splide__arrow ec_splide__arrow--next ecom-swiper-button-next ecom-swiper-controls-thumb"><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
 </svg></button><button type="button" class="ec_splide__arrow ec_splide__arrow--prev ecom-swiper-button-prev ecom-swiper-controls-thumb"><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
 </svg></button></div></div></div></div><div class="ecom-hidden-content">
 <script type="application/json" id="Product-model-{{ product.id }}">
 {{ product.media | where: 'media_type', 'model' | json }}
 </script>
 </div></div></div> </div></div></div></div> <div class="ecom-column ecom-core ecom-c1q4swjycec"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block ecom-3ipkpkrm5y9" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__rating" deep="2"><div class="ecom-product-single__rating-wrapper" data-review-platform="
 {%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}
 {{review_platform}}
 "><div class="ecom-product-single__rating-container">
 {%- if review_platform -%}
 {%- if EComBuilderMode == true -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <div class="ecom-app-extension-info">
 <span class="ecom-theme-app-extension-icon">
 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M364.2 267.2l-104.8-15.28L212.6 156.7C208.7 148.8 200.7 143.9 191.9 143.9C183.2 143.8 175.1 148.8 171.4 156.7L124.5 251.1L19.71 267.2C.9566 269.9-6.618 293.1 7.007 306.5l75.87 74l-17.1 104.6c-1.25 6.75 .5878 13.51 4.963 18.63C74.22 509 80.64 512 87.52 512c3.625 0 7.436-.9554 10.69-2.705l93.79-49.38l93.74 49.38c3.25 1.625 6.991 2.675 10.62 2.675c6.75 0 13.23-2.97 17.61-8.22c4.25-5.125 6.273-11.98 5.148-18.61L301.2 380.5l75.92-74C390.6 293 383.1 269.1 364.2 267.2zM266.7 369.3l17.62 103.1l-92.5-48.75l-92.5 48.75l17.62-103.1L42.22 296.1l103.4-15l46.38-93.88l46.13 93.88l103.5 15L266.7 369.3zM256 112h64v64C320 184.8 327.2 192 336 192S352 184.8 352 176v-64h64c8.844 0 16-7.156 16-16s-7.154-15.1-15.1-15.1L352 80v-64C352 7.156 344.8 0 336 0S320 7.156 320 16v64l-63.99 .0022C247.2 80 240 87.16 240 96S247.2 112 256 112zM496 208h-32v-32c0-8.844-7.155-15.1-15.1-15.1S432 167.2 432 176v32h-32c-8.844 0-15.1 7.162-15.1 16.01S391.2 240 400 240h32v32c0 8.844 7.158 16.01 16 16.01S464 280.8 464 272v-32h32c8.844 0 16-7.151 16-15.99S504.8 208 496 208z"/></svg>
 </span>
 <div class="ecom-theme-app-extension-content">
 <span class="ecom-theme-app-extension-title">{{review_platform | replace: '_', ' ' | replace: '-', ' ' | capitalize}}</span>
 <div class="ecom-placeholder-on-builder-mode" data-ecom-placeholder="The rating element will display when published to Online store"></div>
 </div>
 </div>
 </div>
 {%- else -%}
 {%- case review_platform -%}
 {%- when 'none' -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <p>Please select the rating platform in settings</p>
 </div>
 {%- when 'ali-reviews' -%}
 <div product-id="{{ product.id }}" product-handle="{{ product.handle }}" class="alireviews-review-star-rating"></div>
 {%- when 'vital-reviews'-%}
 <div class="bundle-aggregated_reviews"></div>
 {%- when 'opinew-reviews' -%}
 <div id='opinew-stars-plugin-product'>{% render 'opinew_review_stars_product' product:product %}</div>
 {%- when 'judgeme' -%}
 <div style='{{ jm_style }}' class='jdgm-widget jdgm-preview-badge' data-id='{{ product.id }}'>
 {{ product.metafields.judgeme.badge }}
 </div> 
 {%- when 'product-reviews-addon' -%}
 <span class="stamped-product-reviews-badge stamped-main-badge" data-id="{{ product.id }}" data-product-sku="{{ product.handle }}" style="display: inline-block;">{{- product.metafields.stamped.badge -}}</span>
 {%- when 'areviews-aliexpress'-%}
 <div class="areviews_header_stars"></div>
 {%- when 'loox'-%}
 <a href="#looxReviews">
 <div class="loox-rating" data-id="{{ product.id }}" data-rating="{{ product.metafields.loox.avg_rating }}" data-raters="{{ product.metafields.loox.num_reviews }}"></div>
 </a>
 {% when 'ryviu'%}
 {%- if EComBuilderMode == true -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <div class="ecom-app-extension-info">
 <span class="ecom-theme-app-extension-icon">
 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M364.2 267.2l-104.8-15.28L212.6 156.7C208.7 148.8 200.7 143.9 191.9 143.9C183.2 143.8 175.1 148.8 171.4 156.7L124.5 251.1L19.71 267.2C.9566 269.9-6.618 293.1 7.007 306.5l75.87 74l-17.1 104.6c-1.25 6.75 .5878 13.51 4.963 18.63C74.22 509 80.64 512 87.52 512c3.625 0 7.436-.9554 10.69-2.705l93.79-49.38l93.74 49.38c3.25 1.625 6.991 2.675 10.62 2.675c6.75 0 13.23-2.97 17.61-8.22c4.25-5.125 6.273-11.98 5.148-18.61L301.2 380.5l75.92-74C390.6 293 383.1 269.1 364.2 267.2zM266.7 369.3l17.62 103.1l-92.5-48.75l-92.5 48.75l17.62-103.1L42.22 296.1l103.4-15l46.38-93.88l46.13 93.88l103.5 15L266.7 369.3zM256 112h64v64C320 184.8 327.2 192 336 192S352 184.8 352 176v-64h64c8.844 0 16-7.156 16-16s-7.154-15.1-15.1-15.1L352 80v-64C352 7.156 344.8 0 336 0S320 7.156 320 16v64l-63.99 .0022C247.2 80 240 87.16 240 96S247.2 112 256 112zM496 208h-32v-32c0-8.844-7.155-15.1-15.1-15.1S432 167.2 432 176v32h-32c-8.844 0-15.1 7.162-15.1 16.01S391.2 240 400 240h32v32c0 8.844 7.158 16.01 16 16.01S464 280.8 464 272v-32h32c8.844 0 16-7.151 16-15.99S504.8 208 496 208z"/></svg>
 </span>
 <div class="ecom-theme-app-extension-content">
 <span class="ecom-theme-app-extension-title">Ryviu</span>
 <div class="ecom-placeholder-on-builder-mode" data-ecom-placeholder="The rating element will display when published to Online store"></div>
 </div>
 </div>
 {%- else -%}
 <div class="review-widget">
 <ryviu-widget-total
 reviews_data="{{product.metafields.ryviu.product_reviews_info | escape }}"
 product_id="{{product.id}}" handle="{{product.handle}}">
 </ryviu-widget-total>
 </div>
 {%- endif -%}
 {%- when 'yotpo-social-reviews' -%}
 <div class="yotpo bottomLine" style="display:inline-block" data-product-id="{{ product.id }}"> </div>
 {%- when 'aliexpress-reviews-importer'-%}
 <div id="shop_booster_rate_6" product-id="{{ product.id }}"></div>
 {%- when 'rivyo-product-review'-%}
 <div class="wc_product_review_badge" data-handle="{{ product.handle }}" data-product_id="{{ product.id }}"></div>
 {%-when 'growave' -%}
 {% capture the_snippet_review_avg %}{% render 'ssw-widget-avg-rate-listing', product: product %}{% endcapture %}
 {% unless the_snippet_review_avg contains 'Liquid error' %}
 {{ the_snippet_review_avg }}
 {% endunless %}
 {%- when 'smart-aliexpress-reviews'-%}
 <a href="#scm-reviews-importer">
 <div id="scm-product-detail-rate" class="scm-reviews-rate" data-rate-version2= {{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo | json}}>
 </div>
 </a>
 {%- when 'photo-reviews' -%}
 <div id='opinew-stars-plugin-product'>{% render 'opinew_review_stars_product' product:product %}</div>
 {%- when 'lai-reviews' -%}
 <div class="scm-reviews-rate" data-rate-version2="{{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo | json | escape }}" data-product-id="{{ product.id }}"></div>
 {%- when 'product-reviews' -%}
 <span class="shopify-product-reviews-badge" data-id="{{ product.id }}"></span>
 {%- when 'rivyo' -%}
 <div class="wc_product_review_badge" data-handle="{{ product.handle }}" data-product_id="{{ product.id }}"></div>
 {%- when 'sealapps-product-review' -%}
 <div class="custom-vstar-rating-widget" product-id="{{ product.id }}"></div>
 {%- when 'ali-product-review' -%}
 <div product-id="{{ product.id }}" class="alr-display-review-badge"></div>
 {%- when 'klaviyo-reviews' -%}
 <div class="klaviyo-star-rating-widget" data-id="{{product.id}}" data-product-title="{{product.title}}" data-product-type="{{product.type}}"></div>
 {%- when 'air-reviews' -%}
 <div class="AirReviews-Widget AirReviews-Widget--Stars" data-review-avg="{{ product.metafields.air_reviews_product.review_avg }}" data-review-count="{{ product.metafields.air_reviews_product.review_count }}"></div>
 {%- when 'ait-product-reviews' -%}
 <span class="egg-product-reviews-rating" data-id="{{ product.id }}" id="{{ product.id }}"></span>
 {%- when 'trustify-reviews' -%}
 <div
 class="trustify-review-stars"
 data-review-type="product"
 data-product-id="{{ product.id }}"
 data-product-handle="{{ product.handle }}"
 data-review-avg="{{ product.metafields.tr_reviews_product.review_avg }}"
 data-review-count="{{ product.metafields.tr_reviews_product.review_count }}"
 >
 </div>
 {% else %}
 <p>The rating platform not supported</p>
 {%-endcase-%}
 {% endif %}
 {%- else -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <p>Please select the rating platform in settings</p>
 </div>
 {%- endif -%}
 </div></div></div></div> <div class="ecom-block ecom-core core__block ecom-702lr3xrqpv" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__title" deep="2"><div class="ecom-product-single__title-wrapper"><div class="ecom-product-single__title-container"><h3 class="ecom-product__heading" href="{{ product.url }}" title="{{ product.title }}">{{ product.title }}</h3></div></div></div></div> <div class="ecom-block ecom-core core__block ecom-pd5pb8cfgwo" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__price" deep="2"><div class="ecom-product-single__price-wrapper"><div class="ecom-product-single__price-container"><div class="ecom-product-single__price-container-grid"><div class="ecom-product-single ecom-product-single__price--prices">
 {%- liquid
 if product.has_only_default_variant
 assign target = product
 else
 assign target = product.selected_or_first_available_variant
 endif
 -%}
 <div class="ecom-product-single__price--sale {% if target.compare_at_price == nil or target.compare_at_price <= target.price %} ecom-product-single__price-normal {% endif %}" data-price="{{target.price}}">
 {%- if settings.currency_code_enabled -%}
 {{target.price | money_with_currency}}
 {%- else -%}
 {{target.price| money }}
 {%- endif -%}
 </div>
 
 
 <div class="ecom-product-single__price--regular" {%- if target.compare_at_price == nil or target.compare_at_price <= target.price -%} style="display:none" {% endif %}>
 {%- if settings.currency_code_enabled -%}
 {{target.compare_at_price | money_with_currency}}
 {%- else -%}
 {{target.compare_at_price | money }}
 {%- endif -%}
 </div>
 
 {%- capture taxes_text -%}{%- endcapture -%}
 {%- if cart.taxes_included or shop.shipping_policy.body != blank -%}
 {% if taxes_text %}
 <div class="ec-product__tax caption rte">
 {%- if shop.taxes_included -%}
 
 {%- endif -%}
 {%- if shop.shipping_policy.body != blank -%}
 
 {%- endif -%}
 </div>
 {%- endif -%}
 {%- endif -%}
 </div><div class="ecom-product-single__price--badges-wrapper">
 <div class="ecom-product-single__price--badges">
 {%- assign savings = target.compare_at_price | minus: target.price | times: 100.0 | divided_by: target.compare_at_price | round -%}
 
 <span class="ecom-product-single__price--badges-sale" data-have-sale="{%- unless target.compare_at_price == nil or target.compare_at_price <= target.price or target.available != true -%}true{%endunless%}" data-text=" {price}% OFF" data-sale="{{savings}}" data-type="percent" style="{%- unless target.compare_at_price == nil or target.compare_at_price <= target.price or target.available != true -%} display: none;{% endunless %}">
 {price}% OFF
 </span>
 <span class="ecom-product-single__price--badges-sold-out" {% if target.available %} style="display:none" {% endif %}>
 Sold out
 </span>
 </div>
 </div></div><div class="ecom-product-single-afterpay">
 
 </div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-unwd1epxyr" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed"><font color="#6d4c41">Get up to 15% off at checkout + Free Shipping on All Orders!</font></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-v7sls82pxar" data-core-is="block"><div class="ecom__element ecom-element element__divi" deep="2"><div class="ecom__element-divi" style="--divi-line-height: 1px;"><div class="divi-line divi-style" style="display: block;"></div><div class="divi-cont" style="--flex-desktop: 0.5; --flex-tablet: 0.5; --flex-mobile: 0.5; --divider-width: auto; --divider-width-tablet: auto; --divider-width-mobile: auto; display: none;"><div class="divi-cont-before divi-style" style="display: block;"></div><div class="divi-cont-after divi-style" style="display: block;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block ecom-p1udaa47up8" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__variant-picker" deep="2"><div class="ecom-product-single__variant-picker-wrapper ecom_not_hide_dropdown_arrow"><div class="ecom-product-single__variant-picker-container {% if product.has_only_default_variant%} ecom-product-single__variant-picker--only-default{% endif%} " data-picker-type="image" data-ecom-placeholder="">
 {%- liquid
 assign ecom_has_variant_picker = true
 assign variant_selected = product.selected_or_first_available_variant
 -%}
 <div class="ecom-product-single__variant-picker--main">
 {%- capture swatch_option_temp -%}Color{%- endcapture-%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {%- assign swatch_option = swatch_option | split: ',' -%}
 {% unless product.has_only_default_variant %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-product-single__picker-main ecom-product-single__picker-option-{{current_option.name | handleize }}">
 <span class="ecom-product-single__picker-main-label ecom-product-single__picker--option-label" data-option-index="{{current_option.position | minus: 1}}">
 <span class="ecom-product-variant--option-label-text">{{ current_option.name }}</span>
 </span>
 <ul class="ecom-product-single__picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-image-default ecom-product-single__swatch-item ecom-product-single__picker-images-item {% if option_value == variant_selected[index] %}ecom-image-active{% endif %}" data-value="{{ option_value | escape }}">
 <img src="{{ variant | img_url: "360x" }}" alt="{{ option_value }}" />
 </li>
 {%- endfor -%}
 </ul>
 </div>
 {% continue%}
 {%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-product-single__picker-option-{{option.name | handleize }}">
 <span class="ecom-product-single__picker-radio-label ecom-product-single__picker--option-label" data-option-index="{{option_index}}">
 <span class="ecom-product-variant--option-label-text">{{option.name}}</span>
 </span>
 
 <ul class="ecom-product-single__picker-radio-list">
 {% for value in option.values %}
 <li class="ecom-product-single__swatch-item ecom-product-single__picker-radio-list-item {% if value == variant_selected[index] %}ecom-button-active{% endif %}" data-option-index="{{ option_index }}" data-value="{{ value | escape }}">
 {{value}}
 </li>
 {% endfor %}
 </ul>
 
 </div>
 {%- endfor -%}
 {% endunless %}
 </div><div class="ecom-product-single__variant-picker--options">
 <select name="id" class="ecom-product-single-select-id {% if product.has_only_default_variant %} ecom-product-single__picker-default-variant {% endif %}" data-product-id="{{product.id}}" data-json-product="product-json-{{product.id}}-ecom-p1udaa47up8" id="ecom-variant-selector-{{product.id}}-ecom-p1udaa47up8">
 {% unless product.has_only_default_variant %}{% endunless %}$
 {% for variant in product.variants%}
 <option value="{{variant.id}}" {% if variant_selected.id == variant.id %} selected="selected" {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div><div class="ecom-product-single__variant-picker--json">
 <script type="application/json" id="product-json-{{product.id}}-ecom-p1udaa47up8">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-nrsdzjbuk9" data-core-is="block"><div class="ecom-row ecom-core core__block ecom-9q17hkv4r8g" data-id="ecom-9q17hkv4r8g" style="z-index: inherit;"><div data-deep="3" class="core__row--columns"><div class="ecom-column ecom-core ecom-s3tk95gylis"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-hieo3l4qaf ec-button-action" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__add-to-cart" deep="3"><div class="ecom-product-single__add-to-cart-wrapper"><div class="ecom-product-single__add-to-cart-container"><div class="ecom-product-single__add-to-cart-buttons"><div class="ecom-product-single__add-to-cart-buttons-wrapper"><button type="submit" class="ecom-product-single__add-to-cart--submit ecom-flex" data-text-add-cart="Add to cart" data-text-pre-order="Pre Order" data-text-unavailable="Unavailable" data-text-outstock="Out stock" data-text-added-cart="Added to cart" data-message-added-cart="Added to cart" data-href="#" data-target="" data-action="popup" data-variant-id="{{product.first_available_variant.id}}" name="add"><span class="ecom-add-to-cart-text">Add to cart</span><span class="ecom__element--button-icon"></span></button></div></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-x0ozz60h1ii" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__buy_it_now_btn" data-preview-mode="true" deep="3"><div class="ecom-product-single__buy_it_now_btn-buttons-wrapper"><button type="button" class="ecom-product-single__buy_it_now_btn--checkout" data-variant-id="{{product.first_available_variant.id}}"><span>Buy Now</span></button></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-1bf2y1wmzej" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__metafield" deep="2"><div class="ecom-product-single__metafield-wrapper"><div class="ecom-product-single__metafield-container">{{ product.metafields.custom.multiline_text }}</div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-gn11co2i1pd" data-core-is="block"><div class="ecom-row ecom-core core__block ecom-msbiwdk70ln" data-id="ecom-msbiwdk70ln" style="z-index: inherit;"><div data-deep="3" class="core__row--columns"><div class="ecom-column ecom-core ecom-hiiy4ywueb"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-5j02ypqntx2" data-core-is="block"><div class="ecom__element element__heading ecom-element ecom-html" data-stopdrag="true" deep="3"><h3 class="ecom__heading ecom-db">Buy with confidence</h3></div></div> <div class="ecom-block ecom-core core__block ecom-wz6ba4unx5" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="3"><div class="text-content ecom-html has-drop-cap-view-framed"><div>🚚 Free shipping on all orders.</div><div>🛠️ 4 service methods available.</div><div>📦 60-day return policy.</div><div>🔧 360-day warranty with quick solutions.</div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block ecom-nzijpe9f4p" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed">Guarantee Safe Checkout:</div></div></div> <div class="ecom-block ecom-core core__block ecom-pn2svqepxv" data-core-is="block"><div class="ecom-element ecom-base-image" deep="2"><figure><div class="ecom-container-image ecom-image-align" data-stopdrag=""><div data-stopdrag="" class="ecom-image-content-position ecom-image-default ecom-base-image-container-overlay" style=""><picture class="ecom-image-picture"><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/ecom-edit-image-nAAwlTUOWG120.jpg?v=1739777446" alt="120" width="1000" height="100"></picture></div></div></figure></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> <div class="ecom-block ecom-core core__block elmspace ecom-a7c1lxkf04n" data-core-is="block"><div class="ecom__element ecom-element element__divi" deep="1"><div class="ecom__element-divi" style="--divi-line-height: 1px;"><div class="divi-line divi-style" style="display: block;"></div><div class="divi-cont" style="--flex-desktop: 0.5; --flex-tablet: 0.5; --flex-mobile: 0.5; --divider-width: auto; --divider-width-tablet: auto; --divider-width-mobile: auto; display: none;"><div class="divi-cont-before divi-style" style="display: block;"></div><div class="divi-cont-after divi-style" style="display: block;"></div></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
 {% unless ecom_has_variant_picker %}
 <input type="hidden" name="id" value="{{product.selected_or_first_available_variant.id}}" />
 <script type="application/json" id="product-json-{{product.id}}-ecom-jsuaa3ojd6">
 {%- render "ecom_product_json", product: product -%}
 </script>
 {% endunless %}
 {%- endform -%}
 {% assign ecom_has_variant_picker = false %}
 {%- endif -%}
 
 {% comment %}
 // Reactive
 * this.data.settings.product
 {% endcomment %}
 {% liquid
 unless recommendations.performed
 if request.page_type != 'product' or product == blank
 assign selected_product = ''
 if selected_product == blank or selected_product == '' or all_products[selected_product] == blank
 assign product = collections['all'].products.first
 else
 assign product = all_products[selected_product]
 if product.id == blank
 assign product = collections['all'].products.last
 endif
 endif
 endif
 endunless
 %}
 {%- if product != blank -%}
 {%- capture ec_form_id -%}product_form_{{ product.id }}_ecom-hc4sf0uykfv{%- endcapture -%}
 {%- form 'product', product, id: ec_form_id, class: "ecom-product-form ecom-product-form--single ecom-click", product_id: product.id, data-product_id: product.id,data-product-id: product.id, data-handle: product.handle-%}
 <section class="ecom-row ecom-core ecom-section ecom-hc4sf0uykfv" data-id="ecom-hc4sf0uykfv" style="z-index: inherit;"><div data-deep="1" class="core__row--columns core__row--full"><div class="ecom-column ecom-core ecom-mw2ugxzb8gj"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-requiubiavs" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-dwg0wqpx6lh" data-id="ecom-dwg0wqpx6lh" style="z-index: inherit;"><div data-deep="2" class="core__row--columns"><div class="ecom-column ecom-core ecom-zs2lc2ty5kn"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-dlw2rck2tq" data-core-is="group"><div class="ecom-core ecom-group core__block ecom-ji68axy0gmj"> <div class="core__group--wrapper tabs__wrapper ecom__element ecom__tabs tabs__wrapper--horizontal" data-r="" style=""><div class="tabs__navs tabs__navs--horizontal"><div class="tabs__navs--items"><div class="ecom-items tabs__nav ecom-item-active" data-target=""><div class="tabs_nav--content_right"><div class="tabs_nav--content"><h3 class="tabs_nav--text ecom-items--text"> Product Description</h3></div><div class="tabs_nav--sub-text"></div></div></div><div class="ecom-items tabs__nav" data-target=""><div class="tabs_nav--content_right"><div class="tabs_nav--content"><div class="tabs_nav--text ecom-items--text">Shipping Info</div></div><div class="tabs_nav--sub-text"></div></div></div><div class="ecom-items tabs__nav" data-target=""><div class="tabs_nav--content_right"><div class="tabs_nav--content"><div class="tabs_nav--text ecom-items--text">Return</div></div><div class="tabs_nav--sub-text"></div></div></div><div class="ecom-items tabs__nav" data-target="##review"><div class="tabs_nav--content_right"><div class="tabs_nav--content"><h3 class="tabs_nav--text ecom-items--text">Reviews </h3></div><div class="tabs_nav--sub-text"></div></div></div></div></div><div class="core__group--items"><div id="ecom-dkmqufvzv48" class="core__group--item tab__item ecom-item-active ecom-item-group-init" data-id="ecom-dkmqufvzv48"><div class="core__group--body tabs__body"><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-8zx3hp8buzx" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__description" deep="2"><div class="ecom-product-single__description-wrapper"><div class="ecom-product-single__description-container" data-show-type="full"><div class="ecom-product-single__description--paragraph"><div class="ecom-html-des" style="max-height: var(--ecom-description-height);">{{product.description}}</div></div></div></div></div></div> </div></div></div></div><div id="ecom-v3ws6exhte9" class="core__group--item tab__item" data-id="ecom-v3ws6exhte9"><div class="core__group--body tabs__body"><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-5b7yzmzigia" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed"><div><h3>Shipping Policy</h3></div><div>&nbsp;</div><div>Thank you for shopping at www.sicotas.com! Here’s what you need to know about shipping:</div><div>Ship Areas: We offer free shipping within the contiguous U.S. via UPS or FedEx. We do not ship to Alaska, Hawaii, U.S. territories, or military bases.</div><div><br></div><div><b>Processing &amp; Delivery:</b> Orders are processed within 1-2 business days and shipped in 2-3 business days (excluding weekends and holidays).</div><div><br></div><div><b>Important Notes:</b></div><div>Processing time (1-2 business days) is separate from shipping time.</div><div>High order volumes or unexpected delays may affect delivery. We’ll notify you if needed.</div><div>Invalid addresses or unforeseen circumstances may extend delivery times.</div><div><br></div><div>For any questions or inquiries, <NAME_EMAIL> or 833-742-6827</div></div></div></div> </div></div></div></div><div id="ecom-gweevc51qkh" class="core__group--item tab__item" data-id="ecom-gweevc51qkh"><div class="core__group--body tabs__body"><div class="core__blocks" index="2"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-5qtejsfswj" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed"><h3>Return Policy</h3><div><br></div><div><b>60-Day Hassle-Free Return</b></div><div><br></div><div>To return a product, it must be in its original packaging and shipped within 60 days from the purchase date.</div><div><br></div><div><b>Eligibility for Return</b></div><div><br></div><div>Items must be unused and in their original condition with packaging to qualify for a return.</div><div><br></div><div><b>Return Shipping Fees</b></div><div><br></div><div>Return shipping fees are the customer's responsibility. After shipping the product back, email us with your order number, reason for the return (and photos if necessary). A full refund will be processed once we receive your return and cancellation request via email.</div><div><br></div><div><b>Defective, Incorrect, or Incomplete Merchandise</b></div><div><br></div><div>If you receive defective, incorrect, or incomplete merchandise, contact us immediately. For faster assistance, email us with your order number, contact details, and photos of any damage. Keep all items and packaging materials until your claim is resolved. If a replacement item is out of stock or on back-order, we will inform you of the estimated arrival date or offer a suitable replacement.</div><div><br></div><div><b>360-Day Replacement Repair Warranty</b></div><div><br></div><div>This warranty is valid for 360 days from the order date, applicable if the order is placed within 30 days from the purchase date.</div><div><br></div><div><b>Return Procedure</b></div><div><br></div><div>To complete your return, include a receipt or proof of purchase. Do not send returns to the manufacturer.</div><div><br></div><div><b>Partial Refunds</b></div><div><br></div><div>Partial refunds may be issued for items not in their original condition, damaged, or missing parts not due to our error, or if returned more than 30 days after delivery.</div><div><br></div><div><b>Refund Process</b></div><div><br></div><div>Once we receive and inspect your return, we will email you regarding the approval or rejection of your refund. If approved, your refund will be processed, and a credit will be applied to your original payment method within a specified number of days.</div><div><br></div><div><b>Late or Missing Refunds</b></div><div><br></div><div>If you haven’t received your refund, first check your bank account.</div><div><br></div><div>Contact your credit card company and bank, as there may be processing delays. If you’ve completed these steps and still haven’t received your refund, contact <NAME_EMAIL> with your order number.</div><div><br></div><div><b>Sale Items</b></div><div><br></div><div>Only regular-priced items can be refunded; sale items are non-refundable.</div><div><br></div><div><b>Exchanges</b></div><div><br></div><div>We only replace items if they are defective or damaged.</div><div><br></div><div>For exchanges, email <NAME_EMAIL> and follow our instructions.</div><div><br></div><div><b>Gifts</b></div><div><br></div><div>If an item was marked as a gift and shipped directly to you, you will receive a gift credit for the return value.</div><div><br></div><div>Once the returned item is received, a gift certificate will be mailed to you. If the item was not marked as a gift or if the gift giver had the order shipped to themselves, the refund will be sent to the gift giver, who will be informed of the return.</div><div><br></div><div>For further inquiries or assistance, please contact our customer care <NAME_EMAIL>.</div></div></div></div> </div></div></div></div><div id="ecom-4kguwu7ij7x" class="core__group--item tab__item" data-id="ecom-4kguwu7ij7x"><div class="core__group--body tabs__body"><div class="core__blocks" index="3"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-gtqn2c1yvb" data-core-is="block"><div class="ecom-row ecom-core core__block ecom-2gffqqsuggb" data-id="ecom-2gffqqsuggb" style="z-index: inherit;"><div data-deep="3" class="core__row--columns"><div class="ecom-column ecom-core ecom-1vgq32y7j1t"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-z9oxkn0r0y" data-core-is="block"><div class="ecom-element ecom-product-single ecom-product-single__review" deep="3"><div class="ecom-product-single__review-wrapper" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{review_platform}}"><div class="ecom-product-single__review-container">
 {%- if review_platform and review_platform != 'none' -%}
 {%- if EComBuilderMode == true -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <div class="ecom-app-extension-info">
 <span class="ecom-theme-app-extension-icon">
 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path d="M416 176C416 78.8 322.9 0 208 0S0 78.8 0 176c0 41.48 17.07 79.54 45.44 109.6c-15.17 32.34-38.65 58.07-38.95 58.38c-6.514 6.836-8.309 16.91-4.568 25.67C5.754 378.4 14.26 384 23.66 384c54.19 0 97.76-20.73 125.9-39.17C168.1 349.4 187.7 352 208 352C322.9 352 416 273.2 416 176zM208 320c-16.96 0-34.04-2.098-50.75-6.232L143.7 310.4L132 318.1c-20.43 13.38-51.58 28.99-89.85 32.97c9.377-12.11 22.3-30.63 32.24-51.82l9.242-19.71L68.72 263.7C44.7 238.2 32 207.9 32 176C32 96.6 110.1 32 208 32S384 96.6 384 176S305 320 208 320zM606.4 435.4C627.6 407.1 640 372.9 640 336C640 238.8 554 160 448 160c-.3145 0-.6191 .041-.9336 .043C447.5 165.3 448 170.6 448 176c0 5.43-.4668 10.76-.9414 16.09C447.4 192.1 447.7 192 448 192c88.22 0 160 64.6 160 144c0 28.69-9.424 56.45-27.25 80.26l-13.08 17.47l11.49 18.55c6.568 10.61 13.18 19.74 18.61 26.74c-18.26-1.91-36.45-6.625-54.3-14.09l-12.69-5.305l-12.58 5.557C495.9 475 472.3 480 448 480c-75.05 0-137.7-46.91-154.9-109.7c-10.1 3.336-20.5 6.132-31.2 8.271C282.7 455.1 357.1 512 448 512c29.82 0 57.94-6.414 83.12-17.54C555 504.5 583.7 512 616.3 512c9.398 0 17.91-5.57 21.73-14.32c3.74-8.758 1.945-18.84-4.568-25.67C633.3 471.8 619.6 456.8 606.4 435.4z"/></svg>
 </span>
 <div class="ecom-theme-app-extension-content">
 <span class="ecom-theme-app-extension-title">{{review_platform | replace: '_', ' ' | replace: '-', ' ' | capitalize}}</span>
 <div class="ecom-placeholder-on-builder-mode" data-ecom-placeholder="The review element will display when published to Online store"></div>
 </div>
 </div>
 </div>
 {%- else -%}
 {%- case review_platform -%}
 {%- when 'none' -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <p>Please select the integrated review app platform in settings</p>
 </div>
 {%- when 'ali-reviews' -%}
 <div class="alireviews-review-box"></div>
 {%- when 'opinew-reviews' -%}
 <div style="clear:both;"></div><div id="opinew-reviews-product-page-code" class="opw-widget-wrapper-default">
 <span id="opinew-plugin" data-server-address="https://api.opinew.com"
 data-opw-prodreviews="{{ product.metafields.opinew_metafields['product_plugin'] }}"
 data-opinew-shop-id="{{ shop.id }}" data-shop-url="{{shop.domain}}"
 data-platform-product-id="{{product.id}}">
 <span id="opinew_product_plugin_app"></span>
 </span>
 </div>
 {%- when 'judgeme' -%}
 <div style='clear:both'></div>
 <div id='judgeme_product_reviews' class='jdgm-widget jdgm-review-widget' data-id='{{ product.id }}'>
 {{ product.metafields.judgeme.widget }}
 </div>
 {%- when 'product-reviews-addon' -%}
 <div id="stamped-main-widget"
 data-widget-style="standard"
 data-product-id="{{ product.id }}"
 data-name="{{ product.title | escape }}"
 data-url="{{ shop.url }}{{ product.url }}"
 data-image-url="{{ product.featured_image | product_img_url: "large" |replace: '?', '%3F' | replace: '&','%26'}}"
 data-description="{{ product.description | escape }}"
 data-product-sku="{{product.handle}}"
 data-widget-language="{{ language }}">
 </div>
 {%- when 'areviews-aliexpress'-%}
 <div class="page-full">{%- render 'aliexpress_reviews', product: product-%}</div>
 {%- when 'loox'-%}
 <div id="looxReviews" data-product-id="{{product.id}}" >{{- product.metafields.loox.reviews -}}</div>
 {% when 'ryviu'%}
 <div class="lt-block-reviews">
 <ryviu-widget product_id="{{product.id}}" handle="{{product.handle}}" title_product="{{product.title}}" image_product="{{ product.featured_image.src | img_url: '100x' }}"></ryviu-widget>
 </div>
 {%- when 'yotpo-social-reviews' -%}
 <div class="yotpo yotpo-main-widget" data-product-id="{{ product.id }}" data-name="{{ product.title | escape }}" data-url="{{ shop.url }}{{ product.url }}" data-image-url="{{ product.featured_image | product_img_url: "large" |replace: '?', '%3F' | replace: '&','%26'}}" data-description="{{ product.description | escape }}"></div>
 {%- when 'aliexpress-reviews-importer'-%}
 <div id="shopbooster-ali" product-id="{{ product.id }}" ></div>
 {%- when 'rivyo-product-review'-%}
 <div id="wc_review_section" class="wc_review_main_content" data-url="{{ shop.url }}" data-handle="{{ product.handle }}" data-product_id="{{ product.id }}" data-limit="0"></div>
 {%-when 'growave' -%}
 {% capture the_snippet_reviews %}{% render 'socialshopwave-widget-recommends' with 1, product: product %}{% endcapture %}
 {% unless the_snippet_reviews contains 'Liquid error' %}
 {{ the_snippet_reviews }}
 {% endunless %}
 {%- when 'smart-aliexpress-reviews'-%}
 {% render "reviews-importer" product: product%}
 {%- when 'vital-reviews'-%}
 <div id="bundle-product_reviews"></div>
 {%- when 'photo-reviews' -%}
 <div style="clear:both;"></div>
 <div id="opinew-reviews-product-page-code" class="">
 <span id="opinew-plugin" data-server-address="https://www.opinew.com" data-opw-prodreviews="{{ product.metafields.opinew_metafields['product_plugin'] }}" data-opinew-shop-id="{{ shop.id }}" data-shop-url="{{shop.domain}}" data-platform-product-id="{{product.id}}">
 <span id="opinew_product_plugin_app"></span>
 </span>
 </div>
 {%- when 'product-reviews' -%}
 <div id="shopify-product-reviews" data-id="{{product.id}}">{{- product.metafields.spr.reviews -}}</div>
 {%- when 'lai-reviews' -%}
 {%- if EComBuilderMode -%}
 <div class="ecom-placeholder-on-builder-mode" data-ecom-placeholder="Lai review form will display when published to Online store"></div>
 {%- endif -%}
 {% assign smaTemplatePageBuilderCurrent = template | split: '.' | first %}
 {% if smaTemplatePageBuilderCurrent != 'product' %}
 <div class="scm-container custom" style="display: none;">
 <div id="scm-reviews-importer" class="scm-reviews-importer">
 <iframe id="scm-reviews-importer-iframe" width="100%"></iframe>
 </div>
 </div>
 {% endif %}
 {% assign random_number = "now" | date: "%N" | modulo: 1000 | plus: 0 %}<script>var sectionConfig= (typeof sectionConfig == "undefined" || sectionConfig == null) ? {} : sectionConfig;var scmCustomData= (typeof scmCustomData == "undefined" || scmCustomData == null) ? {} : scmCustomData;var scmCustomDataWigetAll= (typeof scmCustomDataWigetAll == "undefined" || scmCustomDataWigetAll == null) ? [] : scmCustomDataWigetAll; scmCustomDataWigetAll['{{ random_number }}'] ={"id_iframe" : "{{ random_number }}","productId" : "{{ product.id }}","typePage" : 'productPage',"sectionConfig" : JSON.stringify(sectionConfig),"scmCustomData" : JSON.stringify(scmCustomData),'dataProduct' : []};sectionConfig= null;scmCustomData= null;scmCustomDataWigetAll['{{ random_number }}'].dataProduct['product']= {{ product | json }};{% for metafieldValue in product.metafields.scm_review_importer %}scmCustomDataWigetAll['{{ random_number }}'].dataProduct['{{ metafieldValue[0] }}']= `{{ metafieldValue[1] | json }}`;{% endfor %}</script>
 <div class="scm-container" style="display: none;">
 <div class="scm-reviews-importer" data-product-id= {{ product.id }}>
 <iframe class="scm-reviews-importer-iframe" width="100%" title="Sma reviews section Product page" data-idIframe="{{ random_number }}"></iframe>
 </div>
 </div>
 {%- when 'rivyo' -%}
 <div id="wc_review_section" class="wc_review_main_content" data-url="{{ shop.url }}" data-handle="{{ product.handle }}" data-product_id="{{ product.id }}" data-limit="0"></div>
 {%- when 'sealapps-product-review' -%}
 <div class="custom-vstar-review-widget" style="width:100%"></div>
 {%- when 'klaviyo-reviews' -%}
 <div id="klaviyo-reviews-all" data-id="{{product.id}}"></div> 
 {%- when 'air-reviews' -%}
 <div class="AirReviews-Widget AirReviews-Widget--Block"></div>
 {%- when 'ait-product-reviews' -%}
 <div id="aio-product-detial-reviews" class="page-width" attr-value="{{ product.id }}"></div>
 {%- when 'trustify-reviews' -%}
 <div class="trustify-review-box"></div>
 {% else %}
 <div class="ecom-theme-app-extensions-skeleton">
 <p>The reivew platform not supported</p>
 </div>
 {%-endcase-%}
 {%- endif -%}
 {%- else -%}
 <div class="ecom-theme-app-extensions-skeleton">
 <p>Please select the integrated review app platform in settings</p>
 </div>
 {%- endif -%}
 </div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> </div></div></div></div></div></div> </div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
 {% unless ecom_has_variant_picker %}
 <input type="hidden" name="id" value="{{product.selected_or_first_available_variant.id}}" />
 <script type="application/json" id="product-json-{{product.id}}-ecom-hc4sf0uykfv">
 {%- render "ecom_product_json", product: product -%}
 </script>
 {% endunless %}
 {%- endform -%}
 {% assign ecom_has_variant_picker = false %}
 {%- endif -%}
 <section class="ecom-row ecom-core ecom-section ecom-1zs84sn424c" data-id="ecom-1zs84sn424c" style="z-index: inherit;"><div data-deep="1" class="core__row--columns core__row--full"><div class="ecom-column ecom-core ecom-ju3zxun089l"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-animation ecom-block ecom-core core__block elmspace ecom-38lp0p70rrp" data-core-is="block"><div class="ecom__element element__heading ecom-element ecom-html" data-stopdrag="true" deep="1"><h3 class="ecom__heading ecom-db">Related products</h3></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-ibo8s94zj5g" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__product" deep="1"><div class="ecom-collection__product-wrapper"><div class="ecom-collection__product-container ecom-collection__product-container_product"><div class="ecom-collection__product-main ecom-swiper-container ecom-collection_product_template_product" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1,&quot;slidesPerGroup&quot;:1,&quot;spaceBetween&quot;:15},&quot;768&quot;:{&quot;slidesPerView&quot;:3,&quot;spaceBetween&quot;:20},&quot;1025&quot;:{&quot;slidesPerView&quot;:4,&quot;slidesPerGroup&quot;:1,&quot;spaceBetween&quot;:30}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:0,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:200,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;}}" data-week="[%-W] week%!W" data-day="[%-d] day%!D" data-hour="[%-H] hr%!H" data-minute="[%-M] min%!M" data-second="[%-S] sec%!S" data-sale="-{{sale}}%" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{-review_platform-}}" data-countdown-shows="day,hour,minute,second" data-translate="false">
 {%- capture badge_tags -%}Hot{%- endcapture -%}
 {%- liquid
 assign colors = shop.metafields.ecomposer.colors
 assign badge_tags = badge_tags | strip | split: ','
 assign tmp_collection = collection
 assign tmp_product = product
 assign enable_hook = shop.metafields.ecomposer.enable_hook.value
 -%}
 
 {% assign ecom_related_type = '' %}
 {% assign is_collection = false%}
 {% assign current_product = blank %}
 {% assign current_product = product %}
 
 
 {% if current_product != blank %}
 {%- assign products = '' | split: '' -%}
 
 {%- capture limit-%}6{% endcapture %}
 {%- if limit == blank -%}
 {% assign limit = 12 %}
 {% else %}
 {% assign limit = limit | plus: 0 %}
 {%- endif-%}
 
 
 {% if product.collections.size > 0 %}
 {% assign valid_collection_handles = "" %}
 {% for c in product.collections %}
 {% if c.handle == 'all' or c.handle == 'frontpage' or c.products_count < 1 %}{% continue%}{% endif%}
 {% if forloop.first == false %}
 {% assign collection_handle = "," | append: c.handle %}
 {% else %}
 {% assign collection_handle = c.handle %}
 {% endif %}
 {% assign valid_collection_handles = valid_collection_handles | append: collection_handle %}
 {% assign ecom_related_type = 'ecom-product-related-shopify-collection' %}
 {% assign is_collection = true%}
 {% assign collection = c %}
 {% endfor %}
 {% assign collection_handles = valid_collection_handles | split: ',' | uniq%}
 {% for handle in collection_handles %}
 {% assign product_collection = collections[handle].products | map: 'handle' %}
 {% assign products = products | concat: product_collection | uniq %}
 {% endfor %}
 {% endif %}
 
 {% endif %}
 
 {% if false and product == product %}
 {% if true %}
 {% assign products = blank %}
 {% endif %}
 {%- if recommendations.performed? and recommendations.products_count > 0 -%}
 {% assign products = recommendations.products %}
 {%- endif -%}
 {% endif %}
 {% capture quickshop_layout%}full{% endcapture %}
 {% capture product_style%}vertical{% endcapture%}
 {%- assign view_more_only = false -%}
 {% capture product_layout %}slider{% endcapture %}
 {% assign check_min = 1%}
 {% if products and products.size > check_min and products != blank %}
 <div class="ecom-swiper-wrapper
 ecom-collection__product--wrapper-items ecom-collection-product__layout-slider
 {{ecom_related_type}}"
 data-grid-column="4"
 data-grid-column-tablet="3"
 data-grid-column-mobile="1"
 
 
 >
 {% assign ecom_count = 0 %}
 {% for p in products %}
 {% assign product = p %}
 {% if p.handle %}
 {% assign product = p %}
 {% else %}
 {% assign product = all_products[p] %}
 {% endif %}
 {% if product.url == blank %} {% continue %} {% endif %}
 {% if ecom_count >= limit %}{% break %}{% endif %}
 {%- if product.handle == current_product.handle -%}{% continue %}{%-endif -%}
 
 {% assign ecom_count = ecom_count | plus: 1 %}
 {%- capture swatch_option -%}Color{%- endcapture -%}
 {% assign hide_quickshop = true %}
 {% assign other_option_layout = 'hide' %}
 {% capture product_picker%}
 
 {%- if product.has_only_default_variant == false-%}
 {% if quickshop_layout != 'full' and product.options.size > 1%}
 {% assign hide_quickshop = false %}
 {% endif %}
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <div class="ecom-collection__product-variants" data-picker-type="image">
 <button class="ecom-collection__product-close"></button>
 <form class="ecom-collection__product-form ecom-product-form" product_id="{{product.id}}" data-product_id="{{product.id}}" data-product-id="{{product.id}}" data-handle="{{product.handle}}">
 <div class="ecom-child-element" >
 {% assign variant_selected = product.first_available_variant%}
 
 {%- capture swatch_option_temp -%}Color{%- endcapture -%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {% assign swatch_option = swatch_option | split: ',' %}
 {% assign option_index = current_option.position | minus: 1 %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-main ecom-collection__product-picker-option-{{current_option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-collection__product-swatch-item ecom-collection__product-picker-images-item {%comment%}{% if option_value == variant_selected[index] %}ecom-product-swatch-item--active ecom-button-active{% endif %}{%endcomment%}" data-value="{{ option_value | escape }}">
 <span class="ecom-collection__product-swatch-item--wrapper"></span>
 <img src="{{ variant | img_url: "120x120", crop: 'center' }}" alt=" {{ option_value }}" loading='lazy'/>
 </li>
 {%- endfor -%}
 </ul>
 
 </div>
 {% endif %}
 {% endfor %}
 {%- if other_option_layout != 'hide' -%}
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}{% continue%}{%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-other ecom-collection__product-picker-option-{{option.name | handleize }}">
 
 
 <select class="ecom-collection__product-swatch-select ecom-collection__product-picker-hide-list" data-option-index="{{ option_index }}">
 {% for value in option.values %}
 <option value="{{value | escape }}" {% if value == variant_selected[index] %}selected="selected"{% endif %}>
 {{value}}
 </option>
 {% endfor %}
 </select>
 
 </div>
 {%- endfor -%}
 </div>
 {%- endif -%}
 
 
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 <div class="ecom-collection__product-picker-selection" style="display:none;">
 <select name="variant_id" data-product-id="{{product.id}}" id="ecom-variant-selector-{{product.id}}-ecom-ibo8s94zj5g">
 {% for variant in product.variants %}
 <option value="{{variant.id}}" {% if product.first_available_variant.id == variant.id%} selected {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div>
 </div>
 </div>
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions ">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 <div class="ecom-collection__product-quick-shop-add-to-cart-wrapper {% if hide_quickshop or view_more_only %} ecom-collection__product-quick-shop--force-show{% endif %} ">
 <div class="ecom-collection__product-form__actions ">
 
 <button type="button" class="ecom-child-element ecom-collection__product-submit ecom-ajax-cart-submit ecom-collection__product-simple-add-to-cart
 ecom-collection__product-add-cart-icon-before"
 data-text-add-cart="Add to cart"
 data-text-unavailable="Unavailable"
 data-text-sold-out="Sold out"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 data-text-pre-order="Pre order"
 
 >
 
 <span class="ecom-add-to-cart-text">
 Add to cart
 </span>
 </button>
 </div>
 
 </div>
 {%- endif -%}
 </form> {% comment %} End form {% endcomment %}
 <script class="product-json" type="application/json">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% endif %}
 
 {% endcapture%}
 {% capture product_actions%}
 <div class="ecom-collection__product--actions" data-layout="{{quickshop_layout}}">
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions "
 
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% if view_more_only %}
 
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 
 {% elsif product.has_only_default_variant == true and product.available == false %}
 
 
 <div class="ecom-collection__product-form__actions--soldout ecom-collection__product-sold-out-before ecom-child-element"
 
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-sold-out-text">
 Sold out
 </span>
 </div>
 
 {% elsif product.has_only_default_variant %}
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 
 
 <a data-no-instant href="{{ecom_root_url}}cart/add?id={{ product.variants.first.id }}&quantity=1"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 class="ecom-collection__product-submit ecom-collection__product-form__actions--add ecom-collection__product-simple-add-to-cart ecom-ajax-cart-simple ecom-collection__product-add-cart-icon-before ecom-child-element" 
 data-id="{{ product.variants.first.id }}"
 data-handle="{{ product.handle }}" data-pid="{{ product.id }}" title="{{ product.title | escape }}">
 
 <span class="ecom-add-to-cart-text">
 {%- if product.variants.first.inventory_management and product.variants.first.inventory_quantity <= 0 and product.variants.first.inventory_policy == 'continue' -%}
 Pre order
 {%-else-%}
 Add to cart
 {%- endif -%}
 </span>
 </a>
 
 {%- endif -%}
 {% else %}
 
 
 
 {% endif %}
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 </div>
 {% endcapture %}
 <div class="ecom-collection__product-item ecom-swiper-slide" data-product-handle="{{product.handle}}" data-style="{{product_style}}"{% unless product.has_only_default_variant %} ec-variant-init{% endunless %}>
 <div
 class="ecom-collection__product-item--wrapper {% if product_style == 'horizontal'%} ecom-d-flex {% else %} ecom-flex-column {% endif %}">
 <div class="ecom-collection__product-media-wrapper ecom-enable-hover--mobile {% if product_style == 'horizontal'%} ecom-d-flex{% endif %} "
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <a href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}" target="" title="{{product.title | escape }}" class="ecom-collection__product-item--inner ecom-image-default">
 {%- if product.featured_media -%}
 {%- liquid
 assign featured_media_aspect_ratio = product.featured_media.aspect_ratio
 if product.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <div class="ecom-collection__product-media--container">
 <div
 class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--square
 {% if product.media[1] != nil %}ecom-collection__product-media--hover-effect{% endif %}"
 
 
 >
 <img srcset="{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.featured_media | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.featured_media.alt | escape }}"
 loading='lazy'
 class="ecom-collection__product-media-image"
 width="{{ product.featured_media.width }}"
 height="{{ product.featured_media.height }}"
 />
 
 {%- if product.media[1] != nil -%}
 <img srcset="{%- if product.media[1].width >= 533 -%}{{ product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.media[1].width >= 720 -%}{{ product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.media[1].width >= 940 -%}{{ product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.media[1].width >= 1066 -%}{{ product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.media[1] | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.media[1].alt | escape }}"
 loading='lazy'
 class="ecom-collection__product-secondary-media"
 width="{{ product.media[1].width }}"
 height="{{ product.media[1].height }}"
 />
 {%- endif -%}
 
 </div>
 </div>
 {% else %}
 <div class="ecom-collection__product-media--container">
 <div class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--square"
 
 
 >
 {{ 'product-1' | placeholder_svg_tag: 'ecom-colection__product-svg-placeholder' }}
 </div>
 </div>
 {% endif %}
 
 <div class="ecom-collection__product-badge">
 
 
 <span class="ecom-collection__product-badge--sold-out" aria-hidden="true" style="{%- if product.available == true -%}display: none; {%- endif -%}">
 Sold out
 </span>
 <span class="ecom-collection__product-badge--sale" aria-hidden="true" style="{%- unless product.compare_at_price > product.price and product.available -%}display: none;{%- endunless -%}">
 Sale
 </span>
 
 {% if badge_tags %}
 {% for badge in badge_tags %}
 {% for original_tag in product.tags %}
 {% assign tag = original_tag | replace: ' ', ' ' %}
 {% if tag == badge %}
 <span class="ecom-collection__product-badge--custom ecom-collection__product-badge--{{ badge | handleize}}" aria-hidden="true">
 {{ badge }}
 </span>
 {% endif %}
 {% endfor %}
 {%- assign bad = badge | strip -%}
 {% endfor %}
 {% endif %}
 
 {%- if product.has_only_default_variant -%}
 {%- if product.compare_at_price != null and product.compare_at_price > product.price -%}
 {%- assign sale = product.compare_at_price | minus: product.price | times: 100.0 | divided_by: product.compare_at_price | round -%}
 <span class="ecom-collection__product-price--bage-sale">
 -{{sale}}%
 </span>
 {%- endif -%}
 {%- else -%}
 {%- if product.selected_or_first_available_variant.compare_at_price != null and product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
 {%- assign sale = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | times: 100.0 | divided_by: product.selected_or_first_available_variant.compare_at_price | round -%}
 {%else%}
 {%- assign sale = null %}
 {%- endif -%}
 <span class="ecom-collection__product-price--bage-sale" style="{% unless sale %}display:none{% endunless %}">
 -{{sale}}%
 </span>
 {%- endif -%}
 </div>
 </a>
 
 <div class="ecom-collection__product-group-button-action " style="justify-content: start">
 
 
 </div>
 </div>
 <div class="ecom-collection__product-item--information">
 <div class="ecom-collection__product-item--information-wrapper ecom-flex ecom-column">
 {% if product_style == 'absolute' %}
 {{product_actions}}
 {{ product_picker}}
 {% endif %}
 
 
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <h3
 class="ecom-collection__product-title-tag ecom-child-element"
 
 >
 <a
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{product.title | escape }}"
 target=""
 class="ecom-collection__product-item-information-title ecom-title-one-row"
 >
 {{ product.title | strip_html}}
 </a>
 </h3>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 
 
 {% assign showPriceWhenNotLogin = true %}
 {% if undefined and customer == blank%}
 {% assign showPriceWhenNotLogin = false %}
 {% endif %}
 {% if showPriceWhenNotLogin == false and undefined %}
 {% assign showOnLive = true %}
 {% else %}
 {% assign showOnLive = false %}
 {% endif %}
 {% assign showPreview = false %}
 {% if showOnLive or showPreview %}
 <div class="ecom-collection__product-login-to-see">
 undefined
 </div>
 {% endif %}
 {% if true and showPriceWhenNotLogin %}
 <div class="ecom-collection__product-prices ecom-child-element" >
 {% capture ground_price %}
 
 {% endcapture %}
 {%- assign target = product.selected_or_first_available_variant -%}
 
 {%- assign target = product.selected_or_first_available_variant -%}
 <div class="ecom-collection__product-price-wrapper">
 <span
 class="ecom-collection__product-price--regular ecom-collection__product--compare-at-price"
 {%- if product.compare_at_price == nil or product.compare_at_price <= product.price -%} style="display:none" {% endif %}
 >
 {% if settings.currency_code_enabled == true %} {{ target.compare_at_price | money_with_currency }} {% else %} {{ target.compare_at_price | money }} {% endif %}
 </span>
 <span class="ecom-collection__product-price{% if target.compare_at_price > target.price %} ecom-collection__product-price--sale{% endif %}"
 {% if false %} bss-b2b-product-price bss-b2b-variant-price {% endif %}
 {% if false %}
 bss-b2b-product-id="{{ product.id }}"
 bss-b2b-variant-id="{{ product.selected_or_first_available_variant.id }}"
 {% endif %}
 >{% if settings.currency_code_enabled == true %} {{target.price | money_with_currency }} {% else %} {{target.price | money }} {% endif %}</span>
 </div>
 {{ ground_price }}
 
 </div>
 {% endif %}
 
 {% if product_style != 'absolute'%}
 {{ product_picker }}
 {% endif %}
 {% if product_style == 'horizontal' or product_style == 'vertical' or product_layout == 'list' %}
 {{product_actions}}
 {% endif %}
 
 </div>
 </div>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 {% endfor %}
 </div>
 {% else %}
 <div class="ecom-collection__product--wrapper-items ecom-collection__product--no-item ecom-collection-product__layout-slider" >
 <div class="ecom-collection__product-item" >
 There are no product yet!
 </div>
 </div>
 {% endif %}
 
 {% if current_product %}
 {% assign product = current_product %}
 {% endif %}
 
 
 {% assign collection = tmp_collection %}
 {% assign product = tmp_product %}
 </div><div class="ecom-swiper-navigation-position"><button class="ecom-swiper-button ecom-swiper-button-prev" style="--ecom-position: absolute; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M192 448c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25l160-160c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25L77.25 256l137.4 137.4c12.5 12.5 12.5 32.75 0 45.25C208.4 444.9 200.2 448 192 448z"></path></svg></button><button class="ecom-swiper-button ecom-swiper-button-next" style="--ecom-position: absolute; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M64 448c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L178.8 256L41.38 118.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l160 160c12.5 12.5 12.5 32.75 0 45.25l-160 160C80.38 444.9 72.19 448 64 448z"></path></svg></button></div><div class="ecom-swiper-pagination-position ecom-swiper-pagination" style="display: none;"></div></div></div></div></div> <div class="ecom-animation ecom-block ecom-core core__block ecom-vuh7xl98gmq ec-related-block-demo" data-core-is="block" ecom-scrolling-animation="[{&quot;css&quot;:&quot;translateX&quot;,&quot;value&quot;:[]}]"><div class="ecom-element ecom-collection ecom-collection__product" deep="1"><div class="ecom-collection__product-wrapper"><div class="ecom-collection__product-container ecom-collection__product-container_featured"><div class="ecom-collection__product-main ecom-swiper-container ecom-collection_product_template_featured" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;loop&quot;:false,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1.3,&quot;spaceBetween&quot;:15},&quot;768&quot;:{&quot;slidesPerView&quot;:3,&quot;spaceBetween&quot;:15},&quot;1025&quot;:{&quot;slidesPerView&quot;:4,&quot;spaceBetween&quot;:30}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:0,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:800,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;}}" data-week="[%-W] week%!W" data-day="[%-d] day%!D" data-hour="[%-H] hr%!H" data-minute="[%-M] min%!M" data-second="[%-S] sec%!S" data-sale="-{{sale}}%" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{-review_platform-}}" data-countdown-shows="day,hour,minute,second" data-translate="false">
 {%- capture badge_tags -%}Hot,new{%- endcapture -%}
 {%- liquid
 assign colors = shop.metafields.ecomposer.colors
 assign badge_tags = badge_tags | strip | split: ','
 assign tmp_collection = collection
 assign tmp_product = product
 assign enable_hook = shop.metafields.ecomposer.enable_hook.value
 -%}
 
 
 {%- capture limit-%}6{% endcapture %}
 {%- if limit == blank -%}
 {% assign limit = 12 %}
 {% else %}
 {% assign limit = limit | plus: 0 %}
 {%- endif-%}
 
 {%- capture handle_collection -%}bags{% endcapture%}
 {% assign collection = collections[handle_collection] %}
 {%- if handle_collection and collection.handle != blank -%}
 {% assign products = collection.products %}
 {% else %}
 {% assign products = collections.all.products %}
 {% assign collection = collections.all%}
 {%- endif -%}
 {% assign is_collection = true %}
 
 {% if false and featured == product %}
 {% if true %}
 {% assign products = blank %}
 {% endif %}
 {%- if recommendations.performed? and recommendations.products_count > 0 -%}
 {% assign products = recommendations.products %}
 {%- endif -%}
 {% endif %}
 {% capture quickshop_layout%}full{% endcapture %}
 {% capture product_style%}absolute{% endcapture%}
 {%- assign view_more_only = false -%}
 {% capture product_layout %}slider{% endcapture %}
 {% assign check_min = 0%}
 {% if products and products.size > check_min and products != blank %}
 <div class="ecom-swiper-wrapper
 ecom-collection__product--wrapper-items ecom-collection-product__layout-slider
 "
 data-grid-column="4"
 data-grid-column-tablet="3"
 data-grid-column-mobile="1.3"
 
 
 >
 {% assign ecom_count = 0 %}
 {% for p in products %}
 {% assign product = p %}
 {% if p.handle %}
 {% assign product = p %}
 {% else %}
 {% assign product = all_products[p] %}
 {% endif %}
 {% if product.url == blank %} {% continue %} {% endif %}
 {% if ecom_count >= limit %}{% break %}{% endif %}
 
 
 {% assign ecom_count = ecom_count | plus: 1 %}
 {%- capture swatch_option -%}Color{%- endcapture -%}
 {% assign hide_quickshop = true %}
 {% assign other_option_layout = 'hide' %}
 {% capture product_picker%}
 
 {%- if product.has_only_default_variant == false-%}
 {% if quickshop_layout != 'full' and product.options.size > 1%}
 {% assign hide_quickshop = false %}
 {% endif %}
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <div class="ecom-collection__product-variants" data-picker-type="image">
 <button class="ecom-collection__product-close"></button>
 <form class="ecom-collection__product-form ecom-product-form" product_id="{{product.id}}" data-product_id="{{product.id}}" data-product-id="{{product.id}}" data-handle="{{product.handle}}">
 <div class="ecom-child-element" >
 {% assign variant_selected = product.first_available_variant%}
 
 {%- capture swatch_option_temp -%}Color{%- endcapture -%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {% assign swatch_option = swatch_option | split: ',' %}
 {% assign option_index = current_option.position | minus: 1 %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-main ecom-collection__product-picker-option-{{current_option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-collection__product-swatch-item ecom-collection__product-picker-images-item {%comment%}{% if option_value == variant_selected[index] %}ecom-product-swatch-item--active ecom-button-active{% endif %}{%endcomment%}" data-value="{{ option_value | escape }}">
 <span class="ecom-collection__product-swatch-item--wrapper"></span>
 <img src="{{ variant | img_url: "120x120", crop: 'center' }}" alt=" {{ option_value }}" loading='lazy'/>
 </li>
 {%- endfor -%}
 </ul>
 
 </div>
 {% endif %}
 {% endfor %}
 {%- if other_option_layout != 'hide' -%}
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}{% continue%}{%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-other ecom-collection__product-picker-option-{{option.name | handleize }}">
 
 
 <select class="ecom-collection__product-swatch-select ecom-collection__product-picker-hide-list" data-option-index="{{ option_index }}">
 {% for value in option.values %}
 <option value="{{value | escape }}" {% if value == variant_selected[index] %}selected="selected"{% endif %}>
 {{value}}
 </option>
 {% endfor %}
 </select>
 
 </div>
 {%- endfor -%}
 </div>
 {%- endif -%}
 
 
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 <div class="ecom-collection__product-picker-selection" style="display:none;">
 <select name="variant_id" data-product-id="{{product.id}}" id="ecom-variant-selector-{{product.id}}-ecom-vuh7xl98gmq">
 {% for variant in product.variants %}
 <option value="{{variant.id}}" {% if product.first_available_variant.id == variant.id%} selected {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div>
 </div>
 </div>
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions ">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 <div class="ecom-collection__product-quick-shop-add-to-cart-wrapper {% if hide_quickshop or view_more_only %} ecom-collection__product-quick-shop--force-show{% endif %} ">
 <div class="ecom-collection__product-form__actions ">
 
 <button type="button" class="ecom-child-element ecom-collection__product-submit ecom-ajax-cart-submit ecom-collection__product-simple-add-to-cart
 ecom-collection__product-add-cart-icon-before"
 data-text-add-cart="Add to cart"
 data-text-unavailable="Unavailable"
 data-text-sold-out="Sold out"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 data-text-pre-order="Pre order"
 
 >
 
 <span class="ecom-add-to-cart-text">
 Add to cart
 </span>
 </button>
 </div>
 
 </div>
 {%- endif -%}
 </form> {% comment %} End form {% endcomment %}
 <script class="product-json" type="application/json">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% endif %}
 
 {% endcapture%}
 {% capture product_actions%}
 <div class="ecom-collection__product--actions" data-layout="{{quickshop_layout}}">
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions "
 
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% if view_more_only %}
 
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 
 {% elsif product.has_only_default_variant == true and product.available == false %}
 
 
 <div class="ecom-collection__product-form__actions--soldout ecom-collection__product-sold-out-before ecom-child-element"
 
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-sold-out-text">
 Sold out
 </span>
 </div>
 
 {% elsif product.has_only_default_variant %}
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 
 
 <a data-no-instant href="{{ecom_root_url}}cart/add?id={{ product.variants.first.id }}&quantity=1"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 class="ecom-collection__product-submit ecom-collection__product-form__actions--add ecom-collection__product-simple-add-to-cart ecom-ajax-cart-simple ecom-collection__product-add-cart-icon-before ecom-child-element" 
 data-id="{{ product.variants.first.id }}"
 data-handle="{{ product.handle }}" data-pid="{{ product.id }}" title="{{ product.title | escape }}">
 
 <span class="ecom-add-to-cart-text">
 {%- if product.variants.first.inventory_management and product.variants.first.inventory_quantity <= 0 and product.variants.first.inventory_policy == 'continue' -%}
 Pre order
 {%-else-%}
 Add to cart
 {%- endif -%}
 </span>
 </a>
 
 {%- endif -%}
 {% else %}
 
 
 <button class="ecom-collection__product-form__actions--quickshop ecom-collection__product-quickshop-icon-before
 {% if hide_quickshop %} ecom-collection__product-quick-shop--force-hide{% endif %} ecom-child-element" type="button">
 
 <span class="ecom-collection__product-form__actions--quickshop-text">
 Quick Shop
 </span>
 </button>
 
 
 {% endif %}
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 </div>
 {% endcapture %}
 <div class="ecom-collection__product-item ecom-swiper-slide" data-product-handle="{{product.handle}}" data-style="{{product_style}}"{% unless product.has_only_default_variant %} ec-variant-init{% endunless %}>
 <div
 class="ecom-collection__product-item--wrapper {% if product_style == 'horizontal'%} ecom-d-flex {% else %} ecom-flex-column {% endif %}">
 <div class="ecom-collection__product-media-wrapper {% if product_style == 'horizontal'%} ecom-d-flex{% endif %} "
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <a href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}" target="" title="{{product.title | escape }}" class="ecom-collection__product-item--inner ecom-image-default">
 {%- if product.featured_media -%}
 {%- liquid
 assign featured_media_aspect_ratio = product.featured_media.aspect_ratio
 if product.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <div class="ecom-collection__product-media--container">
 <div
 class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt
 {% if product.media[1] != nil %}ecom-collection__product-media--hover-effect{% endif %}"
 style="padding-bottom: {{ 1 | divided_by: featured_media_aspect_ratio | times: 100 }}%;"
 
 >
 <img srcset="{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.featured_media | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.featured_media.alt | escape }}"
 loading='lazy'
 class="ecom-collection__product-media-image"
 width="{{ product.featured_media.width }}"
 height="{{ product.featured_media.height }}"
 />
 
 {%- if product.media[1] != nil -%}
 <img srcset="{%- if product.media[1].width >= 533 -%}{{ product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.media[1].width >= 720 -%}{{ product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.media[1].width >= 940 -%}{{ product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.media[1].width >= 1066 -%}{{ product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.media[1] | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.media[1].alt | escape }}"
 loading='lazy'
 class="ecom-collection__product-secondary-media"
 width="{{ product.media[1].width }}"
 height="{{ product.media[1].height }}"
 />
 {%- endif -%}
 
 </div>
 </div>
 {% else %}
 <div class="ecom-collection__product-media--container">
 <div class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt"
 
 style="padding-bottom: 85%;"
 >
 {{ 'product-1' | placeholder_svg_tag: 'ecom-colection__product-svg-placeholder' }}
 </div>
 </div>
 {% endif %}
 
 <div class="ecom-collection__product-badge">
 
 
 <span class="ecom-collection__product-badge--sold-out" aria-hidden="true" style="{%- if product.available == true -%}display: none; {%- endif -%}">
 sold
 </span>
 <span class="ecom-collection__product-badge--sale" aria-hidden="true" style="{%- unless product.compare_at_price > product.price and product.available -%}display: none;{%- endunless -%}">
 sale
 </span>
 
 {% if badge_tags %}
 {% for badge in badge_tags %}
 {% for original_tag in product.tags %}
 {% assign tag = original_tag | replace: ' ', ' ' %}
 {% if tag == badge %}
 <span class="ecom-collection__product-badge--custom ecom-collection__product-badge--{{ badge | handleize}}" aria-hidden="true">
 {{ badge }}
 </span>
 {% endif %}
 {% endfor %}
 {%- assign bad = badge | strip -%}
 {% endfor %}
 {% endif %}
 
 {%- if product.has_only_default_variant -%}
 {%- if product.compare_at_price != null and product.compare_at_price > product.price -%}
 {%- assign sale = product.compare_at_price | minus: product.price | times: 100.0 | divided_by: product.compare_at_price | round -%}
 <span class="ecom-collection__product-price--bage-sale">
 -{{sale}}%
 </span>
 {%- endif -%}
 {%- else -%}
 {%- if product.selected_or_first_available_variant.compare_at_price != null and product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
 {%- assign sale = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | times: 100.0 | divided_by: product.selected_or_first_available_variant.compare_at_price | round -%}
 {%else%}
 {%- assign sale = null %}
 {%- endif -%}
 <span class="ecom-collection__product-price--bage-sale" style="{% unless sale %}display:none{% endunless %}">
 -{{sale}}%
 </span>
 {%- endif -%}
 </div>
 </a>
 
 <div class="ecom-collection__product-group-button-action " style="justify-content: start">
 
 
 </div>
 </div>
 <div class="ecom-collection__product-item--information">
 <div class="ecom-collection__product-item--information-wrapper ecom-flex ecom-column">
 {% if product_style == 'absolute' %}
 {{product_actions}}
 {{ product_picker}}
 {% endif %}
 
 
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <h3
 class="ecom-collection__product-title-tag ecom-child-element"
 
 >
 <a
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{product.title | escape }}"
 target=""
 class="ecom-collection__product-item-information-title "
 >
 {{ product.title | strip_html}}
 </a>
 </h3>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 
 
 {% assign showPriceWhenNotLogin = true %}
 {% if undefined and customer == blank%}
 {% assign showPriceWhenNotLogin = false %}
 {% endif %}
 {% if showPriceWhenNotLogin == false and undefined %}
 {% assign showOnLive = true %}
 {% else %}
 {% assign showOnLive = false %}
 {% endif %}
 {% assign showPreview = false %}
 {% if showOnLive or showPreview %}
 <div class="ecom-collection__product-login-to-see">
 undefined
 </div>
 {% endif %}
 {% if true and showPriceWhenNotLogin %}
 <div class="ecom-collection__product-prices ecom-child-element" >
 {% capture ground_price %}
 
 {% endcapture %}
 {%- assign target = product.selected_or_first_available_variant -%}
 
 {%- assign target = product.selected_or_first_available_variant -%}
 <div class="ecom-collection__product-price-wrapper">
 <span
 class="ecom-collection__product-price--regular ecom-collection__product--compare-at-price"
 {%- if product.compare_at_price == nil or product.compare_at_price <= product.price -%} style="display:none" {% endif %}
 >
 {% if settings.currency_code_enabled == true %} {{ target.compare_at_price | money_with_currency }} {% else %} {{ target.compare_at_price | money }} {% endif %}
 </span>
 <span class="ecom-collection__product-price{% if target.compare_at_price > target.price %} ecom-collection__product-price--sale{% endif %}"
 {% if false %} bss-b2b-product-price bss-b2b-variant-price {% endif %}
 {% if false %}
 bss-b2b-product-id="{{ product.id }}"
 bss-b2b-variant-id="{{ product.selected_or_first_available_variant.id }}"
 {% endif %}
 >{% if settings.currency_code_enabled == true %} {{target.price | money_with_currency }} {% else %} {{target.price | money }} {% endif %}</span>
 </div>
 {{ ground_price }}
 
 </div>
 {% endif %}
 
 {% if product_style != 'absolute'%}
 {{ product_picker }}
 {% endif %}
 {% if product_style == 'horizontal' or product_style == 'vertical' or product_layout == 'list' %}
 {{product_actions}}
 {% endif %}
 
 </div>
 </div>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 {% endfor %}
 </div>
 {% else %}
 <div class="ecom-collection__product--wrapper-items ecom-collection__product--no-item ecom-collection-product__layout-slider" >
 <div class="ecom-collection__product-item" >
 There are no product yet!
 </div>
 </div>
 {% endif %}
 
 
 {% assign collection = tmp_collection %}
 {% assign product = tmp_product %}
 </div><div class="ecom-swiper-navigation-position"><button class="ecom-swiper-button ecom-swiper-button-prev" style="--ecom-position: absolute; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M192 448c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25l160-160c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25L77.25 256l137.4 137.4c12.5 12.5 12.5 32.75 0 45.25C208.4 444.9 200.2 448 192 448z"></path></svg></button><button class="ecom-swiper-button ecom-swiper-button-next" style="--ecom-position: absolute; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M64 448c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L178.8 256L41.38 118.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l160 160c12.5 12.5 12.5 32.75 0 45.25l-160 160C80.38 444.9 72.19 448 64 448z"></path></svg></button></div><div class="ecom-swiper-pagination-position ecom-swiper-pagination" style="display: none;"></div></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
</div></div>
{% schema %}
{
 "name": "xiangqing",
 "settings": [
 {
 "type": "header",
 "content": "The section was generated by [Ecomposer](https:\/\/ecomposer.io).",
 "info": "\n EComposer is a Visual Page Builder app on Shopify AppStore.\n It provides 100+ pre-built templates in library,\n so you can build unlimited pages that you can imagine with it.\n Please don't add Shopify sections to EComposer's page in the theme customize. If you do this, Shopify sections will be deleted when you republish your page in EComposer\n "
 },
 {
 "type": "header",
 "content": "[EDIT WITH EComposer APP \u2192](https:\/\/ecomposer.app\/shop?open_builder=1&page=product&id=preview-template-product&utm_source=theme-customize)",
 "info": "(*You must install the app to start editing this section [Learn more](https:\/\/help.ecomposer.io\/docs\/getting-started\/installation\/))"
 }
 ],
 "locales": {}
}
{% endschema %}