{%- render 'section-blog-posts' -%}

{% schema %}
{
  "name": "t:labels.blog_posts",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:labels.heading",
      "default": "From the blog"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:labels.heading_size",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "t:labels.sizes.small"
        },
        {
          "value": "h2",
          "label": "t:labels.sizes.medium"
        },
        {
          "value": "h1",
          "label": "t:labels.sizes.large"
        },
        {
          "value": "h0",
          "label": "t:labels.sizes.extra_large"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_position",
      "label": "t:labels.heading_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "center",
          "label": "t:labels.alignments.center"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    },
    {
      "id": "blog",
      "type": "blog",
      "label": "t:labels.blog"
    },
    {
      "type": "checkbox",
      "id": "blog_show_tags",
      "label": "t:actions.show_tags",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_date",
      "label": "t:actions.show_date",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_comments",
      "label": "t:actions.show_comment_count",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "blog_show_author",
      "label": "t:actions.show_author",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "blog_show_excerpt",
      "label": "t:actions.show_excerpt",
      "default": false
    },
    {
      "type": "select",
      "id": "blog_image_size",
      "label": "t:actions.force_image_size",
      "default": "wide",
      "options": [
        {
          "value": "natural",
          "label": "t:labels.natural"
        },
        {
          "value": "square",
          "label": "t:labels.square_11"
        },
        {
          "value": "landscape",
          "label": "t:labels.landscape_43"
        },
        {
          "value": "portrait",
          "label": "t:labels.portrait_23"
        },
        {
          "value": "wide",
          "label": "t:labels.wide_16_9"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:actions.show_section_divider",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:labels.blog_posts",
      "settings": {
        "blog": "News"
      }
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
