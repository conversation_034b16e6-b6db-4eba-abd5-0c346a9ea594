{%- render 'section-scrolling-text' -%}

{% schema %}
{
  "name": "t:labels.scrolling_text",
  "class": "index-section--flush",
  "settings": [
    {
      "type": "text",
      "id": "text",
      "label": "t:labels.text",
      "default": "Free shipping and returns"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:labels.link"
    },
    {
      "type": "range",
      "id": "text_size",
      "label": "t:labels.text_size",
      "default": 70,
      "min": 20,
      "max": 150,
      "step": 2,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "text_spacing",
      "label": "t:actions.add_spacing",
      "default": true
    },
    {
      "type": "select",
      "id": "direction",
      "label": "t:labels.direction",
      "default": "left",
      "options": [
        {
          "label": "t:labels.alignments.left",
          "value": "left"
        },
        {
          "label": "t:labels.alignments.right",
          "value": "right"
        }
      ]
    },
    {
      "type": "range",
      "id": "speed",
      "label": "t:labels.speed",
      "default": 200,
      "min": 50,
      "max": 300,
      "step": 10,
      "unit": "s"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:labels.color_scheme",
      "default": "1",
      "options": [
        {
          "value": "none",
          "label": "t:labels.none"
        },
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:labels.scrolling_text"
    }
  ],
  "disabled_on": {
    "groups": [
      "custom.popups"
    ]
  }
}
{% endschema %}
