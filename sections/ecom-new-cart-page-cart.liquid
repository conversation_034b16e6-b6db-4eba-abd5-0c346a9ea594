{% comment %}
 EComposer (https://ecomposer.io)
 You SHOULD NOT modify source code in this page because
 It is automatically generated from EComposer
 At 2025-01-24 03:04:30
 {% endcomment %} 
 
 {% assign ecom_root_url = routes.root_url %}
 {% if ecom_root_url != '/'%}
 {% assign ecom_root_url = routes.root_url | append: '/' %}
 {% endif %}
 <link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-modal_css" href="https://cdn.ecomposer.app/vendors/css/ecom_modal.css" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.6" /><noscript><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-modal_css" href="https://cdn.ecomposer.app/vendors/css/ecom_modal.css" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.6" /></noscript><script type="text/javascript" asyc="async" id="ecom-vendors-countdown_js" src="https://cdn.ecomposer.app/vendors/js/ecom-countdown.js?ver=2.0" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-modal_js" src="https://cdn.ecomposer.app/vendors/js/ecom_modal.js" ></script>
{%capture section_id %}ecom-new-cart-page-cart{% endcapture%}{% if section and section_id == section.id and headless == true %}
{{ content_for_header }}
{% render 'ecom_header', ECOM_THEME: true %}{% endif %}<link href="https://fonts.googleapis.com/css?family=Jost:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Libre+Franklin:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/>
{{'ecom-67581ab71983b7573809c9a2.css' | asset_url | stylesheet_tag }}
<script src="{{'ecom-67581ab71983b7573809c9a2.js' | asset_url }}" defer="defer"></script>
<script type="text/javascript" class="ecom-page-info">
 window.EComposer = window.EComposer || {};
 window.EComposer.TEMPLATE_ID="67581ab71983b7573809c9a2";
 window.EComposer.TEMPLATE = {"template_id":"67581ab71983b7573809c9a2","title":"New cart page","type":"cart","slug":"ecom-new-cart-page","plan_id":2};
 </script>
<div class="ecom-builder" id="ecom-new-cart-page"><div class="ecom-sections" data-section-id="{{section.id}}"><section class="ecom-row ecom-core ecom-section ecom-rgnd8whr86m" data-id="ecom-rgnd8whr86m" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="1" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-gribdsht8m9"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-9cc83dknhd" data-core-is="block"><div class="ecom__element element__heading ecom-element ecom-html" data-stopdrag="true" deep="1"><h3 class="ecom__heading ecom-db">Your cart</h3></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-qlmc2jjoirq" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-nxujwk1mec" data-id="ecom-nxujwk1mec" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="2" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-pwgr072qie"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block ecom-w9zvmtqf7t" data-core-is="block"><div class="ecom-element ecom-base-iconlist" deep="2"><ul class="ecom-iconlist--list" data-layout="default"><li class="ecom-iconlist--item ecom-html"><div class="ecom-iconlist--container"><span class="ecom-iconlist--icon" data-image="false"><svg width="16" height="21" viewBox="0 0 16 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M8.02406 20.8C11.4914 20.8 14.6075 18.5885 15.6464 15.285C15.8662 14.5862 16 13.8445 16 13.0743C16 8.76575 12.4676 5.28919 12.4676 5.28919C12.4676 5.28919 12.711 6.69657 12.5596 7.89456C12.4082 9.09255 11.8503 9.82797 11.8503 9.82797C11.8503 9.82797 12.2577 7.94688 11.7164 6.68569C11.0933 5.2341 9.93114 4.56071 9.48472 3.2537C8.9709 1.74942 9.74083 0 9.74083 0C9.74083 0 7.20843 0.977181 5.37847 3.49885C3.30303 6.35881 4.70157 10.0434 4.70157 10.0434C4.70157 10.0434 4.50813 9.02569 3.45925 8.20112C2.41037 7.37655 2.83528 6.09771 2.83528 6.09771C2.83528 6.09771 0 9.27092 0 13.0744C0 14.1069 0.296096 15.1483 0.727431 16.1175C2.00287 18.9832 4.88367 20.8 8.02406 20.8Z" fill="#D10028"></path><path d="M12.4676 5.28919C12.4676 5.28919 12.5997 6.05412 12.6085 6.93601C13.4351 8.31861 14.2147 10.159 14.2147 12.1829C14.2147 12.953 14.0809 13.6948 13.8611 14.3936C12.8222 17.697 9.7061 19.9086 6.23873 19.9086C5.32175 19.9084 4.41149 19.7525 3.54691 19.4473C4.87276 20.3299 6.43065 20.8006 8.02406 20.8C11.4914 20.8 14.6075 18.5885 15.6464 15.285C15.8662 14.5862 16 13.8445 16 13.0743C15.9999 8.76575 12.4676 5.28919 12.4676 5.28919Z" fill="#B7022D"></path><path d="M12.0257 14.9588C12.0257 12.7831 10.5964 11.3799 9.70646 10.4796C8.12172 8.87619 8.40308 7.0518 8.40308 7.0518C8.40308 7.0518 5.29706 9.71842 4.80904 11.9302C4.34245 14.0457 5.51621 15.2216 5.51621 15.2216C5.51621 15.2216 4.96927 15.0108 4.62917 14.633C4.19596 14.1519 4.04028 13.569 4.04028 13.569C4.04028 13.569 3.85925 14.0064 4.09661 15.503C4.26532 16.5666 4.81877 17.3627 5.46818 17.9485C7.04659 19.3723 9.50811 19.2345 10.9387 17.6626C11.5517 16.9892 12.0257 16.0996 12.0257 14.9588Z" fill="#FFA91A"></path><path d="M5.89711 15.0238C5.8498 15.0238 5.80258 15.0052 5.76749 14.9682C5.69474 14.8915 5.05631 14.1825 5.05631 12.9073C5.05631 12.6155 5.0905 12.3126 5.15781 12.0071C5.25645 11.5599 5.48024 11.0555 5.82284 10.5077C5.84792 10.4676 5.88793 10.4391 5.93407 10.4284C5.98021 10.4178 6.02869 10.4259 6.06886 10.4509C6.10902 10.4759 6.13758 10.5159 6.14824 10.562C6.15891 10.6081 6.1508 10.6565 6.12572 10.6966C5.80472 11.2098 5.59646 11.6764 5.50657 12.0837C5.4448 12.3641 5.41346 12.6411 5.41346 12.9073C5.41346 14.0639 6.00164 14.6965 6.02672 14.7229C6.05074 14.7482 6.0668 14.78 6.07293 14.8143C6.07905 14.8486 6.07496 14.884 6.06117 14.916C6.04738 14.948 6.02448 14.9753 5.99532 14.9945C5.96617 15.0136 5.93202 15.0238 5.89711 15.0238ZM6.3762 10.1911C6.3432 10.1911 6.31086 10.182 6.28275 10.1647C6.25464 10.1475 6.23187 10.1227 6.21698 10.0933C6.20208 10.0639 6.19563 10.031 6.19836 9.99815C6.20108 9.96531 6.21286 9.93386 6.23239 9.90731L6.28479 9.83662C6.29881 9.81785 6.3164 9.80203 6.33654 9.79005C6.35669 9.77807 6.37899 9.77017 6.4022 9.7668C6.4254 9.76343 6.44904 9.76466 6.47176 9.77041C6.49449 9.77617 6.51586 9.78634 6.53465 9.80034C6.55344 9.81434 6.56929 9.8319 6.58128 9.85201C6.59328 9.87213 6.60119 9.89441 6.60456 9.91758C6.60794 9.94075 6.60671 9.96435 6.60094 9.98705C6.59518 10.0097 6.585 10.0311 6.57098 10.0498L6.5201 10.1184C6.50355 10.141 6.48188 10.1593 6.45688 10.172C6.43187 10.1846 6.40423 10.1912 6.3762 10.1911Z" fill="#FFC91D"></path><path d="M9.70646 10.4796C8.12172 8.87619 8.40308 7.0518 8.40308 7.0518C8.40308 7.0518 7.41633 7.8991 6.4677 9.03451C7.34108 9.93664 8.45495 11.2551 8.45495 13.1759C8.45495 14.3167 7.98094 15.2064 7.36795 15.8799C6.66864 16.6483 5.72286 17.0729 4.75441 17.1411C4.9649 17.446 5.20851 17.7144 5.4681 17.9486C7.0465 19.3724 9.50802 19.2346 10.9386 17.6627C11.5516 16.9892 12.0256 16.0996 12.0256 14.9588C12.0257 12.7831 10.5964 11.3799 9.70646 10.4796Z" fill="#F79219"></path><path d="M9.70646 10.4796C8.12172 8.87619 8.40308 7.0518 8.40308 7.0518C8.40308 7.0518 7.78027 7.5871 7.04882 8.37593C7.2453 8.77252 7.52488 9.18721 7.92113 9.58818C8.81103 10.4885 10.2404 11.8917 10.2404 14.0673C10.2404 15.2082 9.76636 16.0978 9.15337 16.7713C8.20009 17.8187 6.78924 18.2283 5.48684 17.9646C7.06596 19.3708 9.51364 19.2284 10.9387 17.6626C11.5517 16.9892 12.0257 16.0996 12.0257 14.9588C12.0257 12.7831 10.5964 11.3799 9.70646 10.4796Z" fill="#EF7816"></path></svg></span><div class="ecom-iconlist-content"><p class="ecom-iconlist--title">Limited stock – checkout in before vanishes!</p></div></div></li></ul></div></div> <div class="ecom-block ecom-core core__block ecom-4rlstrdwew" data-core-is="block" ec-data-title="These products are limited, checkout within"><div class="ecom-element ecom-base-elements ecom-element__countdown" deep="2"><div class="ecom-element__countdown-wrapper ecom-flex"><div class="ecom-element__countdown-container"><div class="ecom-element__countdown--time" data-countdown-to="2031/6/26 12:30 GMT+0700" data-countdown-from="2022/5/01 12:30 GMT+0700" data-evergreen-cd-time="0:0:0" data-evergreen-redirect-url="" data-evergreen-restart="{&quot;type&quot;:&quot;&quot;,&quot;data&quot;:&quot;0:0:0&quot;}" data-countdown-restart="true" data-countdown-type="time" data-trans-week="[%-W] week%!W" data-trans-day="[%-d] day%!d" data-trans-hour="[%-H] hr%!H" data-trans-minute="[%-M] m :" data-trans-second="[%-S] s" data-show-fields="minute,second"></div></div></div></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
 <form action="{{ routes.cart_url }}" class="ecom-cart-form" method="post" enctype="multipart/form-data">
 <section class="ecom-row ecom-core ecom-section ecom-j6gapoywgss" data-id="ecom-j6gapoywgss" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="1" class="core__row--columns"><div class="ecom-column ecom-core core__column--first ecom-gw9xauaad7"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-89xj0cqcrms" data-core-is="block"><div class="ecom-element ecom-cart ecom-cart__product" deep="1"><div class="ecom-cart__product-wrapper"><div class="ecom-cart__product-container">
						{% capture icon_discount %}
							<svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-discount color-foreground-text" viewBox="0 0 12 12" style="width: 10px;">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M7 0h3a2 2 0 012 2v3a1 1 0 01-.3.7l-6 6a1 1 0 01-1.4 0l-4-4a1 1 0 010-1.4l6-6A1 1 0 017 0zm2 2a1 1 0 102 0 1 1 0 00-2 0z" fill="currentColor">
								</path>
							</svg>
						{% endcapture %}
						
						{% assign is_dummy = false %}
						{%- if cart == empty -%}
							<div class="ecom-cart__product-warnings">
								<h2 class="ecom-cart__product-empty-text">Your cart is empty</h2>
								
								<div class="ecom-cart__product-button--continue ecom-flex">
											<a href="/"
												rel=""
												target=""
												title=""
												class="link ecom-cart__product-empty-link ecom-flex"
												>
												<span class="ecom-cart__product-empty-icon"></span>
												<span>Continue shopping</span>
											</a>
										</div>
							</div>
						{%- else -%}
							<div class="ecom-cart__product-items"
							>
								
				<div class="ecom-cart__product-items-heading">
					<div>Product</div>
					<div>Price</div>
					<div>Quantity</div>
					<div>Total</div>
				</div>
			
								{% for item in cart.items %}
							
								<div class="ecom-cart__product-item apo-cart__item" data-line-id="{{item.id}}"> {% comment %}Integration Avis{% endcomment %}
									{% assign image = item.image%}
									{% if is_dummy %}
										{% assign image = item.featured_image %}
									{% endif %}
									<div class="ecom-cart__product-thumbnail {%- unless image -%}ecom-cart__product--no-thumbnail{%- endunless -%}">
										<div class="ecom-cart__product-thumbnail-img">
											{% if image %}
												<img class="ecom-cart__product-image"
													src="{{ image | img_url: '350x' }}"
													alt="{{ image.alt | escape }}"
													loading="lazy"
													width="75"
													height="{{ 75 | divided_by: image.aspect_ratio | ceil }}"
												>
											{% endif %}
										</div>
									</div>
									<div class="ecom-cart__product-informations">
										<div class="ecom-cart__product-information--wrapper ecom-flex">
											<a href="{% if is_dummy == false %}{{ item.product.url }}{% else %}{{item.url}}{% endif %}" class="ecom-cart__product-thumbnail--tablet">
												{% assign image = item.image%}
												{% if is_dummy %}
													{% assign image = item.featured_image %}
												{% endif %}
												{% if image %}
													<img class="ecom-cart__product-image"
														src="{{image | img_url: '350x'}}"
														alt="{{ image.alt | escape }}"
														loading="lazy"
														width="75"
														height="{{ 75 | divided_by: image.aspect_ratio | ceil }}"
													>
												{% endif %}
											</a>
											<div class="ecom-cart__product-infos">
												<a href="{% if is_dummy == false %}{{ item.product.url }}{% else %}{{item.url}}{% endif %}" class="ecom-cart__product-item-name">{% if is_dummy == false %}{{ item.product.title | escape }}{% else %}{{item.title | escape }}{% endif %}</a>
												{% if is_dummy %}
													{% assign has_only_default_variant = item.has_only_default_variant %}
												{% else %}
													{% assign has_only_default_variant = item.product.has_only_default_variant %}
												{% endif %}
												{%- if has_only_default_variant == false or item.properties.size != 0 or item.selling_plan_allocation != nil -%}
												<dl class="ecom-cart__product-options">
														{%- if has_only_default_variant == false -%}
															{%- for option in item.options_with_values -%}
																<div class="ecom-cart__product-product-option">
																	<dt>{{ option.name }}: </dt>
																	<dd>{% if is_dummy%}{{ option.values | first }}{% else %}{{ option.value }}{% endif %}</dd>
																</div>
															{%- endfor -%}
														{%- else -%}
															{% assign live = true %}
															{%- unless live -%}
																<div class="ecom-cart__product-product-option">
																	<dt>Properties:</dt>
																	<dd>value</dd>
																</div>
															{%- endunless -%}
														{%- endif -%}
														{%- for property in item.properties -%}
															{% if property.first contains '_' %}{% continue %}{% endif %} {% comment %} Integration with Avis PLus {% endcomment %}
															{%- assign property_first_char = property.first | slice: 0 -%}
															{%- if property.last != blank -%}
															<div class="ecom-cart__product-product-option">
																<dt>{{ property.first }}:
																</dt>
																<dd>
																{%- if property.last contains '/uploads/' -%}
																	<a href="{{ property.last }}" target="_blank">
																	{{ property.last | split: '/' | last }}
																	</a>
																{%- else -%}
																	{{ property.last }}
																{%- endif -%}
																</dd>
															</div>
															{%- endif -%}
														{%- endfor -%}
													
													
												</dl>
												<p class="product-option">{{ item.selling_plan_allocation.selling_plan.name }}</p>
												{%- endif -%}
												<ul class="ecom-cart__product-discounts" role="list">
												{% if is_dummy and item.compare_at_price > item.price %}
													<li class="ecom-cart__product-product-option">
															{{icon_discount}} <dd style="display: inline-block;">This is demo text</dd>
														</li>
												{% endif %}
													{%- for discount in item.discounts -%}
																<li class="ecom-cart__product-product-option">
																{{ icon_discount }}
																{{ discount.title }}
																</li>
															{%- endfor -%}
												</ul>
												{% assign enable_hook = shop.metafields.ecomposer.enable_hook.value %}
												{% if enable_hook %}
													{% capture the_ecom_cart__hook %}
														{% render 'ecom_cart_line_item_hook', line_item: item %}
													{% endcapture %}
													{% unless the_ecom_cart__hook contains 'Liquid error' %}
														{{ the_ecom_cart__hook }}
													{% endunless %}
												{% endif %}
											</div>
										</div>
										
										<a href="{{ item.url_to_remove }}" class="ecom-cart__product-item-remove-button responsive">
											
														<svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M9.48706 5.0949L7.58196 7L9.48706 8.9051C9.52752 8.94279 9.55996 8.98825 9.58247 9.03876C9.60497 9.08927 9.61707 9.1438 9.61805 9.19908C9.61903 9.25437 9.60886 9.30929 9.58815 9.36056C9.56744 9.41183 9.53661 9.45841 9.49751 9.49751C9.45841 9.53661 9.41184 9.56743 9.36056 9.58814C9.30929 9.60885 9.25437 9.61902 9.19909 9.61805C9.1438 9.61707 9.08927 9.60497 9.03876 9.58247C8.98825 9.55996 8.94279 9.52751 8.9051 9.48706L7 7.58196L5.0949 9.48706C5.01685 9.55979 4.91361 9.59939 4.80693 9.5975C4.70026 9.59562 4.59848 9.55241 4.52303 9.47697C4.44759 9.40152 4.40438 9.29974 4.40249 9.19307C4.40061 9.08639 4.44021 8.98315 4.51294 8.9051L6.41804 7L4.51294 5.0949C4.44021 5.01684 4.40061 4.9136 4.40249 4.80693C4.40438 4.70025 4.44759 4.59847 4.52303 4.52303C4.59848 4.44759 4.70026 4.40437 4.80693 4.40249C4.91361 4.40061 5.01685 4.44021 5.0949 4.51294L7 6.41804L8.9051 4.51294C8.98316 4.44021 9.0864 4.40061 9.19307 4.40249C9.29975 4.40437 9.40153 4.44759 9.47697 4.52303C9.55241 4.59847 9.59563 4.70025 9.59751 4.80693C9.59939 4.9136 9.55979 5.01684 9.48706 5.0949ZM14 7C14 8.38447 13.5895 9.73785 12.8203 10.889C12.0511 12.0401 10.9579 12.9373 9.67878 13.4672C8.3997 13.997 6.99224 14.1356 5.63437 13.8655C4.2765 13.5954 3.02922 12.9287 2.05026 11.9497C1.07129 10.9708 0.404603 9.7235 0.134506 8.36563C-0.13559 7.00776 0.00303287 5.6003 0.532846 4.32122C1.06266 3.04213 1.95987 1.94888 3.11101 1.17971C4.26215 0.410543 5.61553 0 7 0C8.85585 0.0021795 10.6351 0.740377 11.9473 2.05266C13.2596 3.36494 13.9978 5.14415 14 7ZM13.1765 7C13.1765 5.77841 12.8142 4.58425 12.1355 3.56854C11.4569 2.55282 10.4922 1.76117 9.36363 1.29368C8.23503 0.826203 6.99315 0.703888 5.79503 0.942208C4.59691 1.18053 3.49637 1.76878 2.63258 2.63258C1.76878 3.49637 1.18053 4.59691 0.942211 5.79503C0.703891 6.99315 0.826206 8.23503 1.29369 9.36363C1.76117 10.4922 2.55282 11.4569 3.56854 12.1355C4.58425 12.8142 5.77841 13.1765 7 13.1765C8.63754 13.1747 10.2075 12.5233 11.3654 11.3654C12.5233 10.2075 13.1747 8.63754 13.1765 7Z" fill="black"></path></svg>
													
										</a>
									</div>
									<div class="ecom-cart__product-prices {% if item.compare_at_price > item.price %}ecom-cart__product-prices--has-discount{% endif %}"
									>
										
										<div class="ecom-cart__product-item__price-wrapper">
											{%- if item.original_price and item.original_price != item.final_price -%}
												
													<dl class="ecom-cart__product-item-discounted-prices">
														<dt class="ecom-cart__product-visually-hidden">
															Regular price
														</dt>
														<dd>
															<s class="ecom-cart__product-item-old-price ecom-cart__product-item-price--end">
																{%- if settings.currency_code_enabled -%}
																{{item.original_price | money_with_currency}}
															{%- else -%}
																{{ item.original_price | money }}
															{%- endif -%}
															</s>
														</dd>
														<dt class="ecom-cart__product-visually-hidden">
															Sale
														</dt>
														<dd class="ecom-cart__product-price--end">
															{%- if settings.currency_code_enabled -%}
																{{item.final_price | money_with_currency}}
															{%- else -%}
																{{ item.final_price | money }}
															{%- endif -%}
														</dd>
													</dl>
													
											{% elsif item.compare_at_price > item.price or item.variant.compare_at_price > item.price %}
												{%- if item.compare_at_price -%}
													{%- assign compare_at_price = item.compare_at_price -%}
												{%- else -%}
													{%- assign compare_at_price = item.variant.compare_at_price -%}
												{%- endif -%}
												<dl class="ecom-cart__product-item-discounted-prices">
																<dt class="ecom-cart__product-visually-hidden">
																	Regular price
																</dt>
																<dd>
																	<s class="ecom-cart__product-item-old-price ecom-cart__product-item-price--end">
																		{%- if settings.currency_code_enabled -%}
																			{{compare_at_price | money_with_currency}}
																		{%- else -%}
																			{{ compare_at_price | money }}
																		{%- endif -%}
																	</s>
																</dd>
																<dt class="ecom-cart__product-visually-hidden">
																	Sale
																</dt>
																<dd class="ecom-cart__product-price--end">
																	{%- if settings.currency_code_enabled -%}
																		{{item.price | money_with_currency}}
																	{%- else -%}
																		{{ item.price | money }}
																	{%- endif -%}
																</dd>
															</dl>
											{%- elsif item.original_price-%}
												<span class="ecom-cart__product-price--end">
													{%- if settings.currency_code_enabled -%}
														{{item.original_price | money_with_currency}}
													{%- else -%}
														{{ item.original_price | money }}
													{%- endif -%}
												</span>
											{% else %}
												<span class="ecom-cart__product-price--end">
													{%- if settings.currency_code_enabled -%}
														{{item.price | money_with_currency}}
													{%- else -%}
														{{ item.price | money }}
													{%- endif -%}
												</span>
											{%- endif -%}
											
										</div>
									</div>
									{% if is_dummy %}
										{% assign quantity = 1 %}
									{% else %}
										{% assign quantity = item.quantity %}
									{% endif %}
									<div class="ecom-cart__product-quantity ">
										
										<div class="ecom-cart__product-quantity-wrapper">
											<button class="ecom-cart__product-quantity--button ecom-quantity-minus" name="minus" type="button">
													<svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512"><path d="M432 256c0 8.8-7.2 16-16 16L32 272c-8.8 0-16-7.2-16-16s7.2-16 16-16l384 0c8.8 0 16 7.2 16 16z"/></svg>
												</button>
												<input class="ecom-cart__product-quantity--input"
													type="number"
													name="updates[]"
													value="{{ quantity }}"
													min="0"
													data-key="{{item.key}}"
													data-line="{{forloop.index}}"
													data-index="{{ item.index | plus: 1 }}"
												>
												<button class="ecom-cart__product-quantity--button ecom-quantity-plus" name="plus" type="button">
													<svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512"><path d="M240 64c0-8.8-7.2-16-16-16s-16 7.2-16 16V240H32c-8.8 0-16 7.2-16 16s7.2 16 16 16H208V448c0 8.8 7.2 16 16 16s16-7.2 16-16V272H416c8.8 0 16-7.2 16-16s-7.2-16-16-16H240V64z"/></svg>
												</button>
										</div>
									</div>
									<div class="ecom-cart__product-item__totals">
										
										<div class="ecom-cart__product-item__price-wrapper">
											{%- if item.original_line_price != item.final_line_price -%}
												<dl class="ecom-cart__product-item__discounted-prices">
													<dt class="ecom-cart__product-visually-hidden">
													 Sale
													</dt>
													<dd class="ecom-cart__product-price--end" data-hulkapps-line-price data-key="{{item.key}}">
														{%- if settings.currency_code_enabled -%}
															{{item.final_line_price | money_with_currency}}
														{%- else -%}
															{{ item.final_line_price | money }}
														{%- endif -%}
													</dd>
												</dl>
											{%- elsif item.original_line_price -%}
												<span class="ecom-cart__product-price--end" data-hulkapps-line-price data-key="{{item.key}}">
													{%- if settings.currency_code_enabled -%}
														{{item.original_line_price | money_with_currency}}
													{%- else -%}
														{{ item.original_line_price | money }}
													{%- endif -%}
												</span>
											{% elsif item.compare_at_price > item.price %}
												 <dl class="ecom-cart__product-item__discounted-prices">
													<dt class="ecom-cart__product-visually-hidden">
													 Sale
													</dt>
													<dd class="ecom-cart__product-price--end" data-hulkapps-line-price data-key="{{item.key}}">
														{%- if settings.currency_code_enabled -%}
															{{item.price | money_with_currency}}
														{%- else -%}
															{{ item.price | money }}
														{%- endif -%}
													</dd>
												</dl>
											{% else %}
												<span class="ecom-cart__product-price--end" data-hulkapps-line-price data-key="{{item.key}}">
													{%- if settings.currency_code_enabled -%}
														{{item.price | money_with_currency}}
													{%- else -%}
														{{ item.price | money }}
													{%- endif -%}
												</span>
											{%- endif -%}
										</div>
										
												<a href="{{ item.url_to_remove }}" class="ecom-cart__product-item-remove-button desktop">
													
															<svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M9.48706 5.0949L7.58196 7L9.48706 8.9051C9.52752 8.94279 9.55996 8.98825 9.58247 9.03876C9.60497 9.08927 9.61707 9.1438 9.61805 9.19908C9.61903 9.25437 9.60886 9.30929 9.58815 9.36056C9.56744 9.41183 9.53661 9.45841 9.49751 9.49751C9.45841 9.53661 9.41184 9.56743 9.36056 9.58814C9.30929 9.60885 9.25437 9.61902 9.19909 9.61805C9.1438 9.61707 9.08927 9.60497 9.03876 9.58247C8.98825 9.55996 8.94279 9.52751 8.9051 9.48706L7 7.58196L5.0949 9.48706C5.01685 9.55979 4.91361 9.59939 4.80693 9.5975C4.70026 9.59562 4.59848 9.55241 4.52303 9.47697C4.44759 9.40152 4.40438 9.29974 4.40249 9.19307C4.40061 9.08639 4.44021 8.98315 4.51294 8.9051L6.41804 7L4.51294 5.0949C4.44021 5.01684 4.40061 4.9136 4.40249 4.80693C4.40438 4.70025 4.44759 4.59847 4.52303 4.52303C4.59848 4.44759 4.70026 4.40437 4.80693 4.40249C4.91361 4.40061 5.01685 4.44021 5.0949 4.51294L7 6.41804L8.9051 4.51294C8.98316 4.44021 9.0864 4.40061 9.19307 4.40249C9.29975 4.40437 9.40153 4.44759 9.47697 4.52303C9.55241 4.59847 9.59563 4.70025 9.59751 4.80693C9.59939 4.9136 9.55979 5.01684 9.48706 5.0949ZM14 7C14 8.38447 13.5895 9.73785 12.8203 10.889C12.0511 12.0401 10.9579 12.9373 9.67878 13.4672C8.3997 13.997 6.99224 14.1356 5.63437 13.8655C4.2765 13.5954 3.02922 12.9287 2.05026 11.9497C1.07129 10.9708 0.404603 9.7235 0.134506 8.36563C-0.13559 7.00776 0.00303287 5.6003 0.532846 4.32122C1.06266 3.04213 1.95987 1.94888 3.11101 1.17971C4.26215 0.410543 5.61553 0 7 0C8.85585 0.0021795 10.6351 0.740377 11.9473 2.05266C13.2596 3.36494 13.9978 5.14415 14 7ZM13.1765 7C13.1765 5.77841 12.8142 4.58425 12.1355 3.56854C11.4569 2.55282 10.4922 1.76117 9.36363 1.29368C8.23503 0.826203 6.99315 0.703888 5.79503 0.942208C4.59691 1.18053 3.49637 1.76878 2.63258 2.63258C1.76878 3.49637 1.18053 4.59691 0.942211 5.79503C0.703891 6.99315 0.826206 8.23503 1.29369 9.36363C1.76117 10.4922 2.55282 11.4569 3.56854 12.1355C4.58425 12.8142 5.77841 13.1765 7 13.1765C8.63754 13.1747 10.2075 12.5233 11.3654 11.3654C12.5233 10.2075 13.1747 8.63754 13.1765 7Z" fill="black"></path></svg>
														
												</a>
											
									</div>
								</div>
							{% endfor %}
						</div>
					{%- endif -%}
					</div><div class="ecom-cart--json">
						{% capture ecom_cart_json %}
							{{ cart | json}}
						{% endcapture %}
						<script type="application/json" id="ecom-cart-json">
							{{ ecom_cart_json}}
						</script></div></div></div></div> </div></div></div></div> <div class="ecom-column ecom-core core__column--last ecom-b34fq1k4j24"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-wmebr9k56zs" data-core-is="row"><div class="ecom-row ecom-core core__block ecom-3xnap9n44wp" data-id="ecom-3xnap9n44wp" data-notice-content="This image is for preview only" style="z-index: inherit;"><div data-deep="2" class="core__row--columns"><div class="ecom-column ecom-core core__column--first core__column--last ecom-ery1dme89jh"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-qf234pxskyj" data-core-is="block"><div class="ecom-element ecom-cart ecom-cart__coupon" deep="2">{%- if cart.item_count > 0 -%}<div class="ecom-cart__coupon-wrapper"><div class="ecom-cart__coupon-container"><label class="ecom-cart__coupon-text">Coupon:&nbsp;<div><span>Coupon code will work on checkout page</span></div></label><input type="text" name="coupon" class="ecom-cart__coupon--input" placeholder="Enter your coupon code"></div></div>{%- endif -%}</div></div> <div class="ecom-block ecom-core core__block ecom-59p00hwhoww" data-core-is="block"><div class="ecom-element ecom-cart ecom-cart__informations" deep="2">{%- if cart.item_count > 0 -%}<div class="ecom-cart__informations-wrapper"><div class="ecom-cart__informations-container">
 <dl class="ecom-cart__informations-original_total_price">
 <dt class="ecom-cart__information-title">
 Retail Price
 </dt>
 <dd class="ecom-cart__information-text" data-hulkapps-cart-total>
 {{ cart.original_total_price | money }}
 </dd>
 </dl>
 
 {% if cart.discount_applications.size > 0 %}
 <dl class="ecom-cart__informations-discount_application">
 <dt class="ecom-cart__information-title">Saved</dt>
 <dd class="ecom-cart__information-text">
 {% for discount_application in cart.discount_applications %}
 {%- assign amount = discount_application.total_allocated_amount | money -%}
 <span class="ecom-cart__informations-discount_application-value">
 {% assign title = discount_application.title %}
 {{amount}}
 </span><br>
 {% endfor %}
 </dd>
 </dl>
 {% else %}
 
 {% endif %}
 
 <dl class="ecom-cart__informations-total_price">
 <dt class="ecom-cart__information-title">
 Total
 </dt>
 <dd class="ecom-cart__information-text totals__subtotal-value" data-hulkapps-cart-total data-er-cart-total-price>
 {%- assign amount = cart.total_price | money -%}
 {{amount}}
 </dd>
 </dl>
 </div></div>{%- endif -%}</div></div> <div class="ecom-block ecom-core core__block ecom-9dv8dq3bfyu" data-core-is="block"><div class="ecom-element ecom-cart ecom-cart__checkout-button" deep="2">{%- if cart.item_count > 0 -%}<input type="hidden" name="checkout" value="ec"><div class="ecom-cart__checkout-button-wrapper"><div class="ecom-cart__checkout-button-container"><button type="submit" name="checkout" class="ecom-cart__checkout-button--submit"><span class="ecom-cart-checkout-text">Checkout</span><span class="ecom__element--button-icon"></span></button></div></div>{%- endif -%}</div></div> <div class="ecom-block ecom-core core__block ecom-khc0s4u7o1s" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed ecom-view-more-enable">Payment info</div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-wwzkkplt1aj" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="2"><div class="text-content ecom-html has-drop-cap-view-framed ecom-view-more-enable">Accepted Payment Methods</div></div></div> <div class="ecom-block ecom-core core__block ecom-k327wt9pl6" data-core-is="block"><div class="ecom-element ecom-base-image" deep="2"><figure><div class="ecom-container-image ecom-image-align" data-stopdrag=""><div data-stopdrag="" data-notice-content="This image is for preview only" class="ecom-image-content-position ecom-image-default ecom-base-image-container-overlay" style=""><picture class="ecom-image-picture"><img loading="lazy" fetchpriority="low" src="https://cdn.shopify.com/s/files/1/0464/1030/1595/files/111.png?v=1733820869" alt="111" width="830" height="220" style=""></picture></div></div></figure></div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section><input type="hidden" name="checkout" value="ec" /></form>
</div></div>
{% schema %}
{
 "name": "New cart page",
 "locales": {},
 "settings": [
 {
 "type": "header",
 "content": "The section was generated by [Ecomposer](https:\/\/ecomposer.io).",
 "info": "\n EComposer is a Visual Page Builder app on Shopify AppStore.\n It provides 100+ pre-built templates in library,\n so you can build unlimited pages that you can imagine with it.\n Please don't add Shopify sections to EComposer's page in the theme customize. If you do this, Shopify sections will be deleted when you republish your page in EComposer\n "
 },
 {
 "type": "header",
 "content": "[EDIT WITH EComposer APP \u2192](https:\/\/ecomposer.app\/shop?open_builder=1&page=cart&id=67581ab71983b7573809c9a2&utm_source=theme-customize)",
 "info": "(*You must install the app to start editing this section [Learn more](https:\/\/help.ecomposer.io\/docs\/getting-started\/installation\/))"
 }
 ]
}
{% endschema %}