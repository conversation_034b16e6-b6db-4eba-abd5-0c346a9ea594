{%- render 'section-header' -%}

{% schema %}
{
  "name": "t:labels.header",
  "class": "header-section",
  "settings": [
    {
      "type": "link_list",
      "id": "main_menu_link_list",
      "label": "t:labels.navigation",
      "default": "main-menu"
    },
    {
      "type": "checkbox",
      "id": "hover_menu",
      "label": "t:actions.enable_dropdown_on_hover",
      "default": true
    },
    {
      "type": "select",
      "id": "main_menu_alignment",
      "label": "t:labels.layout",
      "default": "below",
      "options": [
        {
          "value": "below",
          "label": "t:labels.logo_left_menu_below"
        },
        {
          "value": "left",
          "label": "t:labels.logo_left_menu_left"
        },
        {
          "value": "left-center",
          "label": "t:labels.logo_left_menu_center"
        },
        {
          "value": "center",
          "label": "t:labels.logo_center_menu"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "mega_products",
      "label": "t:actions.show_first_product",
      "info": "t:info.learn_mega_menu",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "header_sticky",
      "label": "t:actions.enable_sticky_header",
      "default": true,
      "info": "t:labels.recommended"
    },
    {
      "type": "checkbox",
      "id": "header_footer_menu",
      "label": "t:actions.show_footer_content",
      "default": true,
      "info": "t:labels.recommended"
    },
    {
      "type": "checkbox",
      "id": "sticky_index",
      "label": "t:actions.overlay_header_over_home",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "sticky_collection",
      "label": "t:actions.overlay_header_over_collection",
      "info": "t:info.if_collection_image_enabled",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "logo",
      "name": "t:labels.logo",
      "limit": 1,
      "settings": [
        {
          "type": "image_picker",
          "id": "logo",
          "label": "t:labels.logo"
        },
        {
          "type": "image_picker",
          "id": "logo-inverted",
          "label": "t:labels.white_logo",
          "info": "t:info.used_when_on_top_of_image"
        },
        {
          "type": "range",
          "id": "desktop_logo_width",
          "label": "t:labels.desktop_logo_width",
          "default": 200,
          "min": 100,
          "max": 400,
          "step": 10,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "mobile_logo_width",
          "label": "t:labels.mobile_logo_width",
          "default": 140,
          "min": 60,
          "max": 200,
          "step": 10,
          "unit": "px",
          "info": "t:info.set_as_max_width"
        }
      ]
    },
    { 
      "type":"erlink",
      "name":"超级菜单",
      "settings":[
        {
          "type":"text",
          "id": "pare_name",
          "label": "父级菜单名称",
          "info": "一级菜单名称"
         
        },
        {
          "type":"link_list",
          "id": "link_list",
          "label": "菜单选择"
         
        },
        {
          "type":"url",
          "id": "er_url",
          "label": "二级菜单链接"
         
        },
        {
          "type":"header",
          "content": "热词及其父级菜单"
        },
        {
          "type":"text",
          "id": "pare_name2",
          "label": "父级名称1"
        },
        {
          "type":"text",
          "id": "hottext",
          "label": "热词1"
        },
        {
          "type":"color",
          "id": "hotc1",
          "label": "文本颜色",
          "default":"#fff"
        },
        {
          "type":"color",
          "id": "hotbg1",
          "label": "文本背景色",
          "default":"#CDB399"
        },

        { 
          "type":"text",
          "id": "pare_name3",
          "label": "父级名称2"
         
        },
        {
          "type":"text",
          "id": "hottext2",
          "label": "热词2"
        },
        {
          "type":"color",
          "id": "hotc2",
          "label": "文本颜色",
          "default":"#fff"
        },
        {
          "type":"color",
          "id": "hotbg2",
          "label": "文本背景色",
          "default":"#CDB399"
        },
        {
          "type":"text",
          "id": "pare_name4",
          "label": "父级名称3"
         
        },
        {
          "type":"text",
          "id": "hottext3",
          "label": "热词3"
        },
        {
          "type":"color",
          "id": "hotc3",
          "label": "文本颜色",
          "default":"#fff"
        },
        {
          "type":"color",
          "id": "hotbg3",
          "label": "文本背景色",
          "default":"#CDB399"
        }
      
      ]
      
    },
    {
      "type":"danimage",
      "name":"二级图片",
     
      "settings":[
        {
          "type":"text",
          "id": "pare_name",
          "label": "父级菜单名称",
          "info": "一级菜单名称"
         
        },
        {
          "type":"image_picker",
          "id": "slider_image",
          "label": "图片"
        },
        {
          "type":"text",
          "id": "text",
          "label": "文本"
        },
        {
          "type":"url",
          "id": "texturl",
          "label": "文本链接"
        }
        
       

      ]
      
    },
    {
      "type":"mbblock",
      "name":"手机端菜单1",
     
      "settings":[
        {
          "type":"text",
          "id": "oneclass",
          "label": "一级菜单名称"
         
        },
        
        {
          "type":"url",
          "id": "oneurl",
          "label": "一级菜单链接"
        },
        {
          "type":"header",
          "content": "二级菜单"
        },
        {
          "type":"collection",
          "id": "collection_select",
          "label": "选择分类1"
        },
        {
          "type":"link_list",
          "id": "menu_link1",
          "label": "二级菜单1",
          "info":"选择的菜单，若有二级则作为第三级菜单"
        },
        {
          "type":"collection",
          "id": "collection_select2",
          "label": "选择分类2"
        },
        {
          "type":"link_list",
          "id": "menu_link2",
          "label": "二级菜单2",
          "info":"选择的菜单，若有二级则作为第三级菜单"
        },
        {
          "type":"link_list",
          "id": "menu_link3",
          "label": "二级菜单3",
          "info":"选择的菜单，只有一个级别，即使有二级也不会作为三级菜单"
        }

      ]
      
    },
    {
      "type":"mbblocktwo",
      "name":"手机端菜单2",
     
      "settings":[
        {
          "type":"image_picker",
          "id": "one_picker",
          "label": "一级菜单图片"
        },
        {
          "type":"text",
          "id": "oneclass",
          "label": "一级菜单名称"
         
        },
        
        
        {
          "type":"url",
          "id": "oneurl",
          "label": "一级菜单链接",
          "info":"若填写一级菜单链接，则不能跳转到二级，直接跳转到链接页面"
        },
        {
          "type":"header",
          "content": "二级菜单"
        },
        {
          "type":"collection",
          "id": "collection_select",
          "label": "选择分类1"
        },
        {
          "type":"link_list",
          "id": "menu_link1",
          "label": "二级菜单1",
          "info":"选择的菜单，若有二级则作为第三级菜单"
        },
        {
          "type":"collection",
          "id": "collection_select2",
          "label": "选择分类2"
        },
        {
          "type":"link_list",
          "id": "menu_link2",
          "label": "二级菜单2",
          "info":"选择的菜单，若有二级则作为第三级菜单"
        },
        {
          "type":"link_list",
          "id": "menu_link3",
          "label": "二级菜单3",
          "info":"选择的菜单，只有一个级别，即使有二级也不会作为三级菜单"
        }

      ]
      
    },
    {
      "type":"collectionlist",
      "name":"二级分类集",
     
      "settings":[
        { 
          "type":"text",
          "id": "pare_name",
          "label": "父级菜单名称",
          "info": "一级菜单名称"
        },
        {
          "type":"collection_list",
          "id": "collection_list",
          "label": "分类集"
        }
       
      ]
      
    }
  ],
  "default": {
    "settings": {}
  },
  "disabled_on": {
    "groups": ["footer", "custom.popups"]
  }
}
{% endschema %}
