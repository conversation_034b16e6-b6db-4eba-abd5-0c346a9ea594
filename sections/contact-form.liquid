{%- render 'section-contact-form' -%}

{% schema %}
{
  "name": "t:labels.contact_form",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:info.all_submissions_sent_to_store_email"
    },
    {
      "type": "text",
      "id": "title",
      "label": "t:labels.title",
      "default": "Contact us"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:labels.heading_size",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "t:labels.sizes.small"
        },
        {
          "value": "h2",
          "label": "t:labels.sizes.medium"
        },
        {
          "value": "h1",
          "label": "t:labels.sizes.large"
        },
        {
          "value": "h0",
          "label": "t:labels.sizes.extra_large"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_position",
      "label": "t:labels.heading_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "center",
          "label": "t:labels.alignments.center"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "t:labels.text"
    },
    {
      "type": "checkbox",
      "id": "show_phone",
      "label": "t:actions.add_phone_number"
    },
    {
      "type": "checkbox",
      "id": "narrow_column",
      "label": "t:labels.narrow_column",
      "default": true
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:labels.color_scheme",
      "default": "1",
      "options": [
        {
          "value": "none",
          "label": "t:labels.none"
        },
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:labels.contact_form"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
