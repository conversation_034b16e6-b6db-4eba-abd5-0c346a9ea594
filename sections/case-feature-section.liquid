<style>
    /* 特色区域容器 - 使用vw单位动态缩放 */
    .feature-section-1 {
        width: 100%;
        display: flex;
        flex-direction: column;
        /*gap: 4.167vw; !* 80px/1920 ≈ 4.167vw *!*/
    }

    /* 特色内容区域 */
    .feature-content-1 {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    /* 特色图片 - 高度使用vw保持比例 */
    .feature-image-1 {
        width: calc(100% - 34.896vw); /* 100% - 670px/1920*100vw = 100% - 34.896vw */
        height: 23.906vw; /* 459px/1920 ≈ 23.906vw */
        overflow: hidden;
        position: relative;
        display: flex;
        align-items: center;
    }

    .feature-image-1 img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
        /*cursor: pointer;*/
        /* 优化图片渲染质量 */
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
        image-rendering: auto;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        /* 确保高质量缩放 */
        -webkit-filter: blur(0);
        filter: blur(0);
    }

    .feature-image-1 img:hover {
        transform: scale(1.05);
    }

    /* 移动端和桌面端图片显示控制 */
    .feature-image-1 .mobile-image {
        display: none !important;
    }

    .feature-image-1 .desktop-image {
        display: block !important;
    }

    /* 图片链接样式 */
    .feature-image-link {
        display: block;
        width: 100%;
        height: 100%;
        text-decoration: none;
        outline: none;
    }

    .feature-image-link:focus {
        outline: 2px solid #007acc;
        outline-offset: 2px;
    }

    /* 确保链接内的图片样式保持一致 */
    .feature-image-link img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
        /*cursor: pointer;*/
        /* 优化图片渲染质量 */
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
        image-rendering: auto;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        /* 确保高质量缩放 */
        -webkit-filter: blur(0);
        filter: blur(0);
    }

    .feature-image-link:hover img {
        transform: scale(1.05);
    }

    /* 特色文字内容 - 内边距改用vw */
    .feature-text-1 {
        width: 34.896vw; /* 670px/1920*100vw = 34.896vw */
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 4.688vw 4.571vw; /* 90px/1920 ≈ 4.688vw */
        color: #ffffff;
        background: {{ section.settings.feature_bg_color | default: '#C8A983' }};
    }

    .feature-text-1 h2 {
        font-family: 'Playfair Display', serif;
        font-weight: 400;
        font-size: 1.354vw; /* 26px/1920 ≈ 1.354vw */
        line-height: 1.354vw;
    }

    /* 当h2没有内容时不显示高度 */
    .feature-text-1 h2:empty {
        display: none;
    }

    /* 当h2只包含空白字符时也不显示 */
    .feature-text-1 h2[data-empty="true"] {
        display: none;
    }

    .feature-text-1 .feature-description {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 0.833vw; /* 16px/1920 ≈ 0.833vw */
        line-height: 1.563vw; /* 30px/1920 ≈ 1.563vw */
        margin-top: 1.042vw; /* 20px/1920 ≈ 1.042vw */
        border: none;
        background: transparent;
        outline: none;
        width: 100%;
        color: inherit;
        overflow-y: auto;
        overflow-x: hidden;
        height: auto;
        max-height: calc(23.906vw - 9.635vw); /* 459px-185px ≈ 274px → 14.271vw */
        padding: 0;
        /*white-space: pre-wrap; !* 保留换行和空格 *!*/
        word-wrap: break-word; /* 长单词换行 */
        word-break: break-word; /* 强制换行 */
    }

    /* web端确保不显示br-spacer */
    .feature-text-1 .feature-description .br-spacer {
        display: none;
    }

    /* 响应式设计 - 平板设备 (768px-1024px) */
    @media (max-width: 1024px) {
        .feature-text-1 {
            padding: 2.5vw;
        }
        .feature-text-1 h2 {
            font-size: 1.8vw;
            line-height: 1.8vw;
        }
    }

    /* 响应式设计 - 手机设备 (小于768px) - 基于768px基础使用vw单位 */
    @media (max-width: 768px) {
        .feature-section-1 {
            gap: 6.25vw; /* 48px ÷ 768 × 100 = 6.25vw，移动端间距 */
        }

        .feature-content-1 {
            flex-direction: column;
        }

        .feature-image-1 {
            height: 26.04vw; /* 200px ÷ 768 × 100 = 26.04vw，基于768px基础 */
            flex: auto;
          width: auto;
        }

        /* 移动端图片显示控制 */
        .feature-image-1 .mobile-image {
            display: block !important;
            width: 100% !important;
            height: 100% !important;
            position: relative !important;
        }

        .feature-image-1 .mobile-image img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            display: block !important;
        }

        .feature-image-1 .desktop-image {
            display: none !important;
        }

        /* 调试：确保移动端图片容器可见 */
        .feature-image-1 {
            position: relative;
            overflow: hidden;
        }

        .feature-text-1 {
            padding: 2.60vw 2.08vw; /* 20px 16px 转换为vw (基于768px) */
          width: auto;
        }

        .feature-text-1 h2 {
            font-size: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
            margin-bottom: 1.82vw; /* 14px ÷ 768 × 100 = 1.82vw */
          font-family: Playfair Display;
          font-weight: 400;
          font-style: Regular;
          leading-trim: NONE;
          line-height: 100%;
          letter-spacing: 0px;
        }

        .feature-text-1 .feature-description {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 1.82vw; /* 14px ÷ 768 × 100 = 1.82vw */
            line-height: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
            margin-top: 0; /* 设置为0 */
            max-height: none !important; /* 移动端强制取消高度限制 */
            height: auto !important; /* 强制自动高度 */
            overflow: visible !important; /* 显示所有内容 */
          white-space: normal;
            word-wrap: break-word; /* 长单词换行 */
            word-break: break-word; /* 强制换行 */
        }

        .feature-text-1 .feature-description br {
            /* 清空br样式 */
        }

        /* 在br后添加一个height为10px的div */
        .feature-text-1 .feature-description br + * {
            margin-top: 1.30vw; /* 10px ÷ 768 × 100 = 1.30vw */
        }

        .feature-text-1 .feature-description br::after {
            content: "";
            display: block;
            height: 1.30vw; /* 10px ÷ 768 × 100 = 1.30vw */
            width: 100%;
        }

        /* br后的间距div样式 */
        .feature-text-1 .feature-description .br-spacer {
            display: block;
            height: 1.30vw; /* 10px ÷ 768 × 100 = 1.30vw */
            width: 100%;
            margin: 0;
            padding: 0;
        }
    }

    /* 超小屏幕保护 - 基于375px基础使用vw单位 */
    @media (max-width: 480px) {
        .feature-section-1 {
            gap: 12.8vw; /* 48px ÷ 375 × 100 = 12.8vw，移动端间距 */
        }

        .feature-content-1 {
            flex-direction: column;
        }

        .feature-image-1 {
            height: 53.33vw; /* 200px ÷ 375 × 100 = 53.33vw，基于375px基础 */
            flex: auto;
          width: auto;
        }

        /* 移动端图片显示控制 */
        .feature-image-1 .mobile-image {
            display: block !important;
            width: 100% !important;
            height: 100% !important;
            position: relative !important;
        }

        .feature-image-1 .mobile-image img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            display: block !important;
        }

        .feature-image-1 .desktop-image {
            display: none !important;
        }

        /* 调试：确保移动端图片容器可见 */
        .feature-image-1 {
            position: relative;
            overflow: hidden;
        }

        .feature-text-1 {
            padding: 5.33vw 4.27vw; /* 20px 16px 转换为vw (基于375px) */
            width: auto;
        }

        .feature-text-1 h2 {
            font-size: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
            /*line-height: 6.4vw; !* 24px ÷ 375 × 100 = 6.4vw *!*/
            margin-bottom: 3.73vw; /* 14px ÷ 375 × 100 = 3.73vw */
          font-family: Playfair Display;
          font-weight: 400;
          font-style: Regular;
          leading-trim: NONE;
          line-height: 100%;
          letter-spacing: 0px;

        }

        .feature-text-1 .feature-description {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 3.73vw; /* 14px ÷ 375 × 100 = 3.73vw */
            line-height: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
            margin-top: 0; /* 设置为0 */
            max-height: none !important; /* 移动端强制取消高度限制 */
            height: auto !important; /* 强制自动高度 */
            overflow: visible !important; /* 显示所有内容 */
          white-space: normal;
            word-wrap: break-word; /* 长单词换行 */
            word-break: break-word; /* 强制换行 */
        }

        .feature-text-1 .feature-description br {
            /* 清空br样式 */
        }

        /* 在br后添加一个height为10px的div */
        .feature-text-1 .feature-description br + * {
            margin-top: 2.67vw; /* 10px ÷ 375 × 100 = 2.67vw */
        }

        .feature-text-1 .feature-description br::after {
            content: "";
            display: block;
            height: 2.67vw; /* 10px ÷ 375 × 100 = 2.67vw */
            width: 100%;
        }

        /* br后的间距div样式 */
        .feature-text-1 .feature-description .br-spacer {
            display: block;
            height: 2.67vw; /* 10px ÷ 375 × 100 = 2.67vw */
            width: 100%;
            margin: 0;
            padding: 0;
        }
    }
</style>

<!-- 第一个特色区域 (HTML部分保持不变) -->
<div class="feature-section-1">
    <div class="feature-content-1">
        <div class="feature-image-1">
            <!-- Desktop Image (only shown on desktop) -->
            {% if section.settings.feature_image_1 != blank %}
                {% assign desktop_link = section.settings.feature_image_link %}
                {% assign desktop_new_tab = section.settings.open_link_new_tab %}
                {% assign desktop_image = section.settings.feature_image_1 %}

                {% if desktop_link != blank %}
                    <a href="{{ desktop_link }}"
                       {% if desktop_new_tab %}target="_blank" rel="noopener noreferrer"{% endif %}
                       class="feature-image-link desktop-image">
                        <img src="{{ desktop_image | img_url: '2400x1600' }}"
                             srcset="{{ desktop_image | img_url: '1200x800' }} 1x,
                                     {{ desktop_image | img_url: '2400x1600' }} 2x,
                                     {{ desktop_image | img_url: '3600x2400' }} 3x"
                             sizes="65vw"
                             alt="特色展示"
                             loading="lazy">
                    </a>
                {% else %}
                    <img src="{{ desktop_image | img_url: '2400x1600' }}"
                         srcset="{{ desktop_image | img_url: '1200x800' }} 1x,
                                 {{ desktop_image | img_url: '2400x1600' }} 2x,
                                 {{ desktop_image | img_url: '3600x2400' }} 3x"
                         sizes="65vw"
                         class="desktop-image"
                         alt="特色展示"
                         loading="lazy">
                {% endif %}
            {% endif %}

            <!-- Mobile Image (only shown on mobile) -->
            {% if section.settings.feature_image_1_mobile != blank %}
                {% assign mobile_link = section.settings.feature_image_mobile_link %}
                {% assign mobile_new_tab = section.settings.open_mobile_link_new_tab %}
                {% assign mobile_image = section.settings.feature_image_1_mobile %}

                {% if mobile_link != blank %}
                    <a href="{{ mobile_link }}"
                       {% if mobile_new_tab %}target="_blank" rel="noopener noreferrer"{% endif %}
                       class="feature-image-link mobile-image">
                        <img src="{{ mobile_image | img_url: '800x600' }}"
                             srcset="{{ mobile_image | img_url: '600x400' }} 375w,
                                     {{ mobile_image | img_url: '800x600' }} 768w"
                             sizes="100vw"
                             alt="特色展示"
                             loading="lazy">
                    </a>
                {% else %}
                    <img src="{{ mobile_image | img_url: '800x600' }}"
                         srcset="{{ mobile_image | img_url: '600x400' }} 375w,
                                 {{ mobile_image | img_url: '800x600' }} 768w"
                         sizes="100vw"
                         class="mobile-image"
                         alt="特色展示"
                         loading="lazy">
                {% endif %}
            {% endif %}
        </div>
        <div class="feature-text-1">
            <h2>{{ section.settings.feature_title_1 }}</h2>
            <h2>{{ section.settings.feature_title_2 }}</h2>
            <div class="feature-description">{{ section.settings.feature_description_1 | newline_to_br }}</div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 检查所有h2元素，如果只包含空白字符则隐藏
    const h2Elements = document.querySelectorAll('.feature-text-1 h2');

    h2Elements.forEach(function(h2) {
        // 检查h2是否为空或只包含空白字符
        if (!h2.textContent || h2.textContent.trim() === '') {
            h2.setAttribute('data-empty', 'true');
        }
    });
});
</script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 修改为动态计算最大高度（基于当前元素的实际vw值）
    const descriptionDiv = document.querySelector('.feature-text-1 .feature-description');
    if (descriptionDiv) {
      function adjustDescriptionHeight() {
        const viewportWidth = window.innerWidth;

        // 移动端（768px以下）完全展开内容，无滚动条
        if (viewportWidth <= 768) {
          // 移动端直接设置为完全显示
          descriptionDiv.style.height = 'auto';
          descriptionDiv.style.maxHeight = 'none';
          descriptionDiv.style.overflow = 'visible';
          return;
        }

        // 桌面端保持原有逻辑
        const featureImage = document.querySelector('.feature-image-1');
        if (!featureImage) return;

        // 动态获取图片高度（兼容桌面端）
        const imgHeight = window.getComputedStyle(featureImage).height;
        const maxHeight = parseFloat(imgHeight) - 185 * (window.innerWidth / 1920);

        // 对于div，我们只需要设置maxHeight
        descriptionDiv.style.maxHeight = maxHeight + 'px';
        descriptionDiv.style.overflowY = 'auto';
      }

      adjustDescriptionHeight();
      window.addEventListener('resize', adjustDescriptionHeight);
    }

    // 只在移动端为br标签后添加间距div，web端不添加
    function addMobileSpacers() {
      const viewportWidth = window.innerWidth;

      // 只在移动端（768px以下）添加间距div
      if (viewportWidth <= 768) {
        const descriptionDiv = document.querySelector('.feature-text-1 .feature-description');
        if (descriptionDiv) {
          const brElements = descriptionDiv.querySelectorAll('br');
          brElements.forEach(function(br) {
            // 检查br后面是否已经有间距div
            if (!br.nextElementSibling || !br.nextElementSibling.classList.contains('br-spacer')) {
              const spacerDiv = document.createElement('div');
              spacerDiv.className = 'br-spacer';
              spacerDiv.style.height = viewportWidth <= 480 ? '2.67vw' : '1.30vw';
              spacerDiv.style.width = '100%';
              spacerDiv.style.display = 'block';

              // 在br后插入间距div
              if (br.nextSibling) {
                br.parentNode.insertBefore(spacerDiv, br.nextSibling);
              } else {
                br.parentNode.appendChild(spacerDiv);
              }
            }
          });
        }
      } else {
        // web端（768px以上）移除所有间距div
        const descriptionDiv = document.querySelector('.feature-text-1 .feature-description');
        if (descriptionDiv) {
          const spacerDivs = descriptionDiv.querySelectorAll('.br-spacer');
          spacerDivs.forEach(function(spacer) {
            spacer.remove();
          });
        }
      }
    }

    // 初始执行
    addMobileSpacers();

    // 窗口大小改变时重新执行
    window.addEventListener('resize', function() {
      setTimeout(addMobileSpacers, 100);
    });
  });
</script>

{% schema %}
{
  "name": "Feature Section 1",
  "settings": [
    {
      "type": "image_picker",
      "id": "feature_image_1",
      "label": "Desktop Feature Image"
    },
    {
      "type": "image_picker",
      "id": "feature_image_1_mobile",
      "label": "Mobile Feature Image",
      "info": "Optional: Use a different image for mobile devices. If not set, desktop image will be used."
    },
    {
      "type": "url",
      "id": "feature_image_link",
      "label": "Desktop Image Click Link",
      "info": "Optional: Add a link when users click on the desktop image"
    },
    {
      "type": "checkbox",
      "id": "open_link_new_tab",
      "label": "Open desktop image link in new tab",
      "default": false
    },
    {
      "type": "url",
      "id": "feature_image_mobile_link",
      "label": "Mobile Image Click Link",
      "info": "Optional: Add a link when users click on the mobile image. If not set, desktop link will be used."
    },
    {
      "type": "checkbox",
      "id": "open_mobile_link_new_tab",
      "label": "Open mobile image link in new tab",
      "default": false
    },
    {
      "type": "text",
      "id": "feature_title_1",
      "label": "Feature Title",
      "default": "为什么您会喜欢它："
    },
    {
      "type": "text",
      "id": "feature_title_2",
      "label": "Feature Title",
      "default": "为什么您会喜欢它："
    },
    {
      "type": "textarea",
      "id": "feature_description_1",
      "label": "Feature Description",
      "default": "由可持续采伐的桤木雕刻而成，这个家具系列体现了天体的宁静。木材的微妙纹理如星尘般流淌，而哑光饰面反射着仙后座的低调光芒——因为真正的优雅从不张扬。"
    },
    {
      "type": "color",
      "id": "feature_bg_color",
      "label": "Feature Text Background Color",
      "default": "#C8A983"
    }
  ],
  "presets": [
    {
      "name": "Feature Section 1",
      "category": "Product"
    }
  ]
}
{% endschema %}