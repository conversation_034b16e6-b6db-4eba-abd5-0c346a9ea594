<style>
    /* 基于1920×1080基准的动态缩放样式 */

    /* 设置根字体大小，用于rem计算 */
    html {
        font-size: clamp(14px, 1vw, 18px); /* 响应式根字体大小 */
    }

    /* 特色区域容器 */
    .feature-section-2 {
        width: 100%; /* 保留百分比 */
        display: flex;
        flex-direction: column;
        gap: 4.95vw; /* 95px ÷ 1920 × 100 = 4.95vw */
    }

    /* 特色内容区域 */
    .feature-content-2 {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    /* 特色图片 */
    .feature-image-2 {
        width: calc(100% - 28.91vw); /* 100% - 555px (555px ÷ 1920 × 100 = 28.91vw) */
        height: 31.25vw; /* 600px ÷ 1920 × 100 = 31.25vw */
        overflow: hidden;
        position: relative;
        display: flex;
        align-items: center;
    }

    .feature-image-2 img {
        width: 100%; /* 保留百分比 */
        height: 100%; /* 保留百分比 */
        object-fit: cover; /* 保留固定值 */
        transition: transform 0.3s ease; /* 保留固定动画时间 */
        cursor: pointer; /* 添加pointer样式 */
        image-rendering: -webkit-optimize-contrast; /* Safari优化 */
        image-rendering: crisp-edges; /* 现代浏览器清晰边缘 */
        backface-visibility: hidden; /* 防止变换时模糊 */
        -webkit-backface-visibility: hidden; /* Safari兼容 */
    }

    .feature-image-2 img:hover {
        transform: scale(1.05); /* 保留固定缩放比例 */
    }

    /* 移动端和桌面端图片显示控制 */
    .feature-image-2 .mobile-image {
        display: none !important;
    }

    .feature-image-2 .desktop-image {
        display: block !important;
    }

    /* 图片链接样式 */
    .feature-image-link {
        display: block;
        width: 100%;
        height: 100%;
        text-decoration: none;
        outline: none;
    }

    .feature-image-link:focus {
        outline: 2px solid #007acc;
        outline-offset: 2px;
    }

    /* 确保链接内的图片样式保持一致 */
    .feature-image-link img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
        cursor: pointer;
    }

    .feature-image-link:hover img {
        transform: scale(1.05);
    }

    /* 特色文字内容 */
    .feature-text-2 {
        width: 28.91vw; /* 555px ÷ 1920 × 100 = 28.91vw */
        /*display: flex;*/
        /*flex-direction: column;*/
        /*justify-content: center;*/
        padding: 6.094vw 2.604vw 9.27vw 4.53vw; /* 117px 50px 178px 87px */
        color: #ffffff;
        background: {{ section.settings.feature_bg_color | default: '#C8A983' }};
    }


    .feature-text-2 .feature-description {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 0.83vw; /* 16px ÷ 1920 × 100 = 0.83vw */
        line-height: 1.56vw; /* 30px ÷ 1920 × 100 = 1.56vw */
        border: none;
        background: transparent;
        outline: none;
        width: 100%; /* 保留百分比 */
        color: inherit;
        overflow-y: auto;
        overflow-x: hidden;
        height: auto;
        max-height: calc(31.25vw - 10.21vw); /* (600px - 196px) ÷ 1920 × 100 = 21.04vw */
        min-height: fit-content;
        padding: 0;
        /*white-space: pre-wrap; !* 保留换行和空格 *!*/
        word-wrap: break-word; /* 长单词换行 */
        word-break: break-word; /* 强制换行 */
    }

    .feature-text-2 button {
        background: transparent;
        color: #ffffff;
        font-size: 1.35vw; /* 26px ÷ 1920 × 100 = 1.35vw */
        cursor: pointer;
        transition: all 0.3s ease; /* 保留固定动画时间 */
        margin-bottom: 1.56vw; /* 30px ÷ 1920 × 100 = 1.56vw */
        border-radius: 0.26vw; /* 5px ÷ 1920 × 100 = 0.26vw */
        text-align: left;
        border: none;
        display: flex;
        align-items: center;
    }

    .feature-text-2 button span {
        margin-right: 0.31vw; /* 6px ÷ 1920 × 100 = 1.35vw */
        font-family: Playfair Display;
        font-weight: 400;
        font-style: Regular;
        leading-trim: NONE;
        line-height: 100%;
        letter-spacing: 0px;
    }

    .feature-text-2 button:hover {
        {% comment %}background: #ffffff;{% endcomment %}
        {% comment %}color: {{ section.settings.feature_bg_color | default: '#C8A983' }};{% endcomment %}
    }

    /* 平板端响应式设计 */
    @media (max-width: 1024px) {
        .feature-section-2 {
            gap: 4.69vw; /* 48px ÷ 1024 × 100 = 4.69vw */
        }

        .feature-image-2 {
            height: 35.16vw; /* 360px ÷ 1024 × 100 = 35.16vw */
        }

        .feature-text-2 {
            padding: 3.91vw; /* 40px ÷ 1024 × 100 = 3.91vw */
        }


        .feature-text-2 button {
            font-size: 2.34vw; /* 24px ÷ 1024 × 100 = 2.34vw */
            margin-bottom: 1.95vw; /* 20px ÷ 1024 × 100 = 1.95vw */
        }

        .feature-text-2 .feature-description {
            font-size: 1.37vw; /* 14px ÷ 1024 × 100 = 1.37vw */
            line-height: 2.34vw; /* 24px ÷ 1024 × 100 = 2.34vw */
        }
    }

    /* 移动端响应式设计 - 基于768px基础使用vw单位 */
    @media (max-width: 768px) {
        .feature-section-2 {
            gap: 6.25vw; /* 48px ÷ 768 × 100 = 6.25vw，适当的移动端间距 */
        }

        .feature-content-2 {
            flex-direction: column;
        }

        .feature-image-2 {
            width: auto; /* 移动端自动宽度 */
            height: 39.06vw; /* 300px ÷ 768 × 100 = 39.06vw */
            order: 1; /* 保留固定顺序 */
            flex: 1 1 100%; /* 保留百分比 */
        }

        .feature-image-2 img {
            height: 100%; /* 保留百分比 */
            object-fit: cover; /* 保留固定值 */
        }

        /* 移动端图片显示控制 */
        .feature-image-2 .mobile-image {
            display: block !important;
            width: 100% !important;
            height: 100% !important;
            position: relative !important;
        }

        .feature-image-2 .mobile-image img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            display: block !important;
        }

        .feature-image-2 .desktop-image {
            display: none !important;
        }

        /* 调试：确保移动端图片容器可见 */
        .feature-image-2 {
            position: relative;
            overflow: hidden;
        }

        .feature-text-2 {
            width: auto; /* 移动端自动宽度 */
            padding: 2.60vw 1.17vw 3.91vw 2.08vw; /* 20px 9px 30px 16px 转换为vw (基于768px) */
            order: 2; /* 保留固定顺序 */
        }

        .feature-text-2 button {
            margin-bottom: 1.82vw; /* 14 ÷ 768 × 100 = 1.95vw */
            font-family: Lato;
            font-weight: 400;
            font-size: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
            line-height: 100%; /* 保留百分比 */
            letter-spacing: 0px;
            display: flex;
            align-items: center;
        }

        .feature-text-2 button span {
            margin-right: 0.78vw; /* 6px ÷ 768 × 100 = 0.78vw */
            line-height: 3.13vw; /* 24 ÷ 768 × 100 = 3.13vw */
        }

        .feature-text-2 .feature-description {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 1.82vw; /* 14px ÷ 768 × 100 = 1.82vw */
            line-height: 3.13vw; /* 24px ÷ 768 × 100 = 3.13vw */
            letter-spacing: 0px;
            max-height: none; /* 移动端取消高度限制，显示全部内容 */
            height: auto; /* 自动高度 */
            overflow-y: visible; /* 显示所有内容 */
            /*white-space: pre-wrap; !* 保留换行和空格 *!*/
            word-wrap: break-word; /* 长单词换行 */
            word-break: break-word; /* 强制换行 */
        }

        /* 移动端SVG图标大小控制 - 基于768px计算 */
        .feature-text-2 button svg {
            width: 2.08vw; /* 16px ÷ 768 × 100 = 2.08vw */
            height: 2.08vw; /* 16px ÷ 768 × 100 = 2.08vw */
        }
    }

    /* 超小屏幕保护 - 基于375px基础使用vw单位 */
    @media (max-width: 480px) {
        .feature-section-2 {
            gap: 12.8vw; /* 48px ÷ 375 × 100 = 12.8vw，移动端间距 */
        }

        .feature-content-2 {
            flex-direction: column;
        }

        .feature-image-2 {
            width: auto; /* 移动端自动宽度 */
            height: 64vw; /* 240px ÷ 375 × 100 = 64vw，适合小屏幕 */
            order: 1; /* 保留固定顺序 */
            flex: 1 1 100%; /* 保留百分比 */
        }

        .feature-image-2 img {
            height: 100%; /* 保留百分比 */
            object-fit: cover; /* 保留固定值 */
        }

        /* 移动端图片显示控制 */
        .feature-image-2 .mobile-image {
            display: block !important;
            width: 100% !important;
            height: 100% !important;
            position: relative !important;
        }

        .feature-image-2 .mobile-image img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            display: block !important;
        }

        .feature-image-2 .desktop-image {
            display: none !important;
        }

        /* 调试：确保移动端图片容器可见 */
        .feature-image-2 {
            position: relative;
            overflow: hidden;
        }

        .feature-text-2 {
            width: auto; /* 移动端自动宽度 */
            padding: 5.33vw 2.4vw 8vw 4.27vw; /* 20px 9px 30px 16px 转换为vw (基于375px) */
            order: 2; /* 保留固定顺序 */
        }

        .feature-text-2 button {
            margin-bottom: 3.73vw; /* 14 ÷ 375 × 100 = 4vw */
            font-family: Lato;
            font-weight: 400;
            font-size: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
            line-height: 100%; /* 保留百分比 */
            letter-spacing: 0px;
            display: flex;
            align-items: center;
        }

        .feature-text-2 button span {
            margin-right: 1.6vw; /* 6px ÷ 375 × 100 = 1.6vw */
            line-height: 6.4vw; /* 24 ÷ 375 × 100 = 6.4 */
        }

        .feature-text-2 .feature-description {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 3.73vw; /* 14px ÷ 375 × 100 = 3.73vw */
            line-height: 6.4vw; /* 24px ÷ 375 × 100 = 6.4vw */
            letter-spacing: 0px;
            max-height: none; /* 移动端取消高度限制，显示全部内容 */
            height: auto; /* 自动高度 */
            overflow-y: visible; /* 显示所有内容 */
            /*white-space: pre-wrap; !* 保留换行和空格 *!*/
            word-wrap: break-word; /* 长单词换行 */
            word-break: break-word; /* 强制换行 */
        }

        /* 超小屏幕SVG图标大小控制 - 基于375px计算 */
        .feature-text-2 button svg {
            width: 4.27vw; /* 16px ÷ 375 × 100 = 4.27vw */
            height: 4.27vw; /* 16px ÷ 375 × 100 = 4.27vw */
        }
    }

    /* 超大屏幕优化 - 防止元素过大 */
    @media (min-width: 2560px) {
        .feature-section-2 {
            gap: clamp(95px, 4.95vw, 140px); /* 限制最大间距 */
        }

        .feature-image-2 {
            height: clamp(600px, 31.25vw, 800px); /* 限制最大高度 */
        }

        .feature-image-2 img {
            image-rendering: auto; /* 超大屏幕使用自动渲染 */
            image-rendering: -webkit-optimize-contrast;
        }

        .feature-text-2 {
            padding: clamp(60px, 3.13vw, 100px); /* 限制最大内边距 */
        }


        .feature-text-2 button {
            font-size: clamp(26px, 1.35vw, 36px);
            margin-bottom: clamp(30px, 1.56vw, 40px);
        }

        /*.feature-text-2 textarea {*/
        /*    font-size: clamp(16px, 0.83vw, 20px);*/
        /*    line-height: clamp(30px, 1.56vw, 36px);*/
        /*    max-height: clamp(404px, 21.04vw, 500px); !* 限制最大高度 *!*/
        /*}*/
    }

    /* 高分辨率屏幕优化 */
    @media (min-width: 1920px) and (max-width: 2559px) {
        .feature-image-2 img {
            image-rendering: auto; /* 高分辨率屏幕使用自动渲染 */
            image-rendering: -webkit-optimize-contrast;
        }

        .feature-text-2 button {
            font-size: clamp(24px, 1.35vw, 30px);
        }

        .feature-text-2 textarea {
            font-size: clamp(15px, 0.83vw, 18px);
            line-height: clamp(28px, 1.56vw, 32px);
        }
    }
</style>

<!-- 第二个特色区域 -->
<div class="feature-section-2">
    <div class="feature-content-2">
        <div class="feature-text-2">
            <button onclick="window.location.href='{{ section.settings.feature_link }}'">
                <span>{{ section.settings.feature_title }}</span>
                {%- render 'icon', name: 'case-arrow' %}
            </button>
            <div class="feature-description">{{ section.settings.feature_description | newline_to_br }}</div>
        </div>
        <div class="feature-image-2">
            <!-- Desktop Image (only shown on desktop) -->
            {% if section.settings.feature_image != blank %}
                {% assign desktop_link = section.settings.feature_image_link %}
                {% assign desktop_new_tab = section.settings.open_image_link_new_tab %}
                {% assign desktop_image = section.settings.feature_image %}

                {% if desktop_link != blank %}
                    <a href="{{ desktop_link }}"
                       class="feature-image-link desktop-image"
                       {% if desktop_new_tab %}target="_blank" rel="noopener noreferrer"{% endif %}>
                        <img src="{{ desktop_image | img_url: '1600x1000' }}"
                             srcset="{{ desktop_image | img_url: '1200x800' }} 1024w,
                                     {{ desktop_image | img_url: '1600x1000' }} 1920w,
                                     {{ desktop_image | img_url: '2000x1200' }} 2560w"
                             sizes="(max-width: 1024px) 65vw, 71vw"
                             alt="工艺展示">
                    </a>
                {% else %}
                    <img src="{{ desktop_image | img_url: '1600x1000' }}"
                         srcset="{{ desktop_image | img_url: '1200x800' }} 1024w,
                                 {{ desktop_image | img_url: '1600x1000' }} 1920w,
                                 {{ desktop_image | img_url: '2000x1200' }} 2560w"
                         sizes="(max-width: 1024px) 65vw, 71vw"
                         class="desktop-image"
                         alt="工艺展示">
                {% endif %}
            {% endif %}

            <!-- Mobile Image (only shown on mobile) -->
            {% if section.settings.feature_image_mobile != blank %}
                <!-- Use mobile-specific image and settings -->
                {% assign mobile_link = section.settings.feature_image_mobile_link %}
                {% assign mobile_new_tab = section.settings.open_mobile_image_link_new_tab %}
                {% assign mobile_image = section.settings.feature_image_mobile %}

                {% if mobile_link != blank %}
                    <a href="{{ mobile_link }}"
                       class="feature-image-link mobile-image"
                       {% if mobile_new_tab %}target="_blank" rel="noopener noreferrer"{% endif %}>
                        <img src="{{ mobile_image | img_url: '800x600' }}"
                             srcset="{{ mobile_image | img_url: '600x400' }} 375w,
                                     {{ mobile_image | img_url: '800x600' }} 768w"
                             sizes="100vw"
                             alt="工艺展示">
                    </a>
                {% else %}
                    <img src="{{ mobile_image | img_url: '800x600' }}"
                         srcset="{{ mobile_image | img_url: '600x400' }} 375w,
                                 {{ mobile_image | img_url: '800x600' }} 768w"
                         sizes="100vw"
                         class="mobile-image"
                         alt="工艺展示">
                {% endif %}
            {% elsif section.settings.feature_image != blank %}
                <!-- Fallback: Use desktop image for mobile when mobile image is not set -->
                {% assign fallback_link = section.settings.feature_image_link %}
                {% assign fallback_new_tab = section.settings.open_image_link_new_tab %}
                {% assign fallback_image = section.settings.feature_image %}

                {% if fallback_link != blank %}
                    <a href="{{ fallback_link }}"
                       class="feature-image-link mobile-image"
                       {% if fallback_new_tab %}target="_blank" rel="noopener noreferrer"{% endif %}>
                        <img src="{{ fallback_image | img_url: '800x600' }}"
                             srcset="{{ fallback_image | img_url: '600x400' }} 375w,
                                     {{ fallback_image | img_url: '800x600' }} 768w"
                             sizes="100vw"
                             alt="工艺展示">
                    </a>
                {% else %}
                    <img src="{{ fallback_image | img_url: '800x600' }}"
                         srcset="{{ fallback_image | img_url: '600x400' }} 375w,
                                 {{ fallback_image | img_url: '800x600' }} 768w"
                         sizes="100vw"
                         class="mobile-image"
                         alt="工艺展示">
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>

<script>
// document.addEventListener('DOMContentLoaded', function() {
//     // Auto-resize description div to fit content with dynamic max height constraint
//     const descriptionDiv = document.querySelector('.feature-text-2 .feature-description');
//     if (descriptionDiv) {
//         // Function to calculate dynamic max height based on viewport
//         function adjustDescriptionHeight() {
//             const viewportWidth = window.innerWidth;
//
//             if (viewportWidth <= 768) {
//                 // 移动端: 取消高度限制，显示全部内容
//                 descriptionDiv.style.maxHeight = 'none';
//                 descriptionDiv.style.height = 'auto';
//                 descriptionDiv.style.overflow = 'visible';
//             } else if (viewportWidth <= 1024) {
//                 // 平板端: 基于vw计算
//                 const imageHeight = viewportWidth * 0.3516; // 35.16vw
//                 const padding = viewportWidth * 0.0391 * 2; // 3.91vw * 2
//                 const buttonArea = viewportWidth * 0.0625; // 约6.25vw
//                 const maxHeight = Math.max(150, imageHeight - padding - buttonArea);
//                 descriptionDiv.style.maxHeight = maxHeight + 'px';
//                 descriptionDiv.style.overflowY = 'auto';
//             } else if (viewportWidth >= 2560) {
//                 // 超大屏幕: 使用clamp限制
//                 const maxImageHeight = Math.min(800, viewportWidth * 0.3125); // clamp(600px, 31.25vw, 800px)
//                 const maxPadding = Math.min(100, viewportWidth * 0.0313) * 2; // clamp(60px, 3.13vw, 100px) * 2
//                 const buttonArea = Math.min(140, viewportWidth * 0.0521); // 约5.21vw
//                 const maxHeight = Math.max(300, maxImageHeight - maxPadding - buttonArea);
//                 descriptionDiv.style.maxHeight = maxHeight + 'px';
//                 descriptionDiv.style.overflowY = 'auto';
//             } else {
//                 // 桌面端: 31.25vw - 10.21vw = 21.04vw
//                 const maxHeight = Math.max(200, viewportWidth * 0.2104);
//                 descriptionDiv.style.maxHeight = maxHeight + 'px';
//                 descriptionDiv.style.overflowY = 'auto';
//             }
//         }
//
//         // Adjust height on load
//         adjustDescriptionHeight();
//
//         // Adjust height on window resize with debounce
//         let resizeTimeout;
//         window.addEventListener('resize', function() {
//             clearTimeout(resizeTimeout);
//             resizeTimeout = setTimeout(adjustDescriptionHeight, 100);
//         });
//
//         // Adjust height if content changes (though it's readonly)
//         textarea.addEventListener('input', adjustTextareaHeight);
//     }
// });
</script>

{% schema %}
{
  "name": "Feature Section 2",
  "settings": [
    {
      "type": "image_picker",
      "id": "feature_image",
      "label": "Desktop Feature Image"
    },
    {
      "type": "image_picker",
      "id": "feature_image_mobile",
      "label": "Mobile Feature Image",
      "info": "Optional: Use a different image for mobile devices. If not set, the desktop image will be displayed on mobile devices as a fallback."
    },
    {
      "type": "url",
      "id": "feature_image_link",
      "label": "Desktop Image Click Link",
      "info": "Optional: Add a link when users click on the desktop image"
    },
    {
      "type": "checkbox",
      "id": "open_image_link_new_tab",
      "label": "Open desktop image link in new tab",
      "default": false
    },
    {
      "type": "url",
      "id": "feature_image_mobile_link",
      "label": "Mobile Image Click Link",
      "info": "Optional: Add a link when users click on the mobile image. Only applies when a mobile image is set. If mobile image is not set, the desktop link will be used as fallback."
    },
    {
      "type": "checkbox",
      "id": "open_mobile_image_link_new_tab",
      "label": "Open mobile image link in new tab",
      "default": false
    },
    {
      "type": "text",
      "id": "feature_title",
      "label": "Feature Title",
      "default": "工艺精湛："
    },
    {
      "type": "textarea",
      "id": "feature_description",
      "label": "Feature Description",
      "default": "每件家具都经过精心制作，注重细节和品质。我们使用传统工艺结合现代设计理念，确保每件产品都能经受时间的考验，同时保持其独特的美学魅力。"
    },
    {
      "type": "url",
      "id": "feature_link",
      "label": "Feature Button Link"
    },
    {
      "type": "color",
      "id": "feature_bg_color",
      "label": "Feature Text Background Color",
      "default": "#C8A983"
    }
  ],
  "presets": [
    {
      "name": "Feature Section 2",
      "category": "Product"
    }
  ]
}
{% endschema %}