<!-- 页面样式 -->
<style>
    /* Web端样式 - 基于1920px计算vw */
    .featured-express {
        padding: 3.33vw 5.21vw 3.39vw 5.21vw; /* 64px 100px 65px 100px */
    }
    .featured-express .express-title {
        font-family: Playfair Display;
        font-weight: 400;
        font-style: Regular;
        font-size: 1.35vw; /* 26px */
        leading-trim: NONE;
        line-height: 100%;
        letter-spacing: 0px;
        margin-bottom: 1.56vw; /* 30px */
    }
    .featured-express .container {
        margin: 0 auto;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(15.63vw, 1fr)); /* minmax(300px, 1fr) */
        gap: 2.6vw; /* 50px */
    }

    .featured-express .magazine-card {
        overflow: hidden;
        /*box-shadow: 0 0.1vw 0.52vw rgba(0,0,0,0.1); !* 0 2px 10px *!*/
        transition: transform 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        text-align: center;
        height: 18.75vw; /* 360px */
    }

    /* 左边的magazine-card padding */
    .featured-express .magazine-card:first-child {
        padding: 5.21vw 0 5.16vw 0; /* 100px 0 99px 0 */
    }

    /* 右边的magazine-card padding */
    .featured-express .magazine-card:nth-child(2) {
        padding: 5.89vw 0 5.16vw 0; /* 113px 0 99px 0 */
    }

    /* magazine-content 垂直居中容器 */
    .featured-express .magazine-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
    }

    /*.magazine-card:hover {*/
    /*    transform: translateY(-5px);*/
    /*}*/

    .featured-express .magazine-logo {
        object-fit: contain;
        border-radius: 0.42vw; /* 8px */
    }

    /* 桌面端图片显示，移动端隐藏 */
    .featured-express .magazine-logo-desktop {
        display: block;
    }

    /* 移动端图片在桌面端隐藏 */
    .featured-express .magazine-logo-mobile {
        display: none;
    }

    .featured-express  .magazine-name {
        margin-bottom: 1.146vw; /* 22 */
        text-align: center;
        color: #333333; /* 默认颜色 */
        font-family: Playfair Display;
        font-weight: 400;
        font-style: Regular;
        font-size: 0.94vw; /* 18 */
        leading-trim: NONE;
        line-height: 1.25vw; /* 24 */
        letter-spacing: 0px;
    }

    .featured-express  .read-more {
        display: inline-block;
        width: 8.02vw; /* 154px */
        height: 1.98vw; /* 38px */
        padding: 0.52vw; /* 10px */
        gap: 0.52vw; /* 10px */
        background: transparent;
        color: #6D4C41;
        border: 0.052vw solid #6D4C41; /* 1px border */
        border-radius: 3.125vw; /* 60px */
        font-family: PingFang SC;
        font-style: Semibold;
        font-size: 0.73vw; /* 14px */
        leading-trim: NONE;
        line-height: 100%;
        letter-spacing: 0px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        outline: none;
        opacity: 1;
    }

    /* Web端显示按钮，隐藏图标 */
    .featured-express .read-more-button {
        display: inline-block;
    }

    .featured-express .read-more-icon {
        display: none;
    }

    .featured-express .read-more:hover {
        background: #502212;
        color: #F3E8DD;
        /*transform: translateY(-0.1vw); !* 轻微上移效果 *!*/
    }

    .featured-express .read-more:active {
        transform: translateY(0);
    }

    /* 平板端样式 - 基于768px计算vw */
    @media (max-width: 768px) {
        .featured-express {
            padding: 4.17vw 2.6vw; /* 32px 20px */
        }

        .featured-express .express-title {
            font-size: 2.60vw; /* 20 */
            margin-bottom: 3.91vw; /* 30px */
        }

        .featured-express .container {
            max-width: 100%;
            grid-template-columns: 1fr 1fr; /* 两列布局 */
            gap: 1.95vw; /* 15 */
        }

        .featured-express .magazine-card {
            height: 13.02vw; /* 100 */
            box-shadow: 0 0.26vw 1.3vw rgba(0,0,0,0.1); /* 0 2px 10px */
        }

        /* 平板端左边的magazine-card padding */
        .featured-express .magazine-card:first-child {
            padding: 1.56vw 0 1.56vw 0; /* 12px 0 12px 0 基于768px */
        }

        /* 平板端右边的magazine-card padding */
        .featured-express .magazine-card:nth-child(2) {
            padding: 3.65vw 0 1.56vw 0; /* 28px 0 12px 0 基于768px */
        }

        /* 平板端magazine-content 垂直居中容器 */
        .featured-express .magazine-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
        }

        .featured-express .magazine-logo {
            /*border-radius: 1.04vw; !* 8px *!*/
            width: auto; /* 保持等比例缩放 */
        }

        /* 平板端左边图片高度 - 基于768px计算 */
        .featured-express .magazine-card:first-child .magazine-logo {
            height: 4.30vw; /* 33px / 768px * 100 = 4.30vw */
        }

        /* 平板端右边图片高度 - 基于768px计算 */
        .featured-express .magazine-card:nth-child(2) .magazine-logo {
            height: 2.21vw; /* 17px / 768px * 100 = 2.21vw */
        }

        /* 平板端显示移动端图片，隐藏桌面端图片 */
        .featured-express .magazine-logo-desktop {
            display: none;
        }

        .featured-express .magazine-logo-mobile {
            display: block;
        }

        .featured-express .magazine-name {
            font-family: Playfair Display;
            font-weight: 400;
            font-style: Regular;
            font-size: 1.43vw; /* 11px / 768px * 100 = 1.43vw */
            leading-trim: NONE;
            line-height: 1.82vw; /* 14px / 768px * 100 = 1.82vw */
            letter-spacing: 0px;
            text-align: center;
            margin-bottom: 1.04vw; /* 8 */
        }

        .featured-express .read-more {
            width: 20.05vw; /* 154px */
            height: 4.95vw; /* 38px */
            padding: 1.3vw; /* 10px */
            gap: 1.3vw; /* 10px */
            border: 0.13vw solid white; /* 1px border */
            border-radius: 7.81vw; /* 60px */
            font-family: PingFang SC;
            font-weight: 600;
            font-style: Semibold;
            font-size: 1.82vw; /* 14px */
            leading-trim: NONE;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
        }

        /* 平板端隐藏按钮，显示图标 */
        .featured-express .read-more-button {
            display: none;
        }

        .featured-express .read-more-icon {
            display: inline-block;
            cursor: pointer;
            width: 2.60vw; /* 20px / 768px * 100 = 2.60vw */
            height: 2.60vw; /* 20px / 768px * 100 = 2.60vw */
            background: transparent; /* 背景色透明 */
        }

        .featured-express .read-more-icon svg {
            width: 100%;
            height: 100%;
            /*fill: #333333; !* 线条颜色 *!*/
            stroke: #fff; /* 线条颜色 */
            transition: fill 0.3s ease, stroke 0.3s ease;
        }

        .featured-express .read-more-icon:hover svg {
            /*fill: #333333;*/
            stroke: #333333;
        }
    }

    /* 手机端样式 - 基于375px计算vw */
    @media (max-width: 480px) {
        .featured-express {
            padding: 8.53vw 5.33vw; /* 32px 20px */
        }

        .featured-express .express-title {
            font-size: 5.33vw; /* 20 */
            margin-bottom: 8vw; /* 30px */
        }

        .featured-express .container {
            grid-template-columns: 1fr 1fr; /* 两列布局 */
            gap: 4vw; /* 30px */
        }

        .featured-express .magazine-card {
            height: 26.67vw; /* 100 */
            box-shadow: 0 0.53vw 2.67vw rgba(0,0,0,0.1); /* 0 2px 10px */
            padding: 5.33vw 2.67vw; /* 12 */
        }

        /* 手机端左边的magazine-card padding */
        .featured-express .magazine-card:first-child {
            padding: 3.2vw 0 3.2vw 0; /* 12px 0 12px 0 基于375px */
        }

        /* 手机端右边的magazine-card padding */
        .featured-express .magazine-card:nth-child(2) {
            padding: 7.47vw 0 3.2vw 0; /* 28px 0 12px 0 基于375px */
        }

        /* 手机端magazine-content 垂直居中容器 */
        .featured-express .magazine-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
        }

        .featured-express .magazine-logo {
            /*border-radius: 2.13vw; !* 8px *!*/
            width: auto; /* 保持等比例缩放 */
        }

        /* 手机端左边图片高度 - 基于375px计算 */
        .featured-express .magazine-card:first-child .magazine-logo {
            height: 8.8vw; /* 33px / 375px * 100 = 8.8vw */
        }

        /* 手机端右边图片高度 - 基于375px计算 */
        .featured-express .magazine-card:nth-child(2) .magazine-logo {
            height: 4.53vw; /* 17px / 375px * 100 = 4.53vw */
        }

        /* 手机端显示移动端图片，隐藏桌面端图片 */
        .featured-express .magazine-logo-desktop {
            display: none;
        }

        .featured-express .magazine-logo-mobile {
            display: block;
        }

        .featured-express .magazine-name {
            font-family: Playfair Display;
            font-weight: 400;
            font-style: Regular;
            font-size: 2.93vw; /* 11px / 375px * 100 = 2.93vw */
            leading-trim: NONE;
            line-height: 3.73vw; /* 14px / 375px * 100 = 3.73vw */
            letter-spacing: 0px;
            text-align: center;
            margin-bottom: 2.13vw; /* 8 */
        }

        .featured-express .read-more {
            width: 36vw; /* 减小按钮宽度适应两列布局 */
            height: 10.13vw; /* 38px */
            padding: 2.67vw; /* 10px */
            gap: 2.67vw; /* 10px */
            border: 0.27vw solid white; /* 1px border */
            border-radius: 16vw; /* 60px */
            font-family: PingFang SC;
            font-weight: 600;
            font-style: Semibold;
            font-size: 3.2vw; /* 减小字体大小 */
            leading-trim: NONE;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
        }

        /* 手机端隐藏按钮，显示图标 */
        .featured-express .read-more-button {
            display: none;
        }

        .featured-express .read-more-icon {
            display: inline-block;
            cursor: pointer;
            width: 5.33vw; /* 20px / 375px * 100 = 5.33vw */
            height: 5.33vw; /* 20px / 375px * 100 = 5.33vw */
            background: transparent; /* 背景色透明 */
        }

        .featured-express .read-more-icon svg {
            width: 100%;
            height: 100%;
            /*fill: #333333; !* 线条颜色 *!*/
            stroke: #333333; /* 线条颜色 */
            transition: fill 0.3s ease, stroke 0.3s ease;
        }

        .featured-express .read-more-icon:hover svg {
            /*fill: #333333;*/
            stroke: #333333;
        }
    }
</style>

<div class="featured-express">
    <div class="express-title">{{ section.settings.title | default: 'Featured in Express' }}</div>
    <div class="container">
        {% for block in section.blocks %}
            {% case block.type %}
                {% when 'magazine_card' %}
                    <div class="magazine-card" style="background-color: {{ block.settings.background_color | default: '#333' }};">
                        <div class="magazine-content">
                            {% if block.settings.logo_image or block.settings.logo_image_mobile %}
                                <!-- 桌面端图片 -->
                                {% if block.settings.logo_image %}
                                    <img src="{{ block.settings.logo_image | image_url }}"
                                         alt="{{ block.settings.magazine_name }}"
                                         class="magazine-logo magazine-logo-desktop">
                                {% endif %}
                                <!-- 移动端图片 -->
                                {% if block.settings.logo_image_mobile %}
                                    <img src="{{ block.settings.logo_image_mobile | image_url }}"
                                         alt="{{ block.settings.magazine_name }}"
                                         class="magazine-logo magazine-logo-mobile">
                                {% endif %}
                            {% endif %}
                            <div class="magazine-name" style="color: {{ block.settings.text_color | default: '#333333' }};">{{ block.settings.magazine_name }}</div>
                        </div>
                        {% if block.settings.read_more_url %}
                            <!-- Web端按钮 -->
                            <button onclick="window.open('{{ block.settings.read_more_url }}', '_blank')" class="read-more read-more-button">
                                {{ block.settings.read_more_text | default: 'Read More' }}
                            </button>
                            <!-- 移动端图标 -->
                            <div onclick="window.open('{{ block.settings.read_more_url }}', '_blank')" class="read-more-icon">
                                {%- render 'icon', name: 'more-arrow' %}
                            </div>
                        {% endif %}
                    </div>
            {% endcase %}
        {% endfor %}
    </div>
</div>

{% schema %}
{
  "name": "Featured in Express",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Featured in Express"
    }
  ],
  "blocks": [
    {
      "type": "magazine_card",
      "name": "Magazine Card",
      "settings": [
        {
          "type": "image_picker",
          "id": "logo_image",
          "label": "Magazine Logo (Desktop)"
        },
        {
          "type": "image_picker",
          "id": "logo_image_mobile",
          "label": "Magazine Logo (Mobile)"
        },
        {
          "type": "text",
          "id": "magazine_name",
          "label": "Magazine Name",
          "default": "Magazine Name"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#333333"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Magazine Name Text Color",
          "default": "#333333"
        },
        {
          "type": "url",
          "id": "read_more_url",
          "label": "Read More Link"
        },
        {
          "type": "text",
          "id": "read_more_text",
          "label": "Read More Text",
          "default": "Read More"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Featured in Express",
      "blocks": [
        {
          "type": "magazine_card",
          "settings": {
            "magazine_name": "Architectural Digest",
            "background_color": "#333333"
          }
        },
        {
          "type": "magazine_card",
          "settings": {
            "magazine_name": "The New York Times",
            "background_color": "#333333"
          }
        }
      ]
    }
  ]
}
{% endschema %}