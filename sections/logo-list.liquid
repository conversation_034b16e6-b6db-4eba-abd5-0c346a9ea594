{%- render 'section-logo-list' -%}

{% schema %}
{
  "name": "t:labels.logo_list",
  "class": "index-section",
  "max_blocks": 12,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:labels.heading",
      "default": "Our brands"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:labels.heading_size",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "t:labels.sizes.small"
        },
        {
          "value": "h2",
          "label": "t:labels.sizes.medium"
        },
        {
          "value": "h1",
          "label": "t:labels.sizes.large"
        },
        {
          "value": "h0",
          "label": "t:labels.sizes.extra_large"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_position",
      "label": "t:labels.heading_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "center",
          "label": "t:labels.alignments.center"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:actions.show_section_divider",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "logo_image",
      "name": "t:labels.logo",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:labels.image",
          "info": "t:actions.add_alt_text"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:labels.link",
          "info": "t:labels.optional"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:labels.logo_list",
      "blocks": [
        {
          "type": "logo_image"
        },
        {
          "type": "logo_image"
        },
        {
          "type": "logo_image"
        },
        {
          "type": "logo_image"
        },
        {
          "type": "logo_image"
        },
        {
          "type": "logo_image"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
