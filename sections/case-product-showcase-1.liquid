<style>
  /* 基于1920×1080基准的动态缩放样式 */

  /* 设置根字体大小，用于rem计算 */
  html {
    font-size: clamp(14px, 1vw, 18px); /* 响应式根字体大小 */
  }

  /* 产品展示区域 - 桌面端使用vw，移动端使用rem */
  .product-showcase-1 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 5.21vw; /* 100px ÷ 1920 × 100 = 5.21vw */
    gap: 4.17vw; /* 80px ÷ 1920 × 100 = 4.17vw */
    margin: 4.17vw 0; /* 80px转换为vw */
  }

  .product-showcase-1 .product-item {
    flex: 1 1 45%; /* 保留百分比，确保布局稳定 */
    position: relative;
    overflow: hidden;
    max-width: 42.71vw; /* 820px ÷ 1920 × 100 = 42.71vw */
    aspect-ratio: 1 / 1; /* 保留固定比例 */
  }

  .product-showcase-1 .product-item img {
    width: 100%; /* 保留百分比 */
    height: 100%; /* 保留百分比 */
    object-fit: cover; /* 保留固定值 */
    transition: transform 0.3s ease; /* 保留固定动画时间 */
    cursor: pointer; /* 添加pointer样式 */
  }

  .product-showcase-1 .product-item img:hover {
    transform: scale(1.05); /* 保留固定缩放比例 */
  }

  /* 图片链接样式 */
  .product-showcase-1 .product-image-link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
    outline: none;
  }

  .product-showcase-1 .product-image-link:focus {
    outline: 2px solid #007acc;
    outline-offset: 2px;
  }

  /* 确保链接内的图片样式保持一致 */
  .product-showcase-1 .product-image-link img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    cursor: pointer;
  }

  .product-showcase-1 .product-image-link:hover img {
    transform: scale(1.05);
  }

  .product-showcase-1 .product-button {
    position: absolute;
    bottom: 1.04vw; /* 20px ÷ 1920 × 100 = 1.04vw */
    right: 0;
    background-color: {{ section.settings.button_bg_color | default: 'rgba(255, 255, 255, 0.7)' }};
    color: {{ section.settings.button_text_color | default: '#000000' }};
    padding: 0.677vw 2.45vw; /* 13px 47px 转换为vw */
    font-family: 'Playfair Display', serif;
    font-weight: 400;
    font-size: 1.35vw; /* 26px ÷ 1920 × 100 = 1.35vw */
    line-height: 100%; /* 保留百分比 */
    border: none;
    cursor: pointer;
    transition: all 0.3s ease; /* 保留固定动画时间 */
    border-radius: 2.60vw 0 0 2.60vw; /* 50px转换为vw */
    display: flex;
    align-items: center;
  }

  .product-showcase-1 .product-button:hover {
    background-color: {{ section.settings.button_bg_hover_color | default: 'rgba(255, 255, 255, 1)' }};
    color: {{ section.settings.button_text_hover_color | default: section.settings.button_text_color | default: '#000000' }};
  }

  .product-showcase-1 .product-button svg path {
    stroke: {{ section.settings.button_svg_color | default: '#6D4C41' }};
    transition: stroke 0.3s ease; /* 保留固定动画时间 */
  }

  .product-showcase-1 .product-button:hover svg path {
    stroke: {{ section.settings.button_svg_hover_color | default: section.settings.button_svg_color | default: '#6D4C41' }};
  }

  .product-showcase-1 .product-button span {
    margin-right: 0.42vw; /* 8px ÷ 1920 × 100 = 0.42vw */
  }

  /* 链接按钮样式增强 */
  .product-showcase-1 a.product-button {
    text-decoration: none; /* 移除链接下划线 */
  }

  /* 禁用状态的按钮样式 */
  .product-showcase-1 .product-button-disabled {
    cursor: default;
    opacity: 0.6;
  }

  .product-showcase-1 .product-button-disabled:hover {
    background-color: {{ section.settings.button_bg_color | default: 'rgba(255, 255, 255, 0.7)' }};
    color: {{ section.settings.button_text_color | default: '#000000' }};
    transform: none;
  }

  .product-showcase-1 .product-button-disabled:hover svg path {
    stroke: {{ section.settings.button_svg_color | default: '#6D4C41' }};
  }

  /* 链接按钮的焦点状态 */
  .product-showcase-1 a.product-button:focus {
    outline: 2px solid #007acc;
    outline-offset: 2px;
  }

  /* 平板端响应式设计 */
  @media (max-width: 1024px) {
    .product-showcase-1 {
      padding: 0 4.88vw; /* 50px ÷ 1024 × 100 = 4.88vw */
      gap: 3.91vw; /* 40px ÷ 1024 × 100 = 3.91vw */
    }

    .product-showcase-1 .product-button {
      font-size: 2.34vw; /* 24px ÷ 1024 × 100 = 2.34vw */
      padding: 1.17vw 1.95vw; /* 12px 20px 转换 */
    }
  }


  /* 移动端响应式设计 - 基于768px基础使用vw单位 */
  @media (max-width: 768px) {
    .product-showcase-1 {
      flex-direction: column;
      padding: 0 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
      margin: 2.86vw 0 2.60vw 0; /* 22px 0 20px 0 转换为vw (基于768px) */
      gap: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
    }

    .product-showcase-1 .product-item {
      max-width: 100%; /* 移动端占满宽度 */
    }

    .product-showcase-1 .product-button {
      font-size: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
      padding: 1.56vw 2.60vw; /* 12px 20px 转换为vw (基于768px) */
      border-radius: 3.13vw 0 0 3.13vw; /* 24px ÷ 768 × 100 = 3.13vw */
      bottom: 2.60vw; /* 20px ÷ 768 × 100 = 2.60vw */
    }

    .product-showcase-1 .product-button span {
      margin-right: 1.04vw; /* 8px ÷ 768 × 100 = 1.04vw */
    }

    .product-showcase-1 .product-button svg {
      width: 2.08vw; /* 16px ÷ 768 × 100 = 2.08vw */
      height: 2.08vw; /* 16px ÷ 768 × 100 = 2.08vw */
    }
  }

  /* 超小屏幕保护 - 基于375px基础使用vw单位 */
  @media (max-width: 480px) {
    .product-showcase-1 {
      flex-direction: column;
      padding: 0 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
      margin: 5.87vw 0 5.33vw 0; /* 22px 0 20px 0 转换为vw (基于375px) */
      gap: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
    }

    .product-showcase-1 .product-item {
      max-width: 100%; /* 移动端占满宽度 */
    }

    .product-showcase-1 .product-button {
      font-size: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
      padding: 3.2vw 5.33vw; /* 12px 20px 转换为vw (基于375px) */
      border-radius: 6.4vw 0 0 6.4vw; /* 24px ÷ 375 × 100 = 6.4vw */
      bottom: 5.33vw; /* 20px ÷ 375 × 100 = 5.33vw */
    }

    .product-showcase-1 .product-button span {
      margin-right: 2.13vw; /* 8px ÷ 375 × 100 = 2.13vw */
    }

    .product-showcase-1 .product-button svg {
      width: 4.27vw; /* 16px ÷ 375 × 100 = 4.27vw */
      height: 4.27vw; /* 16px ÷ 375 × 100 = 4.27vw */
    }
  }

  /* 超大屏幕优化 - 防止元素过大 */
  @media (min-width: 2560px) {
    .product-showcase-1 {
      padding: 0 clamp(100px, 5.21vw, 150px); /* 限制最大内边距 */
      gap: clamp(80px, 4.17vw, 120px); /* 限制最大间距 */
      margin: clamp(80px, 4.17vw, 120px) 0;
    }

    .product-showcase-1 .product-item {
      max-width: clamp(820px, 42.71vw, 1000px); /* 限制最大宽度 */
    }

    .product-showcase-1 .product-button {
      font-size: clamp(26px, 1.35vw, 32px); /* 限制最大字体 */
      padding: clamp(15px, 0.78vw, 20px) clamp(25px, 1.30vw, 35px);
      border-radius: clamp(50px, 2.60vw, 60px) 0 0 clamp(50px, 2.60vw, 60px);
    }
  }

  /* 高分辨率屏幕优化 */
  @media (min-width: 1920px) and (max-width: 2559px) {
    .product-showcase-1 .product-button {
      font-size: clamp(24px, 1.35vw, 28px); /* 在标准和大屏之间平滑过渡 */
    }
  }
</style>

<div class="product-showcase-1">
  <!-- 第一个展示项 -->
  {% if section.settings.showcase_image_1 %}
    <div class="product-item">
      {% if section.settings.showcase_image_link_1 != blank %}
        <a href="{{ section.settings.showcase_image_link_1 }}"
           class="product-image-link"
           {% if section.settings.open_image_link_new_tab_1 %}target="_blank" rel="noopener noreferrer"{% endif %}>
          <img src="{{ section.settings.showcase_image_1 | img_url: '800x800' }}" alt="{{ section.settings.showcase_title_1 | escape }}">
        </a>
      {% else %}
        <img src="{{ section.settings.showcase_image_1 | img_url: '800x800' }}" alt="{{ section.settings.showcase_title_1 | escape }}">
      {% endif %}
      {% if section.settings.showcase_link_1 != blank %}
        <a href="{{ section.settings.showcase_link_1 }}"
           class="product-button"
           {% if section.settings.open_link_new_tab_1 %}target="_blank" rel="noopener noreferrer"{% endif %}>
          <span>{{ section.settings.showcase_title_1 | default: 'Featured Product 1' }}</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M18 10H2" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M10 4L18 10L10 16" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </a>
      {% else %}
        <div class="product-button product-button-disabled">
          <span>{{ section.settings.showcase_title_1 | default: 'Featured Product 1' }}</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M18 10H2" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M10 4L18 10L10 16" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      {% endif %}
    </div>
  {% endif %}

  <!-- 第二个展示项 -->
  {% if section.settings.showcase_image_2 %}
    <div class="product-item">
      {% if section.settings.showcase_image_link_2 != blank %}
        <a href="{{ section.settings.showcase_image_link_2 }}"
           class="product-image-link"
           {% if section.settings.open_image_link_new_tab_2 %}target="_blank" rel="noopener noreferrer"{% endif %}>
          <img src="{{ section.settings.showcase_image_2 | img_url: '800x800' }}" alt="{{ section.settings.showcase_title_2 | escape }}">
        </a>
      {% else %}
        <img src="{{ section.settings.showcase_image_2 | img_url: '800x800' }}" alt="{{ section.settings.showcase_title_2 | escape }}">
      {% endif %}
      {% if section.settings.showcase_link_2 != blank %}
        <a href="{{ section.settings.showcase_link_2 }}"
           class="product-button"
           {% if section.settings.open_link_new_tab_2 %}target="_blank" rel="noopener noreferrer"{% endif %}>
          <span>{{ section.settings.showcase_title_2 | default: 'Featured Product 2' }}</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M18 10H2" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M10 4L18 10L10 16" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </a>
      {% else %}
        <div class="product-button product-button-disabled">
          <span>{{ section.settings.showcase_title_2 | default: 'Featured Product 2' }}</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M18 10H2" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M10 4L18 10L10 16" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      {% endif %}
    </div>
  {% endif %}
</div>

{% schema %}
{
  "name": "Product Showcase 1",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "image_picker",
      "id": "showcase_image_1",
      "label": "First Showcase Image"
    },
    {
      "type": "url",
      "id": "showcase_image_link_1",
      "label": "First Image Click Link",
      "info": "Optional: Add a link when users click on the first image"
    },
    {
      "type": "checkbox",
      "id": "open_image_link_new_tab_1",
      "label": "Open first image link in new tab",
      "default": false
    },
    {
      "type": "text",
      "id": "showcase_title_1",
      "label": "First Showcase Title",
      "default": "Featured Product 1"
    },
    {
      "type": "url",
      "id": "showcase_link_1",
      "label": "First Showcase Button Link",
      "info": "Where the button should link to"
    },
    {
      "type": "checkbox",
      "id": "open_link_new_tab_1",
      "label": "Open first button link in new tab",
      "default": false
    },
    {
      "type": "image_picker",
      "id": "showcase_image_2",
      "label": "Second Showcase Image"
    },
    {
      "type": "url",
      "id": "showcase_image_link_2",
      "label": "Second Image Click Link",
      "info": "Optional: Add a link when users click on the second image"
    },
    {
      "type": "checkbox",
      "id": "open_image_link_new_tab_2",
      "label": "Open second image link in new tab",
      "default": false
    },
    {
      "type": "text",
      "id": "showcase_title_2",
      "label": "Second Showcase Title",
      "default": "Featured Product 2"
    },
    {
      "type": "url",
      "id": "showcase_link_2",
      "label": "Second Showcase Button Link",
      "info": "Where the button should link to"
    },
    {
      "type": "checkbox",
      "id": "open_link_new_tab_2",
      "label": "Open second button link in new tab",
      "default": false
    },
    {
      "type": "header",
      "content": "Button Styling"
    },
    {
      "type": "color",
      "id": "button_bg_color",
      "label": "Button Background Color",
      "default": "rgba(255, 255, 255, 0.7)"
    },
    {
      "type": "color",
      "id": "button_bg_hover_color",
      "label": "Button Background Hover Color",
      "default": "rgba(255, 255, 255, 1)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button Text Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "button_text_hover_color",
      "label": "Button Text Hover Color",
      "info": "Leave blank to use the same as text color"
    },
    {
      "type": "color",
      "id": "button_svg_color",
      "label": "Button SVG Color",
      "default": "#6D4C41"
    },
    {
      "type": "color",
      "id": "button_svg_hover_color",
      "label": "Button SVG Hover Color",
      "info": "Leave blank to use the same as SVG color"
    }
  ],
  "presets": [
    {
      "name": "Product Showcase 1",
      "category": "Product"
    }
  ]
}
{% endschema %}