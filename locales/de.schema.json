{"actions": {"add_alt_text": "Alt-Text für verbesserte Suchmaschinenoptimierung mit der Schaltfläche „Bearbeiten“ oben hinzufügen", "add_bottom_spacing": "Abstand unten hinzufügen", "add_my_account_link": "Link „Mein Konto“ hinzufügen, wenn Kundenkonten aktiviert sind", "add_phone_number": "Telefonnummernfeld hinzufügen", "add_reviews_by": "Füge Bewertungen hinzu, indem du die untenstehende Einstellung aktivierst, die [Shopify-Product-Reviews-App](https://apps.shopify.com/product-reviews) installierst und unsere [Einrichtungsanleitung](https://archetypethemes.co/blogs/support/installing-shopifys-product-reviews-app) befolgst.", "add_spacing": "Abstände hinzufügen", "add_spacing_above": "Abstände oben und unten hinzufügen", "add_top_spacing": "Abstand oben hinzufügen", "approve_button_text": "Schaltflächentext genehmigen", "autoplay_video": "Autoplay-Video", "blur_the_image": "Verwischen Sie das Bild", "capitalize_first_letter": "<PERSON><PERSON> großschreiben", "choose_a_menu": "<PERSON><PERSON> w<PERSON>en", "collapse_filters": "<PERSON><PERSON> e<PERSON>", "decline_button_text": "Schaltflächentext ablehnen", "disable_accordion": "Akkordeon deaktivieren", "disable_for_accounts": "<PERSON><PERSON><PERSON>", "do_not_show_on_home": "Nicht auf Startseite anzeigen", "enable_additional_checkout": "Zusätzliche Checkout-Schaltflächen aktivieren", "enable_color_swatches": "Color-Swatches aktivieren", "enable_dropdown_on_hover": "Dropdown beim Hover aktivieren", "enable_dynamic_product": "Aktivieren Sie dynamische Produktoptionen", "enable_filter": "<PERSON>lter aktivieren", "enable_full_width": "Volle Breite aktivieren", "enable_header": "Header aktivieren", "enable_image_zoom": "Bildzoom einschalten", "enable_newsletter": "Newsletter aktivieren", "enable_order_notes": "Bestellhinweise aktivieren", "enable_parallax": "Parallaxe aktivieren", "enable_pickup_availability": "Aktivieren Sie die Funktion Abholbereitschaft", "enable_predictive_search": "Prädiktive Suche aktivieren", "enable_product_reviews": "Produktrezensionen aktivieren", "enable_quick_add": "Schnelles Hinzufügen aktivieren", "enable_quick_view": "Schnellansicht aktivieren", "enable_sticky_header": "Fixierten Header aktivieren", "enable_swipe_on": "Wischen auf Mobilgeräten aktivieren", "enable_terms": "Kontrollkästchen „Geschäftsbedingungen“ aktivieren", "enable_test_mode": "Testmodus aktivieren", "enable_video_looping": "Videoschleife aktivieren", "enlarge_text": "Text vergrößern", "force_image_size": "Bildgröße erzwingen", "hide_all_image_blocks_mobile": "Auf Mobilgeräten alle Bildblöcke verbergen", "hide_controls": "Steuerelemente ausblenden", "init_display_opened": "Wird zunächst im geöffneten Zustand angezeigt", "learn_to_setup_color_swatches": "<PERSON><PERSON><PERSON><PERSON>, dass der Typ auf \"Schaltflächen\" festgelegt ist. [Hier erfährst du, wie du Farbfelder einrichtest] (https://help.archetypethemes.co/en/articles/2765)", "learn_to_setup_local_pickup": "<PERSON><PERSON><PERSON><PERSON>, wie Sie diese Funktion einrichten können [hier](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup)", "learn_to_setup_product_specs": "<PERSON><PERSON><PERSON><PERSON>, wie Sie diese Funktion einrichten [hier](https://help.archetypethemes.co/en/articles/841280)", "learn_to_setup_product_tags": "<PERSON><PERSON><PERSON><PERSON>, wie Sie diese Funktion einrichten können [hier](https://help.archetypethemes.co/en/articles/832384)", "loop_video": "Loop-Video", "mute_video": "Video stumm schalten", "overlay_header_over_collection": "Header über Kategorie legen", "overlay_header_over_home": "Header über Startseite legen", "register_google_maps_api": "<PERSON> muss<PERSON> [einen Google-Maps-API-Schlüssel registrieren](https://help.shopify.com/manual/using-themes/troubleshooting/map-section-api-key), um die Karte anzuzeigen.", "register_mapbox_api": "<PERSON><PERSON> mü<PERSON> [ein Mapbox-API-Zugriffstoken registrieren](https://help.archetypethemes.co/en/articles/1501376), um die Karte anzuzeigen", "repeat_main_menu": "Hauptmenü auf Mobilgeräten wiederholen", "return_button_text": "Schaltflächentext zurückgeben", "select_collections_to_show": "Kategorien zum Anzeigen auswählen", "share_on_facebook": "Auf Facebook teilen", "share_on_social": "In sozialen Netzwerken teilen", "show_arrow": "<PERSON><PERSON>il anzeigen", "show_as_tab": "Als Tab anzeigen", "show_author": "<PERSON><PERSON> <PERSON>", "show_bottom_padding": "Untere Polsterung anzeigen", "show_breadcrumbs": "Brotkrumen anzeigen", "show_cents_as": "Cents als hochgestellte Zahl anzeigen", "show_collection_image": "Kategoriebild anzeigen", "show_collections_in_breadcrumbs": "Kategorieseite in Brotkrumenliste anzeigen", "show_comment_count": "Anzahl der Kommentare anzeigen", "show_copyright": "Copyright anzeigen", "show_currency_flags": "Währungsflaggen anzeigen", "show_currency_selector": "Währungsauswahl anzeigen", "show_date": "<PERSON><PERSON> anzeigen", "show_dynamic_checkout": "Dynamische Checkout-Schaltfläche anzeigen", "show_excerpt": "Ausschnitt zeigen", "show_first_product": "<PERSON>rstes Produkt in Mega-Menüs anzeigen", "show_footer_content": "Fußzeileninhalt auf Mobile-Menü anzeigen", "show_get_directions": "Schaltfläche „Wegbeschreibung“ anzeigen", "show_image": "Bild anzeigen", "show_increment_dividers": "Inkrementteiler anzeigen", "show_inventory_transfer": "Hinweis auf Bestandsumlagerung anzeigen", "show_language_selector": "Sprachauswahl anzeigen", "show_newsletter_signup": "Newsletter-Anmeldung anzeigen", "show_payment_icons": "Zahlungssymbole anzeigen", "show_prefix": "Produktpräfix anzeigen", "show_price": "<PERSON><PERSON> anzeigen", "show_product_tags": "Produkt-Tags anzeigen", "show_recipient_information": "Formular für Empfängerinformationen für Geschenkgutscheinprodukte anzeigen", "show_rss_link": "RSS-Link anzeigen", "show_saved_amount": "Gesparten Betrag anzeigen", "show_section_divider": "Abschnittsteiler anzeigen", "show_sku": "SKU einblenden", "show_social_accounts": "Social-Media-Konten anzeigen", "show_social_icons": "Social-Icons anzeigen", "show_sort_options": "Sortieroptionen anzeigen", "show_swatch_labels": "Musteretiketten anzeigen", "show_tags": "Tags anzeigen", "show_thumbnail_arrows": "Pfeile für Miniaturansichten anzeigen", "show_title": "Titel anzeigen", "show_top_padding": "Obere Polsterung anzeigen", "show_variant_labels": "Variantenbeschriftungen anzeigen", "show_vendor": "<PERSON><PERSON><PERSON> anzeigen", "show_view_all_link": "„Alle anzeigen“-Link anzeigen", "show_wave_transition": "Wellenübergang zeigen", "sort_collections_by": "<PERSON><PERSON><PERSON> sortieren nach:", "tweet_on_twitter": "Twittern Sie auf X", "view_setup_instructions": "[Einrichtungsanleitung anzeigen](https://archetypethemes.co/blogs/expanse/how-do-i-set-up-color-swatches)", "zoom_image_to_fill": "Bild vergrößern, um Platz auszufüllen"}, "info": {"2000x800px_recommended": "2000 x 800 Pixel empfohlen", "Shopify_youtube": "https://www.youtube.com/user/shopify", "aligns_next_to_custom_content": "Richtet sich neben anderen benutzerdefinierten Inhalten aus", "all_submissions_sent_to_store_email": "Alle Einsendungen werden an die Kunden-E-Mail-Adresse deines Shops gesendet. [Mehr erfahren](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).", "allow_your_customers_to_filter": "Ermögliche deinen Kunden, Kategorien und Suchergebnisse nach Produktverfügbarkeit, Preis, Farbe und mehr zu filtern. [Filter anpassen](/admin/menus)", "appears_when_newsletter_popup_closed": "<PERSON><PERSON><PERSON><PERSON>, wenn das Newsletter-Pop-up geschlossen wird.", "applies_a_maximum_width": "Wendet eine maximale Breite an", "applys_when_thumbnail_next_to_media": "<PERSON><PERSON> nur, wenn die Position der Miniaturansicht auf „Neben den Medien“ eingestellt ist.", "buttons_appear_either_cart_or_checkout": "Die Schaltflächen können entweder auf deiner Warenkorb- oder deiner Kassenseite erscheinen, aber nicht auf beiden.", "choose_which_platforms_share_theme_settings": "W<PERSON>hle in den globalen Theme-Einstellungen, auf welchen Plattformen du teilen möchtest", "collections_listed_by_default": "Alle deine Kategorien werden standardmäßig aufgeführt. Um deine Liste anzupassen, klicke auf „Ausgewählt“ und füge Kategorien hinzu.", "content_for_age_verification_failure": "Dieser Inhalt wird angezeigt, wenn der Nutzer die Anforderungen der Verifizierung nicht erfüllt.", "customers_who_subscribe_accept_marketing": "<PERSON><PERSON>, die ein Abonnement abschließen, werden mit ihrer E-Mail-Adresse in die „akzeptiert Marketing“-[Kundenliste](/admin/customers?query=&accepts_marketing=1) aufgenommen.", "darkens_your_image": "Dunkelt dein Bild ab, damit der Text lesbar ist", "darkens_your_video": "Verdunkelt dein Video, damit dein Text lesbar ist", "defaults_to_collection_title": "Standardmäßig wird der Titel der Kategorie verwendet", "defaults_to_menu_title": "Standardmäßig wird der Menütitel verwendet", "does_not_appear_on_mobile": "<PERSON><PERSON><PERSON>t nicht auf Mobilgeräten, um die [Interstitial-Richtlinien](https://webmasters.googleblog.com/2016/08/helping-users-easily-access-content-on.html) von Google für eine bessere Suchmaschinenoptimierung zu erfüllen.", "dynamic_recommendations_use": "Dynamische Empfehlungen nutzen Bestell- und Produktinformationen, um sich im Laufe der Zeit zu verändern und zu verbessern. [Mehr erfahren](https://help.shopify.com/en/themes/development/recommended-products)", "enable_shop_pay_for_shop_app_follow": "Damit Kunden deinem Shop in der Shop-App über deine Storefront folgen können, muss Shop Pay aktiviert sein. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "for_product_with_long_descriptions": "<PERSON>ür Produktreihen mit langen Beschreibungen empfehlen wir, die Beschreibung und die Tabs in diesem Abschnitt zu platzieren.", "forces_the_age_verification": "Erzwingt die Anzeige der Verifizierung bei jeder Aktualisierung und sollte nur zur Bearbeitung des Pop-ups verwendet werden. Vergewissern Si<PERSON> sich, dass der 'Testmodus' bei der Öffnung Ihres Stores deaktiviert ist.", "gift_card_products": "Geschenkkartenprodukte können optional mit einer persönlichen Nachricht direkt an den Empfänger versendet werden.[Mehr erfahren](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)", "google_maps_will_find_location": "Google Maps findet den genauen Standort", "if_collection_image_enabled": "(wenn Kategoriebild aktiviert ist)", "image_displays_if_video_fails": "Bild wird angeze<PERSON>t, falls <PERSON> nicht geladen werden kann", "image_source_adjusted_theme_settings": "Die Bildquelle kann unter Theme-Einstellungen > Kategorie-Ka<PERSON>n ange<PERSON>t werden.", "lazy_loading_enabled_below_fold": "Lazy Loading sollte aktiviert werden, wenn sich Abschnittsbilder unterhalb der Falte befinden. [Mehr erfahren](https://archetypethemes.co/blogs/support/what-is-lazyloading)", "learn_inventory_transfers": "<PERSON><PERSON><PERSON><PERSON>, wie Sie Bestandsumlagerungen erstellen können [hier](https://help.shopify.com/en/manual/products/inventory/transfers/create-transfer)", "learn_mega_menu": "[Mehr erfahren](https://archetypethemes.co/blogs/expanse/how-do-i-create-a-mega-menu)", "learn_more_media_types": "Erfahren Sie mehr über [Medientypen](https://help.shopify.com/en/manual/products/product-media)", "learn_to_setup_product_prefix": "<PERSON><PERSON><PERSON><PERSON>, wie Sie diese Funktion [hier e<PERSON><PERSON><PERSON>](https://help.archetypethemes.co/en/articles/851328)", "lets_customers_checkout_familiar": "Ermöglicht es Kunden, direkt mit einer vertrauten Zahlungsmethode zu bezahlen. [Erfah<PERSON> mehr](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)", "links_to_collections_appear_here": "<PERSON>er werden Link<PERSON> zu den Kategorien aus deinem Menü angezeigt. [<PERSON>hr erfahren](https://archetypethemes.co/blogs/expanse/how-do-i-create-subcollections)", "links_to_youtube_video_player": "<PERSON><PERSON> zu YouTube-Videos werden in einem Videoplayer geöffnet", "mapbox_will_find_location": "Mapbox findet den genauen Standort", "menu_shows_top_level": "Dieses Menü zeigt nur Links der obersten Ebene an", "model_media_type_required": "Das Produkt muss über einen Medientyp des 3D-Modells verfügen.", "no_effect_when_size_natural": "<PERSON><PERSON>, wenn die Rasterbildgröße auf „Natürlich“ eingestellt ist", "not_shown_to_customers_with_accounts": "<PERSON><PERSON><PERSON>, die ein Konto in Ihrem Shop erstellt haben, nicht angezeigt.", "number_of_days_popup_reappears": "<PERSON><PERSON><PERSON> der Tage, bevor ein geschlossenes Pop-up erneut angezeigt wird", "only_appear_on_articles": "Erscheint nur auf Artikeln", "only_applies_on_desktop": "Gilt nur für den Desktop-Viewport", "overrides_video_url_if_both_set": "Überschreibt die Einstellung der Video-URL, wenn beide festgelegt sind", "recently_viewed_products_only_visible": "<PERSON><PERSON><PERSON><PERSON> angesehene Produkte sind nur sichtbar, wenn man außerhalb des Editors browst", "recommended_a_square_image": "<PERSON><PERSON>r ein optimales Nutzererlebnis auf mobilen Geräten wird ein quadratisches Seitenverhältnis empfohlen", "requires_square_images": "Erfordert quadratische Bilder", "scaled_to_32x32": "Wird auf 32 x 32 Pixel verkleinert", "section_appears_when_product_vendor": "<PERSON>ser Abschnitt wird nur angezeigt, wenn du bei einem Produkt bist, für das ein Verkäufer festgelegt wurde.", "section_on_product_page_w_collection": "<PERSON><PERSON> Abschnitt wird nur angezeigt, wenn du dich auf einer Produktseite befindest, die über eine Kategorie erreicht wurde.", "set_as_max_width": "Als maximale B<PERSON><PERSON>, kann kleiner er<PERSON>", "shopify_facebook": "https://www.facebook.com/shopify", "shopify_instagram": "https://instagram.com/shopify", "shopify_linkedin": "https://www.linkedin.com/in/shopify", "shopify_pinterest": "https://www.pinterest.com/shopify", "shopify_snapchat": "https://www.snapchat.com/add/shopify", "shopify_tiktok": "https://www.tiktok.com/@shopify", "shopify_tumblr": "http://shopify.tumblr.com", "shopify_twitter": "https://twitter.com/shopify", "shopify_vimeo": "https://vimeo.com/shopify", "sidebar_is_first_two_sections": "<PERSON>cht<PERSON> da<PERSON>, dass deine Seitenleiste einer der ersten beiden Abschnitte ist", "sign_up_creates_customer": "<PERSON>i jeder Anmeldung wird ein Kunde in Ihrem Shop erstellt. [Kunden anzeigen](/admin/customers?query=&accepts_marketing=1).", "sorting_applies_when_all_selected": "Die Sortierung gilt nur, wenn „Alle“ ausgewählt ist", "styles_online_apply_to_ital_text": "Stile werden nur auf den kursiven Text in der Überschrift angewendet", "supported_video_formats": "Unterstützt YouTube, .MP4 und Vimeo. Nicht alle Funktionen werden von Vimeo unterstützt. [Mehr erfahren](https://help.archetypethemes.co/en/articles/2740)", "text_below_on_mobile": "Text steht auf dem Handy immer unter dem Bild", "to_add_currency_settings": "Um eine Währung hinzuzufügen, gehe zu deinen [Währungseinstellungen.](/admin/settings/payments)", "to_add_language_settings": "Gehe zu den [Spracheinstellungen](/admin/settings/languages), um eine Sprache hinzuzufügen.", "to_select_complementary_add_search_discovery": "Um ergänzende Produkte auszuwählen, füge die Search & Discovery-App hinzu. [Mehr Informationen](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations#complementary-products)", "use_instead_of_google_maps_api": "Anstelle eines API-Schlüssels verwenden", "use_instead_of_mapbox_api": "Verwenden Sie anstelle eines API-Zugriffstokens", "used_when_on_top_of_image": "<PERSON><PERSON><PERSON><PERSON>, wenn über einem Bild", "values_below_10_not_recommended": "Werte unter 10 Sekunden werden nicht empfohlen. Verzögerung im Editor deaktiviert.", "video_with_sound_not_autoplay": "Video mit Ton wird nicht automatisch abgespielt"}, "labels": {"3D_model": "3D-Modell", "404_page": "404-Seite", "75_width": "75 % Weite", "accounts": "<PERSON><PERSON><PERSON>", "additional_copyright_text": "Zusätzlicher Copyright-Text", "additional_footer_content": "Zusätzlicher Fußzeileninhalt", "address_and_hours": "Adresse und Stunden", "advanced_accordion": "Fortgeschrittenes Akkordeon", "age_verification_popup": "Altersüberprüfungs-Popup", "age_verification_question": "Frage zur Altersüberprüfung", "alignment": "Ausrichtung", "alignments": {"bottom": "Unten", "bottom_center": "Unten mittig", "bottom_left": "Unten links", "bottom_right": "Unten rechts", "center": "<PERSON><PERSON><PERSON>", "center_left": "Zentrum-links", "center_right": "Zentrum-rechts", "center_text": "Text zentrieren", "centered": "<PERSON><PERSON><PERSON>", "left": "Links", "left_to_right": "<PERSON>s nach rechts", "middle": "<PERSON><PERSON>", "right": "<PERSON><PERSON><PERSON>", "right_to_left": "Von rechts nach links", "top": "<PERSON><PERSON>", "top_center": "<PERSON><PERSON>", "top_left": "Oben links", "top_right": "<PERSON><PERSON> rechts"}, "all": "Alle", "amount": "<PERSON><PERSON>", "announcement": "Ankündigung", "announcement_bar": "Ankündigungsleiste", "announcement_text": "Ankündigungstext", "apps": "Apps", "arch": "Bogen", "arrows": "<PERSON><PERSON><PERSON>", "article": "Artikel", "article_pages": "Artikelseiten", "author": "Autor", "author_info": "Informationen zum Autor", "authors_image": "Bild des Autors", "autochange_slides": "Slides automatisch wechseln", "back_to_collection": "Zurück zur Kollektion", "background": "Hi<PERSON>grund", "background_image": "Hintergrundbild", "background_video_link": "Hintergrundvideo-Link", "bag": "<PERSON><PERSON>", "banner": "Banner", "bars": "<PERSON><PERSON><PERSON>", "base_size": "Basisgröße", "below_media": "Unterhalb der Medien", "bills": "Bills", "blocks_per_row": "Blöcke pro Reihe", "blog": "Blog", "blog_pages": "Blog-Seiten", "blog_posts": "Blog-Beiträge", "blog_sidebar": "Blog-Seitenleiste", "body": "Nachricht", "body_text": "Hauptteil", "borders": "<PERSON><PERSON><PERSON>", "bottom_text": "Text unten", "button": "Schaltfläche", "button_1_link": "Schaltfläche #1 Link", "button_1_text": "Schaltfläche #1 Text", "button_2_link": "Schaltfläche #2 Link", "button_2_text": "Schaltfläche #2 Text", "button_label": "Schaltflächentext", "button_link": "Schaltflächenlink", "button_style": "Schaltflächendesign", "button_text": "Schaltflächentext", "button_text_2": "Schaltflächentext 2", "buttons": "Schaltflächen", "buy_buttons": "Buy Buttons", "calendar": "<PERSON><PERSON><PERSON>", "capitalize": "Großschreiben", "capitalize_navigation": "Navigation großschreiben", "cart": "<PERSON><PERSON><PERSON>", "cart_dot": "Warenkorbpunkt", "cart_dot_text": "Warenkorbpunkt-Text", "cart_icon": "Warenkorb-Icon", "cart_page": "Warenkorbseite", "cart_recommendations": "Warenkorb-Empfehlungen", "cart_type": "Warenkorb-Typ", "change_images_every": "Bilder wechseln alle", "charity": "Wohltätigkeit", "chat": "Plaudern", "chat_link": "Chat-Link", "checkmark": "<PERSON><PERSON><PERSON><PERSON>", "circle": "Kreis", "circle_11": "Kreis (1:1)", "circular_images": "<PERSON><PERSON> Bilder", "classic": "Klassisch", "cold_blur": "<PERSON><PERSON><PERSON> Un<PERSON>ärfe", "collection": "<PERSON><PERSON><PERSON>", "collection_carousel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collection_description": "Kategoriebeschreibung", "collection_header": "<PERSON><PERSON><PERSON>-<PERSON>er", "collection_image": "Kategoriebild", "collection_list": "Kategorieliste", "collection_tile_background": "Kachelhintergrund der Kategorie", "collection_tile_style": "Stil der Kategorie-Kachel", "collection_tiles": "Kategor<PERSON><PERSON><PERSON><PERSON><PERSON>", "collections_list_page": "Listenseite für Kategorien", "color_scheme": "Farbschema", "color_schemes": {"1": "Farbschema 1", "2": "Farbschema 2", "3": "Farbschema 3"}, "color_swatches": "Farbige Swatches", "colors": "<PERSON><PERSON>", "column": "<PERSON>lt<PERSON>", "contact": "Kontakt", "contact_and_social": "Kontakt und Social", "contact_form": "Kontaktformular", "contact_page": "Kontaktseite", "content": "Inhalt", "content_alignment": "Textausrichtung", "content_alignment_desktop": "Inhaltsausrichtung auf dem Desktop", "content_position": "Inhaltliche Position", "countdown": "Countdown", "custom_1": "Benutzerdefiniert 1", "custom_2": "Benutzerdefiniert 2", "custom_3": "Benutzerdefiniert 3", "custom_content": "Benutzerdefinierter Inhalt", "darken": "Verdunkeln", "day": "Tag", "declined": "Declined", "default": "Standard", "default_product_layout": "Standard-Produktlayout", "delay": "Verzögerung", "description": "Beschreibung", "design": "Design", "desktop_columns_per": "Spalten pro Zeile (Desktop)", "desktop_height": "Desktop-Höhe", "desktop_height_adjustment": "Einstellung der Desktop-Höhe", "desktop_logo_height": "Höhe des Desktop-Logos", "desktop_logo_width": "Breite des Desktop-Logos", "desktop_products_per": "Desktop-Produkte pro Zeile", "direction": "<PERSON><PERSON><PERSON>", "dot": "<PERSON><PERSON>", "dots": "Punkte", "dropdown": "Dropdown", "edges": "<PERSON><PERSON><PERSON>", "email_signup": "E-Mail-Anmeldung", "envelope": "Umschlag", "extras": "Extras", "facebook": "Facebook", "fallback_image": "Fallback-Bild", "faq": "FAQ", "favicon": "Favicon", "favicon_image": "Favicon-Bild", "featured_article": "Ausgewählter Artikel", "featured_collection": "Vorgestellte Kategorie", "featured_product": "Vorgestelltes Produkt", "filtering_and_sorting": "Filtern und Sortieren", "first_product": "Erstes Produkt", "fixed": "Fest", "flexible": "Flexibel", "follow_on_shop": "Folgen Sie auf Shop", "font": "<PERSON><PERSON><PERSON><PERSON>", "font_weights": {"bold": "<PERSON><PERSON>", "extrabold": "Extrafett", "extralight": "Extraleicht", "light": "<PERSON><PERSON>t  ", "semibold": "<PERSON><PERSON><PERSON><PERSON>"}, "footer": "Fußzeile", "footer_group": "Fußgruppe", "footer_promotions": "Fußzeilen-Aktionen", "form": "Formular", "frequency": "Häufigkeit", "full_page_width": "Volle Seitenbreite", "full_width": "Volle Breite", "fullwidth_details": "Details in voller Breite", "gallery": "Galerie", "gears": "Zahnräder", "general": "Allgemein", "gift": "Geschenk", "globe": "Globus", "google_maps_api": "Google-Maps-API-Schlüssel", "grey": "G<PERSON><PERSON>", "grey_round": "<PERSON><PERSON><PERSON> rund", "grey_square": "<PERSON><PERSON><PERSON> quadratisch", "grid_spacing": "Rasterabstand", "handwriting": "Handschrift", "header": "Header", "header_group": "Kopfgruppe", "heading": "Überschrift", "heading_ital_text_style": "Überschrift kursiv geschriebener Textstil", "heading_position": "Kopf<PERSON>", "heading_size": "Überschriftengröße", "heading_text_size": "Größe des Header-Texts", "headings": "Überschriften", "heart": "<PERSON><PERSON>", "height": "<PERSON><PERSON><PERSON>", "hero": "Hero", "hero_optional_slides": "Hero (optionale Slides)", "hide_timer_on": "Timer nach Abschluss ausblenden", "horizontal": "Horizontal", "horizontal_position": "Horizontale Position", "hot_spot_icon": "Hotspot-Symbolstil", "hotspot_color": "Hotspot-Farbe", "hour": "Stunde", "hover_to_reveal": "Zum Anzeigen des zweiten Bildes den Mauszeiger darüber bewegen", "html": "HTML", "html_block": "HTML-Block", "icon": "Symbol", "icon_color": "Icon-Farbe", "icons": "Symbole", "image": "Bild", "image_2": "Bild 2", "image_alignment": "Bildausrichtung", "image_background": "<PERSON><PERSON><PERSON><PERSON>d", "image_breathing_room": "Freiraum für das Bild", "image_comparison": "Bildvergleich", "image_crop": "Bil<PERSON><PERSON><PERSON>nitt", "image_hotspots": "Bild-Hotspots", "image_link": "Bild-Link", "image_on_left": "Bild links", "image_on_right": "Bild rechts", "image_optional_slides": "Bild (optionale Slides", "image_position": "Bildposition", "image_ratio": "Bildverhältnis", "image_section_background": "Hintergrund des Bildausschnitts", "image_shape": "Bildform", "image_size": "Bildbreite", "image_style": "Produktkarte", "image_width": "Bildbreite", "image_with_text": "Bild mit Text", "images_per_row": "Bilder pro Zeile", "indent_image": "Bild e<PERSON>r<PERSON>en", "inline": "Schritthaltend", "instagram": "Instagram", "inventory_status": "Status der Bestände", "label": "Label", "landscape": "Landschaft", "landscape_43": "Landschaft (4:3)", "language": "<PERSON><PERSON><PERSON>", "large_grid": "Großes Raster", "large_image_with": "Großes Bild mit Textfeld", "layout": "Layout", "layout_on_desktop": "Layout auf dem Desktop", "lazy_load_images": "Bilder faul laden", "leaf": "<PERSON><PERSON>", "letter_spacing": "Abstand zwischen Buchstaben", "line_height": "Zeilenhöhe", "linen": "<PERSON><PERSON>", "lines_and_borders": "<PERSON><PERSON> und <PERSON>ä<PERSON>", "link": "Link", "link_2": "Link 2", "link_block": "Linkblock", "link_label": "Link-Label", "linkedin": "LinkedIn", "list": "Liste", "lock": "<PERSON><PERSON><PERSON>", "logo": "Logo", "logo_center_menu": "Lo<PERSON> in der Mitte, <PERSON><PERSON>", "logo_image": "Logo-Bild", "logo_left_menu_below": "Logo links, <PERSON>ü unten", "logo_left_menu_center": "Logo links, Menü Zentrum", "logo_left_menu_left": "Logo links, Menü links", "logo_list": "Logo-Liste", "low_inventory_threshold": "<PERSON><PERSON><PERSON><PERSON>", "map": "<PERSON><PERSON>", "map_address": "Kartenad<PERSON><PERSON>", "mapbox_api": "Mapbox-API-Zugriffstoken", "marble": "<PERSON><PERSON>", "max_products_to": "Max. anzuzeigende Produkte", "maximum_products_to": "Maximal anzuzeigende Produkte", "media": "Medien", "media_crop": "Medienausschnitt", "media_gallery_layout": "Layout der Mediengalerie", "media_on_left": "Medien links", "media_on_right": "Medien rechts", "media_width": "Medienbreite", "media_with_text": "Medien mit Text", "middle_text": "Text in der Mitte", "minimal": "Minimal", "minimal_bag": "Minimale Tasche", "minimal_wave": "Minimale Welle", "minute": "Minute", "mobile_height": "Mobile-Höhe", "mobile_height_adjustment": "Anpassung der Mobile-Höhe", "mobile_image": "Mobile-Bild", "mobile_layout": "Mobiles Layout", "mobile_logo_height": "Höhe des Mobile-Logos", "mobile_logo_width": "Breite des Mobile-Logos", "mobile_text": "Mobile-Text", "model": "<PERSON><PERSON>", "month": "<PERSON><PERSON>", "months": {"april": "April", "august": "August", "december": "Dezember", "february": "<PERSON><PERSON><PERSON>", "january": "<PERSON><PERSON><PERSON>", "july": "<PERSON><PERSON>", "june": "<PERSON><PERSON>", "march": "<PERSON><PERSON>", "may": "<PERSON><PERSON>", "november": "November", "october": "Oktober", "september": "September"}, "more_from_collection": "<PERSON><PERSON>", "more_from_vendor": "<PERSON><PERSON>", "narrow_column": "Schmale Spalte", "natural": "<PERSON><PERSON><PERSON><PERSON>", "navigation": "Navigation", "navigation_font": "Navigationsschrift", "navigation_size": "Navigationsgröße", "newsletter": "Newsletter", "next_to_media": "<PERSON><PERSON><PERSON> dem <PERSON>", "none": "<PERSON><PERSON>", "notebook": "Notizbuch", "number_of_bars": "<PERSON><PERSON><PERSON> der Balken", "number_of_products": "Anzahl der Produkte pro Folie", "number_of_related": "Anzahl der verwandten Produkte", "optimize_for_readability": "Optimierung der Lesbarkeit", "optional": "Optional", "outline": "<PERSON><PERSON><PERSON>", "overlay": "Überlagerung", "overlay_header": "Overlay-Header", "overlay_opacity": "Deckkraft überlagern", "package": "<PERSON><PERSON>", "page": "Seite", "page_full_width": "Seite (volle Breite)", "pagination_type": "Paginierungstyp", "paper": "<PERSON><PERSON><PERSON>", "paragraph_hotspot": "Absatz-Hotspot", "parallax_direction": "Richtung der Parallaxe", "parallax_image": "Parallaxenbild", "payments_and_localization": "Zahlungen und Lokalisierung", "percent": "Prozent", "phone": "Telefonnummer", "phone_number": "Telefonnummer", "pin_on_pinterest": "<PERSON><PERSON>nen", "pinterest": "Pinterest", "plant": "<PERSON><PERSON><PERSON><PERSON>", "plants": "Pflanzen", "plus": "Plus", "policies_menu": "<PERSON><PERSON> <PERSON>Richtlinien“", "popup": "Pop-up", "popups": "Popupgruppe", "portrait": "Porträt", "portrait_23": "Porträt (2:3)", "position": "Position", "post_limit": "Beiträge", "predictive_search": "Prädiktive Suche", "price": "Pre<PERSON>", "primary": "<PERSON><PERSON><PERSON><PERSON>", "product": "Produkt", "product_card": "Produktkarte", "product_grid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_hotspot": "Produkt-Hotspot", "product_reviews": "Produktbewertungen", "product_specs": "Produktspezifikationen", "product_tags": "Produkt Tags", "product_tile_layout": "Layout der Produktkacheln", "product_tile_style": "Stil der Produktkachel", "product_tiles": "Produkt-Kacheln", "products": "Produkte", "promotion_grid": "Aktionsraster", "promotional_grid": "Werberaster", "quantity_selector": "Men<PERSON>aus<PERSON><PERSON>", "question": "Frage", "quote": "Zitat", "recently_viewed": "<PERSON><PERSON><PERSON><PERSON> angesehen", "recommended": "<PERSON><PERSON><PERSON><PERSON>", "recycle": "Recyceln", "regular": "<PERSON><PERSON><PERSON>", "related_products": "Verwandte Produkte", "reminder_label": "Erinnerungszettel", "ribbon": "<PERSON><PERSON><PERSON><PERSON>", "rich_text": "Rich Text", "round": "Rund", "rounded": "Gerundet", "rounded_wave": "Abgerundete Welle", "sale_collection": "Sale-<PERSON><PERSON><PERSON>", "sale_collection_342": "Sale-<PERSON><PERSON><PERSON>", "sale_tag_text": "Sale-Tag-Text", "sale_tags": "Sale-Tags", "sales_point": "Verkaufsstelle", "sales_tag": "Verkaufspreis", "sales_tag_color_setting": "Farbeinstellung „Verkaufsetiketten“.", "sand": "Sand", "save_price": "<PERSON><PERSON> s<PERSON>iche<PERSON>", "savings_display_style": "Anzeigeart der Ersparnisse", "scrolling_text": "Lauftext", "search": "<PERSON><PERSON>", "search_field": "<PERSON><PERSON>", "search_page": "Seite durchsuchen", "secondary": "Sekundär", "section_height": "Abschnittshöhe", "section_layout": "Abschnittslayout", "selected": "Ausgewählt", "separator": "Separator", "serif": "Serife", "share_buttons": "Schaltflächen zum Teilen", "share_links": "<PERSON><PERSON> teilen", "sharing_options": "Optionen zum Teilen", "sharp": "Scharf", "shield": "<PERSON><PERSON><PERSON>", "simple": "<PERSON><PERSON><PERSON>", "size": "Größe", "size_chart": "Größentabelle", "size_chart_page": "Seite mit Größentabelle", "sizes": {"extra_large": "Extra groß", "large": "<PERSON><PERSON><PERSON>", "large_grid": "Großes Raster", "medium": "<PERSON><PERSON><PERSON>", "small": "<PERSON>", "small_grid": "<PERSON><PERSON>"}, "slide": "Folie", "slide_link": "Slide-Link", "slide_link_2": "Slide-Link 2", "slide_link_text": "Slide-Link-Text", "slide_link_text_339": "Slide-Link-Text 2", "slide_navigation_style": "Slide-Navigationsstil", "slider_style": "Slider-Stil", "slightly_round": "<PERSON><PERSON><PERSON> rund", "small_grid": "<PERSON><PERSON>", "snapchat": "Snapchat", "social": "Social", "social_media": "Social Media", "solid": "Fest", "sortings": {"alphabetically_az": "Alphabetisch, A-Z", "alphabetically_za": "Alphabetisch, Z-A", "date_new_to": "<PERSON><PERSON>, neu zu alt", "date_old_to": "<PERSON><PERSON>, alt zu neu", "product_count_high": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hoch zu niedrig", "product_count_low": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ni<PERSON><PERSON> zu hoch"}, "space": "All", "spacing": "Abstand", "speed": "Geschwindigkeit", "splat_1": "Spritzer 1", "splat_2": "Spritzer 2", "splat_3": "Spritzer 3", "splat_4": "Spritzer 4", "split_optional_slides": "Split (optionale Slides)", "square": "<PERSON>uadratisch", "square_11": "Quadratisch (1:1)", "squiggle": "Sc<PERSON>örkel", "stacked_media": "Gestapelte Medien", "star": "Stern", "stars": {"1_star": "1 Stern", "2_stars": "2 Sterne", "3_stars": "3 Sterne", "4_stars": "4 Sterne", "5_stars": "5 Sterne"}, "sticky_reminder": "Haftende Erinnerungsnotiz", "stone": "<PERSON>", "stopwatch": "Stoppuhr", "store": "Geschäft", "store_availability": "Verfügbarkeit im Geschäft", "style": "Stil", "subcollections": "Unterkategorien", "subheading": "Unter-Überschrift", "supports_liquid": "Unterstützt Liquid", "swatch_size": "Swatch-Größe", "swatch_style": "Swatch-Stil", "swirl": "<PERSON><PERSON><PERSON>", "tab": "Tab", "tab_content": "Tab-Inhalt", "tab_content_from": "Tab-Inhalt von Seite", "tag": "Etikett", "tags": "Tags", "terms_and_conditions": "Seite mit den Geschäftsbedingungen", "testimonial": "Erfahrungsbericht", "testimonials": "Erfahrungsberichte", "text": "Text", "text_alignment": "Textausrichtung", "text_block": "Textblock", "text_columns_with": "Textspalten mit Bildern", "text_columns_with_386": "Text mit Icons", "text_direction": "Textrichtung", "text_on_left": "Text ist links", "text_on_right": "Text rechts", "text_position": "Textposition", "text_protection": "Textschutz", "text_size": "Textgröße", "texture": "Textur", "thick_grid": "<PERSON><PERSON>", "thin_grid": "<PERSON><PERSON><PERSON>", "thumbnail_height": "Höhe der Miniaturansicht", "thumbnail_position": "Position der Miniaturansicht", "thumbnails_below_media": "Miniaturansichten unter den Medien", "thumbnails_beside_media": "Miniaturansichten neben den Medien", "thumbs_up": "<PERSON><PERSON><PERSON> hoch", "tiktok": "TikTok", "timer": "Timer", "timer_complete_message": "Timer abgesch<PERSON><PERSON> Meldung", "times": {"01_am": "01:00", "02_am": "02:00", "03_am": "03:00", "04_am": "04:00", "05_am": "05:00", "06_am": "06:00", "07_am": "07:00", "08_am": "08:00", "09_am": "09:00", "10_am": "10:00", "10_pm": "22:00", "11_am": "11:00", "11_pm": "23:00", "12_am": "00:00", "12_pm": "12:00", "1_pm": "13:00", "2_pm": "14:00", "3_pm": "15:00", "4_pm": "16:00", "5_pm": "17:00", "6_pm": "18:00", "7_pm": "19:00", "8_pm": "20:00", "9_pm": "21:00"}, "title": "Titel", "top_text": "Text oben", "trophy": "Trophäe", "truck": "Lieferwagen", "trust_badge": "Vertrauensabzeichen", "tumblr": "Tumblr", "twitter": "X", "two_blocks_per_row_mobile": "Zwei Blöcke pro Reihe auf Mobilgeräten", "type": "Art", "typography": "<PERSON><PERSON><PERSON><PERSON>", "value": "Wert", "vertical": "Vertikal", "vertical_alignment": "<PERSON><PERSON><PERSON><PERSON>", "vertical_position": "Vertikale Position", "video": "Video", "video_hero": "Video-Hero", "video_link": "Video-Link", "video_style": "Video-Stil", "video_url": "Video-URL", "video_with_sound": "Video mit Ton", "video_without_sound": "Videos ohne Ton", "vimeo": "Vimeo", "wallet": "Brieftasche", "warm_blur": "Warme Unschärfe", "wave": "Welle", "weight": "Gewicht", "white": "<PERSON><PERSON>", "white_logo": "Weißes Logo", "white_round": "<PERSON><PERSON> rund", "white_square": "Weiß quadratisch", "wide_16_9": "<PERSON><PERSON><PERSON> (16:9)", "width": "Breite", "wildflower": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON><PERSON>", "youtube": "YouTube", "youtube_vimeo": "YouTube/Vimeo"}}