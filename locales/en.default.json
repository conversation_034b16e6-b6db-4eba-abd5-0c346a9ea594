{"actions": {"slideshow_pause": "Pause slideshow", "slideshow_play": "Play slideshow", "back_to_homepage": "Back to the frontpage", "reduce_item_quantity": "Reduce item quantity by one", "increase_item_quantity": "Increase item quantity by one", "remove": "Remove", "filter": "Filter", "sort": "Sort", "send_as_gift": "I want to send this as a gift", "add_order_note": "Add order note", "close": "Close", "terms_i_agree_html": "I agree with the <a href='{{ url }}' target='_blank'>terms and conditions</a>", "terms_i_agree": "I agree with the terms and conditions", "checkout": "Check out", "expand_submenu": "Expand submenu", "view_all": "View all", "enter_email": "Enter your email", "subscribe": "Subscribe", "close_esc": "Close (esc)", "previous": "Previous", "next": "Next", "add_to_cart": "Add to cart", "preorder": "Pre-order", "view_in_space": "View in your space", "leave_a_comment": "Leave a comment", "post_comment": "Post comment", "back_to": "Back to {{ collection }}", "explore_more": "Explore more", "share_this": "Share this", "send": "Send", "cancel": "Cancel", "continue_shopping": "Continue shopping", "get_directions": "Get directions", "show_all_results": "Show all results for \"{{ terms }}\"", "view_store_info": "View store information", "check_availability": "Check availability at other stores", "shop_on_social": "{{ name }} on {{ platform }}", "share_on_facebook": "Share on Facebook", "share": "Share", "share_on_x": "Tweet on <PERSON>", "pin_on_pinterest": "Pin on Pinterest", "pin_it": "Pin it", "get_in_touch": "Get in touch", "email_us": "Email us", "live_chat": "Live chat", "follow_us": "Follow us", "activate": "Activate account", "add_address": "Add address", "add_to_apple_wallet": "Add to Apple Wallet", "close_cart": "Close cart", "close_menu": "Close menu", "collapse_submenu": "Collapse submenu", "confirm_address_delete": "Are you sure you wish to delete this address?", "confirm_password": "Confirm Password", "continue": "Continue", "continue_as_guest": "Continue as a guest", "continue_reading": "Continue reading", "create": "Register Account", "create_account": "Create Account", "decline_invitation": "Decline invitation", "delete": "Delete", "edit": "Edit", "edit_address": "Edit address", "enter_new_password": "Enter a new password for {{ email }}", "forgot_password": "Forgot password?", "learn_more": "Learn more", "log_in": "Log in", "log_out": "Log out", "login": "<PERSON><PERSON>", "next_product": "Next: {{ title }}", "print": "Print", "reset_account_password": "Reset account password", "reset_password": "Reset Password", "reset_your_password": "Reset your password", "return_to_account": "Return to account", "return_to_store": "Return to Store", "set_default_address": "Set as default address", "show_less": "Show less", "show_more": "Show more", "sign_in": "Sign In", "skip_to_content": "Skip to content", "start_shopping": "Start shopping", "submit": "Submit", "update_address": "Update address", "update_cart": "Update cart", "view_addresses": "View addresses", "view_all_products_count": "View all<br>{{ count }} products", "enter": "Enter"}, "info": {"comments_count": {"one": "{{ count }} comment", "other": "{{ count }} comments"}, "collection_no_products": "Sorry, there are no products in this collection.", "max_characters": "{{ max_chars }} characters max", "shipping_at_checkout_taxes_included": "Taxes included. Shipping and discount codes calculated at checkout.", "shipping_at_checkout": "Shipping, taxes, and discount codes calculated at checkout.", "cart_empty": "Your cart is currently empty.", "newsletter_success": "Thanks for subscribing", "save_amount": "Save {{ saved_amount }}", "placeholder_product_description": "This area is used to describe your product’s details. Tell customers about the look, feel, and style of your product. Add details on color,  materials used, sizing, and where it was made.", "tax_included": "Tax included.", "shipping_policy_html": "<a href='{{ link }}'>Shipping</a> calculated at checkout.", "sold_out": "Sold Out", "view_in_space_explanation": "View in your space, loads item in augmented reality window", "low_stock_count": {"one": "Low stock - {{ count }} item left", "other": "Low stock - {{ count }} items left"}, "in_stock": "In stock, ready to ship", "back_in_stock_on": "Back in stock {{ date }}", "backordered": "Backordered, shipping soon", "section_no_content": "This section doesn’t currently include any content. Add content to this section using the sidebar.", "posted_successfully_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.", "comment_posted_successfully": "Your comment was posted successfully! Thank you!", "comments_must_be_approved": "Please note, comments must be approved before they are published", "product_count": {"one": "{{ count }} product", "other": "{{ count }} products"}, "contact_confirmation": "Thanks for contacting us. We'll get back to you as soon as possible.", "404_page_not_found": "404 Page Not Found", "page_does_not_exist_html": "<p>The page you were looking for does not exist. </p><p><a href='{{ url }}'>Continue shopping</a></p>", "you_save_amount": "You save {{ saved_amount }}", "search_result_count": {"one": "{{ count }} result", "other": "{{ count }} results"}, "search_no_results_html": "Search for \"{{ terms }}\"", "address_error": "Error looking up that address", "address_no_results": "No results for that address", "address_query_limit_html": "You have exceeded the Google API usage limit. Consider upgrading to a <a href=\"https://developers.google.com/maps/premium/usage-limits\">Premium Plan</a>.", "map_auth_error_html": "There was a problem authenticating your Google Maps account. Create and enable the <a href=\"https://developers.google.com/maps/documentation/javascript/get-api-key\">JavaScript API</a> and <a href=\"https://developers.google.com/maps/documentation/geocoding/get-api-key\">Geocoding API</a> permissions of your app.", "pick_up_available": "Pickup available", "pick_up_currently_unavailable": "Pickup currently unavailable", "pick_up_available_at_html": "Pickup available at <strong>{{ location_name }}</strong>", "pick_up_unavailable_at_html": "Pickup currently unavailable at <strong>{{ location_name }}</strong>", "cart_max_quantity": "You can only have {{ quantity }} of {{ title }} in your cart.", "create_your_password_to_activate": "Create your password to activate your account.", "disabled": "Disabled", "email_to_reset_password": "We will send you an email to reset your password.", "fulfilled_date_html": "Fulfilled {{ date }}", "gift_card_description": "Here's your gift card!", "gift_card_expired_on": "Expired on {{ expiry }}", "gift_card_expires_on": "Expires on {{ expiry }}", "gift_card_redeem": "Use this code at checkout to redeem your gift card", "gift_card_title": "Here's your {{ value }} gift card for {{ shop }}!", "no_orders": "You haven't placed any orders yet.", "order_cancelled_on_html": "Order cancelled on {{ date }}", "order_cancelled_reason": "Reason: {{ reason }}", "order_placed_on_html": "Placed on {{ date }}", "ready_to_ship_on": "Ready to ship {{ date }}", "recovery_email_sent": "We've sent you an email with a link to update your password.", "search_results_for_html": "Your search for \"{{ terms }}\" revealed the following:", "signup_success": "We will send you an email when we open!", "unavailable": "Unavailable", "variant_sold_out_or_unavailable": "Variant sold out or unavailable", "will_be_powered_by_html": "This shop will be powered by {{ shopify }}", "you_must_agree": "You must agree with the terms and conditions of sales to check out", "admin_link_html": "Store owner? <a href=\"/admin\" class=\"text-link\">Log in here</a>"}, "labels": {"home": "Home", "collections": "Collections", "quantity": "Quantity", "regular_price": "Regular price", "sale_price": "Sale price", "goes_great_with": "Goes great with", "large": "Large", "small": "Small", "list": "List", "example_collection": "Example collection", "email": "Email", "recipient_email": "Recipient email", "name": "Name", "recipient_name_optional": "Recipient name (optional)", "message_optional": "Message (optional)", "message": "Message", "recipient_send_on": "Send on (optional)", "subtotal": "Subtotal", "search": "Search", "account": "Account", "cart": "<PERSON><PERSON>", "site_navigation": "Site navigation", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "sale": "Sale", "example_product": "Example product", "description": "Description", "quick_shop": "Quick shop", "size_chart": "Size chart", "price": "Price", "more_from": "More from", "latest_posts": "Latest posts", "featured_product": "Featured product", "phone_number": "Phone number", "catalog": "Catalog", "more_from_html": "More from {{ link }}", "password": "Password", "recently_viewed": "Recently viewed", "suggestions": "Suggestions", "pages": "Pages", "articles": "Articles", "products": "Products", "from_price_html": "<span>from</span> {{ price }}", "tagged_with": "Tagged \"{{ tags }}\"", "page_title": "Page {{ page }}", "my_account": "My account", "we_accept": "We accept", "account_details": "Account details", "address1": "Address1", "address2": "Address2", "addresses": "Addresses", "all_products": "All products", "billing_address": "Billing address", "categories": "Categories", "city": "City", "company": "Company", "country": "Country", "date": "Date", "default": "<PERSON><PERSON><PERSON>", "discount": "Discount", "discounts": "Discounts", "first_name": "First name", "fulfillment_status": "Fulfillment status", "last_name": "Last name", "order": "Order", "order_history": "Order History", "order_name": "Order {{ name }}", "payment_status": "Payment status", "phone": "Phone", "popular_posts": "Popular posts", "product": "Product", "province": "Province", "reviews": "Reviews", "shipping": "Shipping", "shipping_address": "Shipping address", "sku": "SKU", "tags": "Tags", "tax": "Tax", "top_searched": "Top searched", "total": "Total", "zip": "Postal/Zip code", "your_password": "Your password"}, "trigger": {"color_swatch": "Color", "size": "Size"}, "date_formats": {"month_day_year": "%b %d, %Y"}, "pagefly": {"products": {"product": {"regular_price": "Regular price", "sold_out": "Sold out", "unavailable": "Unavailable", "on_sale": "Sale", "quantity": "Quantity", "add_to_cart": "Add to cart", "back_to_collection": "Back to {{ title }}", "view_details": "View details"}}, "article": {"tags": "Tags:", "all_topics": "All topics", "by_author": "by {{ author }}", "posted_in": "Posted in", "read_more": "Read more", "back_to_blog": "Back to {{ title }}"}, "comments": {"title": "Leave a comment", "name": "Name", "email": "Email", "message": "Message", "post": "Post comment", "moderated": "Please note, comments must be approved before they are published", "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.", "success": "Your comment was posted successfully! Thank you!", "comments_with_count": {"one": "{{ count }} comment", "other": "{{ count }} comments"}}, "password_page": {"login_form_message": "Enter store using password:", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_submit": "Enter", "signup_form_email_label": "Email", "signup_form_success": "We will send you an email right before we open!", "password_link": "Enter using password"}}, "sections": {"ecom-ajax-cart": {"quantity": {"label": "Quantity", "input_label": "Quantity for         {{ product }}", "increase": "Increase quantity for         {{ product }}", "decrease": "Decrease quantity for         {{ product }}"}, "title": "Your cart", "caption": "Cart items", "remove_title": "Remove         {{ title }}", "subtotal": "Subtotal", "new_subtotal": "New subtotal", "note": "Order note", "checkout": "Checkout", "empty": "Your cart is empty", "term": "I agree with the terms and conditions", "cart_error": "There was an error while updating your cart. Please try again.", "cart_quantity_error_html": "You can only add [quantity] of this item to your cart.", "taxes_and_shipping_policy_at_checkout_html": "Taxes and <a href=\"        {{ link }} \">shipping</a> calculated at checkout", "taxes_included_but_shipping_at_checkout": "Tax included and shipping calculated at checkout", "taxes_included_and_shipping_policy_html": "Tax included. <a href=\"        {{ link }} \">Shipping</a> calculated at checkout.", "taxes_and_shipping_at_checkout": "Taxes and shipping calculated at checkout", "unit_price_separator": "per", "view_cart": "View cart", "headings": {"product": "Product", "price": "Price", "total": "Total", "quantity": "Quantity"}, "update": "Update", "price": {"from_price_html": "From         {{ price }}", "regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price"}, "continue_shopping": "Continue shopping", "message": {"one": "{{ count }} item in your cart", "other": "{{ count }} items in your cart"}}}}