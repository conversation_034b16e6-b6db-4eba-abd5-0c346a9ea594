{"actions": {"slideshow_pause": "slideshow pausa", "slideshow_play": "Reproduzir de <PERSON>", "back_to_homepage": "Regressar à Frontpage", "reduce_item_quantity": "Reduza a quantidade do artigo em um", "increase_item_quantity": "Aumente a quantidade do artigo em um", "remove": "Eliminar", "filter": "Filtrar", "sort": "Ordenar", "send_as_gift": "Eu quero enviar isso como um presente", "add_order_note": "Instruções especiais para o vendedor", "close": "En<PERSON><PERSON>", "terms_i_agree_html": "Eu concordo com os <a href='{{ url }}' target='_blank'>termos e condições</a>", "terms_i_agree": "Eu concordo com os termos e condições", "checkout": "Check-Out", "expand_submenu": "Expandir menu", "view_all": "Ver todo", "enter_email": "Subscreva a nossa lista de emails", "subscribe": "Subscrever", "close_esc": "Encerrar (Esc)", "previous": "Anterior", "next": "Se<PERSON><PERSON>", "add_to_cart": "Adicionar ao Carrinho de Compras", "preorder": "Pedido antecipado", "view_in_space": "Ver no seu espaço", "leave_a_comment": "Deixe um comentário", "post_comment": "Publicar comentário", "back_to": "Regressar à {{ collection }}", "explore_more": "Explore mais", "share_this": "<PERSON><PERSON><PERSON><PERSON><PERSON> isso", "send": "Enviar", "cancel": "<PERSON><PERSON><PERSON>", "continue_shopping": "Continue comprando", "get_directions": "Obter direções", "show_all_results": "<PERSON><PERSON><PERSON> mais", "view_store_info": "Exibir informações de loja", "check_availability": "Ver disponibilidade em outras lojas", "shop_on_social": "{{ name }} na {{ platform }}", "share_on_facebook": "Partilhe no <PERSON>", "share": "Partilhar", "share_on_x": "<PERSON><PERSON><PERSON> no <PERSON>", "pin_on_pinterest": "Adicione no Pinterest", "pin_it": "Pin it", "get_in_touch": "Entrar em contato", "email_us": "Envia-nos um email", "live_chat": "Bate-papo ao vivo", "follow_us": "Siga-nos", "activate": "Ativar Conta", "add_address": "<PERSON><PERSON><PERSON><PERSON>", "add_to_apple_wallet": "Adicionar ao Apple Wallet", "close_cart": "<PERSON><PERSON><PERSON>", "close_menu": "Fechar menu", "collapse_submenu": "Retrair menu", "confirm_address_delete": "Tem a certeza de que quer eliminar este endereço?", "confirm_password": "Confirmar <PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "continue_as_guest": "Continuar como visitante", "continue_reading": "Ler mais", "create": "<PERSON><PERSON><PERSON>", "create_account": "<PERSON><PERSON><PERSON>", "decline_invitation": "<PERSON><PERSON>ar <PERSON>", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "edit_address": "<PERSON><PERSON>", "enter_new_password": "Insira uma nova senha para {{ email }}", "forgot_password": "<PERSON><PERSON><PERSON>u-se da sua senha?", "learn_more": "<PERSON>ber mais", "log_in": "<PERSON><PERSON><PERSON>", "log_out": "<PERSON><PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON>", "next_product": "Seguinte: {{ title }}", "print": "Imprimir", "reset_account_password": "<PERSON>or senha de conta", "reset_password": "<PERSON><PERSON>", "reset_your_password": "Repor a sua senha", "return_to_account": "<PERSON><PERSON><PERSON> a<PERSON>", "return_to_store": "Regress<PERSON>", "set_default_address": "Selecionar como endereço predefinido", "show_less": "<PERSON><PERSON> menos", "show_more": "<PERSON><PERSON> mais", "sign_in": "<PERSON><PERSON><PERSON>", "skip_to_content": "Pular para o Conteúdo", "start_shopping": "<PERSON><PERSON><PERSON> a fazer compras", "submit": "Enviar", "update_address": "<PERSON><PERSON><PERSON><PERSON>", "update_cart": "<PERSON><PERSON><PERSON><PERSON>", "view_addresses": "<PERSON><PERSON>", "view_all_products_count": "Ver todos os<br>{{ count }} produtos", "enter": "Entrar"}, "info": {"comments_count": {"one": "{{ count }} coment<PERSON><PERSON>", "other": "{{ count }} coment<PERSON><PERSON>s"}, "collection_no_products": "<PERSON><PERSON><PERSON><PERSON>, mas nenhum produto corresponde à sua pesquisa.", "max_characters": "{{ max_chars }} m<PERSON><PERSON><PERSON> de caracteres", "shipping_at_checkout_taxes_included": "Taxas incluídas. Códigos de envio e desconto calculados no checkout.", "shipping_at_checkout": "Códigos de desconto, custos de envio e impostos adicionados na finalização de compra.", "cart_empty": "O seu carrinho de compras está neste momento vazio.", "newsletter_success": "Obrigado pela sua subscrição", "save_amount": "Poupe {{ saved_amount }}", "placeholder_product_description": "Esta área é usada para descrever o seu produto detalhadamente. Informe o cliente do aspeto, toque e estilo do seu produto. Adicione detalhes sobre a cor, materiais usados, tamanhos e onde foi fabricado.", "tax_included": "Imposto incluído.", "shipping_policy_html": "<a href='{{ link }}'>Envio</a> calculado na finalização da compra.", "sold_out": "Esgotado", "view_in_space_explanation": "Ver no seu espaço, carrega item na janela de realidade aumentada", "low_stock_count": {"one": "{{ count }} em estoque", "other": "{{ count }} em estoque"}, "in_stock": "Em estoque", "back_in_stock_on": "Estará disponível após {{ date }}", "backordered": "Inventory no caminho", "section_no_content": "Esta sección actualmente no incluye ningún contenido. Añade un contenido a esta sección utilizando la barra lateral.", "posted_successfully_moderated": "O seu comentário foi publicado com sucesso! Obrigado!", "comment_posted_successfully": "Seu comentário foi postado! Obrigado!", "comments_must_be_approved": "Tenha em atenção que os comentários precisam de ser aprovados antes de serem exibidos", "product_count": {"one": "{{ count }} item", "other": "{{ count }} itens"}, "contact_confirmation": "Obri<PERSON> por entrar em contacto connosco. Responder-lhe-emos logo que possível.", "404_page_not_found": "404 Página Não Encontrada", "page_does_not_exist_html": "A página que solicitou não existe. Clique <a href='{{ url }}'>aqui</a> para continuar as suas compras.", "you_save_amount": "Poupe {{ saved_amount }}", "search_result_count": {"one": "{{ count }} resultado", "other": "{{ count }} resultados"}, "search_no_results_html": "A sua pesquisa por \"{{ terms }}\" não produziu resultados.", "address_error": "Não é possível localizar o endereço", "address_no_results": "Nenhum resultado para este endereço", "address_query_limit_html": "Você excedeu a cota de uso da API do Google. Considere a atualização para um <a href=\"https://developers.google.com/maps/premium/usage-limits\">Plano Premium</a>.", "map_auth_error_html": "Houve um problema autenticação de sua conta do Google Maps. Criar e ativar a <a href=\"https://developers.google.com/maps/documentation/javascript/get-api-key\">API JavaScript</a> e permissões de <a href=\"https://developers.google.com/maps/documentation/geocoding/get-api-key\">geocodificação da API</a> do seu aplicativo.", "pick_up_available": "transbordo", "pick_up_currently_unavailable": "Captador atualmente indisponível", "pick_up_available_at_html": "Capt<PERSON> disponível em <strong> {{location_name}} </strong>", "pick_up_unavailable_at_html": "Captador disponível atualmente em <strong> {{location_name}} </strong>", "cart_max_quantity": "Solo puedes tener {{ quantity }} de {{ title }} en tu carrito.", "create_your_password_to_activate": "Crie a sua senha para ativar a sua conta.", "disabled": "<PERSON>v<PERSON><PERSON><PERSON>", "email_to_reset_password": "Vamos enviar-lhe um email para repor a sua senha.", "fulfilled_date_html": "<PERSON><PERSON><PERSON><PERSON><PERSON> em {{ date }}", "gift_card_description": "Aqui está o seu cartão-presente!", "gift_card_expired_on": "Expirou em {{ expiry }}", "gift_card_expires_on": "Expira em {{ expiry }}", "gift_card_redeem": "Use este código ao fazer o check-out para redimir o seu cartão-presente", "gift_card_title": "Aqui está o seu cartão-presente de {{ value }} para {{ shop }}!", "no_orders": "Ainda não efetuou nenhum pedido.", "order_cancelled_on_html": "Pedido Can<PERSON>ado em {{ date }}", "order_cancelled_reason": "Motivo: {{ reason }}", "order_placed_on_html": "Efe<PERSON><PERSON> em {{ date }}", "ready_to_ship_on": "Envio a partir de {{ date }}", "recovery_email_sent": "Enviámos-lhe um email com um link para atualizar a sua senha.", "search_results_for_html": "A sua pesquisa por \"{{ terms }}\" revelou o seguinte:", "signup_success": "Iremos enviar-lhe um email imediatamente antes de abrirmos!", "unavailable": "Indisponível", "variant_sold_out_or_unavailable": "Variante esgotada ou indisponível", "will_be_powered_by_html": "Esta loja será movida por {{ shopify }}", "you_must_agree": "Você deve concordar com os termos e condições de vendas para verificar", "admin_link_html": "É o dono da loja? <a href=\"/admin\" class=\"text-link\">Inicie sessão aqui</a>"}, "labels": {"home": "Início", "collections": "Colecções:", "quantity": "Quantidade", "regular_price": "Preço normal", "sale_price": "Preço de saldo", "goes_great_with": "Vai grande com", "large": "Large", "small": "Small", "list": "List", "example_collection": "Título da Coleção Exemplo", "email": "Email", "recipient_email": "E-mail do destinatário", "name": "Nome", "recipient_name_optional": "Nome do destinatário (opcional)", "message_optional": "Mensagem (opcional)", "message": "Mensagem", "recipient_send_on": "Enviar (opcional)", "subtotal": "Subtotal", "search": "<PERSON><PERSON><PERSON><PERSON>", "account": "Conta", "cart": "Carrinho de Compras", "site_navigation": "Navegação", "language": "Idioma", "currency": "<PERSON><PERSON>", "sale": "<PERSON><PERSON><PERSON>", "example_product": "Título do Produto Exemplo", "description": "Descrição", "quick_shop": "Compra r<PERSON>a", "size_chart": "Gráfico de ta<PERSON>ho", "price": "Preço", "more_from": "<PERSON><PERSON> <PERSON>", "latest_posts": "<PERSON><PERSON><PERSON>", "featured_product": "Produto em destaque", "phone_number": "Número de Telefone", "catalog": "Catálogo", "more_from_html": "<PERSON><PERSON> de {{ link }}", "password": "Palavra-passe", "recently_viewed": "Visto recentemente", "suggestions": "Sugestões", "pages": "Páginas:", "articles": "Artigos:", "products": "<PERSON><PERSON><PERSON>", "from_price_html": "<span>Desde</span> {{ price }}", "tagged_with": "Etiquetado como \"{{ tags }}\"", "page_title": "<PERSON><PERSON><PERSON><PERSON> {{ page }}", "my_account": "A Minha Conta", "we_accept": "Nós <PERSON>", "account_details": "<PERSON><PERSON><PERSON>", "address1": "Endereço1", "address2": "Endereço2", "addresses": "Os Seus Endereços", "all_products": "Todos os Produtos", "billing_address": "Endereço de Faturação", "categories": "Categorias", "city": "Localidade", "company": "Empresa", "country": "<PERSON><PERSON>", "date": "Data", "default": "Predefinição", "discount": "Desconto", "discounts": "Descontos", "first_name": "Nome Próprio", "fulfillment_status": "Estado de Atendimento", "last_name": "Sobrenome", "order": "Pedido", "order_history": "Histórico de Pedidos", "order_name": "Pedido {{ name }}", "payment_status": "Estado de Pagamento", "phone": "Telefone", "popular_posts": "Artigos populares", "product": "Produ<PERSON>", "province": "<PERSON>v<PERSON><PERSON>", "reviews": "Avaliações", "shipping": "<PERSON><PERSON>", "shipping_address": "Endereço de Envio", "sku": "SKU", "tags": "Etiquetas", "tax": "Taxa", "top_searched": "<PERSON><PERSON> pesquis<PERSON>", "total": "Total", "zip": "Código Postal", "your_password": "A sua palavra-passe"}, "trigger": {"color_swatch": "Cor", "size": "<PERSON><PERSON><PERSON>"}, "date_formats": {"month_day_year": "%d de %B de %Y"}, "pagefly": {"products": {"product": {"regular_price": "Regular price", "sold_out": "Sold out", "unavailable": "Unavailable", "on_sale": "Sale", "quantity": "Quantity", "add_to_cart": "Add to cart", "back_to_collection": "Back to {{ title }}", "view_details": "View details"}}, "article": {"tags": "Tags:", "all_topics": "All topics", "by_author": "by {{ author }}", "posted_in": "Posted in", "read_more": "Read more", "back_to_blog": "Back to {{ title }}"}, "comments": {"title": "Leave a comment", "name": "Name", "email": "Email", "message": "Message", "post": "Post comment", "moderated": "Please note, comments must be approved before they are published", "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.", "success": "Your comment was posted successfully! Thank you!", "comments_with_count": {"one": "{{ count }} comment", "other": "{{ count }} comments"}}, "password_page": {"login_form_message": "Enter store using password:", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_submit": "Enter", "signup_form_email_label": "Email", "signup_form_success": "We will send you an email right before we open!", "password_link": "Enter using password"}}}