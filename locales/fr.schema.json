{"actions": {"add_alt_text": "Ajoutez du texte alt pour un meilleur référencement grâce au bouton « Modifier » ci-dessus.", "add_bottom_spacing": "Ajouter l'espacement inférieur", "add_my_account_link": "Ajou<PERSON>z le lien « Mon compte » si les comptes clients sont activés.", "add_phone_number": "Ajouter un champ de numéro de téléphone", "add_reviews_by": "Ajoutez des avis en activant le paramètre ci-dessous, en installant  [l'application Shopify Product Reviews](https://apps.shopify.com/product-reviews) et en suivant notre [guide d'installation](https://archetypethemes.co/blogs/support/installing-shopifys-product-reviews-app).", "add_spacing": "Ajouter un espacement", "add_spacing_above": "Ajouter un espacement au-dessus et en dessous", "add_top_spacing": "Ajouter un espacement supérieur", "approve_button_text": "Approuver le texte du bouton", "autoplay_video": "Lecture automatique de la vidéo", "blur_the_image": "Brouiller l'image", "capitalize_first_letter": "Mettre la première lettre en majuscule", "choose_a_menu": "Choisissez un menu", "collapse_filters": "Réduire les filtres", "decline_button_text": "Refuser le texte du bouton", "disable_accordion": "Désactiver l'accordéon", "disable_for_accounts": "Désactiver pour les titulaires de compte", "do_not_show_on_home": "Ne pas afficher sur la page d'accueil", "enable_additional_checkout": "Activer des boutons de paiement supplémentaires", "enable_color_swatches": "Activer les échantillons de couleur", "enable_dropdown_on_hover": "Activer la liste déroulante au survol", "enable_dynamic_product": "Activer les options de produit dynamiques", "enable_filter": "<PERSON><PERSON> le filtre", "enable_full_width": "<PERSON><PERSON> la pleine largeur", "enable_header": "<PERSON><PERSON> l'en<PERSON>tête", "enable_image_zoom": "<PERSON>z le zoom sur l'image", "enable_newsletter": "Activer le bulletin d'information", "enable_order_notes": "Activer les notes de commande", "enable_parallax": "Activer la parallaxe", "enable_pickup_availability": "Activer la fonction de disponibilité de la collecte", "enable_predictive_search": "Activer la recherche prédictive", "enable_product_reviews": "Activer les avis sur les produits", "enable_quick_add": "Activer l'ajout rapide", "enable_quick_view": "Activer l'affichage rapide", "enable_sticky_header": "<PERSON><PERSON> l'en-tête collé", "enable_swipe_on": "<PERSON><PERSON> le balayage sur mobile", "enable_terms": "Activer la case à cocher des conditions générales", "enable_test_mode": "Activer le mode test", "enable_video_looping": "<PERSON><PERSON> le bouclage de la vidéo", "enlarge_text": "<PERSON><PERSON><PERSON><PERSON> le texte", "force_image_size": "Forcer la taille de l'image", "hide_all_image_blocks_mobile": "Masquer tous les blocs d'images sur le portable", "hide_controls": "Masquer les contrôles", "init_display_opened": "S'affiche initialement comme ouvert", "learn_to_setup_color_swatches": "Nécessite que le type soit défini sur 'Boutons'. [En savoir plus sur la configuration des nuanciers] (https://help.archetypethemes.co/en/articles/2765)", "learn_to_setup_local_pickup": "Apprenez à installer cette fonction [ici](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup)", "learn_to_setup_product_specs": "Découvrez comment configurer cette fonctionnalité [ici](https://help.archetypethemes.co/en/articles/841280)", "learn_to_setup_product_tags": "Dé<PERSON>uvrez comment configurer cette fonctionnalité [ici](https://help.archetypethemes.co/en/articles/832384)", "loop_video": "<PERSON><PERSON><PERSON> vid<PERSON>o", "mute_video": "Couper la vidéo", "overlay_header_over_collection": "En-tête superposé sur la collection", "overlay_header_over_home": "En-tête superposé sur la page d'accueil", "register_google_maps_api": "<PERSON><PERSON> <PERSON> [enregistrer une clé API Google Maps](https://help.shopify.com/manual/using-themes/troubleshooting/map-section-api-key) pour afficher la carte.", "register_mapbox_api": "Vous devrez [enregistrer un jeton d’accès à l’API Mapbox](https://help.archetypethemes.co/en/articles/1501376) pour afficher la carte", "repeat_main_menu": "Répétition du menu principal sur mobile", "return_button_text": "Texte du bouton de retour", "select_collections_to_show": "Sélectionnez les collections à afficher", "share_on_facebook": "Partager sur Facebook", "share_on_social": "Partager sur les réseaux sociaux", "show_arrow": "Afficher la flèche", "show_as_tab": "Afficher sous forme d'onglet", "show_author": "Afficher l'auteur", "show_bottom_padding": "Afficher le rembourrage inférieur", "show_breadcrumbs": "<PERSON><PERSON><PERSON><PERSON> le fil d'Ariane", "show_cents_as": "Afficher les centimes en exposant", "show_collection_image": "Afficher l'image de la collection", "show_collections_in_breadcrumbs": "Afficher la page des collections dans le fil d'Ariane", "show_comment_count": "Afficher le nombre de commentaires", "show_copyright": "<PERSON><PERSON><PERSON><PERSON> le droit d'auteur", "show_currency_flags": "Afficher les drapeaux des devises", "show_currency_selector": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de devise", "show_date": "Affiche<PERSON> la date", "show_dynamic_checkout": "Affichez le bouton de paiement dynamique", "show_excerpt": "Afficher un extrait", "show_first_product": "Afficher le premier produit dans les méga-menus", "show_footer_content": "Afficher le contenu du pied de page dans le menu mobile", "show_get_directions": "<PERSON><PERSON><PERSON><PERSON> le bouton « Obtenir un itinéraire ».", "show_image": "Afficher l'image", "show_increment_dividers": "Afficher les séparateurs d'incréments", "show_inventory_transfer": "Affichez l'avis de transfert de stock", "show_language_selector": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de langue", "show_newsletter_signup": "Afficher l'inscription au bulletin d'information", "show_payment_icons": "Afficher les icônes de paiement", "show_prefix": "<PERSON><PERSON><PERSON><PERSON> le préfixe du produit", "show_price": "<PERSON>ff<PERSON>r le prix", "show_product_tags": "Afficher les étiquettes des produits", "show_recipient_information": "Afficher le formulaire d’information sur le destinataire pour les cartes‑cadeaux en tant que produit", "show_rss_link": "Afficher le lien RSS", "show_saved_amount": "<PERSON>ff<PERSON><PERSON> le montant épargné", "show_section_divider": "Afficher le séparateur de section", "show_sku": "Afficher l'UGS", "show_social_accounts": "Afficher les comptes sociaux", "show_social_icons": "Afficher les icônes sociales", "show_sort_options": "Afficher les options de tri", "show_swatch_labels": "Afficher les étiquettes des échantillons", "show_tags": "Afficher les balises", "show_thumbnail_arrows": "Affichez les flèches des vignettes", "show_title": "<PERSON><PERSON><PERSON><PERSON> le titre", "show_top_padding": "Afficher le rembourrage supérieur", "show_variant_labels": "Afficher les étiquettes des variantes", "show_vendor": "<PERSON><PERSON><PERSON><PERSON> le distributeur", "show_view_all_link": "Afficher le lien « Tout voir »", "show_wave_transition": "<PERSON><PERSON><PERSON><PERSON> la <PERSON>", "sort_collections_by": "Trier les collections par :", "tweet_on_twitter": "Tweeter sur X", "view_setup_instructions": "[Voir les instructions de configuration](https://archetypethemes.co/blogs/expanse/how-do-i-set-up-color-swatches)", "zoom_image_to_fill": "Zoomer l'image pour remplir l'espace"}, "info": {"2000x800px_recommended": "2000 x 800px recommandé", "Shopify_youtube": "https://www.youtube.com/user/shopify", "aligns_next_to_custom_content": "S'aligne à côté d'autres contenus personnalisés", "all_submissions_sent_to_store_email": "Toutes les soumissions sont envoyées à l'adresse e-mail du client de votre magasin. [En savoir plus](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).", "allow_your_customers_to_filter": "Permettez à vos clients de filtrer les collections et les résultats de recherche en fonction de la disponibilité des produits, du prix, de la couleur, etc. [Personnalisez les filtres](/admin/menus)", "appears_when_newsletter_popup_closed": "Apparaît lorsque la fenêtre popup de la newsletter est fermée.", "applies_a_maximum_width": "Applique une largeur maximale", "applys_when_thumbnail_next_to_media": "S'applique uniquement lorsque la position de la vignette est définie sur « À côté des médias ».", "buttons_appear_either_cart_or_checkout": "Les boutons peuvent apparaître soit sur la page du panier, soit sur la page de paiement, mais pas les deux.", "choose_which_platforms_share_theme_settings": "Choisissez les plates-formes à partager dans les paramètres globaux du thème.", "collections_listed_by_default": "Toutes vos collections sont listées par défaut. Pour personnaliser votre liste, choisissez « Sélectionné » et ajoutez des collections.", "content_for_age_verification_failure": "Ce contenu s'affichera si l'utilisateur ne répond pas aux exigences de la vérification.", "customers_who_subscribe_accept_marketing": "Les clients qui s'inscrivent verront leur adresse e-mail ajoutée à la [liste des clients] qui acceptent le marketing (/admin/customers?query=&accepts_marketing=1).", "darkens_your_image": "Assombrit votre image pour garantir la lisibilité de votre texte", "darkens_your_video": "Assombrit votre vidéo pour que votre texte soit lisible", "defaults_to_collection_title": "Le titre de la collection s'affiche par défaut", "defaults_to_menu_title": "Titre du menu par défaut", "does_not_appear_on_mobile": "N'apparaît pas sur les téléphones mobiles afin de respecter les [directives interstitielles de Google] (https://developers.google.com/search/blog/2016/08/helping-users-easily-access-content-on) pour un meilleur référencement.", "dynamic_recommendations_use": "Les recommandations dynamiques utilisent les informations sur les commandes et les produits pour changer et s'améliorer au fil du temps. [En savoir plus](https://help.shopify.com/en/themes/development/recommended-products)", "enable_shop_pay_for_shop_app_follow": "Pour autoriser les clients à suivre votre boutique sur l’application Shop depuis votre boutique en ligne, Shop Pay doit être activé. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "for_product_with_long_descriptions": "Pour les lignes de produits avec de longues descriptions, nous vous recommandons de placer votre description et vos onglets dans cette section.", "forces_the_age_verification": "Force la vérification à s'afficher à chaque actualisation et ne doit être utilisée que pour modifier la fenêtre contextuelle. Assurez-vous que le « mode test » est désactivé lors du lancement de votre boutique.", "gift_card_products": "Les cartes-cadeaux en tant que produits peuvent être envoyées directement au destinataire avec un message personnel. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)", "google_maps_will_find_location": "Google maps trouvera l'emplacement exact", "if_collection_image_enabled": "(si l'image de la collection est activée)", "image_displays_if_video_fails": "L'image s'affichera si la vidéo ne peut pas se charger", "image_source_adjusted_theme_settings": "La source de l'image peut être ajustée sous Paramètres du thème > Tuiles de collection", "lazy_loading_enabled_below_fold": "Le chargement différé doit être activé lorsque les images de section sont sous le pli. [En savoir plus](https://archetypethemes.co/blogs/support/what-is-lazyloading)", "learn_inventory_transfers": "Apprenez à créer des transferts de stock [ici](https://help.shopify.com/en/manual/products/inventory/transfers/create-transfer)", "learn_mega_menu": "[En savoir plus](https://archetypethemes.co/blogs/expanse/how-do-i-create-a-mega-menu)", "learn_more_media_types": "En savoir plus sur les [types de médias](https://help.shopify.com/en/manual/products/product-media)", "learn_to_setup_product_prefix": "Dé<PERSON>uvrez comment configurer cette fonctionnalité [ici](https://help.archetypethemes.co/en/articles/851328)", "lets_customers_checkout_familiar": "Permet aux clients de régler leurs achats directement en utilisant un mode de paiement familier. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)", "links_to_collections_appear_here": "Les liens vers les collections de votre menu apparaîtront ici. [En savoir plus](https://archetypethemes.co/blogs/expanse/how-do-i-create-subcollections)", "links_to_youtube_video_player": "Les liens vers les vidéos YouTube s'ouvrent dans un lecteur vidéo", "mapbox_will_find_location": "Mapbox trouvera l’emplacement exact", "menu_shows_top_level": "Ce menu n'affichera que les liens de premier niveau", "model_media_type_required": "Le produit doit avoir un type de support de modèle 3D", "no_effect_when_size_natural": "Aucun effet lorsque la taille de l'image de la grille est réglée sur « Naturel ».", "not_shown_to_customers_with_accounts": "Ne sera pas visible par les clients qui ont créé un compte sur votre boutique.", "number_of_days_popup_reappears": "Nombre de jours avant qu'une fenêtre contextuelle rejetée ne réapparaisse.", "only_appear_on_articles": "Apparaîtra uniquement sur les articles", "only_applies_on_desktop": "S’applique uniquement dans la fenêtre du bureau", "overrides_video_url_if_both_set": "Remplace le paramètre d’URL de la vidéo si les deux sont définis", "recently_viewed_products_only_visible": "Les produits récemment consultés sont uniquement visibles lorsque vous naviguez en dehors de l'éditeur.", "recommended_a_square_image": "A recommandé un format carré pour une expérience mobile optimale", "requires_square_images": "Requiert des images carrées", "scaled_to_32x32": "Sera réduite à 32 x 32 px", "section_appears_when_product_vendor": "Cette section n'apparaîtra que si vous êtes sur un produit pour lequel un vendeur a été défini.", "section_on_product_page_w_collection": "Cette section n'apparaîtra que si vous êtes sur une page de produit qui a été atteinte via une collection", "set_as_max_width": "Défini comme une largeur maximale, peut apparaître plus petit", "shopify_facebook": "https://www.facebook.com/shopify", "shopify_instagram": "https://instagram.com/shopify", "shopify_linkedin": "https://www.linkedin.com/in/shopify", "shopify_pinterest": "https://www.pinterest.com/shopify", "shopify_snapchat": "https://www.snapchat.com/add/shopify", "shopify_tiktok": "https://www.tiktok.com/@shopify", "shopify_tumblr": "http://shopify.tumblr.com", "shopify_twitter": "https://twitter.com/shopify", "shopify_vimeo": "https://vimeo.com/shopify", "sidebar_is_first_two_sections": "Assurez-vous que votre barre latérale est l'une des deux premières sections", "sign_up_creates_customer": "Les clients qui s'inscrivent verront leur adresse e-mail ajoutée à la [liste des clients] qui acceptent le marketing (/admin/customers?query=&accepts_marketing=1).", "sorting_applies_when_all_selected": "Le tri s'applique uniquement lorsque « Tout » est sélectionné.", "styles_online_apply_to_ital_text": "Les styles s'appliquent uniquement au texte en italique dans le titre", "supported_video_formats": "Prend en charge YouTube, .MP4 et Vimeo. Toutes les fonctionnalités ne sont pas prises en charge par Vimeo. [En savoir plus](https://help.archetypethemes.co/en/articles/2740)", "text_below_on_mobile": "Le texte est toujours sous l'image sur mobile", "to_add_currency_settings": "Pour ajouter une devise, accédez à vos [paramètres de devise](/admin/settings/payments).", "to_add_language_settings": "Pour ajouter une langue, allez à vos [paramètres de langue.](/admin/settings/languages)", "to_select_complementary_add_search_discovery": "Pour sélectionner des produits complémentaires, ajoutez l'application Search & Discovery. [En savoir plus](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations#complementary-products)", "use_instead_of_google_maps_api": "À utiliser à la place d'une clé API", "use_instead_of_mapbox_api": "Utiliser à la place d’un jeton d’accès API", "used_when_on_top_of_image": "U<PERSON><PERSON><PERSON> lorsqu'il se trouve au-dessus d'une image", "values_below_10_not_recommended": "Le délai est désactivé dans l'éditeur de thème pour des raisons de visibilité", "video_with_sound_not_autoplay": "La vidéo avec du son ne sera pas lue automatiquement"}, "labels": {"3D_model": "Modèle 3D", "404_page": "Page 404", "75_width": "Largeur de 75 %", "accounts": "<PERSON><PERSON><PERSON>", "additional_copyright_text": "Texte de droit d'auteur supplémentaire", "additional_footer_content": "Contenu supplémentaire du pied de page", "address_and_hours": "Adresse et horaires", "advanced_accordion": "Accord<PERSON><PERSON> a<PERSON>", "age_verification_popup": "Fenêtre contextuelle de vérification de l'âge", "age_verification_question": "Question de vérification de l'âge", "alignment": "Alignement", "alignments": {"bottom": "Bas", "bottom_center": "En bas au centre", "bottom_left": "En bas à gauche", "bottom_right": "En bas à droite", "center": "Centre", "center_left": "Centre gauche", "center_right": "Centre droite", "center_text": "<PERSON><PERSON> le texte", "centered": "Centré", "left": "G<PERSON><PERSON>", "left_to_right": "De gauche à droite", "middle": "Milieu", "right": "<PERSON><PERSON><PERSON>", "right_to_left": "De droite à gauche", "top": "<PERSON><PERSON>", "top_center": "En haut au centre", "top_left": "En haut à gauche", "top_right": "En haut à droite"}, "all": "<PERSON>ut", "amount": "<PERSON><PERSON>", "announcement": "<PERSON><PERSON><PERSON>", "announcement_bar": "Barre d'annonces", "announcement_text": "Texte de l'annonce", "apps": "Applications", "arch": "Cambre", "arrows": "Flèches", "article": "Article", "article_pages": "Pages de l'article", "author": "<PERSON><PERSON><PERSON>", "author_info": "Infos sur l'auteur", "authors_image": "Image de l'auteur", "autochange_slides": "Changement automatique des diapositives", "back_to_collection": "Retour à la collection", "background": "Arrière-plan", "background_image": "Image de fond", "background_video_link": "Lien vers une vidéo d'arrière-plan", "bag": "Sac", "banner": "Bannière", "bars": "<PERSON><PERSON>", "base_size": "<PERSON><PERSON> de <PERSON> base", "below_media": "En dessous du média", "bills": "Factures", "blocks_per_row": "Blocs par rangée", "blog": "Blog", "blog_pages": "Pages du blog", "blog_posts": "Articles de blog", "blog_sidebar": "Barre latérale du blog", "body": "Corps", "body_text": "Corps du texte", "borders": "Bordures", "bottom_text": "Texte du bas", "button": "Bouton", "button_1_link": "Lien du bouton 1", "button_1_text": "Texte du bouton 1", "button_2_link": "Lien du bouton 2", "button_2_text": "Texte du bouton 2", "button_label": "Texte du bouton", "button_link": "<PERSON>n du bouton", "button_style": "Style du bouton", "button_text": "Texte du bouton", "button_text_2": "Texte du bouton 2", "buttons": "Boutons", "buy_buttons": "Boutons d'achat", "calendar": "<PERSON><PERSON><PERSON>", "capitalize": "Mettre en majuscules", "capitalize_navigation": "Mettre la navigation en majuscules", "cart": "<PERSON><PERSON>", "cart_dot": "Point de panier", "cart_dot_text": "Texte du point de panier", "cart_icon": "Icône de panier", "cart_page": "Page du panier", "cart_recommendations": "Recommandations pour le panier", "cart_type": "Type de panier", "change_images_every": "Changer d'image à chaque fois", "charity": "<PERSON><PERSON><PERSON>", "chat": "Cha<PERSON>", "chat_link": "Lien vers le chat", "checkmark": "Coche", "circle": "Cercle", "circle_11": "Cercle (1:1)", "circular_images": "Images circulaires", "classic": "Classique", "cold_blur": "<PERSON>lou <PERSON>", "collection": "Collection", "collection_carousel": "Carrousel de collecte", "collection_description": "Description de la collection", "collection_header": "En-tête de collection", "collection_image": "Image de la collection", "collection_list": "Liste des collections", "collection_tile_background": "Fond de carreaux de collection", "collection_tile_style": "Style des tuiles de la collection", "collection_tiles": "<PERSON><PERSON> de collection", "collections_list_page": "Page de liste des collections", "color_scheme": "Combinaison de couleurs", "color_schemes": {"1": "Schéma de couleur 1", "2": "Schéma de couleur 2", "3": "Schéma de couleurs 3"}, "color_swatches": "Nuanciers", "colors": "Couleurs", "column": "Colonne", "contact": "Contact", "contact_and_social": "Contact et réseaux sociaux", "contact_form": "Formulaire de contact", "contact_page": "Page de contact", "content": "Contenu", "content_alignment": "Alignement du texte", "content_alignment_desktop": "Alignement du contenu sur le bureau", "content_position": "Emplacement du contenu", "countdown": "Compte à rebours", "custom_1": "Personnalisé 1", "custom_2": "Personnalisé 2", "custom_3": "Personnalisé 3", "custom_content": "<PERSON><PERSON><PERSON>", "darken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "day": "Jour", "declined": "Declined", "default": "<PERSON><PERSON> <PERSON><PERSON>", "default_product_layout": "Mise en page par défaut du produit", "delay": "<PERSON><PERSON><PERSON>", "description": "Description", "design": "Design", "desktop_columns_per": "Colonnes par ligne (ordinateur)", "desktop_height": "Hauteur du bureau", "desktop_height_adjustment": "Réglage de la hauteur du bureau", "desktop_logo_height": "Hauteur du logo pour ordinateur de bureau", "desktop_logo_width": "Largeur du logo pour ordinateur de bureau", "desktop_products_per": "Produits sur l'ordinateur de bureau par ligne", "direction": "Direction", "dot": "Point", "dots": "Points", "dropdown": "<PERSON><PERSON>", "edges": "Bords", "email_signup": "Inscription à la liste de diffusion", "envelope": "Enveloppe", "extras": "Extras", "facebook": "Facebook", "fallback_image": "Image de remplacement", "faq": "FAQ", "favicon": "Favicon", "favicon_image": "Image favicon", "featured_article": "Article vedette", "featured_collection": "Collection en vedette", "featured_product": "Produit en vedette", "filtering_and_sorting": "Filtrage et tri", "first_product": "Premier produit", "fixed": "Fixe", "flexible": "Flexible", "follow_on_shop": "Suivre sur Shop", "font": "Police", "font_weights": {"bold": "Gras", "extrabold": "Extra-gras", "extralight": "Extra-léger", "light": "<PERSON><PERSON><PERSON>", "semibold": "Semi-gras"}, "footer": "Pied de page", "footer_group": "Groupe de pied de page", "footer_promotions": "Promotions en bas de page", "form": "Formulaire", "frequency": "<PERSON><PERSON><PERSON>", "full_page_width": "<PERSON>ur de la page entière", "full_width": "<PERSON><PERSON>e largeur", "fullwidth_details": "<PERSON>é<PERSON> en pleine largeur", "gallery": "Galerie", "gears": "Engrenages", "general": "Général", "gift": "<PERSON><PERSON>", "globe": "Globe", "google_maps_api": "Clé API Google Maps", "grey": "<PERSON><PERSON>", "grey_round": "<PERSON>d gris", "grey_square": "<PERSON>é gris", "grid_spacing": "Espacement de la grille", "handwriting": "Écriture", "header": "<PERSON>-tête", "header_group": "Groupe d'en-tête", "heading": "Titre", "heading_ital_text_style": "Titre style de texte en italique", "heading_position": "Position du titre", "heading_size": "<PERSON><PERSON> du titre", "heading_text_size": "Taille du texte de l'en-tête", "headings": "Titres", "heart": "<PERSON><PERSON><PERSON>", "height": "<PERSON><PERSON>", "hero": "Hero", "hero_optional_slides": "Hero (diapositives facultatives)", "hide_timer_on": "Masquer la minuterie à la fin", "horizontal": "Horizontale", "horizontal_position": "Position horizontale", "hot_spot_icon": "Style des points chauds", "hotspot_color": "Couleur des points chauds", "hour": "<PERSON><PERSON>", "hover_to_reveal": "Passez la souris pour révéler la deuxième image", "html": "HTML", "html_block": "Bloc HTML", "icon": "Icône", "icon_color": "Couleur de l'icône", "icons": "Icônes", "image": "Image", "image_2": "Image 2", "image_alignment": "Alignement des images", "image_background": "Fond d'image", "image_breathing_room": "Espace pour l'image", "image_comparison": "Comparaison d'images", "image_crop": "Recadrage de l'image", "image_hotspots": "Points chauds des images", "image_link": "Lien d'image", "image_on_left": "Image à gauche", "image_on_right": "Image à droite", "image_optional_slides": "Image (diapositives facultatives)", "image_position": "Position de l'image", "image_ratio": "Ratio d'image", "image_section_background": "Fond de section d'image", "image_shape": "Forme de l'image", "image_size": "Largeur de l'image", "image_style": "Style d'image", "image_width": "Largeur d'image", "image_with_text": "Image avec texte", "images_per_row": "Images par ligne", "indent_image": "Indentation de l'image", "inline": "Inline", "instagram": "Instagram", "inventory_status": "État du stock", "label": "Étiquette", "landscape": "Paysage", "landscape_43": "Paysage (4:3)", "language": "<PERSON><PERSON>", "large_grid": "Grande grille", "large_image_with": "Grande image avec zone de texte", "layout": "Mise en page", "layout_on_desktop": "Disposition sur le bureau", "lazy_load_images": "Images de chargement paresseux", "leaf": "<PERSON><PERSON><PERSON>", "letter_spacing": "Espacement des lettres", "line_height": "<PERSON><PERSON> <PERSON> ligne", "linen": "<PERSON>", "lines_and_borders": "Lignes et bordures", "link": "<PERSON><PERSON>", "link_2": "Lien 2", "link_block": "Bloc de liens", "link_label": "Étiquette de lien", "linkedin": "LinkedIn", "list": "Liste", "lock": "Cadenas", "logo": "Logo", "logo_center_menu": "Logo au centre, menu en dessous", "logo_image": "Image du logo", "logo_left_menu_below": "Logo à gauche, menu en-dessous", "logo_left_menu_center": "Logo à gauche, menu au centre", "logo_left_menu_left": "Logo à gauche, menu à gauche", "logo_list": "Liste des logos", "low_inventory_threshold": "Seuil de stocks bas", "map": "<PERSON><PERSON>", "map_address": "<PERSON>ress<PERSON> du plan", "mapbox_api": "Jeton d’accès à l’API Mapbox", "marble": "Marbre", "max_products_to": "Max produits à afficher", "maximum_products_to": "Quantité maximale de produits à afficher", "media": "Support multimédia", "media_crop": "Recadrage média", "media_gallery_layout": "Mise en page de la galerie multimédia", "media_on_left": "Médias à gauche", "media_on_right": "Médias à droite", "media_width": "Largeur du support", "media_with_text": "Média avec texte", "middle_text": "Texte du milieu", "minimal": "Minimal", "minimal_bag": "Sac minimum", "minimal_wave": "Vague minimale", "minute": "Minute", "mobile_height": "<PERSON>ur du mobile", "mobile_height_adjustment": "Réglage de la hauteur du mobile", "mobile_image": "Image mobile", "mobile_layout": "Mise en page sur mobile", "mobile_logo_height": "Hauteur du logo pour mobile", "mobile_logo_width": "Largeur du logo pour mobile", "mobile_text": "Texte sur mobile", "model": "<PERSON><PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "months": {"april": "Avril", "august": "Août", "december": "Décembre", "february": "<PERSON><PERSON><PERSON><PERSON>", "january": "<PERSON><PERSON>", "july": "<PERSON><PERSON><PERSON>", "june": "Juin", "march": "Mars", "may": "<PERSON>", "november": "Novembre", "october": "Octobre", "september": "Septembre"}, "more_from_collection": "Plus de la collection", "more_from_vendor": "Plus du vendeur", "narrow_column": "Réduire la colonne", "natural": "Naturel", "navigation": "Navigation", "navigation_font": "Police de navigation", "navigation_size": "Taille de la navigation", "newsletter": "Bulletin d'information", "next_to_media": "À côté des médias", "none": "Aucun", "notebook": "Carnet", "number_of_bars": "Nombre de barres", "number_of_products": "Nombre de produits par slide", "number_of_related": "Nombre de produits connexes", "optimize_for_readability": "Optimiser pour la lisibilité", "optional": "Optionnel", "outline": "<PERSON>tour", "overlay": "Re<PERSON>uv<PERSON>r", "overlay_header": "En-tê<PERSON> de la superposition", "overlay_opacity": "Opacité de superposition", "package": "Emballage", "page": "Page", "page_full_width": "Page (pleine largeur)", "pagination_type": "Type de pagination", "paper": "<PERSON><PERSON><PERSON>", "paragraph_hotspot": "Points chauds du paragraphe", "parallax_direction": "Direction de la parallaxe", "parallax_image": "Image parallaxe", "payments_and_localization": "Paiements et localisation", "percent": "Pourcentage", "phone": "Téléphone", "phone_number": "Numéro de téléphone", "pin_on_pinterest": "<PERSON><PERSON><PERSON> sur Pinterest", "pinterest": "Pinterest", "plant": "Plante", "plants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plus": "Plus", "policies_menu": "Menu des politiques", "popup": "<PERSON><PERSON><PERSON> popup", "popups": "Groupe contextuel", "portrait": "Portrait", "portrait_23": "Portrait (2:3)", "position": "Position", "post_limit": "Publications", "predictive_search": "Recherche prédictive", "price": "Prix", "primary": "Primaire", "product": "Produit", "product_card": "Fiche produit", "product_grid": "Grille de produit", "product_hotspot": "Points chauds des produits", "product_reviews": "Avis produit", "product_specs": "Spécifications du produit", "product_tags": "Étiquettes de produits", "product_tile_layout": "Disposition des vignettes de produits", "product_tile_style": "Style des tuiles de produits", "product_tiles": "<PERSON><PERSON> de produit", "products": "Produits", "promotion_grid": "Grille promotionnelle", "promotional_grid": "Grille promotionnelle", "quantity_selector": "Sélecteur de quantité", "question": "Question", "quote": "Citations", "recently_viewed": "Produits récemment consultés", "recommended": "Recommandé", "recycle": "Recyclage", "regular": "<PERSON><PERSON><PERSON>", "related_products": "Produits connexes", "reminder_label": "Étiquette de rappel", "ribbon": "Ruban", "rich_text": "Texte enrichi", "round": "<PERSON><PERSON>", "rounded": "Arrondi", "rounded_wave": "<PERSON><PERSON> arrondie", "sale_collection": "Collection de vente", "sale_collection_342": "Collection de vente", "sale_tag_text": "Texte de la balise de vente", "sale_tags": "<PERSON><PERSON> de vente", "sales_point": "Point de vente", "sales_tag": "Étiquette de vente", "sales_tag_color_setting": "Paramètre de couleur des 'étiquettes de vente'", "sand": "Sable", "save_price": "Sauvegarde du prix", "savings_display_style": "Style d'affichage du montant épargné", "scrolling_text": "<PERSON><PERSON>", "search": "Recherche", "search_field": "Champ de recherche", "search_page": "Page de recherche", "secondary": "Secondaire", "section_height": "Hauteur de la section", "section_layout": "Mise en page de la section", "selected": "Sélectionné", "separator": "Séparateur", "serif": "<PERSON><PERSON>", "share_buttons": "Boutons de partage", "share_links": "Liens de partage", "sharing_options": "Options de partage", "sharp": "<PERSON><PERSON><PERSON>", "shield": "Bouclier", "simple": "Simple", "size": "<PERSON><PERSON>", "size_chart": "Guide des tailles", "size_chart_page": "Dimenssionnez la page des graphiques", "sizes": {"extra_large": "Extra-grande", "large": "Grand", "large_grid": "Grande grille", "medium": "<PERSON><PERSON><PERSON>", "small": "<PERSON>", "small_grid": "Petite grille"}, "slide": "Diapositive", "slide_link": "<PERSON><PERSON> de la diapositive", "slide_link_2": "Lien de la diapositive 2", "slide_link_text": "Texte du lien de la diapositive", "slide_link_text_339": "Texte du lien de la diapositive 2", "slide_navigation_style": "Style de navigation des diapositives", "slider_style": "Style de curseur", "slightly_round": "Légèrement arrondi", "small_grid": "Petite grille", "snapchat": "Snapchat", "social": "Réseaux sociaux", "social_media": "<PERSON><PERSON><PERSON><PERSON>", "solid": "Solide", "sortings": {"alphabetically_az": "Alphabétique, de A à Z", "alphabetically_za": "Alphabétique, de Z à A", "date_new_to": "Date, de la plus récente à la plus ancienne", "date_old_to": "Date, de la plus ancienne à la plus récente", "product_count_high": "Nombre de produits, par ordre décroissant", "product_count_low": "Nombre de produits, par ordre croissant"}, "space": "Espace", "spacing": "Espacement", "speed": "Vitesse", "splat_1": "Éclaboussure 1", "splat_2": "Éclaboussure 2", "splat_3": "Éclaboussure 3", "splat_4": "Éclaboussure 4", "split_optional_slides": "Diviser (diapositives facultatives)", "square": "Square", "square_11": "Carré (1:1)", "squiggle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stacked_media": "Médias empi<PERSON>", "star": "<PERSON><PERSON><PERSON>", "stars": {"1_star": "1 étoile", "2_stars": "2 étoiles", "3_stars": "3 étoiles", "4_stars": "4 étoiles", "5_stars": "5 étoiles"}, "sticky_reminder": "Rappel autocollant", "stone": "Calcul", "stopwatch": "Chronomètre", "store": "Ma<PERSON><PERSON>", "store_availability": "Disponibilité en magasin", "style": "Style", "subcollections": "Sous-collections", "subheading": "Sous-titre", "supports_liquid": "Prend en charge Liquid", "swatch_size": "Taille de l'échantillon", "swatch_style": "Style swatch", "swirl": "Tourbillon", "tab": "Onglet", "tab_content": "Contenu de l'onglet", "tab_content_from": "<PERSON><PERSON><PERSON><PERSON> le contenu de la page", "tag": "Étiquette", "tags": "Étiquettes", "terms_and_conditions": "Page des conditions générales", "testimonial": "Témoignage", "testimonials": "Témoignages", "text": "Texte", "text_alignment": "Alignement du texte", "text_block": "Bloc de texte", "text_columns_with": "Colonnes de texte avec images", "text_columns_with_386": "Texte avec icônes", "text_direction": "Direction du texte", "text_on_left": "Texte à gauche", "text_on_right": "Texte à droite", "text_position": "Position du texte", "text_protection": "Protection du texte", "text_size": "<PERSON>lle du texte", "texture": "Texture", "thick_grid": "Grille épa<PERSON>", "thin_grid": "Grille fine", "thumbnail_height": "<PERSON><PERSON> de la vignette", "thumbnail_position": "Position de la vignette", "thumbnails_below_media": "Vignettes sous le média", "thumbnails_beside_media": "Vignettes à côté des médias", "thumbs_up": "<PERSON><PERSON> vers le haut", "tiktok": "TikTok", "timer": "<PERSON><PERSON><PERSON>", "timer_complete_message": "Message de fin de minuterie", "times": {"01_am": "01:00", "02_am": "02:00", "03_am": "03:00", "04_am": "04:00", "05_am": "05:00", "06_am": "06:00", "07_am": "07:00", "08_am": "08:00", "09_am": "09:00", "10_am": "10:00", "10_pm": "22:00", "11_am": "11:00", "11_pm": "23:00", "12_am": "00:00", "12_pm": "12:00", "1_pm": "13:00", "2_pm": "14:00", "3_pm": "15:00", "4_pm": "16:00", "5_pm": "17:00", "6_pm": "18:00", "7_pm": "19:00", "8_pm": "20:00", "9_pm": "21:00"}, "title": "Titre", "top_text": "Texte du haut", "trophy": "Trophée", "truck": "Camion", "trust_badge": "Badge de confiance", "tumblr": "Tumblr", "twitter": "X", "two_blocks_per_row_mobile": "Deux blocs par rangée sur mobile", "type": "Type", "typography": "Typographie", "value": "<PERSON><PERSON>", "vertical": "Verticale", "vertical_alignment": "Alignement vertical", "vertical_position": "Position verticale", "video": "Vidéo", "video_hero": "<PERSON> vidéo", "video_link": "<PERSON>n vid<PERSON>o", "video_style": "Style de la vidéo", "video_url": "URL de la vidéo", "video_with_sound": "Vid<PERSON>o avec du son", "video_without_sound": "<PERSON><PERSON><PERSON><PERSON> sans son", "vimeo": "Vimeo", "wallet": "Portefeuille", "warm_blur": "<PERSON><PERSON>aud", "wave": "Vague", "weight": "Poids", "white": "<PERSON>", "white_logo": "Logo blanc", "white_round": "<PERSON><PERSON> blanc", "white_square": "<PERSON><PERSON> blanc", "wide_16_9": "Large (16:9)", "width": "<PERSON><PERSON>", "wildflower": "Fleurs sauvages", "year": "An", "youtube": "YouTube", "youtube_vimeo": "YouTube/Vimeo"}}