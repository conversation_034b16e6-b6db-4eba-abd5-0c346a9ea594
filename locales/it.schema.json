{"actions": {"add_alt_text": "Aggiungi un testo alt per un migliore SEO con il pulsante \"Modifica\" sopra", "add_bottom_spacing": "Aggiungi spaziatura in basso", "add_my_account_link": "Aggiungi il link \"Il mio account\" se gli account cliente sono abilitati", "add_phone_number": "Aggiungi il campo del numero di telefono", "add_reviews_by": "Aggiungi le recensioni abilitando l'impostazione qui sotto e installando [l'applicazione Shopify Product Reviews](https://apps.shopify.com/product-reviews) e seguendo la nostra [guida alla configurazione](https://archetypethemes.co/blogs/support/installing-shopifys-product-reviews-app)", "add_spacing": "Aggiungi spaziatura", "add_spacing_above": "Aggiungi spaziatura sopra e sotto", "add_top_spacing": "Aggiungi spaziatura superiore", "approve_button_text": "Approva il testo del pulsante", "autoplay_video": "Riproduzione automatica del video", "blur_the_image": "Sfoca l'immagine", "capitalize_first_letter": "Maiuscola la prima lettera", "choose_a_menu": "Scegli un menu", "collapse_filters": "Comprimere i filtri", "decline_button_text": "Rifiuta il testo del pulsante", "disable_accordion": "Disabilita la fisarmonica", "disable_for_accounts": "Disabilita per i proprietari di un account", "do_not_show_on_home": "Non mostrare in home page", "enable_additional_checkout": "Attiva i pulsanti aggiuntivi per il checkout", "enable_color_swatches": "Abilita i campioni di colore", "enable_dropdown_on_hover": "Abilita menu a discesa al passaggio del mouse", "enable_dynamic_product": "Abilita opzioni di prodotto dinamiche", "enable_filter": "Abilita filtro", "enable_full_width": "Abilita larghezza intera", "enable_header": "Attiva intestazione", "enable_image_zoom": "Abilita zoom immagine", "enable_newsletter": "Attiva il bollettino", "enable_order_notes": "Abilita note sull'ordine", "enable_parallax": "<PERSON><PERSON><PERSON> parallas<PERSON>", "enable_pickup_availability": "Abilita disponibilità per il ritiro", "enable_predictive_search": "Abilita la ricerca predittiva", "enable_product_reviews": "Abilita le recensioni dei prodotti", "enable_quick_add": "Attiva aggiunta rapida", "enable_quick_view": "Abilita visualizzazione rapida", "enable_sticky_header": "Abilita header fisso", "enable_swipe_on": "Abilita scorrimento su dispositivo mobile", "enable_terms": "Abilita la casella di controllo termini e condizioni", "enable_test_mode": "Abilita la modalità di prova", "enable_video_looping": "Abilita la riproduzione in loop dei video", "enlarge_text": "Ingrandisci il testo", "force_image_size": "Forza la dimensione dell'immagine", "hide_all_image_blocks_mobile": "Nascondi tutti i blocchi immagine sul cellulare", "hide_controls": "Nascondi i controlli", "init_display_opened": "Inizialmente visualizzato come aperto", "learn_to_setup_color_swatches": "<PERSON><PERSON> che il tipo sia impostato su 'Pulsanti'. [<PERSON><PERSON><PERSON> come impostare i campioni] (https://help.archetypethemes.co/en/articles/2765)", "learn_to_setup_local_pickup": "<PERSON><PERSON><PERSON> come configurare questa funzionalità [qui](https://help.shopify.com/it/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup)", "learn_to_setup_product_specs": "<PERSON><PERSON><PERSON> come configurare questa funzione [qui](https://help.archetypethemes.co/en/articles/841280)", "learn_to_setup_product_tags": "<PERSON><PERSON><PERSON> come impostare questa funzionalità [qui](https://help.archetypethemes.co/en/articles/832384)", "loop_video": "Video in loop", "mute_video": "Video muto", "overlay_header_over_collection": "Intestazione sovrapposta alla collezione", "overlay_header_over_home": "Intestazione sovrapposta alla home page", "register_google_maps_api": "Avrai bisogno di [registrare una chiave API di Google Maps](https://help.shopify.com/manual/using-themes/troubleshooting/map-section-api-key) per visualizzare la mappa", "register_mapbox_api": "Dovrai [registrare un token di accesso all'API Mapbox](https://help.archetypethemes.co/en/articles/1501376) per visualizzare la mappa", "repeat_main_menu": "Ripeti il menu principale su mobile", "return_button_text": "Testo del pulsante di ritorno", "select_collections_to_show": "Seleziona le collezioni da mostrare", "share_on_facebook": "Condividi su <PERSON>", "share_on_social": "Condividi sui social", "show_arrow": "<PERSON>ra freccia", "show_as_tab": "Mostra come scheda", "show_author": "Mostra autore", "show_bottom_padding": "Mostra imbottitura inferiore", "show_breadcrumbs": "Mostra le briciole di pane", "show_cents_as": "Mostra i centesimi come apice", "show_collection_image": "Mostra immagine collezione", "show_collections_in_breadcrumbs": "Mostra la pagina delle collezioni nella lista delle briciole di pane", "show_comment_count": "Mostra il numero di commenti", "show_copyright": "Mostra il copyright", "show_currency_flags": "Mostra le bandiere della valuta", "show_currency_selector": "Mostra il selettore di valuta", "show_date": "Mostra data", "show_dynamic_checkout": "Mostra pulsante check-out dinamico", "show_excerpt": "<PERSON><PERSON> est<PERSON>to", "show_first_product": "Mostra il primo prodotto nei mega menù", "show_footer_content": "Mostra il contenuto a piè di pagina nel menu mobile", "show_get_directions": "Mostra il pulsante \"Ottieni indicazioni\"", "show_image": "<PERSON>ra immagine", "show_increment_dividers": "Mostra divisori di incremento", "show_inventory_transfer": "Mostra nota di trasferimento scorte", "show_language_selector": "Mostra il selettore della lingua", "show_newsletter_signup": "Mostra iscrizione alla newsletter", "show_payment_icons": "Mostra le icone di pagamento", "show_prefix": "Mostra il prefisso del prodotto", "show_price": "<PERSON>ra prezzo", "show_product_tags": "Mostra i tag dei prodotti", "show_recipient_information": "Mostra il modulo informazioni del destinatario per i buoni regalo", "show_rss_link": "Mostra link RSS", "show_saved_amount": "Mostra l'importo risparmiato", "show_section_divider": "Mostra il divisore di sezione", "show_sku": "Mostra SKU", "show_social_accounts": "Mostra gli account sociali", "show_social_icons": "Mostra le icone sociali", "show_sort_options": "Mostra le opzioni di ordinamento", "show_swatch_labels": "Mostra le etichette dei campioni", "show_tags": "Mostra i tag", "show_thumbnail_arrows": "Mostra frecce anteprima", "show_title": "Mostra titolo", "show_top_padding": "Mostra imbottitura superiore", "show_variant_labels": "Mostra etichette varianti", "show_vendor": "<PERSON>ra fornitore", "show_view_all_link": "Mostra il link \"Visualizza tutti\".", "show_wave_transition": "mostra la transizione dell'onda", "sort_collections_by": "Ordina le collezioni per:", "tweet_on_twitter": "<PERSON><PERSON>tta su X", "view_setup_instructions": "[Visualizza le istruzioni di configurazione](https://archetypethemes.co/blogs/expanse/how-do-i-set-up-color-swatches)", "zoom_image_to_fill": "Zoom dell'immagine per riempire lo spazio"}, "info": {"2000x800px_recommended": "Consigliato 2000 x 800px", "Shopify_youtube": "https://www.youtube.com/user/shopify", "aligns_next_to_custom_content": "Allinea quando si trova vicino ad altri contenuti personalizzati", "all_submissions_sent_to_store_email": "<PERSON>tti gli invii sono inviati all'indirizzo email del cliente del tuo negozio. [Per saperne di più](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).", "allow_your_customers_to_filter": "Permetti ai tuoi clienti di filtrare le collezioni e i risultati della ricerca in base alla disponibilità del prodotto, al prezzo, al colore e altro. [Personalizza filtri](/admin/menus)", "appears_when_newsletter_popup_closed": "Appare quando viene chiuso il popup della newsletter.", "applies_a_maximum_width": "Applica una larghezza massima", "applys_when_thumbnail_next_to_media": "Si applica solo quando la posizione dell’Anteprima è impostata su “Accanto ai multimediali”.", "buttons_appear_either_cart_or_checkout": "I pulsanti possono apparire sia nella pagina del carrello che in quella di checkout, ma non in entrambe.", "choose_which_platforms_share_theme_settings": "Scegli su quali piattaforme condividere nelle impostazioni globali del tema", "collections_listed_by_default": "Tutte le tue collezioni sono elencate di default. Per personalizzare la tua lista, scegli \"Selezionati\" e aggiungi le collezioni.", "content_for_age_verification_failure": "Questo contenuto verrà visualizzato se l'utente non soddisfa i requisiti di verifica.", "customers_who_subscribe_accept_marketing": "I clienti che si iscrivono avranno il loro indirizzo email aggiunto alla 'accepts marketing' [lista clienti](/admin/customers?query=&accepts_marketing=1).", "darkens_your_image": "<PERSON><PERSON><PERSON>ce la tua immagine per assicurare che il tuo testo sia leggibile", "darkens_your_video": "Scurisce il tuo video per assicurare che il tuo testo sia leggibile", "defaults_to_collection_title": "Predefinito il titolo della collezione", "defaults_to_menu_title": "Predefinito al titolo del menu", "does_not_appear_on_mobile": "Non appare su mobile per soddisfare le [linee guida interstiziali] di Google (https://webmasters.googleblog.com/2016/08/helping-users-easily-access-content-on.html) per migliorare il SEO", "dynamic_recommendations_use": "Le raccomandazioni dinamiche utilizzano le informazioni sugli ordini e sui prodotti per cambiare e migliorare nel tempo. [Per saperne di più](https://help.shopify.com/en/themes/development/recommended-products)", "enable_shop_pay_for_shop_app_follow": "Shop Pay deve essere abilitato per consentire ai clienti di seguire il tuo negozio sull'app Shop dalla tua vetrina virtuale. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "for_product_with_long_descriptions": "Per le linee di prodotto con lunghe descrizioni, ti consigliamo di posizionare la tua descrizione e le schede all'interno di questa sezione.", "forces_the_age_verification": "Forza la verifica a ogni aggiornamento e dovrebbe essere utilizzata solo per modificare il pop-up. Assicurati che la 'Modalità test' sia disabilitata all'avvio del tuo negozio.", "gift_card_products": "I buoni regalo possono essere inviati direttamente al destinatario con un messaggio personale. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)", "google_maps_will_find_location": "Google Maps troverà la posizione esatta", "if_collection_image_enabled": "(se l'immagine della collezione è abilitata)", "image_displays_if_video_fails": "L'immagine verrà visualizzata se il video non viene caricato", "image_source_adjusted_theme_settings": "La fonte dell'immagine può essere regolata in Impostazioni del tema > Piastrelle della collezione", "lazy_loading_enabled_below_fold": "Il caricamento lento dovrebbe essere abilitato quando le immagini della sezione sono below the fold. [Per saperne di più](https://archetypethemes.co/blogs/support/what-is-lazyloading)", "learn_inventory_transfers": "Sc<PERSON><PERSON> come creare trasferimenti di scorte [qui](https://help.shopify.com/it/manual/products/inventory/transfers/create-transfer)", "learn_mega_menu": "[Per saperne di più](https://archetypethemes.co/blogs/expanse/how-do-i-create-a-mega-menu)", "learn_more_media_types": "Maggiori informazioni sui [tipi di media](https://help.shopify.com/it/manual/products/product-media)", "learn_to_setup_product_prefix": "<PERSON><PERSON><PERSON> come configurare questa funzionalità [qui](https://help.archetypethemes.co/en/articles/851328)", "lets_customers_checkout_familiar": "Consenti ai clienti di effettuare il check-out direttamente tramite un metodo di pagamento familiare. [Maggiori informazioni](https://help.shopify.com/it/manual/online-store/dynamic-checkout)", "links_to_collections_appear_here": "I collegamenti alle collezioni dal tuo menu appariranno qui. [Per saperne di più](https://archetypethemes.co/blogs/expanse/how-do-i-create-subcollections)", "links_to_youtube_video_player": "I link ai video di YouTube saranno aperti in un lettore video", "mapbox_will_find_location": "Mapbox troverà la posizione esatta", "menu_shows_top_level": "<PERSON>o menu mostrerà solo i link di primo livello", "model_media_type_required": "Il prodotto deve avere un tipo di supporto del modello 3D", "no_effect_when_size_natural": "<PERSON><PERSON><PERSON> effetto quando la dimensione dell'immagine della griglia è impostata su \"Naturale\".", "not_shown_to_customers_with_accounts": "Non verrà mostrata ai clienti che hanno creato un account nel tuo negozio.", "number_of_days_popup_reappears": "Numero di giorni prima che un popup eliminato riappaia", "only_appear_on_articles": "Apparirà solo sugli articoli", "only_applies_on_desktop": "Si applica solo al riquadro di visualizzazione desktop", "overrides_video_url_if_both_set": "Sostituisce l'impostazione dell'URL del video se entrambe sono impostate", "recently_viewed_products_only_visible": "I prodotti visti di recente sono visibili solo quando si naviga fuori dall'editor", "recommended_a_square_image": "Consigliato un rapporto d'aspetto quadrato per un'esperienza mobile ottimale", "requires_square_images": "<PERSON><PERSON> immagini quadrate", "scaled_to_32x32": "Verrà ridimensionata a 32 x 32 pixel", "section_appears_when_product_vendor": "Questa sezione apparirà solo quando sei su un prodotto che ha un venditore impostato", "section_on_product_page_w_collection": "Questa sezione apparirà solo quando sei sulla pagina di un prodotto che è stato raggiunto attraverso una collezione", "set_as_max_width": "Impostato come larghezza massima, può apparire più piccolo", "shopify_facebook": "https://www.facebook.com/shopify", "shopify_instagram": "https://instagram.com/shopify", "shopify_linkedin": "https://www.linkedin.com/in/shopify", "shopify_pinterest": "https://www.pinterest.com/shopify", "shopify_snapchat": "https://www.snapchat.com/add/shopify", "shopify_tiktok": "https://www.tiktok.com/@shopify", "shopify_tumblr": "http://shopify.tumblr.com", "shopify_twitter": "https://twitter.com/shopify", "shopify_vimeo": "https://vimeo.com/shopify", "sidebar_is_first_two_sections": "Assicurati che la tua barra laterale sia una delle prime due sezioni", "sign_up_creates_customer": "Ogni registrazione creerà un Cliente nel tuo negozio. [Visualizza clienti](/admin/customers?query=&accepts_marketing=1).", "sorting_applies_when_all_selected": "L'ordinamento si applica solo quando è selezionato \"Tutti\".", "styles_online_apply_to_ital_text": "Gli stili si applicano solo al testo in corsivo all'interno dell'intestazione", "supported_video_formats": "Supporta YouTube, .MP4 e Vimeo. Non tutte le caratteristiche sono supportate da Vimeo. [Per saperne di più](https://help.archetypethemes.co/en/articles/2740)", "text_below_on_mobile": "Il testo è sempre sotto l'immagine su mobile", "to_add_currency_settings": "Per aggiungere una valuta, vai alle tue [impostazioni di valuta](/admin/settings/payments)", "to_add_language_settings": "Per aggiungere una lingua, vai alle [impostazioni lingua.](/admin/settings/languages)", "to_select_complementary_add_search_discovery": "Aggiungi l'app Search & Discovery per selezionare i prodotti complementari. [Maggiori informazioni](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations#complementary-products)", "use_instead_of_google_maps_api": "Da utilizzare al posto di una chiave API", "use_instead_of_mapbox_api": "Utilizzare al posto di un token di accesso API", "used_when_on_top_of_image": "Usato quando si trova sopra un'immagine", "values_below_10_not_recommended": "I valori inferiori a 10 secondi non sono raccomandati. <PERSON><PERSON> disabilitato nell'editor.", "video_with_sound_not_autoplay": "I video sonori non verranno riprodotti automaticamente."}, "labels": {"3D_model": "Modello 3D", "404_page": "Pagina 404", "75_width": "Larghezza 75%", "accounts": "Account", "additional_copyright_text": "Testo aggiuntivo di copyright", "additional_footer_content": "Contenuto aggiuntivo a piè di pagina", "address_and_hours": "Indirizzo e orari", "advanced_accordion": "Fisarmonica avanzata", "age_verification_popup": "Popup di verifica dell'età", "age_verification_question": "Domanda di verifica dell'età", "alignment": "Allineamento", "alignments": {"bottom": "In basso", "bottom_center": "In basso al centro", "bottom_left": "In basso a sinistra", "bottom_right": "In basso a destra", "center": "Al centro", "center_left": "Centro-sinistra", "center_right": "Centro-destra", "center_text": "Centrare il testo", "centered": "Centrato", "left": "A sinistra", "left_to_right": "Da sinistra a destra", "middle": "Al centro", "right": "A destra", "right_to_left": "Da destra a sinistra", "top": "In alto", "top_center": "In alto al centro", "top_left": "In alto a sinistra", "top_right": "In alto a destra"}, "all": "<PERSON><PERSON>", "amount": "Importo", "announcement": "<PERSON><PERSON><PERSON>", "announcement_bar": "Barra degli annunci", "announcement_text": "Testo dell'annuncio", "apps": "App", "arch": "Arco", "arrows": "<PERSON><PERSON><PERSON>", "article": "Articolo", "article_pages": "Pagine articolo", "author": "Autore", "author_info": "Informazioni sull'autore", "authors_image": "Immagine dell'autore", "autochange_slides": "Cambio automatico delle diapositive", "back_to_collection": "Torna alla collezione", "background": "Sfondo", "background_image": "Immagine di sfondo", "background_video_link": "<PERSON> al video in background", "bag": "Borsa", "banner": "Banner", "bars": "<PERSON><PERSON>", "base_size": "Dimensione della base", "below_media": "Sotto il contenuto", "bills": "<PERSON><PERSON><PERSON>", "blocks_per_row": "<PERSON><PERSON> per riga", "blog": "Blog", "blog_pages": "Pagine del blog", "blog_posts": "Articoli del blog", "blog_sidebar": "Barra laterale del blog", "body": "<PERSON><PERSON>", "body_text": "Corpo del testo", "borders": "<PERSON><PERSON>", "bottom_text": "<PERSON>o in basso", "button": "Pulsante", "button_1_link": "Link del pulsante 1", "button_1_text": "Testo del pulsante 1", "button_2_link": "Link del pulsante 2", "button_2_text": "Testo del pulsante 2", "button_label": "Testo del pulsante", "button_link": "<PERSON> pulsante", "button_style": "Stile del pulsante", "button_text": "Testo del pulsante", "button_text_2": "Testo del pulsante 2", "buttons": "<PERSON><PERSON><PERSON><PERSON>", "buy_buttons": "Buy button", "calendar": "Calendario", "capitalize": "Capitalizzare", "capitalize_navigation": "Capitalizza la navigazione", "cart": "<PERSON><PERSON>", "cart_dot": "Punto del carrello", "cart_dot_text": "<PERSON>o punto carrello", "cart_icon": "Icona del carrello", "cart_page": "Pagina del carrello", "cart_recommendations": "Raccomandazioni per il carrello", "cart_type": "Tipo di carrello", "change_images_every": "Cambia immagini ogni", "charity": "Carità", "chat": "Cha<PERSON>", "chat_link": "Link alla chat", "checkmark": "Segno di <PERSON>ta", "circle": "Cerchio", "circle_11": "Cerchio (1:1)", "circular_images": "<PERSON><PERSON><PERSON>i circolari", "classic": "Classica", "cold_blur": "Sfocatura fredda", "collection": "Collezione", "collection_carousel": "Carosello di raccolta", "collection_description": "Descrizione della collezione", "collection_header": "Intestazione della collezione", "collection_image": "Immagine della collezione", "collection_list": "Elenco delle collezioni", "collection_tile_background": "Fondo delle mattonelle della raccolta", "collection_tile_style": "Stile collezione", "collection_tiles": "Piastrelle della collezione", "collections_list_page": "Pagina con l'elenco delle collezioni", "color_scheme": "Schema colori", "color_schemes": {"1": "Schema di colori 1", "2": "Schema di colori 2", "3": "Schema di colore 3"}, "color_swatches": "Campioni di colore", "colors": "Colori", "column": "<PERSON>onna", "contact": "<PERSON><PERSON><PERSON>", "contact_and_social": "Contatto e social network", "contact_form": "Modulo di contatto", "contact_page": "Pagina di contatto", "content": "<PERSON><PERSON><PERSON>", "content_alignment": "Allineamento testo", "content_alignment_desktop": "Allineamento del contenuto sul desktop", "content_position": "Posizione del contenuto", "countdown": "Conto alla rovescia", "custom_1": "Personalizzato 1", "custom_2": "Personalizzato 2", "custom_3": "Personalizzato 3", "custom_content": "<PERSON><PERSON><PERSON>", "darken": "<PERSON><PERSON><PERSON><PERSON>", "day": "<PERSON><PERSON><PERSON>", "declined": "Declined", "default": "Predefinito", "default_product_layout": "Layout predefinito del prodotto", "delay": "<PERSON><PERSON>", "description": "Descrizione", "design": "Design", "desktop_columns_per": "Colonne per riga desktop", "desktop_height": "Altezza del desktop", "desktop_height_adjustment": "Regolazione dell'altezza del desktop", "desktop_logo_height": "Altezza del logo per desktop", "desktop_logo_width": "Larghezza logo desktop", "desktop_products_per": "Prodotti su desktop per riga", "direction": "Direzione", "dot": "Punt<PERSON>", "dots": "<PERSON><PERSON><PERSON>", "dropdown": "Menu a discesa", "edges": "<PERSON><PERSON>", "email_signup": "Iscrizione alla newsletter", "envelope": "Busta", "extras": "Extra", "facebook": "Facebook", "fallback_image": "Immagine di riserva", "faq": "<PERSON><PERSON><PERSON>", "favicon": "Favicon", "favicon_image": "Imma<PERSON>e favicon", "featured_article": "Articolo in primo piano", "featured_collection": "Collezione in evidenza", "featured_product": "Se<PERSON> \"prodotto in primo piano\"", "filtering_and_sorting": "Filtri e ordinamento", "first_product": "Primo prodotto", "fixed": "<PERSON><PERSON>", "flexible": "Fless<PERSON>le", "follow_on_shop": "<PERSON><PERSON><PERSON>", "font": "Font", "font_weights": {"bold": "Grassetto", "extrabold": "Extra grassetto", "extralight": "Extra leggero", "light": "<PERSON><PERSON><PERSON>", "semibold": "Semi-grassetto"}, "footer": "Footer", "footer_group": "Gruppo piè di pagina", "footer_promotions": "Promozioni a piè di pagina", "form": "<PERSON><PERSON><PERSON>", "frequency": "Frequenza", "full_page_width": "<PERSON>rg<PERSON><PERSON> completa della pagina", "full_width": "Larghezza intera", "fullwidth_details": "Det<PERSON><PERSON> a tutta larghezza", "gallery": "Galleria", "gears": "Ingrana<PERSON><PERSON>", "general": "Generale", "gift": "Omaggio", "globe": "Globo", "google_maps_api": "Chiave API di Google Maps", "grey": "<PERSON><PERSON><PERSON>", "grey_round": "Ce<PERSON><PERSON> grigio", "grey_square": "Quadrato grigio", "grid_spacing": "Spaziatura della griglia", "handwriting": "Grafia", "header": "Header", "header_group": "Gruppo di intestazione", "heading": "<PERSON><PERSON>", "heading_ital_text_style": "Intestazione in stile testo in corsivo", "heading_position": "Posizione dell'intestazione", "heading_size": "Dimensione dell'intestazione", "heading_text_size": "Dimensione del testo dell'intestazione", "headings": "<PERSON><PERSON>", "heart": "<PERSON><PERSON><PERSON>", "height": "Altezza", "hero": "Hero", "hero_optional_slides": "Eroe (diapositiva opzionale)", "hide_timer_on": "Nascondi il timer al termine", "horizontal": "Orizzontale", "horizontal_position": "Horizontal position", "hot_spot_icon": "Stile icona punto caldo", "hotspot_color": "Colore icona punto caldo", "hour": "<PERSON>a", "hover_to_reveal": "Passa con il mouse per rivelare la seconda immagine", "html": "HTML", "html_block": "Blocco HTML", "icon": "Icona", "icon_color": "Colore icona", "icons": "Icone", "image": "<PERSON><PERSON><PERSON><PERSON>", "image_2": "Immagine 2", "image_alignment": "Allineamento dell'immagine", "image_background": "S<PERSON>ndo immagine", "image_breathing_room": "Respirazione dell'immagine", "image_comparison": "Confronto di immagini", "image_crop": "<PERSON><PERSON><PERSON> immagine", "image_hotspots": "Punti caldi dell'immagine", "image_link": "Collegamento immagine", "image_on_left": "Immagine a sinistra", "image_on_right": "Immagine a destra", "image_optional_slides": "Immagine (diapositive opzionali)", "image_position": "Posizione immagine", "image_ratio": "Rapporto d'immagine", "image_section_background": "Sfondo sezione immagine", "image_shape": "Forma dell'immagine", "image_size": "<PERSON><PERSON><PERSON><PERSON> immagine", "image_style": "Stile dell'immagine", "image_width": "<PERSON><PERSON><PERSON><PERSON> immagine", "image_with_text": "Immagine con testo", "images_per_row": "Immagini per riga", "indent_image": "Rientro immagine", "inline": "Inline", "instagram": "Instagram", "inventory_status": "Stato dell'inventario", "label": "<PERSON><PERSON><PERSON><PERSON>", "landscape": "Paesaggio", "landscape_43": "<PERSON><PERSON><PERSON><PERSON> (4:3)", "language": "<PERSON><PERSON>", "large_grid": "Griglia grande", "large_image_with": "Immagine grande con casella di testo", "layout": "Layout", "layout_on_desktop": "Disposizione sul desktop", "lazy_load_images": "Caricamento pigro delle immagini", "leaf": "Foglia", "letter_spacing": "Spaziatura lettere", "line_height": "Altezza della linea", "linen": "Biancheria", "lines_and_borders": "Linee e bordi", "link": "Link", "link_2": "Link 2", "link_block": "Blocco di collegamento", "link_label": "Etichetta link", "linkedin": "LinkedIn", "list": "Elenco", "lock": "<PERSON><PERSON><PERSON>", "logo": "Logo", "logo_center_menu": "Logo al centro, menu sotto", "logo_image": "Immagine del logo", "logo_left_menu_below": "Logo a sinistra, menu in basso", "logo_left_menu_center": "Logo a sinistra, menu al centro", "logo_left_menu_left": "Logo a sinistra, menu a sinistra", "logo_list": "Elenco dei loghi", "low_inventory_threshold": "Soglia di inventario bassa", "map": "Mappa", "map_address": "In<PERSON>iz<PERSON> della mappa", "mapbox_api": "Token di accesso API Mapbox", "marble": "Marmo", "max_products_to": "<PERSON> prodotti da mostrare", "maximum_products_to": "<PERSON><PERSON> di prodotti da mostrare", "media": "Contenuti multimediali", "media_crop": "<PERSON><PERSON><PERSON>", "media_gallery_layout": "Layout della galleria multimediale", "media_on_left": "Supporto a sinistra", "media_on_right": "Supporto a destra", "media_width": "Larghezza del supporto", "media_with_text": "<PERSON>i con testo", "middle_text": "Testo centrale", "minimal": "Minimale", "minimal_bag": "Borsa minimale", "minimal_wave": "Onda semplice", "minute": "Min<PERSON>", "mobile_height": "Altezza mobile", "mobile_height_adjustment": "Regolazione altezza mobile", "mobile_image": "Immagine mobile", "mobile_layout": "Layout dispositivo mobile", "mobile_logo_height": "Altezza del logo per mobile", "mobile_logo_width": "Larghezza logo mobile", "mobile_text": "Testo su mobile", "model": "<PERSON><PERSON>", "month": "Mese", "months": {"april": "<PERSON>e", "august": "Agosto", "december": "Dicembre", "february": "<PERSON><PERSON><PERSON>", "january": "Gennaio", "july": "<PERSON><PERSON><PERSON>", "june": "<PERSON><PERSON><PERSON>", "march": "<PERSON><PERSON>", "may": "Maggio", "november": "Novembre", "october": "Ottobre", "september": "Settembre"}, "more_from_collection": "<PERSON><PERSON> dalla collezione", "more_from_vendor": "<PERSON><PERSON> dal venditore", "narrow_column": "<PERSON><PERSON><PERSON> stretta", "natural": "Naturale", "navigation": "Navigazione", "navigation_font": "Carattere di navigazione", "navigation_size": "Dimensione della navigazione", "newsletter": "<PERSON><PERSON><PERSON><PERSON>", "next_to_media": "Accanto al contenuto", "none": "Nessuna", "notebook": "Taccuin<PERSON>", "number_of_bars": "Numero di barre", "number_of_products": "Numero di prodotti per diapositiva", "number_of_related": "Numero di prodotti correlati", "optimize_for_readability": "Ottimizza per la leggibilità", "optional": "Opzionale", "outline": "Contorno", "overlay": "Sovrapposizione", "overlay_header": "Intestazione in sovraimpressione", "overlay_opacity": "Opacità di sovrapposizione", "package": "<PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON><PERSON>", "page_full_width": "<PERSON><PERSON><PERSON> (piena larghezza)", "pagination_type": "Tipo di impaginazione", "paper": "Carta", "paragraph_hotspot": "Paragraph hotspot", "parallax_direction": "Direzione di parallasse", "parallax_image": "<PERSON><PERSON><PERSON><PERSON> para<PERSON>", "payments_and_localization": "Pagamenti e localizzazione", "percent": "Percent<PERSON><PERSON>", "phone": "Numero di telefono", "phone_number": "Numero di telefono", "pin_on_pinterest": "Aggiungi un pin su Pinterest", "pinterest": "Pinterest", "plant": "Pianta", "plants": "Onda semplice", "plus": "<PERSON><PERSON>", "policies_menu": "Menu delle politiche", "popup": "Popup", "popups": "Gruppo pop-up", "portrait": "<PERSON><PERSON><PERSON><PERSON>", "portrait_23": "<PERSON><PERSON><PERSON><PERSON> (2:3)", "position": "Posizione", "post_limit": "Post", "predictive_search": "Ricerca predittiva", "price": "Prezzo", "primary": "Primario", "product": "<PERSON><PERSON><PERSON>", "product_card": "<PERSON><PERSON><PERSON> prodotto", "product_grid": "<PERSON><PERSON><PERSON>", "product_hotspot": "Punto di riferimento del prodotto", "product_reviews": "Recensioni del prodotto", "product_specs": "Specifiche del prodotto", "product_tags": "Tag del prodotto", "product_tile_layout": "Layout dei riquadri del prodotto", "product_tile_style": "Stile delle mattonelle del prodotto", "product_tiles": "Piastrelle di prodotti", "products": "<PERSON><PERSON>tti", "promotion_grid": "Griglia di promozione", "promotional_grid": "Griglia promozionale", "quantity_selector": "Selettore di quantità", "question": "<PERSON><PERSON>", "quote": "Citazione", "recently_viewed": "Visualizzati di recente", "recommended": "Consigliato", "recycle": "Ricicla", "regular": "Normale", "related_products": "<PERSON><PERSON><PERSON> correlati", "reminder_label": "Etic<PERSON><PERSON> promemoria", "ribbon": "Fiocco", "rich_text": "Rich text", "round": "Rotondo", "rounded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rounded_wave": "Onda arro<PERSON>", "sale_collection": "Collezione di vendita", "sale_collection_342": "Collezione di vendita", "sale_tag_text": "Testo del cartellino di vendita", "sale_tags": "Tag di vendita", "sales_point": "Punti vendita", "sales_tag": "Etichetta di vendita", "sales_tag_color_setting": "Impostazione colore 'Etichette di vendita'", "sand": "<PERSON><PERSON><PERSON>", "save_price": "<PERSON><PERSON> prezzo", "savings_display_style": "Stile di visualizzazione del risparmio", "scrolling_text": "<PERSON>o scorrevole", "search": "Cerca", "search_field": "Campo di ricerca", "search_page": "Pagina di ricerca", "secondary": "Secondario", "section_height": "Altezza della sezione", "section_layout": "Disposizione della sezione", "selected": "Selezionato", "separator": "Separatore", "serif": "<PERSON><PERSON>", "share_buttons": "Pulsanti di condivisione", "share_links": "Condividi link", "sharing_options": "Opzioni di condivisione", "sharp": "<PERSON><PERSON><PERSON>", "shield": "<PERSON><PERSON>", "simple": "Semplice", "size": "Dimensione", "size_chart": "Grafico dimensioni", "size_chart_page": "Pagina della guida alle taglie", "sizes": {"extra_large": "Extra grande", "large": "Grande", "large_grid": "Griglia grande", "medium": "Media", "small": "<PERSON><PERSON><PERSON>", "small_grid": "<PERSON><PERSON><PERSON> piccola"}, "slide": "Scorrimento", "slide_link": "Collegamento alla diapositiva", "slide_link_2": "Collegamento alla diapositiva 2", "slide_link_text": "Testo del collegamento alla diapositiva", "slide_link_text_339": "Testo del collegamento alla diapositiva 2", "slide_navigation_style": "Stile di navigazione delle diapositive", "slider_style": "Stile cursore", "slightly_round": "Leggermente arrotondato", "small_grid": "<PERSON><PERSON><PERSON> piccola", "snapchat": "Snapchat", "social": "Social network", "social_media": "Social media", "solid": "Solido", "sortings": {"alphabetically_az": "In ordine alfabetico, A-Z", "alphabetically_za": "In ordine alfabetico, Z-A", "date_new_to": "<PERSON>, da più a meno recente", "date_old_to": "Data, da meno a più recente", "product_count_high": "Conteggio prodotti decrescente", "product_count_low": "Conteggio prodotti crescente"}, "space": "Spazio", "spacing": "Spaziatura", "speed": "Velocità", "splat_1": "Splat 1", "splat_2": "Splat 2", "splat_3": "Splat 3", "splat_4": "Splat 4", "split_optional_slides": "Split (diapositive opzionali)", "square": "Quadrate", "square_11": "Quadrato (1:1)", "squiggle": "Scarabocchi<PERSON>", "stacked_media": "Supporti impilati", "star": "<PERSON>", "stars": {"1_star": "1 stella", "2_stars": "2 stelle", "3_stars": "3 stelle", "4_stars": "4 stelle", "5_stars": "5 stelle"}, "sticky_reminder": "Promemoria post-it", "stone": "<PERSON><PERSON><PERSON>", "stopwatch": "Cronometro", "store": "Negozio", "store_availability": "Disponibilità del negozio", "style": "Stile", "subcollections": "Sottocollezioni", "subheading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supports_liquid": "Supporta Liquid", "swatch_size": "Dimensione dello swatch", "swatch_style": "Stile di swatch", "swirl": "Vortice", "tab": "Scheda", "tab_content": "Contenuto della scheda", "tab_content_from": "Contenuto della scheda dalla pagina", "tag": "<PERSON><PERSON><PERSON><PERSON>", "tags": "Tags", "terms_and_conditions": "Pagina dei termini e delle condizioni", "testimonial": "Testimonianza", "testimonials": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON>", "text_alignment": "Allineamento testo", "text_block": "<PERSON><PERSON> di testo", "text_columns_with": "Colonne di testo con immagini", "text_columns_with_386": "<PERSON>o con icone", "text_direction": "<PERSON><PERSON><PERSON> del testo", "text_on_left": "Testo a sinistra", "text_on_right": "Testo a destra", "text_position": "Posizione del testo", "text_protection": "Protezione del testo", "text_size": "Dimensione del testo", "texture": "Trama", "thick_grid": "<PERSON><PERSON><PERSON> fitta", "thin_grid": "<PERSON><PERSON><PERSON> sottile", "thumbnail_height": "Altezza anteprima", "thumbnail_position": "Posizione anteprima", "thumbnails_below_media": "Miniature sotto il supporto", "thumbnails_beside_media": "Miniature accanto ai media", "thumbs_up": "Pollice in su", "tiktok": "TikTok", "timer": "Timer", "timer_complete_message": "Messaggio del timer completo", "times": {"01_am": "01:00", "02_am": "02:00", "03_am": "03:00", "04_am": "04:00", "05_am": "05:00", "06_am": "06:00", "07_am": "07:00", "08_am": "08:00", "09_am": "09:00", "10_am": "10:00", "10_pm": "22:00", "11_am": "11:00", "11_pm": "23:00", "12_am": "00:00", "12_pm": "12:00", "1_pm": "13:00", "2_pm": "14:00", "3_pm": "15:00", "4_pm": "16:00", "5_pm": "17:00", "6_pm": "18:00", "7_pm": "19:00", "8_pm": "20:00", "9_pm": "21:00"}, "title": "<PERSON><PERSON>", "top_text": "<PERSON>o in alto", "trophy": "Trofeo", "truck": "Camion", "trust_badge": "Trust badge", "tumblr": "Tumblr", "twitter": "X", "two_blocks_per_row_mobile": "Due blocchi per riga sul cellulare", "type": "Tipo", "typography": "Caratteri tipografici", "value": "Valore", "vertical": "Verticale", "vertical_alignment": "Allineamento verticale", "vertical_position": "Vertical position", "video": "Video", "video_hero": "<PERSON><PERSON><PERSON> video", "video_link": "Collegamento video", "video_style": "Stile del video", "video_url": "URL del video", "video_with_sound": "Video sonoro", "video_without_sound": "Video senza suono", "vimeo": "Vimeo", "wallet": "Portafoglio", "warm_blur": "Sfocatura calda", "wave": "On<PERSON>", "weight": "Peso", "white": "Bianco", "white_logo": "Logo bianco", "white_round": "Cerchio bianco", "white_square": "Quadrato bianco", "wide_16_9": "<PERSON><PERSON> (16:9)", "width": "<PERSON><PERSON><PERSON><PERSON>", "wildflower": "<PERSON>ore di campo", "year": "<PERSON><PERSON>", "youtube": "YouTube", "youtube_vimeo": "YouTube/Vimeo"}}