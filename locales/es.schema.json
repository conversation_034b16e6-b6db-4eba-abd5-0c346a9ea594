{"actions": {"add_alt_text": "Añadir texto alternativo para mejorar el SEO con el botón \"Editar\" de arriba", "add_bottom_spacing": "Añadir el espacio inferior", "add_my_account_link": "<PERSON><PERSON><PERSON> el enlace \"Mi cuenta\" si las cuentas de los clientes están habilitadas", "add_phone_number": "Añadir campo de número de teléfono", "add_reviews_by": "Añada reseñas habilitando la configuración de abajo e instalando la [aplicación de reseñas de productos de Shopify](https://apps.shopify.com/product-reviews) y siguiendo nuestra [guía de configuración](https://archetypethemes.co/blogs/support/installing-shopifys-product-reviews-app)", "add_spacing": "<PERSON><PERSON><PERSON>", "add_spacing_above": "<PERSON><PERSON>dir espacio por encima y por debajo", "add_top_spacing": "Añadir el espacio superior", "approve_button_text": "Aprobar el texto del botón", "autoplay_video": "Vídeo de reproducción automática", "blur_the_image": "Desenfocar la imagen", "capitalize_first_letter": "Escriba la primera letra en mayúscula", "choose_a_menu": "Seleccionar un menú", "collapse_filters": "Minimizar los filtros", "decline_button_text": "Texto del botón Rechazar", "disable_accordion": "Deshabilitar acordeón", "disable_for_accounts": "Desactivar para titulares de cuentas", "do_not_show_on_home": "No mostrar en la página de inicio", "enable_additional_checkout": "Activar botones de pago adicionales", "enable_color_swatches": "Activar las muestras de color", "enable_dropdown_on_hover": "Habilitar menú desplegable al pasar el mouse", "enable_dynamic_product": "Habilitar opciones de productos dinámicos", "enable_filter": "Activar el filtro", "enable_full_width": "Habilitar ancho completo", "enable_header": "Habilitar cabecera", "enable_image_zoom": "Habilitar zoom en la imagen", "enable_newsletter": "Activar el boletín de noticias", "enable_order_notes": "Activar las notas de pedido", "enable_parallax": "Habilitar el paralaje", "enable_pickup_availability": "Habilita la función de disponibilidad de recogida", "enable_predictive_search": "Activar la búsqueda predictiva", "enable_product_reviews": "Activar revisiones de productos", "enable_quick_add": "Activar la adición rápida", "enable_quick_view": "Activar la vista rápida", "enable_sticky_header": "Activar encabezado fijo", "enable_swipe_on": "Activar uso de banda magnética en el móvil", "enable_terms": "Activar la casilla de términos y condiciones", "enable_test_mode": "Habilitar modo de prueba", "enable_video_looping": "Activar la reproducción de video en bucle", "enlarge_text": "Ampliar el texto", "force_image_size": "<PERSON><PERSON> el tamaño de la imagen", "hide_all_image_blocks_mobile": "Ocultar todos los bloques de imágenes en el móvil", "hide_controls": "Ocultar controles", "init_display_opened": "Mostrar inicialmente como abierto", "learn_to_setup_color_swatches": "Requiere que el tipo se establezca en 'Botones'. [Más información sobre cómo configurar muestras] (https://help.archetypethemes.co/en/articles/2765)", "learn_to_setup_local_pickup": "Más información sobre cómo establecer esta función [aquí](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup)", "learn_to_setup_product_specs": "Aprenda a configurar esta función [aquí](https://help.archetypethemes.co/en/articles/841280)", "learn_to_setup_product_tags": "Aprenda cómo configurar esta función [aquí](https://help.archetypethemes.co/en/articles/832384)", "loop_video": "De vídeo en bucle", "mute_video": "Silenciar vídeo", "overlay_header_over_collection": "Cabecera superpuesta sobre la colección", "overlay_header_over_home": "Superposición de la cabecera sobre la página de inicio", "register_google_maps_api": "Tendrá que [registrar una clave API de Google Maps](https://help.shopify.com/manual/using-themes/troubleshooting/map-section-api-key) para visualizar el mapa", "register_mapbox_api": "Tendrás que [registrar un token de acceso a la API de Mapbox](https://help.archetypethemes.co/en/articles/1501376) para mostrar el mapa", "repeat_main_menu": "Repetir el menú principal en el móvil", "return_button_text": "Texto del botón de retorno", "select_collections_to_show": "Seleccionar colecciones para mostrar", "share_on_facebook": "Compartir en Facebook", "share_on_social": "Compartir en las redes sociales", "show_arrow": "Mostrar flecha", "show_as_tab": "Mostrar como pestaña", "show_author": "Mostrar autor", "show_bottom_padding": "Mostrar relleno inferior", "show_breadcrumbs": "Mostrar las migas de pan", "show_cents_as": "Mostrar los céntimos como superíndice", "show_collection_image": "Mostrar imagen de la colección", "show_collections_in_breadcrumbs": "Mostrar la página de colecciones en la lista de migas de pan", "show_comment_count": "Mostrar el recuento de comentarios", "show_copyright": "Mostrar derechos de autor", "show_currency_flags": "Mostrar banderas de moneda", "show_currency_selector": "Mostrar el selector de moneda", "show_date": "<PERSON>rar fecha", "show_dynamic_checkout": "Mostrar botón de pago dinámico", "show_excerpt": "Mostrar extracto", "show_first_product": "Mostrar el primer producto en los megamenús", "show_footer_content": "Mostrar el contenido del pie de página en el menú del móvil", "show_get_directions": "Mostrar el botón \"Obtener direcciones\"", "show_image": "Mostrar imagen", "show_increment_dividers": "Mostrar divisores de incremento", "show_inventory_transfer": "Mostrar aviso de transferencia de inventario", "show_language_selector": "Mostrar el selector de idioma", "show_newsletter_signup": "Mostrar la suscripción al boletín de noticias", "show_payment_icons": "Mostrar íconos de pago", "show_prefix": "Mostrar prefijo de producto", "show_price": "<PERSON><PERSON> precio", "show_product_tags": "Mostrar etiquetas de productos", "show_recipient_information": "Mostrar el formulario de información de la persona destinataria para las tarjetas de regalo", "show_rss_link": "<PERSON><PERSON> enlace RSS", "show_saved_amount": "Mostrar la cantidad guardada", "show_section_divider": "Mostrar el divisor de sección", "show_sku": "Mostrar SKU", "show_social_accounts": "Mostrar cuentas sociales", "show_social_icons": "Mostrar iconos sociales", "show_sort_options": "Mostrar opciones de clasificación", "show_swatch_labels": "Mostrar etiquetas de muestra", "show_tags": "Mostrar etiquetas", "show_thumbnail_arrows": "Mostrar las flechas de las miniaturas", "show_title": "<PERSON><PERSON> tí<PERSON>lo", "show_top_padding": "Mostrar relleno superior", "show_variant_labels": "Mostrar etiquetas de variantes", "show_vendor": "<PERSON><PERSON> proveedor", "show_view_all_link": "Mostrar el enlace 'ver todo'", "show_wave_transition": "Mostrar transición de onda", "sort_collections_by": "Ordenar colecciones por:", "tweet_on_twitter": "Tuitear en X", "view_setup_instructions": "[Ver instrucciones de configuración](https://archetypethemes.co/blogs/expanse/how-do-i-set-up-color-swatches)", "zoom_image_to_fill": "Ampliar la imagen para llenar el espacio"}, "info": {"2000x800px_recommended": "2000 x 800px recomendada", "Shopify_youtube": "https://www.youtube.com/user/shopify", "aligns_next_to_custom_content": "Se alinea cuando está junto a otro contenido personalizado", "all_submissions_sent_to_store_email": "Todos los envíos se realizan a la dirección de correo electrónico del cliente de su tienda.  [Saber más](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).", "allow_your_customers_to_filter": "Permitir a sus clientes filtrar las colecciones y los resultados de la búsqueda por disponibilidad de productos, precio, color y mucho más. [Personalizar filtros](/admin/menus)", "appears_when_newsletter_popup_closed": "Aparece cuando se cierra la ventana emergente del boletín informativo.", "applies_a_maximum_width": "Aplica un ancho máximo", "applys_when_thumbnail_next_to_media": "Solo se aplica cuando la posición de la miniatura se establece en 'Junto a multimedia'.", "buttons_appear_either_cart_or_checkout": "Los botones pueden aparecer en la página del carrito o en la página de pago, pero no en ambas.", "choose_which_platforms_share_theme_settings": "Elegir a qué plataformas compartir en los ajustes globales del tema", "collections_listed_by_default": "Todas sus colecciones aparecen en la lista por defecto. Para personalizar su lista, elegir \"seleccionado\" y añadir colecciones.", "content_for_age_verification_failure": "Este contenido se mostrará si el usuario no cumple con los requisitos de verificación.", "customers_who_subscribe_accept_marketing": "Los clientes que se suscriban se les añadirá su dirección de correo electrónico a la [lista de clientes] que aceptan comunicaciones de marketing (/admin/customers?query=&accepts_marketing=1).", "darkens_your_image": "Oscurece su imagen para asegurar que su texto sea legible", "darkens_your_video": "Oscurece el vídeo para que el texto sea legible", "defaults_to_collection_title": "Por defecto, el título de la colección", "defaults_to_menu_title": "Por defecto, el título del menú", "does_not_appear_on_mobile": "Non appare su mobile per soddisfare le [linee guida interstiziali] di Google (https://webmasters.googleblog.com/2016/08/helping-users-easily-access-content-on.html) per migliorare il SEO", "dynamic_recommendations_use": "Las recomendaciones dinámicas utilizan la información de los pedidos y los productos para cambiar y mejorar con el tiempo. [Más información](https://help.shopify.com/en/themes/development/recommended-products)", "enable_shop_pay_for_shop_app_follow": "Para que los clientes puedan seguir tu tienda en la aplicación Shop desde la tienda, Shop Pay debe estar activado. [Más información](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "for_product_with_long_descriptions": "Para las líneas de productos con descripciones largas, le recomendamos que coloque su descripción y sus fichas dentro de esta sección.", "forces_the_age_verification": "Obliga a que la verificación se muestre en cada actualización y solo debe usarse para editar la ventana emergente. Asegúrese de que el 'Modo de prueba' esté deshabilitado al iniciar su tienda.", "gift_card_products": "Opcionalmente, las tarjetas de regalo se pueden enviar directamente a un destinatario junto con un mensaje personal. [Más información](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)", "google_maps_will_find_location": "Google Maps encontrará la ubicación exacta", "if_collection_image_enabled": "(si la imagen de la colección está activada)", "image_displays_if_video_fails": "La imagen se mostrará si el vídeo no se carga", "image_source_adjusted_theme_settings": "La fuente de la imagen puede ajustarse en Configuración del tema > Mosaicos de la colección", "lazy_loading_enabled_below_fold": "La carga diferida debe estar habilitada cuando las imágenes de la sección están debajo del pliegue. [Más información](https://archetypethemes.co/blogs/support/what-is-lazyloading)", "learn_inventory_transfers": "Más información sobre cómo crear transferencias de inventario [aquí](https://help.shopify.com/en/manual/products/inventory/transfers/create-transfer)", "learn_mega_menu": "[Saber más](https://archetypethemes.co/blogs/expanse/how-do-i-create-a-mega-menu)", "learn_more_media_types": "Más información sobre [tipos de medios de comunicación](https://help.shopify.com/en/manual/products/product-media)", "learn_to_setup_product_prefix": "Aprenda a configurar esta función [aquí](https://help.archetypethemes.co/en/articles/851328)", "lets_customers_checkout_familiar": "Permite a los clientes pagar directamente con un método de pago conocido. [Más información](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)", "links_to_collections_appear_here": "Los enlaces a las colecciones de su menú aparecerán aquí. [Más información](https://archetypethemes.co/blogs/expanse/how-do-i-create-subcollections)", "links_to_youtube_video_player": "Los enlaces a los vídeos de YouTube se abrirán en un reproductor de vídeo", "mapbox_will_find_location": "Mapbox encontrará la ubicación exacta", "menu_shows_top_level": "Este menú sólo mostrará los enlaces de nivel superior", "model_media_type_required": "El producto debe tener un tipo de soporte de modelo 3D", "no_effect_when_size_natural": "No tiene efecto cuando el tamaño de la imagen de la cesta se ajusta a \"Natural\".", "not_shown_to_customers_with_accounts": "No se mostrará a los clientes que hayan creado una cuenta en tu tienda.", "number_of_days_popup_reappears": "Número de días antes de que vuelva a aparecer una ventana emergente descartada", "only_appear_on_articles": "Solo aparecerá en los artículos", "only_applies_on_desktop": "Solo se aplica en la ventanilla de escritorio", "overrides_video_url_if_both_set": "Anula la configuración de URL de vídeo si ambas están configuradas", "recently_viewed_products_only_visible": "Los productos vistos recientemente solo son visibles cuando se navega fuera del editor", "recommended_a_square_image": "Se recomienda una proporción cuadrada para una experiencia óptima en el móvil", "requires_square_images": "Necesita imágenes cuadradas", "scaled_to_32x32": "Se reducirá a 32 x 32px", "section_appears_when_product_vendor": "Esta sección solo aparecerá cuando esté en un producto que tenga un proveedor establecido", "section_on_product_page_w_collection": "Esta sección solo aparecerá cuando se encuentre en una página de producto a la que haya llegado a través de una colección", "set_as_max_width": "Establecido como ancho máximo, puede aparecer más pequeño", "shopify_facebook": "https://www.facebook.com/shopify", "shopify_instagram": "https://instagram.com/shopify", "shopify_linkedin": "https://www.linkedin.com/in/shopify", "shopify_pinterest": "https://www.pinterest.com/shopify", "shopify_snapchat": "https://www.snapchat.com/add/shopify", "shopify_tiktok": "https://www.tiktok.com/@shopify", "shopify_tumblr": "http://shopify.tumblr.com", "shopify_twitter": "https://twitter.com/shopify", "shopify_vimeo": "https://vimeo.com/shopify", "sidebar_is_first_two_sections": "Asegúrese de que su barra lateral es una de las dos primeras secciones", "sign_up_creates_customer": "Cada registro creará un cliente en su tienda. [Ver clientes](/admin/customers?query=&accepts_marketing=1).", "sorting_applies_when_all_selected": "La clasificación sólo se aplica cuando se selecciona \"todo\".", "styles_online_apply_to_ital_text": "Los estilos solo se aplican al texto en cursiva dentro del encabezado", "supported_video_formats": "Admite YouTube, .MP4 y Vimeo. No todas las funciones son compatibles con Vimeo. [Más información](https://help.archetypethemes.co/en/articles/2740)", "text_below_on_mobile": "El texto siempre está debajo de la imagen en el móvil", "to_add_currency_settings": "Para añadir una moneda, vaya a su [configuración de moneda](/admin/settings/payments)", "to_add_language_settings": "Para agregar un idioma, ve a tu [configuración de idiomas.](/admin/settings/languages)", "to_select_complementary_add_search_discovery": "Para seleccionar productos complementarios, agrega la aplicación Search & Discovery. [Más información](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations#complementary-products)", "use_instead_of_google_maps_api": "Utilizar en lugar de una clave API", "use_instead_of_mapbox_api": "Usar en lugar de un token de acceso a la API", "used_when_on_top_of_image": "Utilizar cuando está encima de una imagen", "values_below_10_not_recommended": "No se recomiendan valores inferiores a 10 segundos. Retraso desactivado en el editor.", "video_with_sound_not_autoplay": "El vídeo con sonido no se reproducirá de forma automática"}, "labels": {"3D_model": "Modelo 3D", "404_page": "página 404", "75_width": "75 % de ancho", "accounts": "Cuentas", "additional_copyright_text": "Texto adicional sobre derechos de autor", "additional_footer_content": "Contenido adicional a pie de página", "address_and_hours": "Dirección y horario", "advanced_accordion": "Acordeón avanzado", "age_verification_popup": "Ventana emergente de verificación de edad", "age_verification_question": "Pregunta de verificación de edad", "alignment": "Alineación", "alignments": {"bottom": "Abajo", "bottom_center": "Abajo en el centro", "bottom_left": "Abajo a la izquierda", "bottom_right": "Abajo a la derecha", "center": "Centrado", "center_left": "Centro izquierda", "center_right": "Centro derecha", "center_text": "Centrar el texto", "centered": "Centrado", "left": "Iz<PERSON>erda", "left_to_right": "De izquierda a derecha", "middle": "Centrada", "right": "Derecha", "right_to_left": "De derecha a izquierda", "top": "Arriba", "top_center": "Arriba en el centro", "top_left": "Arriba a la izquierda", "top_right": "Arriba a la derecha"}, "all": "Todo", "amount": "Cantidad", "announcement": "<PERSON><PERSON><PERSON>", "announcement_bar": "Barra de anuncios", "announcement_text": "Texto del anuncio", "apps": "Aplicaciones", "arch": "Arco", "arrows": "flechas", "article": "<PERSON><PERSON><PERSON><PERSON>", "article_pages": "Páginas del artículo", "author": "Autor", "author_info": "Información del autor", "authors_image": "Imagen del autor", "autochange_slides": "Diapositivas de cambio automático", "back_to_collection": "Volver a la colección", "background": "Historial", "background_image": "Imagen de fondo", "background_video_link": "Enlace al vídeo de fondo", "bag": "Bolsa", "banner": "Bandera", "bars": "Barras", "base_size": "Tamaño de la base", "below_media": "Medios de comunicación de abajo", "bills": "Cuentas", "blocks_per_row": "Bloques por fila", "blog": "Blog", "blog_pages": "Páginas del blog", "blog_posts": "Artículos de blog", "blog_sidebar": "Barra lateral del blog", "body": "<PERSON><PERSON><PERSON>", "body_text": "Texto del cuerpo", "borders": "Fronteras", "bottom_text": "Texto inferior", "button": "Botón", "button_1_link": "Enlace del botón nº 1", "button_1_text": "Texto del botón nº 1", "button_2_link": "Enlace del botón nº 2", "button_2_text": "Texto del botón nº 2", "button_label": "Texto del botón", "button_link": "<PERSON>lace de botón", "button_style": "<PERSON><PERSON><PERSON> b<PERSON>", "button_text": "Texto del botón", "button_text_2": "Texto del botón 2", "buttons": "Botones", "buy_buttons": "Botones de compras", "calendar": "Calendario", "capitalize": "Escribir en mayúsculas", "capitalize_navigation": "Navegar con mayúsculas", "cart": "<PERSON><PERSON>", "cart_dot": "Punto de la cesta", "cart_dot_text": "Texto del punto del carrito", "cart_icon": "Icono del carrito", "cart_page": "Página de la cesta", "cart_recommendations": "Recomendaciones para los carritos", "cart_type": "<PERSON><PERSON><PERSON> de <PERSON>", "change_images_every": "Cambiar las imágenes cada", "charity": "Caridad", "chat": "Cha<PERSON>a", "chat_link": "Enlace al chat", "checkmark": "Marca de verificación", "circle": "Circulo", "circle_11": "<PERSON><PERSON><PERSON><PERSON> (1:1)", "circular_images": "Imágenes circulares", "classic": "Clásica", "cold_blur": "Desenfoque frío", "collection": "Colección", "collection_carousel": "Carrusel de colección", "collection_description": "Descripción de la colección", "collection_header": "Cabeza de la colección", "collection_image": "Imagen de la colección", "collection_list": "Lista de colecciones", "collection_tile_background": "Fondo de mosaico de colección", "collection_tile_style": "Estilo de mosaico de la colección", "collection_tiles": "Mosaicos de la colección", "collections_list_page": "Página de lista de colecciones", "color_scheme": "Esquema de colores", "color_schemes": {"1": "Combinación de colores 1", "2": "Combinación de colores 2", "3": "Combinación de colores 3"}, "color_swatches": "Muestras de color", "colors": "Colores", "column": "Columna", "contact": "Contacto", "contact_and_social": "Contacto y social", "contact_form": "Formulario de contacto", "contact_page": "Página de contacto", "content": "Contenido", "content_alignment": "Alineación de texto", "content_alignment_desktop": "Alineación de contenido en el escritorio", "content_position": "Posición del contenido", "countdown": "Cuenta regresiva", "custom_1": "Personalizado 1", "custom_2": "Personalizado 2", "custom_3": "Personalizado 3", "custom_content": "Contenido personalizado", "darken": "Oscurecido", "day": "Día", "declined": "Declined", "default": "Predeterminada", "default_product_layout": "Diseño del producto por defecto", "delay": "<PERSON><PERSON><PERSON>", "description": "Descripción", "design": "Diseño", "desktop_columns_per": "Columnas de escritorio por fila", "desktop_height": "Altura del escritorio", "desktop_height_adjustment": "Ajustar la altura de la mesa", "desktop_logo_height": "Altura del logotipo de escritorio", "desktop_logo_width": "Ancho del logotipo de escritorio", "desktop_products_per": "Productos de escritorio por fila", "direction": "Dirección", "dot": "Punt<PERSON>", "dots": "Punt<PERSON>", "dropdown": "Desplegable", "edges": "<PERSON><PERSON>", "email_signup": "Suscriptor de correo electrónico", "envelope": "Sobre", "extras": "Extras", "facebook": "Facebook", "fallback_image": "Imagen alternativa", "faq": "Preguntas frecuentes", "favicon": "Favicon", "favicon_image": "Imagen de favicon", "featured_article": "<PERSON><PERSON><PERSON><PERSON>", "featured_collection": "Colección destacada", "featured_product": "Producto destacado", "filtering_and_sorting": "Filtrado y ordenado", "first_product": "Primer producto", "fixed": "<PERSON><PERSON>", "flexible": "Flexible", "follow_on_shop": "<PERSON><PERSON><PERSON> en <PERSON>", "font": "Fuente", "font_weights": {"bold": "Negrita", "extrabold": "En negrita", "extralight": "Ultraligero", "light": "Ligero", "semibold": "Seminegrita"}, "footer": "Pie de página", "footer_group": "Grupo de pie de página", "footer_promotions": "Promociones a pie de página", "form": "Formulario", "frequency": "Frecuencia", "full_page_width": "<PERSON><PERSON> de <PERSON><PERSON>a completo", "full_width": "<PERSON><PERSON> completo", "fullwidth_details": "Detalles de ancho completo", "gallery": "Galería", "gears": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general": "General", "gift": "Regalo", "globe": "Globo", "google_maps_api": "Clave API de Google Maps", "grey": "<PERSON><PERSON>", "grey_round": "<PERSON><PERSON> medio", "grey_square": "Cuadrado gris", "grid_spacing": "Espaciado de la cuadrícula", "handwriting": "Escritura", "header": "Encabezado", "header_group": "Grupo de encabezado", "heading": "Encabezado", "heading_ital_text_style": "Título estilo de texto en cursiva", "heading_position": "Posición de rumbo", "heading_size": "Tamaño del encabezado", "heading_text_size": "Tamaño del texto de la cabecera", "headings": "<PERSON><PERSON><PERSON><PERSON>", "heart": "Corazón", "height": "Altura", "hero": "Hero", "hero_optional_slides": "Hero (diapositivas opcionales)", "hide_timer_on": "Ocultar el temporizador en completo", "horizontal": "Horizontal", "horizontal_position": "Posición horizontal", "hot_spot_icon": "Estilo de icono de punto caliente", "hotspot_color": "Color del punto de acceso", "hour": "<PERSON><PERSON>", "hover_to_reveal": "Pasar el ratón para ver la segunda imagen", "html": "HTML", "html_block": "Bloque HTML", "icon": "Ícono", "icon_color": "Color del icono", "icons": "Íconos", "image": "Imagen", "image_2": "Imagen 2", "image_alignment": "Alineación de imagen", "image_background": "Fondo de la imagen", "image_breathing_room": "Respiración de la imagen", "image_comparison": "Comparación de imágenes", "image_crop": "Recorte de imagen", "image_hotspots": "Puntos de acceso de imagen", "image_link": "<PERSON><PERSON> de imagen", "image_on_left": "Imagen a la izquierda", "image_on_right": "Imagen de la derecha", "image_optional_slides": "Imagen (diapositivas opcionales)", "image_position": "Posición de la imagen", "image_ratio": "Relación de aspecto", "image_section_background": "Fondo de la sección de imágenes", "image_shape": "Forma de la imagen", "image_size": "<PERSON><PERSON>", "image_style": "Estilo <PERSON>", "image_width": "<PERSON><PERSON>", "image_with_text": "Imagen con texto", "images_per_row": "Imá<PERSON>s por fila", "indent_image": "Imagen de sangría", "inline": "Inline", "instagram": "Instagram", "inventory_status": "Estado del inventario", "label": "Etiqueta", "landscape": "<PERSON><PERSON><PERSON>", "landscape_43": "<PERSON><PERSON><PERSON> (4:3)", "language": "Idioma", "large_grid": "Rejilla grande", "large_image_with": "Imagen grande con caja de texto", "layout": "Diseño", "layout_on_desktop": "Diseño en el escritorio", "lazy_load_images": "Imágenes de carga diferida", "leaf": "Hoja", "letter_spacing": "Espac<PERSON> entre letras", "line_height": "Altura de línea", "linen": "<PERSON><PERSON>", "lines_and_borders": "Líneas y bordes", "link": "Enlace", "link_2": "Enlace 2", "link_block": "<PERSON><PERSON><PERSON>", "link_label": "Vincular etiqueta", "linkedin": "LinkedIn", "list": "Lista", "lock": "Candado", "logo": "Logotipo", "logo_center_menu": "Logotipo en el centro, menú abajo", "logo_image": "Imagen del logo", "logo_left_menu_below": "Logotipo a la izquierda, menú abajo", "logo_left_menu_center": "Logotipo a la izquierda, menú al centro", "logo_left_menu_left": "Logotipo a la izquierda, menú a la izquierda", "logo_list": "Lista de logotipos", "low_inventory_threshold": "Umbral de inventario bajo", "map": "Mapa", "map_address": "Dirección del mapa", "mapbox_api": "Token de acceso a la API de Mapbox", "marble": "Mármol", "max_products_to": "Max productos para mostrar", "maximum_products_to": "Máximo de productos para mostrar", "media": "Multimedia", "media_crop": "Recorte de medios", "media_gallery_layout": "Diseño de la galería de medios", "media_on_left": "Medios a la izquierda", "media_on_right": "Medios a la derecha", "media_width": "<PERSON><PERSON> de medios", "media_with_text": "Medios con texto", "middle_text": "Texto central", "minimal": "<PERSON><PERSON><PERSON>", "minimal_bag": "<PERSON><PERSON><PERSON> m<PERSON>ima", "minimal_wave": "<PERSON><PERSON> m<PERSON>", "minute": "Min<PERSON>", "mobile_height": "Altura móvil", "mobile_height_adjustment": "Ajuste de altura móvil", "mobile_image": "<PERSON>n m<PERSON>", "mobile_layout": "Diseño para móviles", "mobile_logo_height": "Altura del logotipo móvil", "mobile_logo_width": "Anchura del logotipo en el móvil", "mobile_text": "Texto móvil", "model": "<PERSON><PERSON>", "month": "<PERSON><PERSON>", "months": {"april": "Abril", "august": "Agosto", "december": "Diciembre", "february": "<PERSON><PERSON><PERSON>", "january": "<PERSON><PERSON>", "july": "<PERSON>", "june": "<PERSON><PERSON>", "march": "<PERSON><PERSON>", "may": "Mayo", "november": "Octubre", "october": "Octubre", "september": "Septiembre"}, "more_from_collection": "Más de la colección", "more_from_vendor": "<PERSON><PERSON> del vendedor", "narrow_column": "Columna estrecha", "natural": "Natural", "navigation": "Navegación", "navigation_font": "Fuente de navegación", "navigation_size": "Tamaño de la navegación", "newsletter": "Boletín de noticias", "next_to_media": "Junto a multimedia", "none": "<PERSON><PERSON><PERSON>", "notebook": "Cuaderno", "number_of_bars": "Número de <PERSON>", "number_of_products": "Número de productos por diapositiva", "number_of_related": "Número de productos relacionados", "optimize_for_readability": "Optimizar la legibilidad", "optional": "Opcional", "outline": "Contorno", "overlay": "<PERSON><PERSON><PERSON><PERSON>", "overlay_header": "Cabecera superpuesta", "overlay_opacity": "Opacidad superpuesta", "package": "<PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON><PERSON><PERSON>", "page_full_width": "<PERSON><PERSON><PERSON><PERSON> (ancho completo)", "pagination_type": "tipo de paginación", "paper": "Papel", "paragraph_hotspot": "punto de acceso de párrafo", "parallax_direction": "dirección de paralaje", "parallax_image": "Imagen de paralaje", "payments_and_localization": "Pagos y localización", "percent": "Po<PERSON>entaj<PERSON>", "phone": "Teléfono", "phone_number": "Número de teléfono", "pin_on_pinterest": "Guardar en Pinterest", "pinterest": "Pinterest", "plant": "Planta", "plants": "Plantas", "plus": "Más", "policies_menu": "Menú de políticas", "popup": "Ventana emergente", "popups": "Grupo emergente", "portrait": "Retrato", "portrait_23": "Retrato (2:3)", "position": "Posición", "post_limit": "Publicaciones", "predictive_search": "Búsqueda predictiva", "price": "Precio", "primary": "Primario", "product": "Producto", "product_card": "Tarjeta de producto", "product_grid": "Cuadrícula de productos", "product_hotspot": "punto de acceso del producto", "product_reviews": "Reseñas de productos", "product_specs": "Especificaciones del producto", "product_tags": "Etiquetas de productos", "product_tile_layout": "Diseño de mosaico de productos", "product_tile_style": "Estilo de mosaico del producto", "product_tiles": "Mosaicos de productos", "products": "Productos", "promotion_grid": "Red de promoción", "promotional_grid": "Red de promoción", "quantity_selector": "Selector de cantidad", "question": "Pregunta", "quote": "Presupuesto", "recently_viewed": "Vistos recientemente", "recommended": "Recomendado", "recycle": "Reciclar", "regular": "Regular", "related_products": "Productos relacionados", "reminder_label": "Etiqueta recordatoria", "ribbon": "<PERSON><PERSON><PERSON>", "rich_text": "Texto enriquecido", "round": "Vuelta ", "rounded": "Redondeado", "rounded_wave": "<PERSON>da redondeada", "sale_collection": "Colección de venta", "sale_collection_342": "Colección de venta", "sale_tag_text": "Texto de la etiqueta de venta", "sale_tags": "Etiquetas de venta", "sales_point": "Puntos de venta", "sales_tag": "Etiqueta de ventas", "sales_tag_color_setting": "Configuración de color de 'Etiquetas de venta'", "sand": "Arena", "save_price": "Guardar precio", "savings_display_style": "Estilo de visualización del ahorro", "scrolling_text": "Desplazamiento de texto", "search": "Búsqueda", "search_field": "Campo de búsqueda", "search_page": "Página de búsqueda", "secondary": "Secundario", "section_height": "Altura de la sección", "section_layout": "Disposición de la sección", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "separator": "Separador", "serif": "<PERSON><PERSON>", "share_buttons": "Compartir botones", "share_links": "Compartir enlaces", "sharing_options": "Opciones para compartir", "sharp": "<PERSON><PERSON><PERSON><PERSON>", "shield": "Escudo", "simple": "Simple", "size": "<PERSON><PERSON><PERSON>", "size_chart": "Tabla de tamaños", "size_chart_page": "Página de la tabla de medidas", "sizes": {"extra_large": "Extra larga", "large": "Grande", "large_grid": "Rejilla grande", "medium": "Mediana", "small": "Pequeña", "small_grid": "<PERSON><PERSON><PERSON> peque<PERSON>"}, "slide": "Diapositiva", "slide_link": "<PERSON>lace de diapositivas", "slide_link_2": "Enlace de la diapositiva 2", "slide_link_text": "Texto del enlace de la diapositiva", "slide_link_text_339": "Texto de enlace de la diapositiva 2", "slide_navigation_style": "Estilo de navegación con diapositivas", "slider_style": "<PERSON><PERSON><PERSON>", "slightly_round": "Ligeramente redondo", "small_grid": "<PERSON><PERSON><PERSON> peque<PERSON>", "snapchat": "Snapchat", "social": "Social", "social_media": "Redes sociales", "solid": "<PERSON><PERSON><PERSON><PERSON>", "sortings": {"alphabetically_az": "Alfabéticamente, A-Z", "alphabetically_za": "Alfabéticamente, Z-A", "date_new_to": "Fecha: reciente a antigua", "date_old_to": "Fecha: antigua a reciente", "product_count_high": "<PERSON><PERSON><PERSON> de productos, de mayor a menor", "product_count_low": "<PERSON><PERSON><PERSON> de productos, de menor a mayor"}, "space": "Espacio", "spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "speed": "Velocidad", "splat_1": "Salpicar 1", "splat_2": "Salpicar 2", "splat_3": "Salpicar 3", "splat_4": "Salpicar 4", "split_optional_slides": "Dividir (diapositivas opcionales)", "square": "Cuadrado", "square_11": "Cuadrado (1:1)", "squiggle": "Garabato", "stacked_media": "Medios apilados", "star": "Estrella", "stars": {"1_star": "1 estrella", "2_stars": "2 estrellas", "3_stars": "3 estrellas", "4_stars": "4 estrellas", "5_stars": "5 estrellas"}, "sticky_reminder": "Recordatorio adhesivo", "stone": "Piedra", "stopwatch": "Cronómetro", "store": "Tienda", "store_availability": "Disponibilidad de la tienda", "style": "<PERSON><PERSON><PERSON>", "subcollections": "Subcolecciones", "subheading": "Subtítulo", "supports_liquid": "Compatible con Liquid", "swatch_size": "Tamaño de la muestra", "swatch_style": "Estilo de la muestra", "swirl": "Espiral", "tab": "Pestaña", "tab_content": "Contenido de la pestaña", "tab_content_from": "Pestaña de contenido de la página", "tag": "Etiqueta", "tags": "Etiquetas", "terms_and_conditions": "Página de términos y condiciones", "testimonial": "Opinión", "testimonials": "Opiniones", "text": "Texto", "text_alignment": "Alineación de texto", "text_block": "Bloque de texto", "text_columns_with": "Columnas de texto con imágenes", "text_columns_with_386": "Text columns with icons", "text_direction": "Dirección del texto", "text_on_left": "Texto a la izquierda", "text_on_right": "Texto a la derecha", "text_position": "Posición del texto", "text_protection": "Protección del texto", "text_size": "Tamaño del texto", "texture": "Textura", "thick_grid": "<PERSON><PERSON><PERSON> gruesa", "thin_grid": "<PERSON><PERSON><PERSON> fina", "thumbnail_height": "Altura de la miniatura", "thumbnail_position": "Posición de la miniatura", "thumbnails_below_media": "Miniaturas debajo de los medios", "thumbnails_beside_media": "Miniaturas junto a los medios", "thumbs_up": "Pulgar arriba", "tiktok": "TikTok", "timer": "Temporizador", "timer_complete_message": "Mensaje de temporizador completo", "times": {"01_am": "01:00", "02_am": "02:00", "03_am": "03:00", "04_am": "04:00", "05_am": "05:00", "06_am": "06:00", "07_am": "07:00", "08_am": "08:00", "09_am": "09:00", "10_am": "10:00", "10_pm": "22:00", "11_am": "11:00", "11_pm": "23:00", "12_am": "00:00", "12_pm": "12:00", "1_pm": "13:00", "2_pm": "14:00", "3_pm": "15:00", "4_pm": "16:00", "5_pm": "17:00", "6_pm": "18:00", "7_pm": "19:00", "8_pm": "20:00", "9_pm": "21:00"}, "title": "<PERSON><PERSON><PERSON><PERSON>", "top_text": "Texto superior", "trophy": "Trofeo", "truck": "Camión", "trust_badge": "Insignia de confianza", "tumblr": "Tumblr", "twitter": "X", "two_blocks_per_row_mobile": "Dos bloques por fila en el móvil", "type": "Tipo", "typography": "Tipografía", "value": "Valor", "vertical": "Vertical", "vertical_alignment": "Alineación vertical", "vertical_position": "Posición vertical", "video": "Video", "video_hero": "Video hero", "video_link": "Enlace de vídeo", "video_style": "Estilo de vídeo", "video_url": "URL del vídeo", "video_with_sound": "Vídeo con sonido", "video_without_sound": "Vídeo sin sonido", "vimeo": "Vimeo", "wallet": "Bill<PERSON>a", "warm_blur": "Desenfoque cálido", "wave": "On<PERSON>", "weight": "Peso", "white": "<PERSON>", "white_logo": "Logotipo blanco", "white_round": "Redondo blanco", "white_square": "Cuadrado blanco", "wide_16_9": "<PERSON><PERSON> (16:9)", "width": "<PERSON><PERSON>", "wildflower": "<PERSON><PERSON> silvestre", "year": "<PERSON><PERSON>", "youtube": "YouTube", "youtube_vimeo": "YouTube/Vimeo"}}