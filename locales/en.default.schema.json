{"actions": {"add_alt_text": "Add alt text for better SEO with 'Edit' button above", "add_bottom_spacing": "Add bottom spacing", "add_my_account_link": "Add 'My account' link if customer accounts enabled", "add_phone_number": "Add phone number field", "add_reviews_by": "Add reviews by enabling the setting below and installing the [Shopify Product Reviews app](https://apps.shopify.com/product-reviews) and following our [setup guide](https://archetypethemes.co/blogs/support/installing-shopifys-product-reviews-app)", "add_spacing": "Add spacing", "add_spacing_above": "Add spacing above and below", "add_top_spacing": "Add top spacing", "approve_button_text": "Approve button text", "autoplay_video": "Autoplay video", "blur_the_image": "Blur the image", "capitalize_first_letter": "Capitalize first letter", "choose_a_menu": "Choose a menu", "collapse_filters": "Collapse filters", "decline_button_text": "Decline button text", "disable_accordion": "Disable accordion", "disable_for_accounts": "Disable for account holders", "do_not_show_on_home": "Do not show on home page", "enable_additional_checkout": "Enable additional checkout buttons", "enable_color_swatches": "Enable color swatches", "enable_dropdown_on_hover": "Enable dropdown on hover", "enable_dynamic_product": "Enable dynamic product options", "enable_filter": "Enable filter", "enable_full_width": "Enable full width", "enable_header": "Enable header", "enable_image_zoom": "Enable image zoom", "enable_newsletter": "Enable newsletter", "enable_order_notes": "Enable order notes", "enable_parallax": "Enable parallax", "enable_pickup_availability": "Enable pickup availability feature", "enable_predictive_search": "Enable predictive search", "enable_product_reviews": "Enable product reviews", "enable_quick_add": "Enable quick add", "enable_quick_view": "Enable quick view", "enable_sticky_header": "Enable sticky header", "enable_swipe_on": "Enable swipe on mobile", "enable_terms": "Enable terms and conditions checkbox", "enable_test_mode": "Enable test mode", "enable_video_looping": "Enable video looping", "enlarge_text": "Enlarge text", "force_image_size": "Force image size", "hide_all_image_blocks_mobile": "Hide all image blocks on mobile", "hide_controls": "Hide controls", "init_display_opened": "Initially display as opened", "learn_to_setup_color_swatches": "Requires type to be set to 'Buttons'. [Learn how to set up swatches](https://help.archetypethemes.co/en/articles/2765)", "learn_to_setup_local_pickup": "Learn how to setup this feature [here](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup)", "learn_to_setup_product_tags": "Learn how to setup this feature [here](https://help.archetypethemes.co/en/articles/832384)", "learn_to_setup_product_specs": "Learn how to setup this feature [here](https://help.archetypethemes.co/en/articles/841280)", "loop_video": "Loop video", "mute_video": "Mute video", "overlay_header_over_collection": "Overlay header over collection", "overlay_header_over_home": "Overlay header over home page", "register_google_maps_api": "You'll need to [register a Google Maps API Key](https://help.shopify.com/manual/using-themes/troubleshooting/map-section-api-key) to display the map", "register_mapbox_api": "You'll need to [register a Mapbox API access token](https://help.archetypethemes.co/en/articles/1501376) to display the map", "repeat_main_menu": "Repeat main menu on mobile", "return_button_text": "Return button text", "select_collections_to_show": "Select collections to show", "share_on_facebook": "Share on Facebook", "share_on_social": "Share on social", "show_arrow": "Show arrow", "show_as_tab": "Show as tab", "show_author": "Show author", "show_bottom_padding": "Show bottom padding", "show_breadcrumbs": "Show breadcrumbs", "show_cents_as": "Show cents as superscript", "show_collection_image": "Show collection image", "show_collections_in_breadcrumbs": "Show collections page in breadcrumb list", "show_comment_count": "Show comment count", "show_copyright": "Show copyright", "show_currency_flags": "Show currency flags", "show_currency_selector": "Show currency selector", "show_date": "Show date", "show_dynamic_checkout": "Show dynamic checkout button", "show_excerpt": "Show excerpt", "show_first_product": "Show first product in mega menus", "show_footer_content": "Show footer content on mobile menu", "show_get_directions": "Show 'Get directions' button", "show_image": "Show image", "show_inventory_transfer": "Show inventory transfer notice", "show_increment_dividers": "Show increment dividers", "show_language_selector": "Show language selector", "show_newsletter_signup": "Show newsletter signup", "show_payment_icons": "Show payment icons", "show_price": "Show price", "show_prefix": "Show product prefix", "show_product_tags": "Show product tags", "show_recipient_information": "Show recipient information form for gift card products", "show_rss_link": "Show RSS link", "show_saved_amount": "Show saved amount", "show_section_divider": "Show section divider", "show_sku": "Show SKU", "show_social_accounts": "Show social accounts", "show_social_icons": "Show social icons", "show_sort_options": "Show sort options", "show_swatch_labels": "Show swatch labels", "show_tags": "Show tags", "show_thumbnail_arrows": "Show thumbnail arrows", "show_title": "Show title", "show_top_padding": "Show top padding", "show_variant_labels": "Show variant labels", "show_vendor": "Show vendor", "show_view_all_link": "Show 'View all' link", "show_wave_transition": "Show wave transition", "sort_collections_by": "Sort collections by:", "tweet_on_twitter": "Tweet on <PERSON>", "view_setup_instructions": "[View setup instructions](https://archetypethemes.co/blogs/expanse/how-do-i-set-up-color-swatches)", "zoom_image_to_fill": "Zoom image to fill space"}, "info": {"2000x800px_recommended": "2000 x 800px recommended", "Shopify_youtube": "https://www.youtube.com/user/shopify", "aligns_next_to_custom_content": "Aligns when next to other custom content", "all_submissions_sent_to_store_email": "All submissions are sent to the customer email address of your store. [Learn more](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).", "allow_your_customers_to_filter": "Allow your customers to filter collections and search results by product availability, price, color, and more. [Customize filters](/admin/menus)", "appears_when_newsletter_popup_closed": "Appears when newsletter popup is closed.", "applies_a_maximum_width": "Applies a maximum width", "applys_when_thumbnail_next_to_media": "Only applies when Thumbnail position is set to 'Next to media'.", "buttons_appear_either_cart_or_checkout": "The buttons can appear on either your cart page or your checkout page, but not both.", "choose_which_platforms_share_theme_settings": "Choose which platforms to share to in global theme settings", "collections_listed_by_default": "All of your collections are listed by default. To customize your list, choose 'Selected' and add collections.", "content_for_age_verification_failure": "This content will display if the user does not meet the age verification requirements.", "customers_who_subscribe_accept_marketing": "Customers who subscribe will have their email address added to the 'accepts marketing' [customer list](/admin/customers?query=&accepts_marketing=1).", "darkens_your_image": "Darkens your image to ensure your text is readable", "darkens_your_video": "Darkens your video to ensure your text is readable", "defaults_to_collection_title": "Defaults to collection title", "defaults_to_menu_title": "Defaults to menu title", "does_not_appear_on_mobile": "Does not appear on mobile in order to meet [Google's interstitial guidelines](https://developers.google.com/search/blog/2016/08/helping-users-easily-access-content-on) for improved SEO", "dynamic_recommendations_use": "Dynamic recommendations use order and product information to change and improve over time. [Learn more](https://help.shopify.com/en/themes/development/recommended-products)", "enable_shop_pay_for_shop_app_follow": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "for_product_with_long_descriptions": "For product lines with long descriptions, we recommend placing your Description and Tabs within this section.", "forces_the_age_verification": "Forces the age verification to show on every refresh, and should only be used for editing the popup. Ensure 'Test mode' is disabled when launching your store.", "gift_card_products": "Gift card products can optionally be sent direct to a recipient along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)", "google_maps_will_find_location": "Google maps will find the exact location", "if_collection_image_enabled": "(if collection image is enabled)", "image_displays_if_video_fails": "Image will display if video fails to load", "image_source_adjusted_theme_settings": "Image source can be adjusted under Theme Settings > Collection Tiles", "lazy_loading_enabled_below_fold": "Lazy loading should be enabled when section images are below the fold. [Learn more](https://archetypethemes.co/blogs/support/what-is-lazyloading)", "learn_inventory_transfers": "Learn how to create inventory transfers [here](https://help.shopify.com/en/manual/products/inventory/transfers/create-transfer)", "learn_mega_menu": "[Learn more](https://archetypethemes.co/blogs/expanse/how-do-i-create-a-mega-menu)", "learn_more_media_types": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)", "learn_to_setup_product_prefix": "Learn how to setup this feature [here](https://help.archetypethemes.co/en/articles/851328)", "lets_customers_checkout_familiar": "Lets customers check out directly using a familiar payment method. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)", "links_to_collections_appear_here": "Links to collections from your menu will appear here. [Learn more](https://archetypethemes.co/blogs/expanse/how-do-i-create-subcollections)", "links_to_youtube_video_player": "Links to YouTube videos will be opened in a video player", "mapbox_will_find_location": "Mapbox will find the exact location", "menu_shows_top_level": "This menu will only show top-level links", "model_media_type_required": "Product must have a 3D model media type", "no_effect_when_size_natural": "No effect when grid image size set to 'Natural'", "not_shown_to_customers_with_accounts": "Will not be shown to customers who have created an account on your shop.", "number_of_days_popup_reappears": "Number of days before a dismissed popup reappears", "only_appear_on_articles": "Will only appear on articles", "only_applies_on_desktop": "Only applies on desktop viewport", "overrides_video_url_if_both_set": "Overrides video URL setting if both are set", "recently_viewed_products_only_visible": "Recently viewed products are only visible when browsing outside of the editor", "recommended_a_square_image": "Recommended a square ratio for optimal mobile experience", "requires_square_images": "Requires square images", "scaled_to_32x32": "Will be scaled down to 32 x 32px", "section_appears_when_product_vendor": "This section will only appear when you are on a product that has a vendor set", "section_on_product_page_w_collection": "This section will only appear when you are on a product page that was reached through a collection", "set_as_max_width": "Set as a max-width, may appear smaller", "shopify_facebook": "https://www.facebook.com/shopify", "shopify_instagram": "https://instagram.com/shopify", "shopify_linkedin": "https://www.linkedin.com/in/shopify", "shopify_pinterest": "https://www.pinterest.com/shopify", "shopify_snapchat": "https://www.snapchat.com/add/shopify", "shopify_tiktok": "https://www.tiktok.com/@shopify", "shopify_tumblr": "https://shopify.tumblr.com", "shopify_twitter": "https://twitter.com/shopify", "shopify_vimeo": "https://vimeo.com/shopify", "sidebar_is_first_two_sections": "Make sure your sidebar is one of the first two sections", "sign_up_creates_customer": "Every sign up will create a Customer on your store. [View customers](/admin/customers?query=&accepts_marketing=1).", "sorting_applies_when_all_selected": "Sorting only applies when 'All' is selected", "styles_online_apply_to_ital_text": "Styles only apply to italicized text within the heading", "supported_video_formats": "Supports YouTube, .MP4 and Vimeo. Not all features supported by Vimeo. [Learn more](https://help.archetypethemes.co/en/articles/2740)", "text_below_on_mobile": "Text is always below image on mobile", "to_add_currency_settings": "To add a currency, go to your [currency settings.](/admin/settings/payments)", "to_add_language_settings": "To add a language, go to your [language settings.](/admin/settings/languages)", "to_select_complementary_add_search_discovery": "To select complementary products, add the Search & Discovery app. [Learn more](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations#complementary-products)", "use_instead_of_google_maps_api": "Use instead of an API key", "use_instead_of_mapbox_api": "Use instead of an API access token", "used_when_on_top_of_image": "Used when on top of an image", "values_below_10_not_recommended": "Values below 10 seconds are not recommended. Delay disabled in the editor.", "video_with_sound_not_autoplay": "Video with sound will not autoplay"}, "labels": {"3D_model": "3D model", "404_page": "404 page", "75_width": "75% width", "accounts": "Accounts", "additional_copyright_text": "Additional copyright text", "additional_footer_content": "Additional footer content", "address_and_hours": "Address and hours", "advanced_accordion": "Advanced accordion", "age_verification_popup": "Age Verification Popup", "age_verification_question": "Age Verification question", "alignment": "Alignment", "alignments": {"bottom": "Bottom", "bottom_center": "Bottom center", "bottom_left": "Bottom left", "bottom_right": "Bottom right", "center": "Center", "center_left": "Center left", "center_right": "Center right", "center_text": "Center text", "centered": "Centered", "left": "Left", "left_to_right": "Left to right", "middle": "Middle", "right": "Right", "right_to_left": "Right to left", "top": "Top", "top_center": "Top center", "top_left": "Top left", "top_right": "Top right"}, "all": "All", "amount": "Amount", "announcement": "Announcement", "announcement_bar": "Announcement bar", "announcement_text": "Announcement text", "apps": "Apps", "arch": "Arch", "arrows": "Arrows", "article": "Article", "article_pages": "Article pages", "author": "Author", "author_info": "Author info", "authors_image": "Author's image", "autochange_slides": "Auto-change slides", "back_to_collection": "Back to collection", "background": "Background", "background_image": "Background image", "background_video_link": "Background video link", "bag": "Bag", "banner": "Banner", "bars": "Bars", "base_size": "Base size", "below_media": "Below media", "bills": "Bills", "blocks_per_row": "Blocks per row", "blog": "Blog", "blog_pages": "Blog pages", "blog_posts": "Blog posts", "blog_sidebar": "Blog sidebar", "body": "Body", "body_text": "Body text", "borders": "Borders", "bottom_text": "Bottom text", "button": "<PERSON><PERSON>", "button_1_link": "Button #1 link", "button_1_text": "Button #1 text", "button_2_link": "Button #2 link", "button_2_text": "Button #2 text", "button_label": "Button label", "button_link": "Button link", "button_style": "Button style", "button_text": "Button text", "button_text_2": "Button text 2", "buttons": "Buttons", "buy_buttons": "Buy buttons", "calendar": "Calendar", "capitalize": "Capitalize", "capitalize_navigation": "Capitalize navigation", "cart": "<PERSON><PERSON>", "cart_dot": "Cart dot", "cart_dot_text": "Cart dot text", "cart_icon": "Cart icon", "cart_page": "Cart page", "cart_recommendations": "Cart recommendations", "cart_type": "Cart type", "change_images_every": "Change images every", "charity": "Charity", "chat": "Cha<PERSON>", "chat_link": "Chat link", "checkmark": "Checkmark", "circle": "Circle", "circle_11": "Circle (1:1)", "circular_images": "Circular images", "classic": "Classic", "cold_blur": "Cold Blur", "collection": "Collection", "collection_carousel": "Collection carousel", "collection_description": "Collection description", "collection_header": "Collection header", "collection_image": "Collection image", "collection_list": "Collection list", "collection_tile_background": "Collection tile background", "collection_tile_style": "Collection tile style", "collection_tiles": "Collection tiles", "collections_list_page": "Collections list page", "color_scheme": "Color scheme", "color_schemes": {"1": "Color scheme 1", "2": "Color scheme 2", "3": "Color scheme 3"}, "color_swatches": "Color swatches", "colors": "Colors", "column": "Column", "contact": "Contact", "contact_and_social": "Contact and social", "contact_form": "Contact form", "contact_page": "Contact page", "content": "Content", "content_alignment": "Content alignment", "content_alignment_desktop": "Content alignment on desktop", "content_position": "Content position", "countdown": "Countdown", "custom_1": "Custom 1", "custom_2": "Custom 2", "custom_3": "Custom 3", "custom_content": "Custom content", "darken": "Darken", "day": "Day", "declined": "Declined", "default": "<PERSON><PERSON><PERSON>", "default_product_layout": "Default product layout", "delay": "Delay", "description": "Description", "design": "Design", "desktop_columns_per": "Desktop columns per row", "desktop_height": "Desktop height", "desktop_height_adjustment": "Desktop height adjustment", "desktop_logo_height": "Desktop logo height", "desktop_logo_width": "Desktop logo width", "desktop_products_per": "Desktop products per row", "direction": "Direction", "dot": "Dot", "dots": "Dots", "dropdown": "Dropdown", "edges": "<PERSON>s", "email_signup": "Email signup", "envelope": "Envelope", "extras": "Extras", "facebook": "Facebook", "fallback_image": "Fallback image", "faq": "FAQ", "favicon": "Favicon", "favicon_image": "Favicon image", "featured_article": "Featured article", "featured_collection": "Featured collection", "featured_product": "Featured product", "filtering_and_sorting": "Filtering and sorting", "first_product": "First product", "fixed": "Fixed", "flexible": "Flexible", "follow_on_shop": "Follow on Shop", "font": "Font", "font_weights": {"bold": "Bold", "extrabold": "Extra-bold", "extralight": "Extra-light", "light": "Light", "semibold": "Semi-bold"}, "footer": "Footer", "footer_group": "Footer group", "footer_promotions": "Footer promotions", "form": "Form", "frequency": "Frequency", "full_page_width": "Full page width", "full_width": "Full width", "fullwidth_details": "Full-width details", "gallery": "Gallery", "gears": "Gears", "general": "General", "gift": "Gift", "globe": "Globe", "google_maps_api": "Google Maps API key", "grey": "Grey", "grey_round": "Grey round", "grey_square": "Grey square", "grid_spacing": "Grid spacing", "handwriting": "Handwriting", "header": "Header", "header_group": "Header group", "heading": "Heading", "heading_ital_text_style": "Heading italicized text style", "heading_position": "Heading position", "heading_size": "Heading size", "heading_text_size": "Heading text size", "headings": "Headings", "heart": "Heart", "height": "Height", "hero": "Hero", "hero_optional_slides": "Hero (optional slides)", "hide_timer_on": "Hide timer on complete", "horizontal": "Horizontal", "horizontal_position": "Horizontal position", "hot_spot_icon": "Hot spot icon style", "hotspot_color": "Hotspot color", "hour": "Hour", "hover_to_reveal": "Hover to reveal second image", "html": "HTML", "html_block": "HTML block", "icon": "Icon", "icon_color": "Icon color", "icons": "Icons", "image": "Image", "image_2": "Image 2", "image_alignment": "Image alignment", "image_background": "Image background", "image_breathing_room": "Image breathing room", "image_comparison": "Image comparison", "image_crop": "Image crop", "image_hotspots": "Image hotspots", "image_link": "Image link", "image_on_left": "Image on left", "image_on_right": "Image on right", "image_optional_slides": "Image (optional slides)", "image_position": "Image position", "image_ratio": "Image ratio", "image_section_background": "Image section background", "image_shape": "Image shape", "image_size": "Image size", "image_style": "Image style", "image_width": "Image width", "image_with_text": "Image with text", "images_per_row": "Images per row", "indent_image": "Indent image", "inline": "Inline", "instagram": "Instagram", "inventory_status": "Inventory status", "label": "Label", "landscape": "Landscape", "landscape_43": "Landscape (4:3)", "language": "Language", "large_grid": "Large grid", "large_image_with": "Large image with text box", "layout": "Layout", "layout_on_desktop": "Layout on desktop", "lazy_load_images": "Lazy load images", "leaf": "Leaf", "letter_spacing": "Letter spacing", "line_height": "Line height", "linen": "Linen", "lines_and_borders": "Lines and borders", "link": "Link", "link_2": "Link 2", "link_block": "Link block", "link_label": "Link label", "linkedin": "LinkedIn", "list": "List", "lock": "Lock", "logo": "Logo", "logo_center_menu": "Logo center, menu below", "logo_image": "Logo image", "logo_left_menu_below": "Logo left, menu below", "logo_left_menu_center": "Logo left, menu center", "logo_left_menu_left": "Logo left, menu left", "logo_list": "Logo list", "low_inventory_threshold": "Low inventory threshold", "map": "Map", "map_address": "Map address", "mapbox_api": "Mapbox API access token", "marble": "Marble", "max_products_to": "Max products to show", "maximum_products_to": "Maximum products to show", "media": "Media", "media_crop": "Media crop", "media_gallery_layout": "Media gallery layout", "media_on_left": "Media on left", "media_on_right": "Media on right", "media_width": "Media width", "media_with_text": "Media with text", "middle_text": "Middle text", "minimal": "Minimal", "minimal_bag": "Minimal bag", "minimal_wave": "Minimal Wave", "minute": "Minute", "mobile_height": "Mobile height", "mobile_height_adjustment": "Mobile height adjustment", "mobile_image": "Mobile image", "mobile_layout": "Mobile layout", "mobile_logo_height": "Mobile logo height", "mobile_logo_width": "Mobile logo width", "mobile_text": "Mobile text", "model": "Model", "month": "Month", "months": {"april": "April", "august": "August", "december": "December", "february": "February", "january": "January", "july": "July", "june": "June", "march": "March", "may": "May", "november": "November", "october": "October", "september": "September"}, "more_from_collection": "More from collection", "more_from_vendor": "More from vendor", "narrow_column": "Narrow column", "natural": "Natural", "navigation": "Navigation", "navigation_font": "Navigation font", "navigation_size": "Navigation size", "newsletter": "Newsletter", "next_to_media": "Next to media", "none": "None", "notebook": "Notebook", "number_of_bars": "Number of bars", "number_of_products": "Number of products per slide", "number_of_related": "Number of related products", "optimize_for_readability": "Optimize for readability", "optional": "Optional", "outline": "Outline", "overlay": "Overlay", "overlay_header": "Overlay header", "overlay_opacity": "Overlay opacity", "package": "Package", "page": "Page", "page_full_width": "Page (full width)", "pagination_type": "Pagination type", "paper": "Paper", "paragraph_hotspot": "Paragraph hotspot", "parallax_direction": "Parallax direction", "parallax_image": "Parallax image", "payments_and_localization": "Payments and localization", "percent": "Percent", "phone": "Phone", "phone_number": "Phone number", "pin_on_pinterest": "Pin on Pinterest", "pinterest": "Pinterest", "plant": "Plant", "plants": "Plants", "plus": "Plus", "policies_menu": "Policies menu", "popup": "Popup", "popups": "Popups", "portrait": "Portrait", "portrait_23": "Portrait (2:3)", "position": "Position", "post_limit": "Posts", "predictive_search": "Predictive search", "price": "Price", "primary": "Primary", "product": "Product", "product_card": "Product card", "product_grid": "Product grid", "product_hotspot": "Product hotspot", "product_reviews": "Product reviews", "product_tags": "Product tags", "product_specs": "Product specs", "product_tile_layout": "Product tile layout", "product_tile_style": "Product tile style", "product_tiles": "Product tiles", "products": "Products", "promotion_grid": "Promotion grid", "promotional_grid": "Promotional grid", "quantity_selector": "Quantity selector", "question": "Question", "quote": "Quote", "recently_viewed": "Recently viewed", "recommended": "Recommended", "recycle": "Recycle", "regular": "Regular", "related_products": "Related products", "reminder_label": "Reminder label", "ribbon": "Ribbon", "rich_text": "Rich text", "round": "Round", "rounded": "Rounded", "rounded_wave": "Rounded wave", "sale_collection": "Sale Collection", "sale_collection_342": "Sale collection", "sale_tag_text": "Sale tag text", "sale_tags": "Sale tags", "sales_point": "Sales point", "sales_tag": "Sales tag", "sales_tag_color_setting": "'Sale tags' Color Setting", "sand": "Sand", "save_price": "Save price", "savings_display_style": "Savings display style", "scrolling_text": "Scrolling text", "search": "Search", "search_field": "Search field", "search_page": "Search page", "secondary": "Secondary", "section_height": "Section height", "section_layout": "Section layout", "selected": "Selected", "separator": "Separator", "serif": "<PERSON><PERSON>", "share_buttons": "Share buttons", "share_links": "Share links", "sharing_options": "Sharing options", "sharp": "<PERSON>", "shield": "Shield", "simple": "Simple", "size": "Size", "size_chart": "Size chart", "size_chart_page": "Size chart page", "sizes": {"extra_large": "Extra large", "large": "Large", "medium": "Medium", "small": "Small", "large_grid": "Large grid", "small_grid": "Small grid"}, "slide": "Slide", "slide_link": "Slide link", "slide_link_2": "Slide link 2", "slide_link_text": "Slide link text", "slide_link_text_339": "Slide link text 2", "slide_navigation_style": "Slide navigation style", "slider_style": "Slider style", "slightly_round": "Slightly round", "small_grid": "Small grid", "snapchat": "Snapchat", "social": "Social", "social_media": "Social media", "solid": "Solid", "sortings": {"alphabetically_az": "Alphabetically, A-Z", "alphabetically_za": "Alphabetically, Z-A", "date_new_to": "Date, new to old", "date_old_to": "Date, old to new", "product_count_high": "Product count, high to low", "product_count_low": "Product count, low to high"}, "space": "Space", "spacing": "Spacing", "speed": "Speed", "splat_1": "Splat 1", "splat_2": "Splat 2", "splat_3": "Splat 3", "splat_4": "Splat 4", "split_optional_slides": "Split (optional slides)", "square": "Square", "square_11": "Square (1:1)", "squiggle": "Squiggle", "stacked_media": "Stacked media", "star": "Star", "stars": {"1_star": "1 star", "2_stars": "2 stars", "3_stars": "3 stars", "4_stars": "4 stars", "5_stars": "5 stars"}, "sticky_reminder": "Sticky reminder", "stone": "Stone", "stopwatch": "Stopwatch", "store": "Store", "store_availability": "Store availability", "style": "Style", "subcollections": "Subcollections", "subheading": "Subheading", "supports_liquid": "Supports Liquid", "swatch_size": "Swatch size", "swatch_style": "Swatch style", "swirl": "Swirl", "tab": "Tab", "tab_content": "Tab content", "tab_content_from": "Tab content from page", "tag": "Tag", "tags": "Tags", "terms_and_conditions": "Terms and conditions page", "testimonial": "Testimonial", "testimonials": "Testimonials", "text": "Text", "text_alignment": "Text alignment", "text_block": "Text block", "text_columns_with": "Text columns with images", "text_columns_with_386": "Text columns with icons", "text_direction": "Text direction", "text_on_left": "Text on left", "text_on_right": "Text on right", "text_position": "Text position", "text_protection": "Text protection", "text_size": "Text size", "texture": "Texture", "thick_grid": "Thick grid", "thin_grid": "Thin grid", "thumbnail_height": "Thumbnail height", "thumbnail_position": "Thumbnail position", "thumbnails_below_media": "Thumbnails below media", "thumbnails_beside_media": "Thumbnails beside media", "thumbs_up": "Thumbs up", "tiktok": "TikTok", "timer": "Timer", "timer_complete_message": "Timer complete message", "times": {"01_am": "01 AM", "02_am": "02 AM", "03_am": "03 AM", "04_am": "04 AM", "05_am": "05 AM", "06_am": "06 AM", "07_am": "07 AM", "08_am": "08 AM", "09_am": "09 AM", "10_am": "10 AM", "10_pm": "10 PM", "11_am": "11 AM", "11_pm": "11 PM", "12_am": "12 AM", "12_pm": "12 PM", "1_pm": "1 PM", "2_pm": "2 PM", "3_pm": "3 PM", "4_pm": "4 PM", "5_pm": "5 PM", "6_pm": "6 PM", "7_pm": "7 PM", "8_pm": "8 PM", "9_pm": "9 PM"}, "title": "Title", "top_text": "Top text", "trophy": "Trophy", "truck": "Truck", "trust_badge": "Trust badge", "tumblr": "Tumblr", "twitter": "X", "two_blocks_per_row_mobile": "Two blocks per row on mobile", "type": "Type", "typography": "Typography", "value": "Value", "vertical": "Vertical", "vertical_alignment": "Vertical alignment", "vertical_position": "Vertical position", "video": "Video", "video_hero": "Video hero", "video_link": "Video link", "video_style": "Video style", "video_url": "Video URL", "video_with_sound": "Video with sound", "video_without_sound": "Video without sound", "vimeo": "Vimeo", "wallet": "Wallet", "warm_blur": "Warm Blur", "wave": "Wave", "weight": "Weight", "white": "White", "white_logo": "White logo", "white_round": "White round", "white_square": "White square", "wide_16_9": "Wide (16:9)", "width": "<PERSON><PERSON><PERSON>", "wildflower": "Wildflower", "year": "Year", "youtube": "YouTube", "youtube_vimeo": "YouTube/Vimeo"}}