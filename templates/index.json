/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "layout": "theme",
  "sections": {
    "ecom-new-home-page-home": {
      "type": "ecom-new-home-page-home",
      "disabled": true,
      "settings": {}
    },
    "slideshow_banner_qpF9wG": {
      "type": "slideshow-banner",
      "blocks": {
        "hero_T3eFJJ": {
          "type": "hero",
          "settings": {
            "top_subheading": "",
            "title": "<p>Overlaid Hero Slide</p>",
            "title_size": 70,
            "subheading": "Perfect for rich lifestyle photography.",
            "link": "",
            "link_text": "Optional button",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "#fff",
            "text_align": "vertical-bottom horizontal-left",
            "overlay_opacity": 0
          }
        },
        "hero_tXbDBN": {
          "type": "hero",
          "settings": {
            "top_subheading": "",
            "title": "<p>Make an Impact</p>",
            "title_size": 70,
            "subheading": "Supports multiple text alignments and mobile-specific images.",
            "link": "",
            "link_text": "Optional button",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "#fff",
            "text_align": "vertical-center horizontal-center",
            "overlay_opacity": 0
          }
        }
      },
      "block_order": [
        "hero_T3eFJJ",
        "hero_tXbDBN"
      ],
      "name": "Slideshow Banner",
      "settings": {
        "full_width": true,
        "desktop_size": 0,
        "mobile_size": 0,
        "style": "arrows",
        "autoplay": true,
        "autoplay_speed": 7
      }
    },
    "shop_by_category_yJTzD7": {
      "type": "shop-by-category",
      "blocks": {
        "product_card_arxQJj": {
          "type": "product_card",
          "settings": {
            "desktop_image": "shopify://shop_images/2_91a5987f-3fec-4fd5-bbb5-c92ce65c19c8.png",
            "image_url": "https://www.sicotas.com/collections/nightstands/products/rattan-nightstand-type-c-charging-port",
            "button_url": "https://www.sicotas.com/collections/nightstands",
            "button_text": "Nightstands"
          }
        },
        "product_card_7CphgL": {
          "type": "product_card",
          "settings": {
            "desktop_image": "shopify://shop_images/2_1.png",
            "image_url": "https://www.sicotas.com/collections/dressers/products/savanna-6-drawers-wood-dresser-56-9in-wide-chest-of-drawers-with-large",
            "button_url": "https://www.sicotas.com/collections/dressers",
            "button_text": "Nightstands"
          }
        },
        "product_card_MYBjnr": {
          "type": "product_card",
          "settings": {
            "desktop_image": "shopify://shop_images/3_1_c374df82-5022-420f-b945-b8e341c23902.png",
            "image_url": "https://www.sicotas.com/collections/shoe-storages-cabinet-shoe-benches/products/rattan-shoe-cabinet-24-pairs-shoes",
            "button_url": "https://www.sicotas.com/collections/shoe-storages-cabinet-shoe-benches",
            "button_text": "Nightstands"
          }
        },
        "product_card_m3fMAx": {
          "type": "product_card",
          "settings": {
            "desktop_image": "shopify://shop_images/4_1.png",
            "image_url": "https://www.sicotas.com/collections/bookcases/products/rattan-bookshelves-large-bookcase-with-door",
            "button_url": "https://www.sicotas.com/collections/bookcases",
            "button_text": "Nightstands"
          }
        },
        "product_card_ky8wKf": {
          "type": "product_card",
          "settings": {
            "desktop_image": "shopify://shop_images/image_17.png",
            "image_url": "https://www.sicotas.com/collections/tv-stand/products/wood-tv-stands-for-45-55-65-75-tvs-adjustable-shelves-media-console",
            "button_url": "https://www.sicotas.com/collections/tv-stand",
            "button_text": "Nightstands"
          }
        },
        "product_card_Eek6d7": {
          "type": "product_card",
          "settings": {
            "desktop_image": "shopify://shop_images/image_18.png",
            "image_url": "https://www.sicotas.com/collections/buffet-sideboard/products/rattan-sideboard-buffet-cabinet",
            "button_url": "https://www.sicotas.com/collections/buffet-sideboard",
            "button_text": "Nightstands"
          }
        },
        "product_card_hgEjBy": {
          "type": "product_card",
          "settings": {
            "desktop_image": "shopify://shop_images/image_19.png",
            "image_url": "https://www.sicotas.com/collections/sicotas-console-table/products/savanna-rattan-boho-end-table-beside-tables-black-narrow-open-table",
            "button_url": "https://www.sicotas.com/collections/sicotas-console-table",
            "button_text": "Nightstands"
          }
        },
        "product_card_iyDhpt": {
          "type": "product_card",
          "settings": {
            "desktop_image": "shopify://shop_images/image_20.png",
            "image_url": "https://www.sicotas.com/collections/bar-wine-cabinet/products/buffet-sideboard-bar-cabinet",
            "button_url": "https://www.sicotas.com/collections/bar-wine-cabinet",
            "button_text": "Nightstands"
          }
        }
      },
      "block_order": [
        "product_card_arxQJj",
        "product_card_7CphgL",
        "product_card_MYBjnr",
        "product_card_m3fMAx",
        "product_card_ky8wKf",
        "product_card_Eek6d7",
        "product_card_hgEjBy",
        "product_card_iyDhpt"
      ],
      "name": "Shop by Category",
      "settings": {
        "title": "Shop by Category"
      }
    },
    "combination_product_kpmUmf": {
      "type": "combination-product",
      "name": "Combination Product",
      "settings": {
        "heading_size": "h2",
        "heading_position": "left",
        "image": "shopify://shop_images/Banner_3.png",
        "indent_image": false,
        "image_position": "left",
        "hotspot_style": "bag",
        "hotspot_color": "#000000"
      }
    },
    "slideshow_room_73frCM": {
      "type": "slideshow-room",
      "blocks": {
        "slide_fyCfqX": {
          "type": "slide",
          "settings": {
            "image": "shopify://shop_images/5_1.png",
            "link": "",
            "title": "Dining Room"
          }
        },
        "slide_zTm6ii": {
          "type": "slide",
          "settings": {
            "image": "shopify://shop_images/image_21.png",
            "link": "",
            "title": "Bedroom"
          }
        },
        "slide_FeTFCN": {
          "type": "slide",
          "settings": {
            "image": "shopify://shop_images/image_22.png",
            "link": "",
            "title": "Living Room"
          }
        }
      },
      "block_order": [
        "slide_fyCfqX",
        "slide_zTm6ii",
        "slide_FeTFCN"
      ],
      "name": "section-slideshow-room",
      "settings": {
        "full_width": true,
        "height": 650,
        "height_mobile": 450,
        "style": "arrows",
        "autoplay": true,
        "autoplay_speed": 7,
        "right_image": "shopify://shop_images/A_2-1_1_84f347a7-9272-4d62-b14a-4b9fa08f858d.png",
        "right_link": ""
      }
    },
    "best_sellers_Dd4qtN": {
      "type": "best-sellers",
      "blocks": {
        "product_card_xxmeXa": {
          "type": "product_card",
          "settings": {
            "product": "prelude-end-tables-nightstands-with-2-drawers-and-charger-station",
            "show_sale": true,
            "discount": ""
          }
        },
        "product_card_kyLmJM": {
          "type": "product_card",
          "settings": {
            "product": "helio-tall-buffet-cabinet",
            "show_sale": false,
            "discount": ""
          }
        },
        "product_card_Kbqmgq": {
          "type": "product_card",
          "settings": {
            "product": "savanna-shoe-cabinet-30-pairs-shoes",
            "show_sale": false,
            "discount": ""
          }
        },
        "product_card_MzTHe9": {
          "type": "product_card",
          "settings": {
            "product": "rattan-nightstand-type-c-charging-port-end-tabke-for-couch-side-table",
            "show_sale": false,
            "discount": ""
          }
        },
        "product_card_BpRDV8": {
          "type": "product_card",
          "settings": {
            "product": "boho-rattan-sideboards-accent-wood-buffet-cabinet-with-door-and-drawer",
            "show_sale": false,
            "discount": ""
          }
        }
      },
      "block_order": [
        "product_card_xxmeXa",
        "product_card_kyLmJM",
        "product_card_Kbqmgq",
        "product_card_MzTHe9",
        "product_card_BpRDV8"
      ],
      "name": "Best Sellers",
      "settings": {
        "title": "Best Sellers",
        "fallback_collection": "",
        "enable_auto_replacement": true
      }
    },
    "furniture_showcase_KifYD7": {
      "type": "furniture-showcase",
      "name": "Furniture Showcase",
      "settings": {
        "title": "Discover our curated collections and find the perfect pieces to elevate your space",
        "heading_size": "h2",
        "nav_item_1": "Savanna",
        "nav_link_1": "https://www.sicotas.com/collections/savanna-collections-furniture",
        "nav_item_2": "Cas",
        "nav_link_2": "https://www.sicotas.com/collections/cas-collections",
        "nav_item_3": "Helio",
        "nav_link_3": "https://www.sicotas.com/collections/helio-collections",
        "nav_item_4": "Terra",
        "nav_link_4": "https://www.sicotas.com/collections/terra-collection",
        "nav_item_5": "Stria",
        "nav_link_5": "https://www.sicotas.com/collections/stria-collections",
        "nav_item_6": "Opus",
        "nav_link_6": "https://www.sicotas.com/collections/white-fluted-furniture-set",
        "image_1": "shopify://shop_images/b84630a378d76748308920737c479c10.png",
        "video_url_1": "",
        "image_2": "shopify://shop_images/1_003e02b5-5c16-4a18-ab22-6c30d3c9dd16.png",
        "video_url_2": "",
        "image_3": "shopify://shop_images/image_10.png",
        "video_url_3": "",
        "image_4": "shopify://shop_images/image_11_07b55fac-4ee4-4092-b821-66adc4c74f2a.png",
        "video_url_4": "",
        "image_5": "shopify://shop_images/image_12_4ef654cf-61d5-458e-9997-9ce0eaa8d43e.png",
        "video_url_5": "",
        "mobile_video_url_1": "",
        "mobile_video_url_2": "",
        "mobile_video_url_3": "",
        "mobile_video_url_4": "",
        "slide2_image_1": "shopify://shop_images/image_13.png",
        "slide2_video_url_1": "",
        "slide2_image_2": "shopify://shop_images/image_32.png",
        "slide2_video_url_2": "",
        "slide2_image_3": "shopify://shop_images/image_15.png",
        "slide2_video_url_3": "",
        "slide2_image_4": "shopify://shop_images/image_16.png",
        "slide2_video_url_4": "",
        "slide2_image_5": "shopify://shop_images/Mask_group_1.png",
        "slide2_video_url_5": "",
        "slide2_mobile_video_url_1": "",
        "slide2_mobile_video_url_2": "",
        "slide2_mobile_video_url_3": "",
        "slide2_mobile_video_url_4": "",
        "slide3_video_url_1": "",
        "slide3_video_url_2": "",
        "slide3_video_url_3": "",
        "slide3_video_url_4": "",
        "slide3_video_url_5": "",
        "slide3_mobile_video_url_1": "",
        "slide3_mobile_video_url_2": "",
        "slide3_mobile_video_url_3": "",
        "slide3_mobile_video_url_4": "",
        "slide4_video_url_1": "",
        "slide4_video_url_2": "",
        "slide4_video_url_3": "",
        "slide4_video_url_4": "",
        "slide4_video_url_5": "",
        "slide4_mobile_video_url_1": "",
        "slide4_mobile_video_url_2": "",
        "slide4_mobile_video_url_3": "",
        "slide4_mobile_video_url_4": "",
        "slide5_video_url_1": "",
        "slide5_video_url_2": "",
        "slide5_video_url_3": "",
        "slide5_video_url_4": "",
        "slide5_video_url_5": "",
        "slide5_mobile_video_url_1": "",
        "slide5_mobile_video_url_2": "",
        "slide5_mobile_video_url_3": "",
        "slide5_mobile_video_url_4": "",
        "slide6_video_url_1": "",
        "slide6_video_url_2": "",
        "slide6_video_url_3": "",
        "slide6_video_url_4": "",
        "slide6_video_url_5": "",
        "slide6_mobile_video_url_1": "",
        "slide6_mobile_video_url_2": "",
        "slide6_mobile_video_url_3": "",
        "slide6_mobile_video_url_4": "",
        "button_label": "Read More",
        "button_style": "secondary",
        "button_link_1": "https://www.sicotas.com/collections/savanna-collections-furniture",
        "button_link_2": "https://www.sicotas.com/collections/cas-collections",
        "button_link_3": "https://www.sicotas.com/collections/helio-collections",
        "button_link_4": "https://www.sicotas.com/collections/terra-collection",
        "button_link_5": "https://www.sicotas.com/collections/stria-collections",
        "button_link_6": "https://www.sicotas.com/collections/white-fluted-furniture-set",
        "auto_play": true,
        "auto_play_speed": 5,
        "background_color": "#f8f8f8",
        "divider": false,
        "top_padding": true,
        "bottom_padding": true
      }
    },
    "text_icon_gz3nmF": {
      "type": "text-icon",
      "blocks": {
        "svg_FjpdxC": {
          "type": "svg",
          "settings": {
            "svg": "<svg width=\"42\" height=\"28\" viewbox=\"0 0 44 30\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"shipping-icon\">\n                    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M11.2685 28.0668C13.3303 28.0668 15.0018 26.3953 15.0018 24.3334C15.0018 22.2716 13.3303 20.6001 11.2685 20.6001C9.20663 20.6001 7.53516 22.2716 7.53516 24.3334C7.53516 26.3953 9.20663 28.0668 11.2685 28.0668Z\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linejoin=\"round\"></path>\n                    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32.7333 28.0668C34.7952 28.0668 36.4667 26.3953 36.4667 24.3334C36.4667 22.2716 34.7952 20.6001 32.7333 20.6001C30.6715 20.6001 29 22.2716 29 24.3334C29 26.3953 30.6715 28.0668 32.7333 28.0668Z\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linejoin=\"round\"></path>\n                    <path d=\"M7.53359 24.3331H1.93359V1.93311H29.0003V24.3331H15.0003\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                    <path d=\"M29 24.3335V8.4668H37L43 16.4001V24.3335H37.2238\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                </svg>",
            "pc_title": "Free Shipping",
            "mb_title": "FreeShipping",
            "text": "On all orders",
            "link": ""
          }
        },
        "svg_UifUBt": {
          "type": "svg",
          "settings": {
            "svg": "<svg width=\"37\" height=\"28\" viewbox=\"0 0 39 30\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"evenodd-icon\">\n                    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M1.93359 28.0664H36.778V14.9998V1.93311H19.3558H1.93359V14.9998V28.0664Z\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linejoin=\"round\"></path>\n                    <path d=\"M1.93359 1.93311L19.3558 14.9998L36.778 1.93311\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                    <path d=\"M19.3558 1.93311H1.93359V14.9998\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                    <path d=\"M36.7777 14.9998V1.93311H19.3555\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                </svg>",
            "pc_title": "Multi-channel Service Support",
            "mb_title": "4 Service Methods",
            "text": "4 Service Methods",
            "link": ""
          }
        },
        "svg_m6myqG": {
          "type": "svg",
          "settings": {
            "svg": "<svg width=\"29\" height=\"28\" viewbox=\"0 0 29 28\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"return-icon\">\n                    <path d=\"M23.6715 27.3574L24.3574 22.4046L19.4047 21.7187\" fill=\"#6D4C41\"></path>\n                    <path d=\"M13.8643 21V7\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                    <path d=\"M10.3636 19.7275C10.3636 19.7275 13.5548 19.7275 15.1364 19.7275C16.7179 19.7275 18 18.4455 18 16.8639C18 15.2823 16.7179 14.0003 15.1364 14.0003\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                    <path d=\"M17.3633 8.27273C17.3633 8.27273 14.1721 8.27273 12.5906 8.27273C11.009 8.27273 9.72692 9.55481 9.72692 11.1364C9.72692 12.7179 11.009 14 12.5906 14H15.136\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                    <path d=\"M24.8768 8.92357C25.1107 9.42387 25.7059 9.63979 26.2062 9.40586C26.7065 9.17193 26.9224 8.57672 26.6885 8.07643L24.8768 8.92357ZM14 27V26C7.37258 26 2 20.6274 2 14H1H0C0 21.732 6.26801 28 14 28V27ZM1 14H2C2 7.37258 7.37258 2 14 2V1V0C6.26801 0 0 6.26801 0 14H1ZM14 1V2C18.8114 2 22.9634 4.83158 24.8768 8.92357L25.7826 8.5L26.6885 8.07643C24.4585 3.30739 19.6165 0 14 0V1ZM22 24.2477L21.3841 23.4599C19.3478 25.0518 16.786 26 14 26V27V28C17.2481 28 20.2404 26.8926 22.6159 25.0355L22 24.2477Z\" fill=\"#6D4C41\"></path>\n                </svg>",
            "pc_title": "60 Days Return",
            "mb_title": "60 Days Return",
            "text": "Returns Within 60 Days Of Delivery",
            "link": ""
          }
        },
        "svg_eg64Ye": {
          "type": "svg",
          "settings": {
            "svg": "<svg width=\"29\" height=\"28\" viewbox=\"0 0 27 30\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" class=\"day-icon\">\n                    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M1.93359 5.283L13.4121 1.93311L24.8797 5.283V12.1528C24.8797 19.3735 20.2587 25.7839 13.4083 28.0664C6.55601 25.7839 1.93359 19.3721 1.93359 12.1496V5.283Z\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linejoin=\"round\"></path>\n                    <path d=\"M7.66797 14.0439L12.1297 18.5056L19.7784 10.8569\" stroke=\"#6D4C41\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                </svg>",
            "pc_title": "360 Days Warranty",
            "mb_title": "360 Days Warranty",
            "text": "Offer a Quick Solution",
            "link": ""
          }
        }
      },
      "block_order": [
        "svg_FjpdxC",
        "svg_UifUBt",
        "svg_m6myqG",
        "svg_eg64Ye"
      ],
      "name": "Text Icon",
      "settings": {}
    },
    "pinglun_iUHWrp": {
      "type": "pinglun",
      "blocks": {
        "pinlin_x4jtUt": {
          "type": "pinlin",
          "settings": {
            "image": "shopify://shop_images/header.png",
            "is_true": true,
            "name": "Angel Hsia",
            "icon": "none",
            "content": "I'm so happy with my Sicotas Cas \nShoe Cabinet. The sleek, modern \ndesign fits beautifully in my entryway. \na touch of elegance without \noverpowering the space.I'm so happy \nwith my Sicotas Cas Shoe Cabinet. \nThe sleek, modern design fits \nbeautifully in my entryway.The quality \nof this item is exceptional.",
            "product": "sicotas-round-coffee-dining-table-for-kitchen-living-room-table"
          }
        },
        "pinlin_qawjFN": {
          "type": "pinlin",
          "settings": {
            "image": "shopify://shop_images/z3.png",
            "is_true": true,
            "name": "BHH",
            "icon": "4-stars",
            "content": "The quality of this item is exceptional, and building it\nwas very straightforward, thanks to the clear\ninstructions. 1000/1000.I'm so happy with my Sicotas \nCas Shoe Cabinet.",
            "product": "72-tall-kitchen-pantry-wood-storage-cabinet-with-adjustable-cabinet"
          }
        },
        "pinlin_y9Wm7A": {
          "type": "pinlin",
          "settings": {
            "image": "shopify://shop_images/z4.png",
            "is_true": true,
            "name": "Bgel Hsa",
            "icon": "3-stars",
            "content": "I'm so happy with my Sicotas Cas Shoe Cabinet. The\nsleek, modern design fits beautifully in my entryway. a\ntouch of elegance without overpowering the space.I'm \nso happy with my Sicotas Cas Shoe Cabinet. The\nsleek, modern design fits beautifully in my entryway.\nThe quality of this item is exceptional.",
            "product": "accent-wood-storage-cabinet-rattan-sideboard-buffet-cabinet"
          }
        },
        "pinlin_ey3qdh": {
          "type": "pinlin",
          "settings": {
            "image": "shopify://shop_images/header.png",
            "is_true": true,
            "name": "Angel Hsia",
            "icon": "none",
            "content": "I'm so happy with my Sicotas Cas \nShoe Cabinet. The sleek, modern \ndesign fits beautifully in my entryway. \na touch of elegance without \noverpowering the space.I'm so happy \nwith my Sicotas Cas Shoe Cabinet. \nThe sleek, modern design fits \nbeautifully in my entryway.The quality \nof this item is exceptional.",
            "product": "sicotas-round-coffee-dining-table-for-kitchen-living-room-table"
          }
        },
        "pinlin_RRLKE3": {
          "type": "pinlin",
          "settings": {
            "image": "shopify://shop_images/z3.png",
            "is_true": true,
            "name": "BHH",
            "icon": "4-stars",
            "content": "The quality of this item is exceptional, and building it\nwas very straightforward, thanks to the clear\ninstructions. 1000/1000.I'm so happy with my Sicotas \nCas Shoe Cabinet.",
            "product": "72-tall-kitchen-pantry-wood-storage-cabinet-with-adjustable-cabinet"
          }
        },
        "pinlin_yyYrAj": {
          "type": "pinlin",
          "settings": {
            "image": "shopify://shop_images/z4.png",
            "is_true": true,
            "name": "Bgel Hsa",
            "icon": "3-stars",
            "content": "I'm so happy with my Sicotas Cas Shoe Cabinet. The\nsleek, modern design fits beautifully in my entryway. a\ntouch of elegance without overpowering the space.I'm \nso happy with my Sicotas Cas Shoe Cabinet. The\nsleek, modern design fits beautifully in my entryway.\nThe quality of this item is exceptional.",
            "product": "accent-wood-storage-cabinet-rattan-sideboard-buffet-cabinet"
          }
        },
        "pinlin_aFqJ7W": {
          "type": "pinlin",
          "settings": {
            "image": "shopify://shop_images/z5.png",
            "is_true": true,
            "name": "Angel Hsia",
            "icon": "2-stars",
            "content": "I'm so happy with my Sicotas Cas Shoe Cabinet. The\nsleek, modern design fits beautifully in my entryway. a\ntouch of elegance without overpowering the space.I'm \nso happy with my Sicotas Cas Shoe Cabinet. The\nsleek, modern design fits beautifully in my entryway.\nThe quality of this item is exceptional.",
            "product": "andy-nightstand-set-with-2-doors"
          }
        }
      },
      "block_order": [
        "pinlin_x4jtUt",
        "pinlin_qawjFN",
        "pinlin_y9Wm7A",
        "pinlin_ey3qdh",
        "pinlin_RRLKE3",
        "pinlin_yyYrAj",
        "pinlin_aFqJ7W"
      ],
      "name": "评论轮播",
      "settings": {
        "title": "Reviews from Our Customers",
        "button": "Read More",
        "link": ""
      }
    },
    "featured_in_express_AqyPhg": {
      "type": "featured-in-express",
      "blocks": {
        "magazine_card_LRLW7x": {
          "type": "magazine_card",
          "settings": {
            "logo_image": "shopify://shop_images/AD_1.png",
            "logo_image_mobile": "shopify://shop_images/AD_1_f176db95-072a-4c8b-87c8-7686e483313c.png",
            "magazine_name": "Architectural Digest",
            "background_color": "#fcf7f2",
            "text_color": "#333333",
            "read_more_url": "https://www.architecturaldigest.com/sponsored/story/elevate-your-home-with-thoughtfully-crafted-sicotas-furniture",
            "read_more_text": "Read More"
          }
        },
        "magazine_card_CUzdgm": {
          "type": "magazine_card",
          "settings": {
            "logo_image": "shopify://shop_images/new_York_times_logo.png",
            "logo_image_mobile": "shopify://shop_images/new_York_times_logo_981d7c5f-323f-4276-918a-22e9b01041b0.png",
            "magazine_name": "",
            "background_color": "#f7f7f7",
            "text_color": "#333333",
            "read_more_url": "https://nypost.com/2025/06/04/shopping/apartment-makeover-sicotas-furniture/",
            "read_more_text": "Read More"
          }
        }
      },
      "block_order": [
        "magazine_card_LRLW7x",
        "magazine_card_CUzdgm"
      ],
      "name": "Featured in Express",
      "settings": {
        "title": "Featured in Express"
      }
    },
    "social_img_hwdp6k": {
      "type": "social-img",
      "blocks": {
        "svg_jKp49e": {
          "type": "svg",
          "settings": {
            "pcimage": "shopify://shop_images/social1.png",
            "mbimage": "shopify://shop_images/mbsocial.png",
            "title": "Top-tier influencer",
            "text": "@inspirationbyblanca",
            "link": ""
          }
        },
        "svg_tWajBi": {
          "type": "svg",
          "settings": {
            "pcimage": "shopify://shop_images/soc2.png",
            "mbimage": "shopify://shop_images/mbsoc2.png",
            "title": "Top-tier influencer",
            "text": "@anubeauty.tips",
            "link": ""
          }
        }
      },
      "block_order": [
        "svg_jKp49e",
        "svg_tWajBi"
      ],
      "name": "社媒图片",
      "settings": {
        "title": "Live with SICOTAS"
      }
    }
  },
  "order": [
    "ecom-new-home-page-home",
    "slideshow_banner_qpF9wG",
    "shop_by_category_yJTzD7",
    "combination_product_kpmUmf",
    "slideshow_room_73frCM",
    "best_sellers_Dd4qtN",
    "furniture_showcase_KifYD7",
    "text_icon_gz3nmF",
    "pinglun_iUHWrp",
    "featured_in_express_AqyPhg",
    "social_img_hwdp6k"
  ]
}
