/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {}
    },
    "case_breadcrumb_DDLNcC": {
      "type": "case-breadcrumb",
      "name": "Breadcrumb Navigation",
      "settings": {
        "home_text": "Home",
        "separator": "·",
        "current_text": ""
      }
    },
    "case_banner_CHTMx7": {
      "type": "case-banner",
      "blocks": {
        "slide_g9tCai": {
          "type": "slide",
          "settings": {
            "type": "image",
            "image": "shopify://shop_images/Banner_1.png",
            "mobile_image": "shopify://shop_images/2_97e36d56-73d5-4cc1-9b58-f2d3f7c3a0ee.jpg",
            "video": "",
            "title": "",
            "description": ""
          }
        },
        "slide_fzXJbw": {
          "type": "slide",
          "settings": {
            "type": "image",
            "image": "shopify://shop_images/Banner_1_1.png",
            "mobile_image": "shopify://shop_images/cas_3_1.png",
            "video": "",
            "title": "",
            "description": ""
          }
        }
      },
      "block_order": [
        "slide_g9tCai",
        "slide_fzXJbw"
      ],
      "name": "Banner Slider",
      "settings": {
        "autoplay_delay": 100
      }
    },
    "case_graphic_section_mFaFxi": {
      "type": "case-graphic-section",
      "disabled": true,
      "custom_css": [],
      "name": "Content Section",
      "settings": {
        "logo_text": "Cas",
        "main_description": "For those looking to introduce a bit more texture and visual intrigue into their interiors, the Cas Collection is notable for its waveform \npanels that add depth and dimension to any space. While the wavy pattern might make a bold statement, the furniture comes in subtle \ncool-toned woods, from white to dark gray alder, and feels rather calming. The collection is named for Cassiopeia, a constellation \nrepresenting good luck, the fulfillment of wishes, and kindness and purity—all things you want to incorporate into your home! Whether \nyou're furnishing a cozy bedroom or a chic living room, Cas adds a layer of refinement without overwhelming the senses.",
        "logo_font_size": 100,
        "background_color": "#ffffff",
        "text_color": "#8f8884",
        "line_color": "#8f8884"
      }
    },
    "case_content_section_top_PaPbXk": {
      "type": "case-content-section-top",
      "name": "Content Section Top",
      "settings": {
        "logo_text": "Savanna",
        "description_text": "<p>The Savanna Collection is inspired by its namesake's expansive landscapes, exuding natural, rustic vibes with natural wood tones. The Savanna collection prominently features rattan, available in reclaimed caramel oak, light oak, and black oak finishes, underscoring SICOTAS' commitment to using low-impact materials. The clean lines and natural textures evoke the tranquility of the great outdoors, making it the perfect choice for those seeking to create a serene, grounded living space.</p>",
        "background_color": "#fcf7f2",
        "text_color": "#6d4c41",
        "line_color": "#6d4c41"
      }
    },
    "case_feature_section_F6MCjW": {
      "type": "case-feature-section",
      "name": "Feature Section 1",
      "settings": {
        "feature_image_1": "shopify://shop_images/Terra-Collection_1_dcfc561c-93bc-4141-ab0e-acda67770ef6.png",
        "feature_image_1_mobile": "shopify://shop_images/3_b74307ab-f6c2-40bb-9a74-6bb910e4796b.jpg",
        "feature_image_link": "https://www.sicotas.com/collections/savanna-nightstand",
        "open_link_new_tab": false,
        "feature_image_mobile_link": "https://www.sicotas.com/collections/savanna-nightstand",
        "open_mobile_link_new_tab": false,
        "feature_title_1": "Why You'll Love It:",
        "feature_title_2": "",
        "feature_description_1": "✓ 100% Natural Materials – Ethically sourced teak with zero synthetic finishes\n✓ Timeless Earthy Palette – From sand-beige rattan to amber wood stains\n✓ Breathable Comfort – Cane webbing adapts to humidity for lasting resilience",
        "feature_bg_color": "#c8a983"
      }
    },
    "case_product_showcase_1_EYVwUm": {
      "type": "case-product-showcase-1",
      "name": "Product Showcase 1",
      "settings": {
        "showcase_image_1": "shopify://shop_images/image_11_83622c0e-4031-47e2-be05-fdcc7627e919.png",
        "showcase_image_link_1": "https://www.sicotas.com/collections/bookcases/products/rattan-bookshelves-with-doors",
        "open_image_link_new_tab_1": false,
        "showcase_title_1": "Bookcases",
        "showcase_link_1": "https://www.sicotas.com/collections/bookcases/products/rattan-bookshelves-with-doors",
        "open_link_new_tab_1": false,
        "showcase_image_2": "shopify://shop_images/5_2_e186c7d0-d89a-4f2f-8598-04232d82c8ca.png",
        "showcase_image_link_2": "https://www.sicotas.com/collections/savanna-buffet",
        "open_image_link_new_tab_2": false,
        "showcase_title_2": "Buffet Cabinets",
        "showcase_link_2": "https://www.sicotas.com/collections/savanna-buffet",
        "open_link_new_tab_2": false,
        "button_bg_color": "#ffffffb3",
        "button_bg_hover_color": "#ffffff",
        "button_text_color": "#6d4c41",
        "button_text_hover_color": "",
        "button_svg_color": "#6d4c41",
        "button_svg_hover_color": ""
      }
    },
    "case_feature_section_t_N6UwFz": {
      "type": "case-feature-section-t",
      "name": "Feature Section 2",
      "settings": {
        "feature_image": "shopify://shop_images/Nightstand_with_one_drawer_A1_1_d3d5a47e-37ba-4c40-bc89-11e7794ff721.png",
        "feature_image_mobile": "shopify://shop_images/1_dfc36d36-838a-4fb3-94d6-46b30b600864.jpg",
        "feature_image_link": "https://www.sicotas.com/collections/savanna-nightstand",
        "open_image_link_new_tab": false,
        "feature_image_mobile_link": "",
        "open_mobile_image_link_new_tab": false,
        "feature_title": "Nightstands",
        "feature_description": "Drift into serenity with our nature-toned wooden bedroom collection, where handwoven rattan meets sustainably harvested oak. Each grain tells a forest’s story, every cane stitch breathes with organic rhythm—crafting a sleep sanctuary that calms the soul.",
        "feature_link": "https://www.sicotas.com/collections/savanna-nightstand",
        "feature_bg_color": "#886a50"
      }
    },
    "case_product_showcase_2_dwDyq7": {
      "type": "case-product-showcase-2",
      "name": "Product Showcase 2",
      "settings": {
        "showcase_image_1": "shopify://shop_images/image_12_f3f1f793-819f-45b4-9d97-ce16a3ad51b5.png",
        "showcase_image_link_1": "https://www.sicotas.com/collections/savanna-sideboards-1",
        "open_image_link_new_tab_1": false,
        "showcase_title_1": "Sideboards",
        "showcase_link_1": "https://www.sicotas.com/collections/savanna-sideboards-1",
        "open_link_new_tab_1": false,
        "showcase_image_2": "shopify://shop_images/image_94ce4511-bf5d-4211-bf18-43b2863502e0.png",
        "showcase_image_link_2": "https://www.sicotas.com/collections/savanna-shoe-cabinets",
        "open_image_link_new_tab_2": false,
        "showcase_title_2": "Shoes Cabinets",
        "showcase_link_2": "https://www.sicotas.com/collections/savanna-shoe-cabinets",
        "open_link_new_tab_2": false,
        "button_bg_color": "#ffffffb3",
        "button_bg_hover_color": "#ffffff",
        "button_text_color": "#6d4c41",
        "button_text_hover_color": "",
        "button_svg_color": "#6d4c41",
        "button_svg_hover_color": ""
      }
    },
    "case_product_grid_daPnA6": {
      "type": "case-product-grid",
      "name": "Product Grid Module",
      "settings": {
        "grid_title": "Top 10",
        "featured_collection": "top-ten"
      }
    }
  },
  "order": [
    "main",
    "case_breadcrumb_DDLNcC",
    "case_banner_CHTMx7",
    "case_graphic_section_mFaFxi",
    "case_content_section_top_PaPbXk",
    "case_feature_section_F6MCjW",
    "case_product_showcase_1_EYVwUm",
    "case_feature_section_t_N6UwFz",
    "case_product_showcase_2_dwDyq7",
    "case_product_grid_daPnA6"
  ]
}
