{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the desktop navigation.

  Accepts:
  - main_menu {linklist} - The main menu object
  - hover_menu {boolean} - Whether to show the menu on hover
  - nav_position {string} - The navigation position
  - show_mega_products {boolean} - Whether to show mega products
  - show_mega_collections {boolean} - Whether to show mega collections

  Usage:
  {% render 'header-desktop-nav', hover_menu: true %}
{%- endcomment -%}

{%- liquid
  assign main_menu = main_menu | default: section.settings.main_menu_link_list
  assign hover_menu = hover_menu | default: section.settings.hover_menu, allow_false: true | default: false, allow_false: true
  assign nav_position = nav_position | default: 'below'
  assign show_mega_products = show_mega_products | default: section.settings.mega_products, allow_false: true | default: true, allow_false: true
  assign show_mega_collections = show_mega_collections | default: section.settings.mega_collections, allow_false: true
  assign megamenu_type = 'products'

  if show_mega_collections
    assign show_mega_products = false
    assign megamenu_type = 'collections'
  endif

  unless limit
    assign limit = main_menu.links.size
  endunless

  unless offset
    assign offset = 0
  endunless
-%}
<style>
.page-width.image_or_listurl{
  /* padding:24px 30px */
}
.site-nav__dropdown.second, .megamenu{
  padding: 24px 0 42px;
  background: #F7F7F7;
}
.page-width.image_or_listurl .megamenu__wrapper{
  justify-content: center;
  gap: 90px;
}
.image_or_listurl .megamenu__cols {
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex: unset;
    flex-flow: unset;
    columns: unset;
    gap: 80px;
}
.page-width.image_or_listurl .erlink__cols{
  padding: 0;
}
.page-width.image_or_listurl .erlink__cols .megamenu__col-title{
  margin-bottom: 10px;
}
.page-width.image_or_listurl .erlink__cols.collection .megamenu__col-title{
  margin-bottom: unset;
}
.page-width.image_or_listurl .erlink__cols .site-nav__dropdown-link{

font-weight: 400;
font-style: Regular;
font-size: 14px;
text-decoration: none;
line-height: 26px;
}
.page-width.image_or_listurl .erlink__cols .site-nav__dropdown-link.site-nav__dropdown-link--top-level{
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    line-height: 28px;
 
}
.page-width.image_or_listurl .erlink__cols .site-nav__dropdown-link .hottext{
  font-weight: 600;
    font-style: Semibold;
    font-size: 14px;
    leading-trim: NONE;
    /* line-height: 1.2; */
    letter-spacing: 0px;
    text-align: center;
    padding: 0 15px 2px;
    border-radius: 30px;
    margin-left: 8px;
}
.page-width.image_or_listurl .erlink__cols .site-nav__dropdown-link:hover,.erimage_cols a:hover{
  color:#6D4C41;
  background-color: unset;
}
.page-width.image_or_listurl .megamenu__featured {
  display: flex;
    gap: 30px;
    justify-content: center;
    flex: unset;
    padding-bottom: unset;
    margin-top: 5px;
}
.erimage_cols img{
  height: auto;
}
.erimage_cols a{

font-weight: 400;
font-style: Regular;
font-size: 14px;
text-decoration: underline;
line-height: 28px;
}
.unshow{
  display: none;
}

</style>
<ul class="site-nav site-navigation site-navigation--{{ nav_position }} small--hide">
  {%- for link in main_menu.links limit: limit offset: offset -%}
    {%- liquid
      assign has_dropdown = false
      assign is_megamenu = false

      if link.levels > 0
        assign has_dropdown = true
        if link.levels > 1
          assign is_megamenu = true
        endif
      endif

      assign is_collection = false
      if show_mega_products or show_mega_collections
        if is_megamenu and link.url contains routes.collections_url
          assign collection_handle = link.url | remove: routes.collections_url | remove: '/'
          assign collection_drop = collections[collection_handle]
          assign is_collection = true
        endif
      endif
    -%}
    {% assign second_titlep = false %}
    {% assign second_image = false %}
    {% assign second_collection = false %}
    {% for block in section.blocks  %}
        
      {% case block.type %}
      
        
           {% when 'erlink' %}

            
            {% assign has_title_erlink = main_menu.links | where: 'title', block.settings.pare_name %}

            

            {% for ertitle in  has_title_erlink %}
              {% if ertitle.title ==  link.title%}
                {% assign second_titlep = true %}
              {% endif  %}
             {% endfor %}

             {% when 'danimage' %}
              {% assign has_title_image = main_menu.links | where: 'title', block.settings.pare_name %}
              {% for timage in  has_title_image %}
                {% if timage.title ==  link.title%}
                  {% assign second_image = true %}
                {% endif  %}
              {% endfor %} 
            {% when 'collectionlist' %}
              {% assign has_title_collection = main_menu.links | where: 'title', block.settings.pare_name %}
              {% for ecollection in  has_title_collection %}
                {% if ecollection.title ==  link.title%}
                  {% assign second_collection = true %}
                {% endif  %}
              {% endfor %}

      {% endcase %} 
    {% endfor %}

    <li class="site-nav__item site-nav__expanded-item{% if has_dropdown or second_titlep or second_image or second_collection %} site-nav--has-dropdown{% endif %}{% if is_megamenu or second_titlep or second_image or second_collection %} site-nav--is-megamenu{% endif %}" {{second_titlep}}---8888>
      {% if is_megamenu or has_dropdown or second_titlep%}
        <nav-dropdown>
          <details
            id="site-nav-item--{{ forloop.index }}"
            class="site-nav__details"
            data-hover="{{ hover_menu }}"
          >
      {% endif %}
      {% if is_megamenu or has_dropdown or second_titlep or second_image or second_collection %}
        <summary
          data-link="{{ link.url }}"
          aria-expanded="false"
          aria-controls="site-nav-item--{{ forloop.index }}"
          class="site-nav__link site-nav__link--underline{% if has_dropdown %} site-nav__link--has-dropdown{% endif %}"
        >
          {{ link.title }}
          {% render 'icon', name: 'chevron-down' %}
        </summary>
      {% endif %}
      {% unless is_megamenu or has_dropdown or second_titlep or second_image or second_collection %}
        <a
          href="{{ link.url }}"
          class="site-nav__link site-nav__link--underline{% if has_dropdown %} site-nav__link--has-dropdown{% endif %}"
        >
          {{ link.title }}
        </a>
      {% endunless %}

      {% if second_titlep or second_image or second_collection %}


        {% comment %} | new {% endcomment %}

        <div class="site-nav__dropdown megamenu megamenu--products text-left second">
          <div class="page-width image_or_listurl">
            <div class="site-nav__dropdown-animate megamenu__wrapper">
              <div class="megamenu__cols {% unless second_titlep or second_collection %}unshow{% endunless %}" >
                {% comment %} {% for erlinks in unique_names_erlink %} {% endcomment %}
                  {% comment %} {{erlinks}}----8888 {% endcomment %}
                {% for block in section.blocks  %}
        
                  {% case block.type %}

                  {% when 'erlink' %}
                    {% if block.settings.pare_name == link.title%}
                  <div class="megamenu__col erlink__cols">
                    <div>

                      <div class="megamenu__col-title">
                        <a href="{{block.settings.er_url}}"
                          class="site-nav__dropdown-link site-nav__dropdown-link--top-level">
                          {{block.settings.link_list.title}}
                          </a>
                      </div>
                      {% for linke in block.settings.link_list.links %}
                      <a href="{{linke.url}}"
                        class="site-nav__dropdown-link">
                        {{linke.title}}
                        {% if block.settings.pare_name2 == linke.title %}

                          <span class="hottext" style="color:{{block.settings.hotc1}};background:{{block.settings.hotbg1}}">{{block.settings.hottext}}</span>
                        {% endif %}
                        {% if block.settings.pare_name3 == linke.title %}
                          <span class="hottext" style="color:{{block.settings.hotc2}};background:{{block.settings.hotbg2}}">{{block.settings.hottext2}}</span>
                        {% endif %}
                        {% if block.settings.pare_name4 == linke.title %}
                          <span class="hottext" style="color:{{block.settings.hotc3}};background:{{block.settings.hotbg3}}">{{block.settings.hottext3}}</span>
                        {% endif %}
                      </a>
                      {% endfor %}
                    </div>
                  </div>
                    {% endif %}
             

                {% when 'collectionlist' %}
                  {% if block.settings.pare_name == link.title%}
                    <div class="megamenu__col erlink__cols collection">
                      <div>
                        {% for rcollection in block.settings.collection_list %}
                        <div class="megamenu__col-title">
                          <a href="{{rcollection.url}}"
                            class="site-nav__dropdown-link site-nav__dropdown-link--top-level">
                            {{rcollection.title}}
                            </a>
                        </div>
                      {% endfor %}
                      </div>
                    </div>
                    
                  {% endif %}
              {% endcase %}
          {% endfor %}
              </div>
              <div class="megamenu__featured {% unless second_image %}unshow{% endunless %}">
                {% for block in section.blocks  %}
        
                  {% case block.type %}
                
                {% when 'danimage' %}
                  {% if block.settings.pare_name == link.title%}
                  <div class="erimage_cols">
                    <div class="image-text-url">
                      <img src="{{block.settings.slider_image | image_url: width:290 }}" alt="{{block.settings.slider_image.alt}}">
                      <a href="{{block.settings.texturl}}" class="image-url">{{block.settings.text}}</a>
    
                    </div>
                  </div>
                  {% endif %}
    
                   
                {% endcase %} 
              {% endfor %}
              </div>

              
            </div>
          </div>
        </div> 
   





{% comment %} newend {% endcomment %}

{% else %}

      {%- if is_megamenu -%}
        {%- assign previous_column_type = '' -%}
        <div class="site-nav__dropdown megamenu megamenu--{{ megamenu_type }} text-left">
          <div class="page-width">
            <div class="site-nav__dropdown-animate megamenu__wrapper">
              <div class="megamenu__cols">
                <div class="megamenu__col">
                  {%- for childlink in link.links -%}
                    {%- liquid
                      assign create_new_column = false

                      if childlink.levels > 0 and forloop.index != 1
                        assign create_new_column = true
                      endif

                      if childlink.levels == 0 and previous_column_type == 'full'
                        assign create_new_column = true
                      endif
                    -%}

                    {%- if create_new_column -%}
                      </div>
                    {%- endif -%}
                    {%- if create_new_column -%}
                      <div class="megamenu__col">
                    {%- endif -%}

                    {%- if childlink.levels > 0
                      and childlink.type == 'collection_link'
                      and childlink.object.featured_image != blank
                      and show_mega_collections
                    -%}
                      <a href="{{ childlink.url }}">
                        {%- render 'image-element',
                          img: childlink.object.featured_image,
                          sizeVariable: '20vw',
                          alt: childlink.object.title,
                          classes: 'megamenu__collection-image'
                        -%}
                      </a>
                    {%- endif -%}

                    <div>
                      <div class="megamenu__col-title">
                        <a
                          href="{{ childlink.url }}"
                          class="site-nav__dropdown-link site-nav__dropdown-link--top-level"
                        >
                          {{- childlink.title -}}
                        </a>
                      </div>

                      {%- liquid
                        if childlink.levels > 0
                          assign previous_column_type = 'full'
                        else
                          assign previous_column_type = 'single'
                        endif
                      -%}

                      {%- for grandchildlink in childlink.links -%}
                        <a href="{{ grandchildlink.url }}" class="site-nav__dropdown-link">
                          {{ grandchildlink.title }}
                        </a>
                      {%- endfor -%}
                    </div>
                  {%- endfor -%}
                </div>
              </div>
              {%- if is_collection and show_mega_products -%}
                <div class="megamenu__featured">
                  <div class="product-grid">
                    {%- liquid
                      assign mega_product = collection_drop.products.first
                      render 'product-grid-item', product: mega_product, sizeVariable: '19vw'
                    -%}
                  </div>
                </div>
              {%- endif -%}
            </div>
          </div>
        </div>
      {%- elsif has_dropdown -%}
        <div class="site-nav__dropdown">
          <ul class="site-nav__dropdown-animate site-nav__dropdown-list text-left">
            {%- for childlink in link.links -%}
              {%- liquid
                assign has_sub_dropdown = false
                if childlink.levels > 0
                  assign has_sub_dropdown = true
                endif
              -%}

              <li class="{% if has_sub_dropdown %} site-nav__deep-dropdown-trigger{% endif %}">
                <a
                  href="{{ childlink.url }}"
                  class="site-nav__dropdown-link site-nav__dropdown-link--second-level{% if has_sub_dropdown %} site-nav__dropdown-link--has-children{% endif %}"
                >
                  {{ childlink.title | escape }}
                  {%- if has_sub_dropdown -%}
                    {% render 'icon', name: 'chevron-down' %}
                  {%- endif -%}
                </a>
                {%- if has_sub_dropdown -%}
                  <ul class="site-nav__deep-dropdown">
                    {%- for grandchildlink in childlink.links -%}
                      <li>
                        <a href="{{ grandchildlink.url }}" class="site-nav__dropdown-link">
                          {{- grandchildlink.title | escape -}}
                        </a>
                      </li>
                    {%- endfor -%}
                  </ul>
                {%- endif -%}
              </li>
            {%- endfor -%}
          </ul>
        </div>
      {%- endif -%}

      


{% endif %}

      {% if is_megamenu or has_dropdown or second_titlep %}
        </details>
        </nav-dropdown>
        <script type="module">
          import '@archetype-themes/modules/nav-dropdown'
        </script>
      {% endif %}
    </li>
  {%- endfor -%}
</ul>
