{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the customer login section.

  Accepts:
  - hydration {string} - The hydration strategy

  Usage:
  {% render 'section-main-login' %}
{%- endcomment -%}

{%- liquid
  assign hydration = hydration | default: 'on:visible'
-%}

<is-land {{ hydration }}>
  <section-main-login>
    <style>

  #MainContent{
    background: #fff;
  }
  .form-login-register .input-full{
    /* width: 300px; */
    /* max-width: 300px; */
    border-radius: 30px;
    color: #6D4C41 !important;
    background: #fff;
    border: 1px solid #F3E8DD;
  }
  .form-login-register .login-submit-form{
    /* width: 300px;
    max-width: 300px; */
    border-radius: 30px;
    background: #6D4C41;
    color: #F3E8DD;
    margin-bottom: 24px;
    /* font-family: <PERSON>Fang SC; */
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    leading-trim: NONE;
    /* line-height: 100%; */
    /* letter-spacing: 0px; */
    text-align: center;
  }
  .form-login-register .form-login-submit-p{
    margin: 0;
    padding: 0;
  }
  .form-login-register .login-submit-form:hover{
    background: #502212;
  }
  .oxi_social_wrapper:last-child{
    display: none;
  }

  .form-login-register .input-full:focus,.form-login-register .input-full:active,.form-login-register .input-full:focus-visible{
    background: #fff;
    border: 1px solid #6D4C41;
    border-color: #6D4C41;
    outline: none;
  }
  .page-width.page-width--tiny{
    margin: 0 auto;
    padding: 0 0;
    max-width: 100% !important;
  }
  .form-login-register .errors{
  background: #FFECD9;
  border: none !important;
}
.form-login-register .errors ul{
  list-style: none;
  color: #EB5E30;
}
  #CustomerLoginForm label,label.label-fullpaswword{
    margin-bottom: 10px;
    line-height: 1;
    font-weight: 500;
  }
  .form-vertical input{
    margin-bottom: 10px;
  }
  .form-login-email.fomr,.form-reg-email.fomr{
    margin-bottom: 24px;
  }
  .form-login-register{
    display: flex;
    gap: 150px;
    justify-content: center;
    padding: 30px 0 64px;
  }
  .fa-facebook-f:before, .fa-facebook:before {
    content: "\f09a";
    color: #4364A2;
    
}
.login-div{
   width: 300px;
}
.sl-vertical a.social_login{
  display: flex;
  background-color: #fff;
  border-radius: 30px;
  justify-content: center;
}
a.social_login.facebook,span,a.social_login.google span,a.social_login.amazon span{
  color:#000
}
.fa-google:before{
  color: red;
}
.form-login-register .required_span{
  font-family: PingFang SC;
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
    color: #666666;
    leading-trim: NONE;
    line-height: 100%;
    letter-spacing: 0px;
}
.hr_1{ 
  /* width: 1px;
  height: auto; 
  border: 1px solid #F3E8DD; */

} 
.label-info.forget{
  line-height: 1;
}
.label-info.forget a{
  color: #666666;
}
#RecoverPasswordForm {
  max-width: 300px;
}
#RecoverPasswordForm .section-header .section-header__title,.login-div .section-header .section-header__title{
  font-family: Playfair Display, sans-serif;
  font-size: 26px;
}
#RecoverPasswordForm .section-header,.login-div .section-header{
  margin-bottom: 25px;
  
}
label.RecoverEmail{
  font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    letter-spacing: 0px;
    line-height: 1;
    margin-bottom: 10px;
}
#RecoverPasswordForm  .form-vertical h2{
  margin-bottom: 5px;
    line-height: 1.3;
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    leading-trim: NONE;
    line-height: 100%;
    letter-spacing: 0px;
}
#RecoverPasswordForm  .form-vertical p{
    margin-bottom: 32px;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.5;
    letter-spacing: 0px;
    color: #666666;
}
#RecoverPasswordForm .form-actions-RecoverPasswordForm{
  display: flex;
  align-items: center;
  gap: 10px; 
}
#RecoverPasswordForm .form-actions-RecoverPasswordForm button{ 
  border-radius: 30px;
    
    width: 100%;
    background: #6D4C41;

}
#RecoverPasswordForm .form-actions-RecoverPasswordForm button:hover,
#RecoverPasswordForm .form-actions-RecoverPasswordForm #HideRecoverPasswordLink:hover{
  background: #502212;
  color: #F3E8DD;
}
#RecoverPasswordForm .form-actions-RecoverPasswordForm .btn{
  margin-bottom:unset;
  font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    leading-trim: NONE;
    /* line-height: 100%; */
    /* letter-spacing: 0px; */
    text-align: center;
    color: #F3E8DD;

}
#RecoverPasswordForm .form-actions-RecoverPasswordForm #HideRecoverPasswordLink{
  line-height: 1.42;
    -webkit-text-decoration: none;
    text-decoration: none;
    text-align: center;
    white-space: normal;
    font-size: calc(var(--type-base-size) + 2px);
    font-weight: var(--type-header-weight);
    display: inline-block;
    padding: var(--button-padding);
    margin: 0;
    
    color: #6D4C41;

    min-width: 90px;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid #fff0;
    user-select: none;
    -webkit-appearance: none;
    background: #F3E8DD;
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    leading-trim: NONE;
    /* line-height: 100%; */
    /* letter-spacing: 0px; */
    text-align: center;

}

.login-div .Create-Account-a{
display: none;
}
.login-div .text-center-spase{
  display: none;
}
@media screen and (max-width: 890px){
  #MainContent {
    background: #FCF7F2;
}
  .login-div{
    width: 100%;
  }
  .form-login-register{
    gap: unset;
    align-items: center;
    flex-direction: column;
    padding: 30px 26px 0;
  }
  .register-div{
    display: none;
  }
  .login-div .section-header__title{
    font-size: 20px; 
  }
  .form-login-register .login-submit-form{
    margin-bottom: 10px !important;
    height: 48px;
    max-width: 100%;
  }
  .hr_1{
    display: none;
  }
  #RecoverPasswordForm{
    width: 100%;
    max-width: 100%;
  }
  .login-div .Create-Account-a{
    border-radius: 30px;
    background: #F3E8DD;
    display: inline-block;
    width: 100%;
    line-height: 1.42;
    -webkit-text-decoration: none;
    text-decoration: none;
    text-align: center;
    white-space: normal;
    font-size: calc(var(--type-base-size) + 2px);
    font-weight: var(--type-header-weight);
    display: inline-block;
    padding: var(--button-padding);
    margin: 0;
    min-width: 90px;
    vertical-align: middle;
    cursor: pointer;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    border: 1px solid #fff0;
    color: #6D4C41;
    line-height: 1.3;
    height: 48px;
    max-width: 100%;
}
.login-div .text-center-upspase{
  display: none;
}
.form-login-register .input-full{
  height: 48px;
  padding: 13px 16px;
        font-size: 14px;
}
#CustomerLoginForm label, label.label-fullpaswword{
  font-size: 18px;
}
.label-info.forget a{
  font-weight: 400;
    font-style: Regular;
    font-size: 16px;

    text-align: right;
}
#RecoverPasswordForm .form-actions-RecoverPasswordForm .btn,#RecoverPasswordForm .form-actions-RecoverPasswordForm #HideRecoverPasswordLink{
  height: 48px;
}
label.RecoverEmail{
  font-weight: 500;
    font-style: Medium;
    font-size: 18px;
}
#ecom-footer{
  display: none;
}
.login-div .text-center-spase{
  
  display: block;
  font-weight: 400;
  font-style: Regular;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  text-transform: uppercase;
  color: #666666;
  position:relative
}
.login-div .text-center-spase::before,
.login-div .text-center-spase::after {
  position: absolute;
  content: "";
  flex: 1;
  border-top: 1px solid #ccc;
  width: 134px;
}

.login-div .text-center-spase::before {
  top: 50%;
  right: 55%; 
}

.login-div .text-center-spase::after {
  top: 50%;
  left: 55%;
}
.form-login-register .form-login-submit-p{
  margin-bottom: 30px;
}
}
</style>
    <div class="page-width page-width--tiny page-content">
      <div class="form-login-register">
        <div class="login-div" id="customerloginform">
          <header class="section-header">
            <h1 class="section-header__title">
              {% render 't_with_fallback', key: 'actions.login', fallback: 'Login' %}
            </h1>
          </header>

          <div class="note note--success hide" id="ResetSuccess">
            {% render 't_with_fallback',
              key: 'info.recovery_email_sent',
              fallback: "We've sent you an email with a link to update your password."
            %}
          </div>
          
            <div id="CustomerLoginForm" class="form-vertical">
              {%- form 'customer_login' -%}
                {{ form.errors | default_errors }}

                <label for="CustomerEmail">
                  {% render 't_with_fallback', key: 'labels.email', fallback: 'Email' -%}
                </label>
                <input 
                  type="email"
                  name="customer[email]"
                  id="CustomerEmail"
                  class="input-full{% if form.errors contains 'email' %} error{% endif %} form-login-email fomr"
                  autocorrect="off"
                  autocapitalize="off"
                  autofocus
                >

                {%- if form.password_needed -%}
                  <div class="float-grid clearfix">
                    <div class="grid__item one-half">
                      <label for="CustomerPassword">
                        {% render 't_with_fallback', key: 'labels.password', fallback: 'Password' -%}
                      </label>
                    </div>
                    <div class="grid__item one-half text-right">
                      <small class="label-info forget">
                        <a href="#recover" id="RecoverPassword">
                          {% render 't_with_fallback', key: 'actions.forgot_password', fallback: 'Forgot password?' %}
                        </a>
                      </small>
                    </div>
                  </div>
                  <input
                    type="password"
                    value=""
                    name="customer[password]"
                    id="CustomerPassword"
                    class="input-full{% if form.errors contains 'password' %} error{% endif %} form-login-email"
                  >
                {%- endif -%}

                <p class="form-login-submit-p">
                  <button type="submit" class="btn btn--full login-submit-form">
                    {% render 't_with_fallback', key: 'actions.sign_in', fallback: 'Sign In' %}
                  </button>
                  <a href="{{ routes.account_register_url }}" class="Create-Account-a">
                    Create Account
                  </a>
                </p>
                <p style="display: none;">
                  <a href="{{ routes.account_register_url }}">
                    {% render 't_with_fallback', key: 'actions.create_account', fallback: 'Create account' -%}
                  </a>
                </p>
                {% comment %} <p style="text-align: center;" class="text-center-upspase">
                  or
                </p>
                <p style="text-align: center;" class="text-center-spase">
                  OR
                </p> {% endcomment %}
              {%- endform -%}
            </div>
        </div>
        <div id="RecoverPasswordForm" class="hide" >
          <header class="section-header">
            <h1 class="section-header__title">
              {% render 't_with_fallback', key: 'actions.login', fallback: 'Login' %}
            </h1>
          </header>
        <div class="form-vertical">
          <h2>
            {% render 't_with_fallback', key: 'actions.reset_your_password', fallback: 'Reset your password' %}
          </h2>
          <p>
            {% render 't_with_fallback',
              key: 'info.email_to_reset_password',
              fallback: 'We will send you an email to reset your password.'
            %}
          </p>

          {%- form 'recover_customer_password' -%}
            {{ form.errors | default_errors }}

            {%- if form.posted_successfully? -%}
              <span class="hide reset-password-success"></span>
            {%- endif -%}

            <label for="RecoverEmail" class="RecoverEmail">
              {% render 't_with_fallback', key: 'labels.email', fallback: 'Email' -%}
            </label>
            <input
              type="email"
              value=""
              name="email"
              id="RecoverEmail"
              class="input-full"
              autocorrect="off"
              autocapitalize="off"
            >

            <p class="form-actions-RecoverPasswordForm">
              <button type="submit" class="btn">
                {% render 't_with_fallback', key: 'actions.submit', fallback: 'Submit' %}
              </button>
              <button type="button" id="HideRecoverPasswordLink">
                {% render 't_with_fallback', key: 'actions.cancel', fallback: 'Cancel' %}
              </button>
            </p>
           
          {%- endform -%}
        </div>
      </div>
        <div class="hr_1"><svg width="2" height="278" viewBox="0 0 2 278" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 0L1 278" stroke="#F3E8DD"/>
        </svg>
        </div>
        <div class="register-div">
          {% render 'section-main-register' %}
        </div>
      </div>
      
 
      {%- if shop.checkout.guest_login -%}
        <hr class="hr--clear">

        <h1>
          {% render 't_with_fallback', key: 'actions.continue_as_guest', fallback: 'Continue as a guest' %}
        </h1>
        <hr class="hr--small">

        {%- form 'guest_login' -%}
          <button type="submit" class="btn">
            {% render 't_with_fallback', key: 'actions.continue', fallback: 'Continue' %}
          </button>
        {%- endform -%}
      {%- endif -%}
    </div>
  </section-main-login>

  <template data-island>
    <script type="module">
      import 'components/section-main-login'
    </script>
  </template>
</is-land>
