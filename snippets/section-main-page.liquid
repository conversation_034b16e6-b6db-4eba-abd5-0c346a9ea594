{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a page section with its title and content.

  Accepts:
  - page {page} - The page object

  Usage:
  {% render 'section-main-page' %}
{%- endcomment -%}

<div class="page-width page-width--narrow page-content">
  <header class="section-header">
    <h1 class="section-header__title">
      {{ page.title | escape }}
    </h1>
  </header>

  {%- render 'rte', slot: page.content, class: 'rte--no-margin' -%}
</div>
