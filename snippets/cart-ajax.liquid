{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{% comment %}
  JS-load cart markup without bloat of a full layout.

  This is used in both the mini cart drawer and cart page.
  When a quantity is changed, this file is scraped and data-products
    is fully replaced to account for possible cart discounts changing

  The cart-wide discount div also replaces what is originally in the cart
    as it can change anytime a cart-item changes
{% endcomment %}
<div
  class="cart__items"
  data-count="{{ cart.item_count }}"
>
  {% for item in cart.items %}
    {%- render 'cart-item',
      item: item,
      section: section,
      sizes: '100px',
      routes: routes,
      shop: shop,
      superscript_decimals: settings.superscript_decimals
    -%}
  {% endfor %}
</div>

<div class="cart__discounts text-right{% if cart.cart_level_discount_applications == blank %} hide{% endif %}">
  <div>
    {% for cart_discount in cart.cart_level_discount_applications %}
      {%- assign savings = cart_discount.total_allocated_amount | money -%}
      <div class="cart__discount">
        {% assign info_you_save_amount = 'info.you_save_amount' | t: saved_amount: savings %}
        {%- capture fallback_info_you_save_amount -%}
          You save {{ savings }}
        {%- endcapture -%}
        {% render 't_with_fallback', t: info_you_save_amount, fallback: fallback_info_you_save_amount %}
        ({{ cart_discount.title }})
      </div>
    {% endfor %}
  </div>
</div>

<div class="cart__subtotal">
  {%- render 'price', price: cart.total_price -%}
</div>

<span class="cart-link__bubble{% if cart.item_count > 0 %} cart-link__bubble--visible{% endif %}">
  <span class="cart-link__bubble-num">{{ cart.item_count }}</span>
</span>
