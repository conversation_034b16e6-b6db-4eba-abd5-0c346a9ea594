{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a FAQ section with a list of questions and answers.

  Accepts:
  - title {string} - The title of the section
  - heading_size {'h0'|'h1'|'h2'|'h3'} - The size of the heading
  - heading_position {'left'|'center'|'right'} - The position of the heading
  - divider {boolean} - Whether to add a divider above the section

  Usage:
  {% render 'section-faq' %}
{%- endcomment -%}

{%- liquid
  assign title = title | default: section.settings.title
  assign heading_size = heading_size | default: section.settings.heading_size | default: 'h2'
  assign heading_position = heading_position | default: section.settings.heading_position | default: 'left'
  assign divider = divider | default: section.settings.divider | default: true
-%}

{%- if divider -%}<div class="section--divider">{%- endif -%}

<div class="page-width page-width--narrow">
  {%- if title != blank -%}
    <header class="section-header text-{{ heading_position }}">
      <h2 class="section-header__title {{ heading_size }}">
        {{ title | escape }}
      </h2>
    </header>
  {%- endif -%}

  {%- if section.blocks.size > 0 -%}
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
          {% for block in section.blocks %}
            {% case block.type %}
              {% when 'question' %}
                {
                  "@type": "Question",
                  "name": {{ block.settings.title | json }},
                  "acceptedAnswer": {
                    "@type": "Answer",
                    "text": {{ block.settings.text | json }}
                  }
                }{% unless forloop.last %},{% endunless %}
            {% endcase %}
          {% endfor %}
        ]
      }
    </script>

    {%- for block in section.blocks -%}
      <div {{ block.shopify_attributes }}>
        {% case block.type %}
          {% when 'rich-text' %}
            <div class="index-section index-section--faq">
              <div class="text-{{ block.settings.align_text }}">
                {%- if block.settings.title != blank -%}
                  <p class="h2">
                    {{ block.settings.title | escape }}
                  </p>
                {%- endif -%}
                {%- if block.settings.text != blank -%}
                  <div class="rte clearfix">
                    {%- if block.settings.enlarge_text %}<div class="enlarge-text">{% endif -%}
                    {{ block.settings.text }}
                    {%- if block.settings.enlarge_text %}</div>{% endif -%}
                  </div>
                {%- endif -%}
              </div>
            </div>

          {% when 'question' %}
            {%- capture slot_button -%}
              <span class="collapsible-trigger__layout">
                <span>
                  <span>{{ block.settings.title }}</span>
                </span>
                <span class="collapsible-trigger__icon collapsible-trigger__icon--open" role="presentation">
                  {% render 'icon', name: 'chevron-down' %}
                </span>
              </span>
            {%- endcapture -%}
            {%- render 'collapsible',
              id: block.id,
              slot_button: slot_button,
              slot_collapsible: block.settings.text,
              borders: true
            -%}
        {% endcase %}
      </div>
    {%- endfor -%}
  {%- endif -%}

  {%- render 'placeholder-noblocks' -%}
</div>

{%- if divider -%}</div>{%- endif -%}
