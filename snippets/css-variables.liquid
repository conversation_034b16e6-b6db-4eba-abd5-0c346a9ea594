{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the list of CSS variables. Typically, this is referenced inside of a
  theme's layout file.

  Usage:
  {% render 'css-variables' %}
{%- endcomment -%}

{%- style -%}
  :root {
    --color-body: {{ settings.color_body_bg | default: "#fff" }};
    --color-body-alpha-005: {{ settings.color_body_bg | default: "#fff" | color_modify: 'alpha', 0.05 }};
    --color-body-dim: {{ settings.color_body_bg | default: "#1c1d1d" | color_darken: 5 }};

    --color-border: {{ settings.color_borders | default: "#1c1d1d" }};

    --color-button-primary: {{ settings.color_button | default: "#000" }};
    --color-button-primary-light: {{ settings.color_button | default: "#000" | color_lighten: 10 }};
    --color-button-primary-dim: {{ settings.color_button | default: "#000" | color_darken: 5 }};
    --color-button-primary-text: {{ settings.color_button_text | default: "#fff" }};

    --color-cart-dot: {{ settings.color_cart_dot | default: "#ff4f33" }};
    --color-cart-dot-text: {{ settings.color_cart_dot_text | default: "#fff" }};

    --color-footer: {{ settings.color_footer | default: "#111" }};
    --color-footer-border: {{ settings.color_footer_border | default: "#222" }};
    --color-footer-text: {{ settings.color_footer_text | default: "#fff" }};

    --color-link: {{ settings.color_body_text | default: "#1c1d1d" }};

    --color-modal-bg: {{ settings.color_large_image_bg | default: "#1c1d1d" | color_modify: 'alpha', 0.6 }};

    --color-nav: {{ settings.color_header | default: "#fff" }};
    --color-nav-search: {{ settings.color_header_search | default: "#fff" }};
    --color-nav-text: {{ settings.color_header_text | default: "#000" }};

    --color-price: {{ settings.color_price | default: "#1c1d1d" }};

    --color-sale-tag: {{ settings.color_sale_tag | default: "#1c1d1d" }};
    --color-sale-tag-text: {{ settings.color_sale_tag_text | default: "#ffffff" }};

    --color-scheme-1-text: {{ settings.color_scheme_1_text | default: "#000" }};
    --color-scheme-1-bg: {{ settings.color_scheme_1_bg | default: "#fff" }};
    --color-scheme-2-text: {{ settings.color_scheme_2_text | default: "#000" }};
    --color-scheme-2-bg: {{ settings.color_scheme_2_bg | default: "#fff" }};
    --color-scheme-3-text: {{ settings.color_scheme_3_text | default: "#000" }};
    --color-scheme-3-bg: {{ settings.color_scheme_3_bg | default: "#fff" }};

    --color-text-body: {{ settings.color_body_text | default: "#1c1d1d" }};
    --color-text-body-alpha-005: {{ settings.color_body_text | default: "#1c1d1d" | color_modify: 'alpha', 0.05 }};
    --color-text-body-alpha-008: {{ settings.color_body_text | default: '#1c1d1d' | color_modify: 'alpha', 0.08 }};
    --color-text-savings: {{ settings.color_savings_text | default: "#1c1d1d" }};

    --color-toolbar: {{ settings.color_announcement | default: "#fff" }};
    --color-toolbar-text: {{ settings.color_announcement_text | default: "#000" }};

    --url-ico-select: url({{ 'ico-select.svg' | asset_url | split: '?' | first }});
    --url-swirl-svg: url({{ 'swirl.svg' | asset_url | split: '?' | first }});

    --header-padding-bottom: 0;

    --page-top-padding: 35px;
    --page-narrow: 780px;
    --page-width-padding: 40px;
    --grid-gutter: 22px;
    --index-section-padding: 60px;
    --section-header-bottom: 40px;
    --collapsible-icon-width: 12px;

    --size-chart-margin: 30px 0;
    --size-chart-icon-margin: 5px;

    --newsletter-reminder-padding: 20px 30px 20px 25px;

    --text-frame-margin: 10px;

    /*Shop Pay Installments*/
    --color-body-text: {{ settings.color_body_text | default: "#1c1d1d" }};
    --color-body: {{ settings.color_body_bg | default: "#fff" }};
    --color-bg: {{ settings.color_body_bg | default: "#fff" }};

    --type-header-primary: {{ settings.type_header_font_family.family }};
    --type-header-fallback: {{ settings.type_header_font_family.fallback_families }};
    --type-header-size: {{ settings.type_header_base_size | default: '32' | append: 'px' }};
    --type-header-weight: {{ settings.type_header_font_family.weight }};
    --type-header-line-height: {{ settings.type_header_line_height | default: 1.4 }};
    --type-header-spacing: {{ settings.type_header_spacing | default: '25' | divided_by: 1000.00 | append: 'em' }};

    {% if settings.type_header_capitalize %}
      --type-header-transform: uppercase;
    {% else %}
      --type-header-transform: none;
    {% endif %}

    --type-base-primary:{{ settings.type_base_font_family.family }};
    --type-base-fallback:{{ settings.type_base_font_family.fallback_families }};
    --type-base-size: {{ settings.type_base_size | default: 16 | append: 'px' }};
    --type-base-weight: {{ settings.type_base_font_family.weight }};
    --type-base-spacing: {{ settings.type_base_spacing | default: '50' | divided_by: 1000.00 | append: 'em' }};
    --type-base-line-height: {{ settings.type_base_line_height | default: 1.4 }};

    --color-small-image-bg: {{ settings.color_small_image_bg | default: '#eee' }};
    --color-small-image-bg-dark: {{ settings.color_small_image_bg | default: '#eee' | color_darken: 3 }};
    --color-large-image-bg: {{ settings.color_large_image_bg | default: '#1c1d1d' }};
    --color-large-image-bg-light: {{ settings.color_large_image_bg | default: '#1c1d1d' | color_lighten: 13 }};

    --icon-stroke-width: {{ settings.icon_weight | default: '5px' }};
    --icon-stroke-line-join: {{ settings.icon_linecaps | default: 'miter' }};

    {% if settings.button_style == 'round-slight' %}
      --button-radius: 3px;
      --button-padding: 11px 25px;
    {% elsif settings.button_style == 'round' %}
      --button-radius: 50px;
      --button-padding: 11px 25px;
    {% else %}
      --button-radius: 0;
      --button-padding: 11px 20px;
    {% endif %}

    {% if settings.edges == 'round' %}
      --roundness: 20px;
    {% else %}
      --roundness: 0;
    {% endif %}

    {% if settings.product_grid_style == 'gridlines-thick' %}
      --grid-thickness: 2px;
    {% elsif settings.product_grid_style == 'gridlines-thin' %}
      --grid-thickness: 1px;
    {% else %}
      --grid-thickness: 0;
    {% endif %}

    --product-tile-margin: {{ settings.product_grid_image_margin | default: '10' }}%;
    --collection-tile-margin: {{ settings.collection_grid_image_margin | default: '10' }}%;

    --swatch-size: {{ settings.swatch_size | default: '40' }}px;

    {% if settings.swatch_style == 'round' %}
      --swatch-border-radius: 50%;
    {% else %}
      --swatch-border-radius: 0;
    {% endif %}
  }

  @media screen and (max-width: 768px) {
    :root {
      --page-top-padding: 15px;
      --page-narrow: 330px;
      --page-width-padding: 17px;
      --grid-gutter: 16px;
      --index-section-padding: 40px;
      --section-header-bottom: 25px;
      --collapsible-icon-width: 10px;
      --text-frame-margin: 7px;
      --type-base-size: {{ settings.type_base_size | default: 16 | minus: 2 | append: 'px' }};

      {% if settings.edges == 'round' %}
        --roundness: 15px;
        --button-padding: 9px 25px;
      {% else %}
        --roundness: 0;
        --button-padding: 9px 17px;
      {% endif %}
    }
  }
{%- endstyle -%}
