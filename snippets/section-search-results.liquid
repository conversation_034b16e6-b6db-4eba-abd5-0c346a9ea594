{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a section with search results.

  Accepts:
  - predictive_search_show_vendor {boolean} - Whether to show the vendor in the predictive search results
  - predictive_search_show_price {boolean} - Whether to show the price in the predictive search results
  - product_save_amount {boolean} - Whether to show the amount saved on a product
  - product_save_type {'dollar'|'percent'} - The type of amount saved to show
  - prefix_enable {boolean} - Whether to show the prefix on the product

  Usage:
  {% render 'section-search-results' %}
{%- endcomment -%}

{% liquid
  assign predictive_search_show_price = predictive_search_show_price | default: section.settings.predictive_search_show_price, allow_false: true | default: true, allow_false: true
  assign predictive_search_show_vendor = predictive_search_show_vendor | default: section.settings.predictive_search_show_vendor, allow_false: true | default: true, allow_false: true
  assign product_save_amount = product_save_amount | default: settings.product_save_amount, allow_false: true | default: true, allow_false: true
  assign product_save_type = product_save_type | default: settings.product_save_type | default: 'dollar'
  assign prefix_enable = prefix_enable | default: settings.prefix_enable, allow_false: true | default: false, allow_false: true

  if predictive_search_show_price
    assign predictive_search_show_price = true
  endif

  if predictive_search_show_vendor
    assign predictive_search_show_vendor = true
  endif

  assign resources = predictive_search.resources
  assign columnCount = resources.queries.size | plus: resources.pages.size | plus: resources.articles.size | plus: resources.collections.size
  assign totalCount = resources.queries.size | plus: resources.pages.size | plus: resources.articles.size | plus: resources.collections.size | plus: resources.products.size
%}

{%- if predictive_search.performed -%}
  <div
    class="predictive-search-results {% if totalCount == 0 %}predictive-search-results--none{% endif %}"
    id="predictive-search-results"
  >
    {% if totalCount > 0 %}
      <div
        class="results__group-1"
        {% if columnCount == 0 %}
          style="display: none"
        {% endif %}
      >
        {%- if predictive_search.resources.queries.size > 0 -%}
          <div class="results results--queries">
            <h3 class="h4" id="predictive-search-suggestions">
              {% render 't_with_fallback', key: 'labels.suggestions', fallback: 'Suggestions' %}
            </h3>
            <ul role="listbox" aria-labelledby="predictive-search-queries">
              {%- for query in predictive_search.resources.queries -%}
                <li role="option">
                  <a href="{{ query.url }}">
                    <span>{{ query.styled_text }}</span>
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
        {%- if predictive_search.resources.pages.size > 0 -%}
          <div class="results results--pages">
            <h3 class="h4" id="predictive-search-pages">
              {% render 't_with_fallback', key: 'labels.pages', fallback: 'Pages' %}
            </h3>
            <ul role="listbox" aria-labelledby="predictive-search-pages">
              {%- for page in predictive_search.resources.pages -%}
                <li role="option">
                  <a href="{{ page.url }}">
                    <span>{{ page.title }}</span>
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
        {%- if predictive_search.resources.articles.size > 0 -%}
          <div class="results results--articles">
            <h3 class="h4" id="predictive-search-articles">
              {% render 't_with_fallback', key: 'labels.articles', fallback: 'Articles' %}
            </h3>
            <ul role="listbox" aria-labelledby="predictive-search-articles">
              {%- for article in predictive_search.resources.articles -%}
                <li role="option">
                  <a href="{{ article.url }}">
                    <span>{{ article.title }}</span>
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
        {%- if predictive_search.resources.collections.size > 0 -%}
          <div class="results results--collections">
            <h3 class="h4" id="predictive-search-collections">
              {% render 't_with_fallback', key: 'labels.collections', fallback: 'Collections' %}
            </h3>
            <ul role="listbox" aria-labelledby="predictive-search-collections">
              {%- for collection in predictive_search.resources.collections -%}
                <li role="option">
                  <a href="{{ collection.url }}">
                    <span>{{ collection.title }}</span>
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
      </div>

      <div class="results__group-2">
        {%- if predictive_search.resources.products.size > 0 -%}
          <div class="results results--products">
            <h3 class="h4" id="predictive-search-products">
              {% render 't_with_fallback', key: 'labels.products', fallback: 'Products' %}
            </h3>
            <ul role="listbox" aria-labelledby="predictive-search-products">
              {%- for product in predictive_search.resources.products -%}
                <li role="option">
                  <a href="{{ product.url }}">
                    <div class="results-products__image grid__image-ratio">
                      {% if product.media != blank %}
                        {%- render 'image-element',
                          img: product.featured_media,
                          widths: '80, 160, 240',
                          classes: 'animate-instant'
                        -%}
                      {% endif %}
                    </div>
                    <div class="results-products__info">
                      {%- if prefix_enable and product.metafields.archetype.prefix.value != blank -%}
                        <span class="grid-product__vendor">
                          {{- product.metafields.archetype.prefix.value | escape -}}
                        </span>
                      {%- endif -%}

                      <span>{{ product.title }}</span>
                      {% if predictive_search_show_vendor %}
                        <span class="grid-product__vendor">{{ product.vendor }}</span>
                      {% endif %}
                      {% if predictive_search_show_price %}
                        {%- if on_sale -%}
                          <span class="visually-hidden">
                            {% render 't_with_fallback', key: 'labels.regular_price', fallback: 'Regular price' -%}
                          </span>
                          <span class="grid-product__price--original">{{ product.compare_at_price | money }}</span>
                          <span class="visually-hidden">
                            {% render 't_with_fallback', key: 'labels.sale_price', fallback: 'Sale price' -%}
                          </span>
                        {%- endif -%}

                        {%- if product.price_varies -%}
                          {%- assign price = product.price_min | money -%}
                          <span class="grid-product__price">
                            {%- assign labels_from_price_html = 'labels.from_price_html' | t: price: price %}
                            {%- capture fallback_labels_from_price_html -%}
                              <span>from</span> {{ price }}
                            {%- endcapture -%}
                            {% render 't_with_fallback',
                              t: labels_from_price_html,
                              fallback: fallback_labels_from_price_html
                            -%}
                          </span>
                        {%- else -%}
                          <span class="grid-product__price">{{ product.price | money }}</span>
                        {%- endif -%}
                        {%- if on_sale -%}
                          {%- if product_save_amount -%}
                            {%- liquid
                              if product_save_type == 'dollar'
                                assign saved_amount = product.compare_at_price | minus: product.price | money
                              else
                                assign saved_amount = product.compare_at_price | minus: product.price | times: 100.0 | divided_by: product.compare_at_price | round
                              endif
                            -%}
                            <span class="grid-product__price--savings">
                              {% assign info_save_amount = 'info.save_amount' | t: saved_amount: saved_amount %}
                              {%- capture fallback_info_save_amount -%}
                                Save {{ saved_amount }}
                              {%- endcapture -%}
                              {% render 't_with_fallback', t: info_save_amount, fallback: fallback_info_save_amount %}
                            </span>
                          {%- endif -%}
                        {%- endif -%}
                      {% endif %}
                    </div>
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
        {%- if predictive_search.resources.pages.size > 0 -%}
          <div class="results results--pages">
            <h3 class="h4" id="predictive-search-pages">
              {% render 't_with_fallback', key: 'labels.pages', fallback: 'Pages' %}
            </h3>
            <ul role="listbox" aria-labelledby="predictive-search-pages">
              {%- for page in predictive_search.resources.pages -%}
                <li role="option">
                  <a href="{{ page.url }}">
                    <span>{{ page.title }}</span>
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
        {%- if predictive_search.resources.articles.size > 0 -%}
          <div class="results results--articles">
            <h3 class="h4" id="predictive-search-articles">
              {% render 't_with_fallback', key: 'labels.articles', fallback: 'Articles' %}
            </h3>
            <ul role="listbox" aria-labelledby="predictive-search-articles">
              {%- for article in predictive_search.resources.articles -%}
                <li role="option">
                  <a href="{{ article.url }}">
                    <span>{{ article.title }}</span>
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
        {%- if predictive_search.resources.collections.size > 0 -%}
          <div class="results results--collections">
            <h3 class="h4" id="predictive-search-collections">
              {% render 't_with_fallback', key: 'labels.collections', fallback: 'Collections' %}
            </h3>
            <ul role="listbox" aria-labelledby="predictive-search-collections">
              {%- for collection in predictive_search.resources.collections -%}
                <li role="option">
                  <a href="{{ collection.url }}">
                    <span>{{ collection.title }}</span>
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
      </div>
    {% else %}
      <div class="results">
        <a
          class="predictive-search__no-results"
          href="{{ routes.search_url }}?q={{ predictive_search.terms | escape }}"
        >
          {% assign info_search_no_results_html = 'info.search_no_results_html' | t: terms: predictive_search.terms %}
          {%- capture fallback_info_search_no_results_html -%}
            Search for "{{ predictive_search.terms }}"
          {%- endcapture -%}
          {% render 't_with_fallback', t: info_search_no_results_html, fallback: fallback_info_search_no_results_html %}
        </a>
      </div>
    {% endif %}
  </div>

  {% if totalCount > 0 %}
    <button class="results__search-btn">
      {% assign actions_show_all_results = 'actions.show_all_results' | t: terms: predictive_search.terms %}
      {%- capture fallback_actions_show_all_results -%}
        Show all results for "{{ predictive_search.terms }}"
      {%- endcapture -%}
      {% render 't_with_fallback', t: actions_show_all_results, fallback: fallback_actions_show_all_results %}
      {% render 'icon', name: 'arrow-right' %}
    </button>
  {% endif %}
{%- endif -%}
