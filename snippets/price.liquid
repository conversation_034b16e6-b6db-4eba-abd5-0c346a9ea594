{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a formatted price.

  Accepts:
  - superscript_decimals {boolean} - Whether to superscript decimals
  - price {number} - Price to be formatted

  Usage:
  {% render 'price', price: price %}
{%- endcomment -%}

{%- liquid
  assign superscript_decimals = superscript_decimals | default: settings.superscript_decimals, allow_false: true | default: true, allow_false: true
  assign formatted_price = price | money

  unless shop.money_format contains 'money' or shop.money_format contains '.'
    if superscript_decimals
      if shop.money_format contains '{{amount}}' or shop.money_format contains '{{ amount }}'
        assign formatted_price = formatted_price | replace: '.', '<sup>' | append: '</sup>'
      elsif shop.money_format contains '{{amount_with_comma_separator}}' or shop.money_format contains '{{ amount_with_comma_separator }}'
        assign formatted_price = formatted_price | replace: ',', '<sup>' | append: '</sup>'
      endif
    endif
  endunless
-%}

<span aria-hidden="true">{{ formatted_price }}</span>
<span class="visually-hidden">{{ price | money }}</span>
