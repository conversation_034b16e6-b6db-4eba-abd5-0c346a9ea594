{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a 404 section.

  Usage:
  {% render 'section-main-404' %}
{%- endcomment -%}

<div class="page-width page-content text-center">
  <header class="section-header section-header--404">
    <h1 class="section-header__title">
      {% render 't_with_fallback', key: 'info.404_page_not_found', fallback: '404 Page Not Found' %}
    </h1>
    <div class="rte text-spacing clearfix">
      {% assign info_page_does_not_exist_html = 'info.page_does_not_exist_html' | t: url: routes.root_url %}
      {%- capture fallback_info_page_does_not_exist_html -%}
        <p>The page you were looking for does not exist. </p><p><a href='{{ routes.root_url }}'>Continue shopping</a></p>
      {%- endcapture -%}
      {% render 't_with_fallback', t: info_page_does_not_exist_html, fallback: fallback_info_page_does_not_exist_html %}
    </div>
  </header>
</div>
