<script type="importmap">
{
  "imports": {
    "@archetype-themes/custom-elements/base-media": "{{ 'base-media.js' | asset_url }}",
    "@archetype-themes/custom-elements/disclosure": "{{ 'disclosure.js' | asset_url }}",
    "@archetype-themes/custom-elements/header-search": "{{ 'header-search.js' | asset_url }}",
    "@archetype-themes/custom-elements/product-recommendations": "{{ 'product-recommendations.js' | asset_url }}",
    "@archetype-themes/custom-elements/swatches": "{{ 'swatches.js' | asset_url }}",
    "@archetype-themes/custom-elements/theme-element": "{{ 'theme-element.js' | asset_url }}",
    "@archetype-themes/modules/cart-form": "{{ 'cart-form.js' | asset_url }}",
    "@archetype-themes/modules/clone-footer": "{{ 'clone-footer.js' | asset_url }}",
    "@archetype-themes/modules/collection-sidebar": "{{ 'collection-sidebar.js' | asset_url }}",
    "@archetype-themes/modules/drawers": "{{ 'drawers.js' | asset_url }}",
    "@archetype-themes/modules/modal": "{{ 'modal.js' | asset_url }}",
    "@archetype-themes/modules/nav-dropdown": "{{ 'nav-dropdown.js' | asset_url }}",
    "@archetype-themes/modules/photoswipe": "{{ 'photoswipe.js' | asset_url }}",
    "@archetype-themes/modules/slideshow": "{{ 'slideshow.js' | asset_url }}",
    "@archetype-themes/utils/a11y": "{{ 'a11y.js' | asset_url }}",
    "@archetype-themes/utils/ajax-renderer": "{{ 'ajax-renderer.js' | asset_url }}",
    "@archetype-themes/utils/currency": "{{ 'currency.js' | asset_url }}",
    "@archetype-themes/utils/events": "{{ 'events.js' | asset_url }}",
    "@archetype-themes/utils/product-loader": "{{ 'product-loader.js' | asset_url }}",
    "@archetype-themes/utils/resource-loader": "{{ 'resource-loader.js' | asset_url }}",
    "@archetype-themes/utils/storage": "{{ 'storage.js' | asset_url }}",
    "@archetype-themes/utils/theme-editor-event-handler-mixin": "{{ 'theme-editor-event-handler-mixin.js' | asset_url }}",
    "@archetype-themes/utils/utils": "{{ 'utils.js' | asset_url }}",
    "@archetype-themes/vendors/flickity": "{{ 'flickity.js' | asset_url }}",
    "@archetype-themes/vendors/flickity-fade": "{{ 'flickity-fade.js' | asset_url }}",
    "@archetype-themes/vendors/in-view": "{{ 'in-view.js' | asset_url }}",
    "@archetype-themes/vendors/photoswipe-ui-default.min": "{{ 'photoswipe-ui-default.min.js' | asset_url }}",
    "@archetype-themes/vendors/photoswipe.min": "{{ 'photoswipe.min.js' | asset_url }}",
    "components/add-to-cart": "{{ 'add-to-cart.js' | asset_url }}",
    "components/announcement-bar": "{{ 'announcement-bar.js' | asset_url }}",
    "components/block-buy-buttons": "{{ 'block-buy-buttons.js' | asset_url }}",
    "components/block-price": "{{ 'block-price.js' | asset_url }}",
    "components/block-variant-picker": "{{ 'block-variant-picker.js' | asset_url }}",
    "components/cart-note": "{{ 'cart-note.js' | asset_url }}",
    "components/close-cart": "{{ 'close-cart.js' | asset_url }}",
    "components/collapsible": "{{ 'collapsible.js' | asset_url }}",
    "components/collection-mobile-filters": "{{ 'collection-mobile-filters.js' | asset_url }}",
    "components/gift-card-recipient-form": "{{ 'gift-card-recipient-form.js' | asset_url }}",
    "components/header-cart-drawer": "{{ 'header-cart-drawer.js' | asset_url }}",
    "components/header-drawer": "{{ 'header-drawer.js' | asset_url }}",
    "components/header-mobile-nav": "{{ 'header-mobile-nav.js' | asset_url }}",
    "components/header-nav": "{{ 'header-nav.js' | asset_url }}",
    "components/item-grid": "{{ 'item-grid.js' | asset_url }}",
    "components/map": "{{ 'map.js' | asset_url }}",
    "components/model-media": "{{ 'model-media.js' | asset_url }}",
    "components/newsletter-reminder": "{{ 'newsletter-reminder.js' | asset_url }}",
    "components/parallax-image": "{{ 'parallax-image.js' | asset_url }}",
    "components/predictive-search": "{{ 'predictive-search.js' | asset_url }}",
    "components/price-range": "{{ 'price-range.js' | asset_url }}",
    "components/product-images": "{{ 'product-images.js' | asset_url }}",
    "components/product-inventory": "{{ 'product-inventory.js' | asset_url }}",
    "components/quantity-selector": "{{ 'quantity-selector.js' | asset_url }}",
    "components/quick-add": "{{ 'quick-add.js' | asset_url }}",
    "components/quick-shop": "{{ 'quick-shop.js' | asset_url }}",
    "components/rte": "{{ 'rte.js' | asset_url }}",
    "components/section-advanced-accordion": "{{ 'section-advanced-accordion.js' | asset_url }}",
    "components/section-age-verification-popup": "{{ 'section-age-verification-popup.js' | asset_url }}",
    "components/section-background-image-text": "{{ 'section-background-image-text.js' | asset_url }}",
    "components/section-collection-header": "{{ 'section-collection-header.js' | asset_url }}",
    "components/section-countdown": "{{ 'section-countdown.js' | asset_url }}",
    "components/section-footer": "{{ 'section-footer.js' | asset_url }}",
    "components/section-header": "{{ 'section-header.js' | asset_url }}",
    "components/section-hotspots": "{{ 'section-hotspots.js' | asset_url }}",
    "components/section-image-compare": "{{ 'section-image-compare.js' | asset_url }}",
    "components/section-main-addresses": "{{ 'section-main-addresses.js' | asset_url }}",
    "components/section-main-cart": "{{ 'section-main-cart.js' | asset_url }}",
    "components/section-main-login": "{{ 'section-main-login.js' | asset_url }}",
    "components/section-more-products-vendor": "{{ 'section-more-products-vendor.js' | asset_url }}",
    "components/section-newsletter-popup": "{{ 'section-newsletter-popup.js' | asset_url }}",
    "components/section-password-header": "{{ 'section-password-header.js' | asset_url }}",
    "components/section-recently-viewed": "{{ 'section-recently-viewed.js' | asset_url }}",
    "components/section-testimonials": "{{ 'section-testimonials.js' | asset_url }}",
    "components/store-availability": "{{ 'store-availability.js' | asset_url }}",
    "components/theme-editor": "{{ 'theme-editor.js' | asset_url }}",
    "components/toggle-cart": "{{ 'toggle-cart.js' | asset_url }}",
    "components/toggle-menu": "{{ 'toggle-menu.js' | asset_url }}",
    "components/toggle-search": "{{ 'toggle-search.js' | asset_url }}",
    "components/tool-tip": "{{ 'tool-tip.js' | asset_url }}",
    "components/tool-tip-trigger": "{{ 'tool-tip-trigger.js' | asset_url }}",
    "components/variant-sku": "{{ 'variant-sku.js' | asset_url }}",
    "components/video-media": "{{ 'video-media.js' | asset_url }}",
    "nouislider": "{{ 'nouislider.js' | asset_url }}"
  }
}
</script>