{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a tool tip.

  Accepts:
  - context {string} - The context for the tool tip

  Usage:
  {% render 'tool-tip' %}
{%- endcomment -%}

<tool-tip data-tool-tip="{{ context }}">
  <div class="tool-tip__inner" data-tool-tip-inner>
    <button class="tool-tip__close btn btn--circle btn--icon" data-tool-tip-close="">
      {% render 'icon', name: 'close' %}
    </button>
    <div data-tool-tip-title>{{ page_title }}</div>
    <div class="tool-tip__content" data-tool-tip-content></div>
  </div>
</tool-tip>

<script type="module">
  import 'components/tool-tip'
</script>
