{% comment %}
  Component: header-mobile-nav
  Version: 3.0.1
  Source: Archetype Themes Limited Partnership © 2024
  License: Shopify Theme Store License
  https://www.shopify.com/legal/terms#9-additional-services

  Renders the mobile header navigation.

  Parameters:
  - container {string}       - The container ID
  - main_menu {linklist}     - The main menu object
  - text_direction {string}  - The store's text direction (ltr or rtl)
  - in_header {boolean}      - Whether the mobile nav is rendered inside the header

  Usage:
  {% render 'header-mobile-nav', container: 'MobileNav' %}
{% endcomment %}

{%- liquid
assign main_menu = main_menu | default: section.settings.main_menu_link_list
assign text_direction = text_direction | default: settings.text_direction
assign in_header = in_header | default: false, allow_false: true

if text_direction == 'rtl'
assign chevron_icon = 'chevron-left'
else
assign chevron_icon = 'chevron-right'
endif
-%}
<style>
  .site-header__drawer.is-active {
    height: 96vh;
  }

  .slide-nav__wrapper {
    height: 100% ;
  }

  .header-botton .header-button-top .slide-nav-top{
    display: flex;
    justify-content: center;
    gap: 11px;
  }

  .header-botton .header-button-top .slide-nav__link{
    flex-direction: column;
    padding: 15px 11px 15.43px;
  }

  .header-botton .header-button-top .slide-nav__item {
    padding: unset;
  }

  .header-botton .header-button-top .slide-nav__item .slide-nav__button,
  .header-botton .header-button-top .slide-nav__item .slide-nav__link 
  {
    background: #fcf7f2;
    border-radius: 10px;
  }

  .header-botton .header-button-top .slide-nav__link .text-title {
    margin-bottom: 8px;

    font-weight: 500;
    font-style: Medium;
    font-size: 16px;

    line-height: 1.3;
    letter-spacing: 0px;
  }

  .header-down .slide-nav-down {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .header-down .slide-nav__item {
    padding-top: unset;
  }

  .header-down .slide-nav__item .slide-nav__button {
    background: #f7f7f7;
    border-radius: 10px;
  }

  .header-down .slide-nav__item .slide-nav__link {
    padding: 0 24px 0 20px;
  }

  .header-down .slide-nav__item .slide-nav__link .text-title {
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    leading-trim: NONE;
    line-height: 100%;
    letter-spacing: 0px;
  }

  .header-down .slide-nav__item .slide-nav__link .icon-image {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
  }
  .header-down .slide-nav__item .slide-nav__link .icon-image img{
    width: 100px;
    height: 100px;
  }

  .mobile-nav-line {
    margin: 24px 0;
  }

  /* 二级 */
  .header-botton .header-button-top .slide-nav__dropdown.is-active .slide-nav__link,.header-down .slide-nav__dropdown.is-active .slide-nav__link {
    background-color: #fff !important;
    flex-direction: unset;
    padding: unset;
  }

  .header-botton .header-button-top .slide-nav__dropdown.is-active .slide-nav__link .er-text ,.header-down .slide-nav__dropdown.is-active .slide-nav__link .er-text{
    font-family: Playfair Display, -apple-system, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    font-weight: 400;
    font-style: Regular;
    font-size: 20px;
    leading-trim: NONE;
    line-height: 1.3;
    letter-spacing: 0px;
    text-decoration: unset;
  }

  .slide-nav-top .slide-nav__dropdown.is-active .slide-nav__link--back,.header-down .slide-nav__dropdown.is-active .slide-nav__link--back{
    background: #fff;
    flex-direction: unset;
  }

  .slide-nav-top .slide-nav__dropdown.is-active .hr-line,.header-down .slide-nav__dropdown.is-active .hr-line {
    margin: 16px 0 20px;
  }
  .slide-nav-top .slide-nav__dropdown.is-active .hr-lines,.header-down .slide-nav__dropdown.is-active .hr-lines{
    margin:24px 0;
  }

  .header-botton .header-button-top .slide-nav__dropdown.is-active .collections a,.header-down .slide-nav__dropdown.is-active .collections a {
    display: flex;
    flex-wrap: wrap;
    gap: 13px;
  }

  .header-botton .header-button-top .slide-nav__dropdown.is-active .collections .slide-nav__text-flex,.header-down .slide-nav__dropdown.is-active .collections .slide-nav__text-flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .slide-nav__dropdown.is-active .collections .slide-nav__text-flex .er-collection-title {
    font-family: PingFang SC;
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    leading-trim: NONE;
    line-height: 1.4;
    letter-spacing: 0px;
  }

  .slide-nav__dropdown.is-active .collections .slide-nav__text-flex .er-collection-view {
    font-family: PingFang SC;
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
    leading-trim: NONE;
    line-height: 1.5;
    letter-spacing: 0px;
    text-align: center;
    text-decoration: underline;
    text-decoration-style: solid;
  }

  .header-botton .header-button-top .slide-nav__dropdown.is-active .collections .slide-nav-image,.header-down .slide-nav__dropdown.is-active .collections .slide-nav-image {
    position: relative;
  }

  .header-botton .header-button-top .slide-nav__dropdown.is-active .collections .slide-nav-image svg,.header-down .slide-nav__dropdown.is-active .collections .slide-nav-image svg {
    position: absolute;
    top: 40%;
    right: 10px;
  }

  .slide-nav__dropdown.is-active .san_dump.firstone {
    margin-top: 20px;
  }

  .slide-nav__dropdown.is-active .san_dump .slide-nav__sublist-link {
    font-family: PingFang SC;
    font-weight: 400;
    font-style: Regular;
    font-size: 16px;
    leading-trim: NONE;
    line-height: 36px;
    letter-spacing: 0px;
    color: #666666;
  }
  .slide-nav__dropdown.is-active .slide-nav__item .slide-nav__link .golinks{
    font-family: PingFang SC;
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    leading-trim: NONE;
    line-height: 1.4;
    letter-spacing: 0px;
  }
  .slide-nav__dropdown.is-active .slide-nav__item .slide-nav__link .golinks:last-child{
    padding-bottom: 24px;
  }
  .slide-nav__dropdown.is-active .slide-nav__item.santext .slide-nav__link {
    font-family: PingFang SC;
    font-weight: 400;
    font-style: Regular;
    font-size: 16px;
    leading-trim: NONE;
    line-height: 36px;
    letter-spacing: 0px;
    color: #666666;
  }
</style>

<mobile-nav class="slide-nav__wrapper overlay" data-level="1" container="{{ container }}" inHeader="{{ in_header }}"
  defer-hydration>
  <ul class="slide-nav">
    <div class="header-botton">
      <div class="header-button-top">
        <div class="slide-nav-top">
          {% for block in section.blocks %}
            {% case block.type %}
            {% when 'mbblock' %}
              {% if block.settings.oneurl == blank %}
                {%- liquid
                  assign link_index = forloop.index
                  assign child_list_handle = block.settings.oneclass| handleize | append: link_index
                  %}
              <li class="slide-nav__item">
                {% if block.settings.oneclass %}
                <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-2-{{ child_list_handle }}"
                  data-active="true">
                  <span class="slide-nav__link"><span class="text-title">{{block.settings.oneclass}}</span>

                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_4211_9231)">
                        <circle cx="10" cy="10" r="10" fill="white" />
                        <path
                          d="M10.6818 10L7.94621 7.21373C7.69846 6.9614 7.69846 6.55713 7.94621 6.30479C8.20047 6.04582 8.61772 6.04582 8.87198 6.30479L11.8388 9.32656C12.2059 9.70049 12.2059 10.2996 11.8388 10.6735L8.87198 13.6952C8.61772 13.9542 8.20047 13.9542 7.94621 13.6952C7.69846 13.4429 7.69846 13.0386 7.94621 12.7863L10.6818 10Z"
                          fill="#666666" />
                      </g>
                      <defs>
                        <clipPath id="clip0_4211_9231">
                          <rect width="20" height="20" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>

                  </span>
                </button>
                {% endif %}
                
                
                
                {% if block.settings.collection_select != blank or block.settings.collection_select2 != blank %}
                <ul class="slide-nav__dropdown" data-parent="tier-2-{{ child_list_handle }}" data-level="2">
                  <li class="slide-nav__item">
                    <button type="button" class="slide-nav__button js-toggle-submenu">
                      <span class="slide-nav__link slide-nav__link--back">
                        <span class="er-text">{{block.settings.oneclass}}</span>
                      </span>
                    </button>
                  </li>
                  <div class="hr-line">
                    <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                    </svg>
                  </div>

                 
                  <li class="slide-nav__item collections">
                    <a href="{{block.settings.collection_select.url}}" class="slide-nav__link">
                      <div class="slide-nav__text-flex">
                        <span class="er-collection-title">{{block.settings.collection_select.title}} </span>
                        <span class="er-collection-view">view all </span>
                      </div>

                      <div class="slide-nav-image">
                        <img
                          src="{{block.settings.collection_select.featured_image |  image_url }}"
                          alt="">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_4211_9231)">
                            <circle cx="10" cy="10" r="10" fill="white" />
                            <path
                              d="M10.6818 10L7.94621 7.21373C7.69846 6.9614 7.69846 6.55713 7.94621 6.30479C8.20047 6.04582 8.61772 6.04582 8.87198 6.30479L11.8388 9.32656C12.2059 9.70049 12.2059 10.2996 11.8388 10.6735L8.87198 13.6952C8.61772 13.9542 8.20047 13.9542 7.94621 13.6952C7.69846 13.4429 7.69846 13.0386 7.94621 12.7863L10.6818 10Z"
                              fill="#666666" />
                          </g>
                          <defs>
                            <clipPath id="clip0_4211_9231">
                              <rect width="20" height="20" fill="white" />
                            </clipPath>
                          </defs>
                        </svg>

                      </div>

                    </a>
                  </li>
                 

                  {% if block.settings.menu_link1 != blank %}
                    {% for link in block.settings.menu_link1.links %}

                      {%- liquid
                        assign link_indexx = forloop.index
                        assign grand_child_list_handle1 = link.title | handleize
                        assign grand_child_link_id1 = grand_child_list_handle1 | append: link_indexx
                        -%}
                    <li class="slide-nav__item san_dump {% if forloop.index0 == 0 %} firstone {% endif %}">
                    <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-3-{{grand_child_link_id1}}"
                      data-active="true">
                      <span class="slide-nav__link slide-nav__sublist-link">
                        <span>{{link.title}}</span>



                       
                      </span>
                    </button>
                    {% if link.levels > 0 %}
                    <ul class="slide-nav__dropdown" data-parent="tier-3-{{grand_child_link_id1}}" data-level="3">
                      
                      <li class="slide-nav__item ">
                        <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-2-{{ child_list_handle }}">
                          <span class="slide-nav__link slide-nav__link--back">
                        
                            <span class="er-text">{{link.title}}</span>
                          </span>
                        </button>
                      </li>

                      <div class="hr-line">
                        <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                        </svg>
                      </div>
                     
                      {%- for childlink in link.links -%}
                    
                      <li class="slide-nav__item santext">
                        <a href="{{childlink.url}}"
                          class="slide-nav__link">
                          <span >{{childlink.title}}</span>

                          
                        </a>
                      </li>
                      {% endfor %}
                    </ul>
                    {% endif %}

                  </li>
                    {% endfor %}
                  {% endif %}
                  
                  {% comment %} 第二组 {% endcomment %}
                  {% if block.settings.collection_select2 != blank %}
                  <div class="hr-lines">
                    <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                    </svg>
                  </div>


                  
                  <li class="slide-nav__item collections">
                    <a href="{{block.settings.collection_select2.url}}" class="slide-nav__link">
                      <div class="slide-nav__text-flex">
                        <span class="er-collection-title">{{block.settings.collection_select2.title}} </span>
                        <span class="er-collection-view">view all </span>
                      </div>

                      <div class="slide-nav-image">
                        <img
                          src="{{block.settings.collection_select2.featured_image |  image_url }}"
                          alt="">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_4211_9231)">
                            <circle cx="10" cy="10" r="10" fill="white" />
                            <path
                              d="M10.6818 10L7.94621 7.21373C7.69846 6.9614 7.69846 6.55713 7.94621 6.30479C8.20047 6.04582 8.61772 6.04582 8.87198 6.30479L11.8388 9.32656C12.2059 9.70049 12.2059 10.2996 11.8388 10.6735L8.87198 13.6952C8.61772 13.9542 8.20047 13.9542 7.94621 13.6952C7.69846 13.4429 7.69846 13.0386 7.94621 12.7863L10.6818 10Z"
                              fill="#666666" />
                          </g>
                          <defs>
                            <clipPath id="clip0_4211_9231">
                              <rect width="20" height="20" fill="white" />
                            </clipPath>
                          </defs>
                        </svg>

                      </div>

                    </a>
                  </li>
                  {% endif %}
                  
                  {% if block.settings.menu_link2 != blank %}
                    {% for link in block.settings.menu_link2.links %}
                      {%- liquid
                      assign indexin = forloop.index
                        assign grand_child_list_handle2 = link.title | handleize
                        assign grand_child_link_id2 = grand_child_list_handle2 | append: indexin
                        -%}
                    <li class="slide-nav__item san_dump {% if forloop.index0 == 0 %} firstone {% endif %}">
                    <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-3-{{grand_child_link_id2}}"
                      data-active="true">
                      <span class="slide-nav__link slide-nav__sublist-link">
                        <span>{{link.title}}</span>
                      </span>
                    </button>
                   

                    {% if block.settings.menu_link2.links.levels > 0 %}
                    <ul class="slide-nav__dropdown" data-parent="tier-3-{{grand_child_link_id2}}" data-level="3">
                      
                      <li class="slide-nav__item ">
                        <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-2-{{ child_list_handle }}">
                          <span class="slide-nav__link slide-nav__link--back">
                        
                            <span class="er-text">{{link.title}}</span>
                          </span>
                        </button>
                      </li>

                      <div class="hr-line">
                        <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                        </svg>
                      </div>
                     
                      {%- for childlink in link.links -%}
                    
                      <li class="slide-nav__item santext">
                        <a href="{{childlink.url}}"
                          class="slide-nav__link">
                          <span >{{childlink.title}}</span>

                          
                        </a>
                      </li>
                      {% endfor %}
                    </ul>
                    {% endif %}

                  </li>
                    {% endfor %}
                  {% endif %}

                  <div class="hr-lines">
                    <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                    </svg>
                  </div>

                  {% for link in block.settings.menu_link3.links %}
                  <li class="slide-nav__item">
                    <a href="{{link.url}}" class="slide-nav__link"><span class="golinks">{{link.title}}</span></a></li>

                  <div class="hr-lines">
                    <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                    </svg>
                  </div>

                  {% endfor %}
                  
                </ul>
                {% endif %}
              </li>  
              {% else %}
              <li class="slide-nav__item">
                <a href="{{block.settings.oneurl}}" class="slide-nav__link">
                  <span class="text-title">{{block.settings.oneclass}}</span>
    
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_4211_9231)">
                      <circle cx="10" cy="10" r="10" fill="white" />
                      <path
                        d="M10.6818 10L7.94621 7.21373C7.69846 6.9614 7.69846 6.55713 7.94621 6.30479C8.20047 6.04582 8.61772 6.04582 8.87198 6.30479L11.8388 9.32656C12.2059 9.70049 12.2059 10.2996 11.8388 10.6735L8.87198 13.6952C8.61772 13.9542 8.20047 13.9542 7.94621 13.6952C7.69846 13.4429 7.69846 13.0386 7.94621 12.7863L10.6818 10Z"
                        fill="#666666" />
                    </g>
                    <defs>
                      <clipPath id="clip0_4211_9231">
                        <rect width="20" height="20" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </a>
              </li>
            {% endif %}
          {% endcase %}
          {% endfor %}
          
        </div>

      </div>
    </div>

    <div class="mobile-nav-line">
      <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
        <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
      </svg>
    </div>

    <div class="header-down">
      <div class="slide-nav-down">
        {% for block in section.blocks %}
          {% case block.type %}
          {% when 'mbblocktwo' %}
            {% if block.settings.oneurl == blank%}
              {%- liquid
              assign link_index = forloop.index
              assign child_list_handle = block.settings.oneclass| handleize | append: link_index
              %}
        <li class="slide-nav__item">
          {% if block.settings.oneclass != blank %}
          <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-2-{{ child_list_handle }}"
            data-active="true">
            <span class="slide-nav__link">
              <span class="text-title">{{block.settings.oneclass}}</span>
              <div class="icon-image">
                {% if block.settings.one_picker != blank %}
                  <img src="{{block.settings.one_picker |  image_url }}" alt="{{block.settings.one_picker.alt}}">
                {% endif %}
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_4211_9231)">
                    <circle cx="10" cy="10" r="10" fill="white" />
                    <path
                      d="M10.6818 10L7.94621 7.21373C7.69846 6.9614 7.69846 6.55713 7.94621 6.30479C8.20047 6.04582 8.61772 6.04582 8.87198 6.30479L11.8388 9.32656C12.2059 9.70049 12.2059 10.2996 11.8388 10.6735L8.87198 13.6952C8.61772 13.9542 8.20047 13.9542 7.94621 13.6952C7.69846 13.4429 7.69846 13.0386 7.94621 12.7863L10.6818 10Z"
                      fill="#666666" />
                  </g>
                  <defs>
                    <clipPath id="clip0_4211_9231">
                      <rect width="20" height="20" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </span>
          </button>
          {% endif %}

          {% if block.settings.collection_select != blank or block.settings.collection_select2 != blank %}
            <ul class="slide-nav__dropdown" data-parent="tier-2-{{ child_list_handle }}" data-level="2">
              <li class="slide-nav__item">
                <button type="button" class="slide-nav__button js-toggle-submenu">
                  <span class="slide-nav__link slide-nav__link--back">
                    <span class="er-text">{{block.settings.oneclass}}</span>
                  </span>
                </button>
              </li>
              <div class="hr-line">
                <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                </svg>
              </div>

             
              <li class="slide-nav__item collections">
                <a href="{{block.settings.collection_select.url}}" class="slide-nav__link">
                  <div class="slide-nav__text-flex">
                    <span class="er-collection-title">{{block.settings.collection_select.title}} </span>
                    <span class="er-collection-view">view all </span>
                  </div>

                  <div class="slide-nav-image">
                    <img
                      src="{{block.settings.collection_select.featured_image |  image_url }}"
                      alt="">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_4211_9231)">
                        <circle cx="10" cy="10" r="10" fill="white" />
                        <path
                          d="M10.6818 10L7.94621 7.21373C7.69846 6.9614 7.69846 6.55713 7.94621 6.30479C8.20047 6.04582 8.61772 6.04582 8.87198 6.30479L11.8388 9.32656C12.2059 9.70049 12.2059 10.2996 11.8388 10.6735L8.87198 13.6952C8.61772 13.9542 8.20047 13.9542 7.94621 13.6952C7.69846 13.4429 7.69846 13.0386 7.94621 12.7863L10.6818 10Z"
                          fill="#666666" />
                      </g>
                      <defs>
                        <clipPath id="clip0_4211_9231">
                          <rect width="20" height="20" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>

                  </div>

                </a>
              </li>
             

              {% if block.settings.menu_link1 != blank %}
                {% for link in block.settings.menu_link1.links %}
                  {%- liquid
                  assign link_indexx = forloop.index
                  assign grand_child_list_handle1 = link.title | handleize
                  assign grand_child_link_id1 = grand_child_list_handle1 | append: link_indexx
                  -%}
                <li class="slide-nav__item san_dump {% if forloop.index0 == 0 %} firstone {% endif %}">
                <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-3-{{grand_child_link_id1}}"
                  data-active="true">
                  <span class="slide-nav__link slide-nav__sublist-link">
                    <span>{{link.title}}</span>



                   
                  </span>
                </button>
                {% if block.settings.menu_link1.links.levels > 0 %}
                 
                 
                 
                <ul class="slide-nav__dropdown" data-parent="tier-3-{{grand_child_link_id1}}" data-level="3">
                  
                  <li class="slide-nav__item ">
                    <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-2-{{ child_list_handle }}">
                      <span class="slide-nav__link slide-nav__link--back">
                    
                        <span class="er-text">{{link.title}}</span>
                      </span>
                    </button>
                  </li>

                  <div class="hr-line">
                    <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                    </svg>
                  </div>
                 
                  {%- for childlink in link.links -%}
                
                  <li class="slide-nav__item santext">
                    <a href="{{childlink.url}}"
                      class="slide-nav__link">
                      <span >{{childlink.title}}</span>

                      
                    </a>
                  </li>
                  {% endfor %}
                </ul>
                {% endif %}

              </li>
                {% endfor %}
              {% endif %}
              
              {% comment %} 第二组 {% endcomment %}
              {% if block.settings.collection_select2 != blank %}
              <div class="hr-lines">
                <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                </svg>
              </div>


              
              <li class="slide-nav__item collections">
                <a href="{{block.settings.collection_select2.url}}" class="slide-nav__link">
                  <div class="slide-nav__text-flex">
                    <span class="er-collection-title">{{block.settings.collection_select2.title}} </span>
                    <span class="er-collection-view">view all </span>
                  </div>

                  <div class="slide-nav-image">
                    <img
                      src="{{block.settings.collection_select2.featured_image |  image_url }}"
                      alt="">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_4211_9231)">
                        <circle cx="10" cy="10" r="10" fill="white" />
                        <path
                          d="M10.6818 10L7.94621 7.21373C7.69846 6.9614 7.69846 6.55713 7.94621 6.30479C8.20047 6.04582 8.61772 6.04582 8.87198 6.30479L11.8388 9.32656C12.2059 9.70049 12.2059 10.2996 11.8388 10.6735L8.87198 13.6952C8.61772 13.9542 8.20047 13.9542 7.94621 13.6952C7.69846 13.4429 7.69846 13.0386 7.94621 12.7863L10.6818 10Z"
                          fill="#666666" />
                      </g>
                      <defs>
                        <clipPath id="clip0_4211_9231">
                          <rect width="20" height="20" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>

                  </div>

                </a>
              </li>
              {% endif %}
              
              {% if block.settings.menu_link2 != blank %}
                {% for link in block.settings.menu_link2.links %}
                  {%- liquid
                  assign indexin = forloop.index
                    assign grand_child_list_handle2 = link.title | handleize
                    assign grand_child_link_id2 = grand_child_list_handle2 | append: indexin
                    -%}

                <li class="slide-nav__item san_dump {% if forloop.index0 == 0 %} firstone {% endif %}">
                <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-3-{{grand_child_link_id2}}"
                  data-active="true">
                  <span class="slide-nav__link slide-nav__sublist-link">
                    <span>{{link.title}}</span>
                  </span>
                </button>
                {% if block.settings.menu_link2.links.levels > 0 %}
                <ul class="slide-nav__dropdown" data-parent="tier-3-{{grand_child_link_id2}}" data-level="3">
                  
                  <li class="slide-nav__item ">
                    <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-2-{{ child_list_handle }}">
                      <span class="slide-nav__link slide-nav__link--back">
                    
                        <span class="er-text">{{link.title}}</span>
                      </span>
                    </button>
                  </li>

                  <div class="hr-line">
                    <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                    </svg>
                  </div>
                 
                  {%- for childlink in link.links -%}
                
                  <li class="slide-nav__item santext">
                    <a href="{{childlink.url}}"
                      class="slide-nav__link">
                      <span >{{childlink.title}}</span>

                      
                    </a>
                  </li>
                  {% endfor %}
                </ul>
                {% endif %}

              </li>
                {% endfor %}
              {% endif %}

              <div class="hr-lines">
                <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                </svg>
              </div>

              {% for link in block.settings.menu_link3.links %}
              <li class="slide-nav__item">
                <a href="{{link.url}}" class="slide-nav__link"><span class="golinks">{{link.title}}</span></a></li>

              <div class="hr-lines">
                <svg width="343" height="1" viewBox="0 0 343 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <line x1="0.5" y1="0.5" x2="342.5" y2="0.5" stroke="#EEEEEE" stroke-linecap="square" />
                </svg>
              </div>

              {% endfor %}
              
            </ul>
            {% endif %}
        </li>
        {% else %}
        <li class="slide-nav__item">
          <a href="{{block.settings.oneurl}}">
            <button type="button" class="slide-nav__button js-toggle-submenu" data-target="{{block.settings.oneclass}}"
              data-active="true">
              <span class="slide-nav__link">
                <span class="text-title">{{block.settings.oneclass}}</span>
                <div class="icon-image">
            {% if block.settings.one_picker != blank %}<img src="{{block.settings.one_picker |  image_url }}" alt="{{block.settings.one_picker.alt}}">{% endif %}
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_4211_9231)">
                      <circle cx="10" cy="10" r="10" fill="white" />
                      <path
                        d="M10.6818 10L7.94621 7.21373C7.69846 6.9614 7.69846 6.55713 7.94621 6.30479C8.20047 6.04582 8.61772 6.04582 8.87198 6.30479L11.8388 9.32656C12.2059 9.70049 12.2059 10.2996 11.8388 10.6735L8.87198 13.6952C8.61772 13.9542 8.20047 13.9542 7.94621 13.6952C7.69846 13.4429 7.69846 13.0386 7.94621 12.7863L10.6818 10Z"
                        fill="#666666" />
                    </g>
                    <defs>
                      <clipPath id="clip0_4211_9231">
                        <rect width="20" height="20" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
              </span>
            </button>
            </a>
          </li>
          {% endif %}
          {% endcase %}
        {% endfor %}

      </div>
    </div>

    {%- for link in main_menu.links -%}
    {%- liquid
    assign link_index = forloop.index
    assign child_list_handle = link.title | handleize | append: link_index

    assign is_collection = false
    assign have_image = false
    if link.url contains '/collections/'
    assign lang_code_string = request.locale.iso_code | prepend: '/' | downcase
    assign subcollection_handle = link.url | remove: '/collections/' | remove: lang_code_string
    assign subcollection_drop = collections[subcollection_handle]
    assign have_image = subcollection_drop.products.first.featured_media.preview_image
    assign is_collection = true
    endif
    -%}

    {%- if have_image -%}
    {%- capture collection_image -%}
    <div class="slide-nav__image">
      {%- render 'image-element',
      img: have_image,
      loading: 'eager',
      sizes: '40px',
      alt: collections[subcollection_handle].title,
      -%}
    </div>
    {%- endcapture -%}
    {%- endif -%}

    <li class="slide-nav__item">
      {%- if link.levels > 0 -%}
      <button type="button" class="slide-nav__button js-toggle-submenu" data-target="tier-2-{{ child_list_handle }}" {%
        if link.active or link.child_active %} data-active="true" {% endif %}>
        <span class="slide-nav__link">
          {%- if have_image -%}
          {{ collection_image }}
          {%- endif -%}
          <span>{{ link.title }}</span>
          {% render 'icon', name: chevron_icon %}
          <span class="icon__fallback-text visually-hidden">
            {% render 't_with_fallback', key: 'actions.expand_submenu', fallback: 'Expand submenu' -%}
          </span>
        </span>
      </button>

      <ul class="slide-nav__dropdown" data-parent="tier-2-{{ child_list_handle }}" data-level="2">
        <li class="slide-nav__item">
          <button type="button" class="slide-nav__button js-toggle-submenu">
            <span class="slide-nav__link slide-nav__link--back">
              {% render 'icon', name: chevron_icon %}
              <span>{{ link.title }}</span>
            </span>
          </button>
        </li>

        {% comment %}
        If a collection, automatically add 'view all' link
        {% endcomment %}
        {%- if is_collection -%}
        <li class="slide-nav__item">
          <a href="{{ link.url }}" class="slide-nav__link">
            <span>
              {% render 't_with_fallback', key: 'actions.view_all', fallback: 'View all' -%}
            </span>
            {% render 'icon', name: chevron_icon %}
          </a>
        </li>
        {%- endif -%}

        {%- for childlink in link.links -%}
        {%- liquid
        assign grand_child_list_handle = childlink.title | handleize
        assign grand_child_link_id = grand_child_list_handle | append: link_index
        -%}

        <li class="slide-nav__item">
          {%- if childlink.levels > 0 -%}
          <button type="button" class="slide-nav__button js-toggle-submenu"
            data-target="tier-3-{{ grand_child_link_id }}" {% if link.active or link.child_active %} data-active="true"
            {% endif %}>
            <span class="slide-nav__link slide-nav__sublist-link">
              <span>{{ childlink.title }}</span>
              {% render 'icon', name: chevron_icon %}
              <span class="icon__fallback-text visually-hidden">
                {% render 't_with_fallback', key: 'actions.expand_submenu', fallback: 'Expand submenu' -%}
              </span>
            </span>
          </button>
          <ul class="slide-nav__dropdown" data-parent="tier-3-{{ grand_child_link_id }}" data-level="3">
            <li class="slide-nav__item">
              <button type="button" class="slide-nav__button js-toggle-submenu"
                data-target="tier-2-{{ child_list_handle }}">
                <span class="slide-nav__link slide-nav__link--back">
                  {% render 'icon', name: chevron_icon %}
                  <span>{{ childlink.title }}</span>
                </span>
              </button>
            </li>

            {% comment %}
            If a collection, automatically add 'view all' link
            {% endcomment %}
            {%- if childlink.url contains '/collections/' -%}
            <li class="slide-nav__item">
              <a href="{{ childlink.url }}" class="slide-nav__link">
                <span>
                  {% render 't_with_fallback', key: 'actions.view_all', fallback: 'View all' -%}
                </span>
                {% render 'icon', name: chevron_icon %}
              </a>
            </li>
            {%- endif -%}

            {%- for grandchildlink in childlink.links -%}
            <li class="slide-nav__item">
              <a href="{{ grandchildlink.url }}" class="slide-nav__link">
                <span>{{ grandchildlink.title | escape }}</span>
                {% render 'icon', name: chevron_icon %}
              </a>
            </li>
            {%- endfor -%}
          </ul>
          {%- else -%}
          <a href="{{ childlink.url }}" class="slide-nav__link">
            <span>{{ childlink.title | escape }}</span>
            {% render 'icon', name: chevron_icon %}
          </a>
          {%- endif -%}
        </li>
        {%- endfor -%}
      </ul>
      {%- else -%}
      <a href="{{ link.url }}" class="slide-nav__link">
        {%- if have_image -%}
        {{ collection_image }}
        {%- endif -%}

        <span>{{ link.title | escape }}</span>
        {% render 'icon', name: chevron_icon %}
      </a>
      {%- endif -%}
    </li>
    {%- endfor -%}
  </ul>
</mobile-nav>

<script type="module">
  import 'components/header-mobile-nav';
</script>