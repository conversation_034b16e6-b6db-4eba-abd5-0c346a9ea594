{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the customer reset password section.

  Usage:
  {% render 'section-main-reset-password %}
{%- endcomment -%}

<div class="page-width page-width--tiny page-content">
  <div class="form-vertical">
    {%- form 'reset_customer_password' -%}
      <header class="section-header">
        <h1 class="section-header__title">
          {% render 't_with_fallback', key: 'actions.reset_account_password', fallback: 'Reset account password' %}
        </h1>
        <p>
          {% assign actions_enter_new_password = 'actions.enter_new_password' | t: email: email %}
          {%- capture fallback_actions_enter_new_password -%}
            Enter a new password for {{ email }}
          {%- endcapture -%}
          {% render 't_with_fallback', t: actions_enter_new_password, fallback: fallback_actions_enter_new_password %}
        </p>
      </header>

      {{ form.errors | default_errors }}

      <label for="ResetPassword" class="visually-hidden">
        {% render 't_with_fallback', key: 'labels.password', fallback: 'Password' -%}
      </label>
      <input
        type="password"
        value=""
        name="customer[password]"
        id="ResetPassword"
        class="input-full{% if form.errors contains 'password' %} error{% endif %}"
        placeholder="{% render 't_with_fallback', key: 'labels.password', fallback: 'Password' -%}"
      >

      <label for="PasswordConfirmation" class="visually-hidden">
        {% render 't_with_fallback', key: 'actions.confirm_password', fallback: 'Confirm password' -%}
      </label>
      <input
        type="password"
        value=""
        name="customer[password_confirmation]"
        id="PasswordConfirmation"
        class="input-full{% if form.errors contains 'password_confirmation' %} error{% endif %}"
        placeholder="{% render 't_with_fallback', key: 'actions.confirm_password', fallback: 'Confirm Password' -%}"
      >

      <div class="text-center">
        <label for="reset-password-submit" class="visually-hidden">
          {% render 't_with_fallback', key: 'actions.reset_password', fallback: 'Reset password' -%}
        </label>
        <button type="submit" id="reset-password-submit" class="btn btn--full">
          {% render 't_with_fallback', key: 'actions.reset_password', fallback: 'Reset Password' %}
        </button>
      </div>
    {%- endform -%}
  </div>
</div>
