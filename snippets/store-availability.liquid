{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment %}
  Renders the store availability of a product

  Accepts:
  - product {product} - Product object

  Usage:
  {% render 'section-store-availability' %}
{%- endcomment %}

{%- liquid
  assign product = section.settings.product | default: product
-%}

<store-availability
  data-store-availability-holder
  data-product-name="{{ product.title | escape }}"
  data-base-url="{{ shop.url }}{{ routes.root_url }}"
  data-variant-id="{{- product.selected_or_first_available_variant.id -}}"
  data-product-id="{{- product.id -}}"
  data-section-id="{{ section.id }}"
></store-availability>

<script type="module">
  import 'components/store-availability'
</script>
