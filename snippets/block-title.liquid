{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the product title

  Accepts:
  - block {block} - Block object
  - product {product} - Product object
  - vendor_enable {boolean} - Whether to display the vendor
  - sku_enable {boolean} - Whether to display the SKU

  Usage:
  {% render 'block-title', block: block %}
{%- endcomment -%}

{%- liquid
  assign product = section.settings.product | default: product
  assign title_default = 'labels.example_product' | t
  assign title = product.title | default: title_default
  assign current_variant = product.selected_or_first_available_variant
  assign vendor_enable = vendor_enable | default: block.settings.vendor_enable, allow_false: true
  assign sku_enable = sku_enable | default: block.settings.sku_enable, allow_false: true
  assign collection_handle = product.vendor | handleize
  assign collection_for_vendor = collections[collection_handle]
  assign vendor_url_default = product.vendor | url_for_vendor
  assign vendor_url = collection_for_vendor.url | default: vendor_url_default
-%}

<div class="product-block" data-section-id="{{ section.id }}" {{ block.shopify_attributes }}>
  <h1 class="h2 product-single__title">
    {{- title -}}
  </h1>

  {%- if vendor_enable or sku_enable -%}
    <div class="product-single__vendor-sku">
      {%- if vendor_enable -%}
        <span class="product-single__vendor">
          <a href="{{ vendor_url }}">
            {{- product.vendor -}}
          </a>
        </span>
      {%- endif -%}

      {%- if sku_enable -%}
        <span class="product-single__sku">
          {%- render 'variant-sku', variant: current_variant -%}
        </span>
      {%- endif -%}
    </div>
  {%- endif -%}
</div>
