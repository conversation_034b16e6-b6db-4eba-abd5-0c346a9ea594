{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the header icons.

  Accepts:
  - cart_icon {string} - The cart icon to use
  - nav_position {string} - The navigation position
  - predictive_search_enabled {boolean} - Whether predictive search is enabled
  - header_icons_slot {string} - The header icons slot content

  Usage:
  {% render 'header-icons', cart_icon: 'bag' %}
{%- endcomment -%}

{%- liquid
  assign cart_icon = cart_icon | default: settings.cart_icon | default: 'cart'
  assign nav_position = nav_position | default: 'below'
  assign predictive_search_enabled = predictive_search_enabled | default: settings.predictive_search_enabled, allow_false: true

  assign hide_icon_labels = true

  if nav_position == 'below'
    assign hide_icon_labels = false
  endif
-%}

{%- capture search_link -%}
  <a
    href="{{ routes.search_url }}"
    class="site-nav__link site-nav__link--icon{% if nav_position == 'below' %} medium-up--hide{% endif %} js-no-transition"
    {% if predictive_search_enabled %}
      aria-expanded="false"
      aria-haspopup="listbox"
    {% endif %}
  >
    {% render 'icon', name: 'search' %}
    <span class="icon__fallback-text visually-hidden">
      {% render 't_with_fallback', key: 'labels.search', fallback: 'Search' -%}
    </span>
  </a>
{%- endcapture -%}

{%- capture cart_link -%}
  <a
    href="{{ routes.cart_url }}"
    id="HeaderCartTrigger"
    aria-controls="HeaderCart"
    class="site-nav__link site-nav__link--icon site-nav__link--cart js-no-transition"
    data-icon="{{ cart_icon }}"
    aria-label="{% render 't_with_fallback', key: 'labels.cart', fallback: 'Cart' -%}"
  >
    <span class="cart-link">
      {% render 'icon', name: cart_icon %}
      <span class="cart-link__bubble{% if cart.item_count > 0 %} cart-link__bubble--visible{% endif %}">
        <span class="cart-link__bubble-num">{{ cart.item_count }}</span>
      </span>
    </span>
    <span class="site-nav__icon-label small--hide{% if hide_icon_labels %} icon__fallback-text visually-hidden{% endif %}">
      {% render 't_with_fallback', key: 'labels.cart', fallback: 'Cart' %}
    </span>
  </a>
{%- endcapture -%}

{%- capture menu_button -%}
  <button
    type="button"
    aria-controls="MobileNav"
    class="site-nav__link site-nav__link--icon medium-up--hide mobile-nav-trigger"
  >
    {% render 'icon', name: 'hamburger' %}
    <span class="icon__fallback-text visually-hidden">
      {% render 't_with_fallback', key: 'labels.site_navigation', fallback: 'Site navigation' -%}
    </span>
  </button>
{%- endcapture -%}

{%- capture header_icons_slot_default -%}
  {%- render 'toggle-search', search_link: search_link -%}
  {%- if shop.customer_accounts_enabled -%}
    <a class="site-nav__link site-nav__link--icon small--hide" href="{{ routes.account_url }}">
      {% render 'icon', name: 'user' %}
      <span class="site-nav__icon-label small--hide{% if hide_icon_labels %} icon__fallback-text visually-hidden{% endif %}">
        {% render 't_with_fallback', key: 'labels.account', fallback: 'Account' %}
      </span>
    </a>
  {%- endif -%}

  {%- render 'toggle-cart', cart_link: cart_link -%}

  {%- render 'toggle-menu', menu_button: menu_button -%}
{%- endcapture -%}

{%- liquid
  assign header_icons_slot = header_icons_slot | default: header_icons_slot_default
-%}

<div class="site-nav__icons">
  {{- header_icons_slot -}}
</div>
