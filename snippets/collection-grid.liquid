{% # components v2.10.64 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a collection grid.

  Accepts:
  - context {object} - The collection or search object
  - grid_style {'large'|'medium'|'list'} - The grid style

  Usage:
  {% render 'collection-grid', context: collection %}
{%- endcomment -%}

{% liquid
  assign grid_style = grid_style | default: section.settings.grid_style | default: 'medium'

  if context.products
    assign count = context.products_count
    assign count_label = 'info.product_count' | t: count: count
    assign items = items | default: context.products
  endif

  if context.results
    assign count = context.results_count
    assign count_label = 'info.search_result_count' | t: count: count
    assign items = items | default: context.results
  endif

  assign current_filter_size = 0
  for filter in filters
    assign current_filter_size = current_filter_size | plus: filter.active_values.size
  endfor
%}

<div class="collection-grid__wrapper">
  <div class="collection-filter">
    <div class="collection-filter__inner">
      <div class="collection-filter__item collection-filter__item--drawer">
        <button
          type="button"
          class="collection-filter__btn text-link"
        >
          {% render 'icon', name: 'filter' %}
          {{ 'actions.filter' | t }}
          {%- if current_filter_size > 0 -%}
            ({{ current_filter_size }})
          {%- endif -%}
        </button>
      </div>

      <div class="collection-filter__item collection-filter__item--count small--hide">
        {{ count_label }}
      </div>

      <div class="collection-filter__item collection-filter__item--right">
        {%- assign sort_by = context.sort_by | default: context.default_sort_by -%}
        <div class="collection-filter__sort small--hide">
          <label for="SortBy" class="visually-hidden">{{ 'actions.sort' | t }}</label>
          <select name="SortBy" id="SortBy" data-default-sortby="{{ context.default_sort_by }}">
            <option
              value="title-ascending"
              {% if sort_by == context.default_sort_by %}
                selected="selected"
              {% endif %}
            >
              {{ 'actions.sort' | t }}
            </option>
            {%- for option in context.sort_options -%}
              <option
                value="{{ option.value }}"
                {% if option.value == sort_by %}
                  selected="selected"
                {% endif %}
              >
                {{ option.name }}
              </option>
            {%- endfor -%}
          </select>
        </div>

        {%- liquid
          assign productGridView = grid_style
          if cart.attributes.product_view != blank
            assign productGridView = cart.attributes.product_view
          endif
        -%}

        <ul class="no-bullets inline-list text-right">
          <li>
            <button
              type="button"
              class="grid-view-btn{% if productGridView == 'large' %} is-active{% endif %}"
              data-view="large"
              title="{{ 'labels.large' | t }}"
            >
              {% render 'icon', name: 'view-large' %}
              <span class="icon__fallback-text visually-hidden">{{ 'labels.large' | t }}</span>
            </button>
          </li>
          <li>
            <button
              type="button"
              class="grid-view-btn{% if productGridView == 'medium' %} is-active{% endif %}"
              data-view="medium"
              title="{{ 'labels.small' | t }}"
            >
              {% render 'icon', name: 'view-small' %}
              <span class="icon__fallback-text visually-hidden">{{ 'labels.small' | t }}</span>
            </button>
          </li>
          <li>
            <button
              type="button"
              class="grid-view-btn{% if productGridView == 'list' %} is-active{% endif %}"
              data-view="list"
              title="{{ 'labels.list' | t }}"
            >
              {% render 'icon', name: 'view-list' %}
              <span class="icon__fallback-text visually-hidden">{{ 'labels.list' | t }}</span>
            </button>
          </li>
        </ul>
      </div>
    </div>

    <div class="collection-mobile-filters medium-up--hide">
      <div class="collection-mobile-filters__holder" id="CollectionInlineFilterWrap"></div>
    </div>
  </div>

  {%- if items == blank -%}
    <div class="index-section">{{ 'info.collection_no_products' | t }}</div>
  {%- endif -%}

  <div
    class="new-grid product-grid collection-grid"
    data-view="{{ productGridView }}"
    data-scroll-to
  >
    {%- liquid
      for item in items
        if item.object_type == 'product'
          render 'product-grid-item', product: item
        else
          render 'search-grid-item', item: item
        endif
      endfor
    -%}
  </div>
</div>
