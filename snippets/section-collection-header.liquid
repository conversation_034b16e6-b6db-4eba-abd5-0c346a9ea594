{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the collection header section.

  Accepts:
  - enable {boolean} - Whether to show the section
  - collection_image_enable {boolean} - Whether to show the collection image
  - parallax {boolean} - Whether to enable parallax
  - parallax_direction {'top'|'left'} - The direction of the parallax
  - disable_sticky_header {boolean} - Whether to disable the sticky header
  - hydration {string} - The hydration strategy

  Usage:
  {% render 'section-collection-header', parallax: false %}
{%- endcomment -%}

{%- liquid
  assign enable = enable | default: section.settings.enable, allow_false: true | default: true, allow_false: true
  assign collection_image_enable = collection_image_enable | default: section.settings.collection_image_enable, allow_false: true | default: true, allow_false: true
  assign parallax = parallax | default: section.settings.parallax, allow_false: true | default: false, allow_false: true
  assign parallax_direction = parallax_direction | default: section.settings.parallax_direction | default: 'top'
  assign disable_sticky_header = disable_sticky_header | default: false, allow_false: true
  assign hydration = hydration | default: 'on:idle'
-%}

{% capture image_content %}
  {% comment %}Full width image so don't need to adjust sizes attribute, fallback is 100vw{% endcomment %}
  {%- render 'image-element',
    img: collection.image,
    img_width: 2400,
    classes: 'collection-hero__image image-fit',
    preload: true,
    loading: 'eager'
  -%}
{% endcapture %}

{%- if enable -%}
  {%- if collection_image_enable and collection.image -%}
    <is-land {{ hydration }}>
      <collection-header
        id="CollectionHeaderSection"
        {% if parallax %}
          data-parallax="true"
        {% endif %}
      >
        <div class="collection-hero loading">
          {%- if parallax -%}
            {% render 'parallax-image', slot: image_content, direction: parallax_direction %}
          {%- else -%}
            {{ image_content }}
          {%- endif -%}

          <div class="collection-hero__content overlay">
            <div class="page-width">
              <header class="section-header section-header--flush">
                {%- render 'breadcrumbs' -%}

                <h1 class="section-header__title">
                  {{ collection.title }}
                </h1>
              </header>
            </div>
          </div>
        </div>
      </collection-header>

      <template data-island>
        <script type="module">
          import 'components/section-collection-header'
        </script>
      </template>
    </is-land>

    <div class="page-width medium-up--hide" data-collection-count style="padding-top: 15px;">
      {% assign info_product_count = 'info.product_count' | t: count: collection.products_count %}
      {%- capture fallback_info_product_count -%}
        {{ collection.products_count }} product{%- if collection.products_count != 1 -%}s{%- endif -%}
      {%- endcapture -%}
      {% render 't_with_fallback', t: info_product_count, fallback: fallback_info_product_count %}
    </div>

  {%- else -%}
    {%- assign disable_sticky_header = true -%}
    <div class="page-width page-content page-content--top">
      <header class="section-header section-header--flush">
        {%- render 'breadcrumbs' -%}

        <h1 class="section-header__title">
          {{ collection.title }}
        </h1>
        <p class="medium-up--hide" data-collection-count>
          {% assign info_product_count = 'info.product_count' | t: count: collection.products_count %}
          {%- capture fallback_info_product_count -%}
            {{ collection.products_count }} product{%- if collection.products_count != 1 -%}s{%- endif -%}
          {%- endcapture -%}
          {% render 't_with_fallback', t: info_product_count, fallback: fallback_info_product_count %}
        </p>
      </header>
    </div>
  {%- endif -%}
{% else %}
  {%- assign disable_sticky_header = true -%}
{%- endif -%}

{%- if disable_sticky_header -%}
  {% comment %}
    Div to trigger theme.CollectionHeader JS
  {% endcomment %}
  <is-land {{ hydration }}>
    <div
      id="CollectionHeaderSection"
      data-section-id="{{ section.id }}"
      data-section-type="collection-header"
    ></div>

    <template data-island>
      <script type="module">
        import 'components/section-collection-header'
      </script>
    </template>
  </is-land>
{%- endif -%}
