{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the product description

  Accepts:
  - block {block} - Block object

  Usage:
  {% render 'product-description', block: block %}
{%- endcomment -%}

<div
  class="product-block{% if block.settings.is_tab %} product-block--tab{% endif %}"
  {{ block.shopify_attributes }}
>
  {%- render 'product-description', block: block -%}
</div>
