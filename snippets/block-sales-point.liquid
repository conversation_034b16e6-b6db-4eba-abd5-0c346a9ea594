{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a sales point block

  Accepts:
  - block {block} - Block object

  Usage:
  {% render 'block-sales-point', block: block %}
{%- endcomment -%}

{%- unless block.settings.text == blank -%}
  <div class="product-block product-block--sales-point" {{ block.shopify_attributes }}>
    <ul class="sales-points">
      <li class="sales-point">
        <span class="icon-and-text">
          {% render 'icon', name: block.settings.icon %}
          <span>{{ block.settings.text }}</span>
        </span>
      </li>
    </ul>
  </div>
{%- endunless -%}
