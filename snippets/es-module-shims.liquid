{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Uses es-module-shims 1.8.2
  https://github.com/guybedford/es-module-shims/releases/tag/1.8.2
{%- endcomment -%}
<script>
  if (!(HTMLScriptElement.supports && HTMLScriptElement.supports('importmap'))) {
    const el = document.createElement('script')
    el.async = true
    el.src = "{{ 'es-module-shims.min.js' | asset_url }}"
    document.head.appendChild(el)
  }
</script>
