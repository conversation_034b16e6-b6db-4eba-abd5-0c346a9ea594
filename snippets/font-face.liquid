{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the header and body fonts. Typically, this is referenced inside of a
  theme's layout file and must reference settings with the proper IDs.

  Usage:
  {% render 'font-face' %}
{%- endcomment -%}

{%- liquid
  assign header_font = settings.type_header_font_family
  assign base_font = settings.type_base_font_family
  assign base_font_bold = base_font | font_modify: 'weight', '600'
  assign base_font_italic = base_font | font_modify: 'style', 'italic'
  assign base_font_bold_italic = base_font_bold | font_modify: 'style', 'italic'
-%}

{%- style -%}
  {{ header_font | font_face: font_display: 'swap' }}
  {{ base_font | font_face: font_display: 'swap' }}

  {{ base_font_bold | font_face: font_display: 'swap' }}
  {{ base_font_italic | font_face: font_display: 'swap' }}
  {{ base_font_bold_italic | font_face: font_display: 'swap' }}
{%- endstyle -%}
