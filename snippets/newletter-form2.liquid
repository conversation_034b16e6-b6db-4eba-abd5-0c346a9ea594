{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a newsletter form.

  Accepts:
  - context {string} - The context in which the form is rendered

  Usage:
  {% render 'newsletter-form', context: 'footer' %}
{%- endcomment -%}

{%- liquid
  assign newsletter_form_id = 'newsletter-' | append: section.id
-%}

{% form 'customer', id: newsletter_form_id %}
  {%- if form.posted_successfully? -%}
    <div class="note note--success">
      {% render 't_with_fallback', key: 'info.newsletter_success', fallback: 'Thanks for subscribing' %}
    </div>
  {%- endif -%}
  {%- if form.errors and form.context == context -%}
    {{ form.errors | default_errors }}
  {%- endif -%}

  {%- unless form.posted_successfully? -%}
    <label for="Email-{{ section.id }}" class="visually-hidden">
      {% render 't_with_fallback', key: 'actions.enter_email', fallback: 'Enter email' -%}
    </label>
    <label for="newsletter-form-submit-{{ section.id }}" class="visually-hidden">
      {% render 't_with_fallback', key: 'actions.subscribe', fallback: 'Subscribe' -%}
    </label>
    <input type="hidden" name="contact[tags]" value="prospect,newsletter">
    <input type="hidden" name="contact[context]" value="{{ context }}">
    <div class="input-group newsletter__input-group">
      <input
        type="email"
        value="{% if customer %}{{ customer.email }}{% endif %}"
        placeholder="{% render 't_with_fallback', key: 'actions.enter_email', fallback: 'Enter your email' -%}"
        name="contact[email]"
        id="Email-{{ section.id }}"
        class="input-group-field newsletter__input"
        autocorrect="off"
        autocapitalize="off"
        required
      >
      <div class="input-group-btn">
        <button 
          type="submit"
          id="newsletter-form-submit-{{ section.id }}"
          class="btn buttom"
          name="commit"
          aria-label="
            {% render 't_with_fallback', key: 'actions.subscribe', fallback: 'Subscribe' -%}
          "
        >
       <span>Subscribe</span> 
        </button>
      </div>
    </div>
  {%- endunless -%}
{% endform %} 
