{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a text columns with image section.

  Accepts:
  - title {string} - The title of the section
  - align_text {'left'|'center'|'right'} - The alignment of the text
  - divider {boolean} - Whether to show a divider
  - heading_size {string} - The size of the heading
  - color_scheme {'none'|'1'|'2'|'3'} - The color scheme of the section
  - hydration {string} - The hydration strategy for the section

  Usage:
  {% render 'section-text-columns' %}
{%- endcomment -%}

{%- liquid
  assign align_text = align_text | default: section.settings.align_text | default: 'center'
  assign title = title | default: section.settings.title
  assign divider = divider | default: section.settings.divider, allow_false: true | default: false, allow_false: true
  assign heading_size = heading_size | default: section.settings.heading_size | default: 'h2'
  assign color_scheme = color_scheme | default: section.settings.color_scheme
  assign hydration = hydration | default: 'on:idle'

  unless color_scheme
    assign class_name = 'text-columns-' | append: section.id
    render 'color-scheme-override', class_name: class_name
  endunless
-%}

{%- if divider -%}<div class="section--divider">{%- endif -%}

<div class="index-section color-scheme-{{ color_scheme }} text-columns-{{ section.id }}">
  {%- if color_scheme != 'none' -%}
    {%- render 'color-scheme-texture', color_scheme: color_scheme -%}
  {%- endif -%}

  <div class="page-width">
    {%- if title != blank -%}
      <div class="section-header text-{{ align_text }}">
        <h2 class="{% if heading_size %}{{ heading_size }}{% endif %}">{{ title }}</h2>
      </div>
    {%- endif -%}

    <div class="float-grid{% unless section.blocks.size == 5 %} grid--uniform{% endunless %} grid--flush-bottom clearfix">
      {%- liquid
        assign grid_item_width = 'medium-up--one-third'
        case section.blocks.size
          when 1
            assign grid_item_width = 'medium-up--one-half'
          when 2
            assign grid_item_width = 'medium-up--one-half'
          when 4
            assign grid_item_width = 'medium-up--one-half'
        endcase
      -%}
      {%- for block in section.blocks -%}
        {%- if section.blocks.size == 5 and forloop.index < 3 -%}
          {%- assign column_width = 'medium-up--one-half' -%}
        {%- else -%}
          {%- assign column_width = grid_item_width -%}
        {%- endif -%}
        <div class="grid__item {{ column_width }} text-{{ align_text }}" {{ block.shopify_attributes }}>
          {%- if block.settings.enable_image -%}
            <div style="{% if align_text == 'center' %}margin: 0 auto;{% endif %} max-width: {{ block.settings.image_width }}px;">
              {%- if block.settings.button_link != blank -%}
                <a href="{{ block.settings.button_link }}">
              {%- endif -%}
              {%- if block.settings.image != blank -%}
                <div
                  class="image-wrap text-spacing {% if block.settings.image_mask != 'none' %}svg-mask svg-mask--{{ block.settings.image_mask }}{% endif %}"
                  style="height: 0; padding-bottom: {{ 100 | divided_by: block.settings.image.aspect_ratio }}%;"
                >
                  {%- render 'image-element',
                    img: block.settings.image,
                    sizeVariable: column_width,
                    widths: '180, 360, 540, 720, 900, 1080',
                    style: 'height: 100%;'
                  -%}
                </div>
              {%- else -%}
                <div class="image-wrap text-spacing">
                  {%- render 'placeholder-svg', name: 'image', no_padding: true -%}
                </div>
              {%- endif -%}
              {%- if block.settings.button_link != blank -%}
                </a>
              {%- endif -%}
            </div>
          {%- endif -%}
          {%- if block.settings.title != blank -%}
            <h2 class="{% if heading_size %}{{ heading_size }}{% else %}h3{% endif %} rte--block">
              {{ block.settings.title }}
            </h2>
          {%- endif -%}
          {%- if block.settings.text != blank -%}
            <div class="rte-setting rte--block text-spacing">{{ block.settings.text }}</div>
          {%- endif -%}
          {%- if block.settings.button_label != blank -%}
            <a href="{{ block.settings.button_link }}" class="btn btn--secondary btn--small">
              {{ block.settings.button_label }}
            </a>
          {%- endif -%}
        </div>
      {%- endfor -%}
    </div>
  </div>
</div>
{%- if divider -%}</div>{%- endif -%}
