{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a cart item.

  Accepts:
  - item {line_item} - Cart item to be rendered

  Usage:
  {% render 'cart-item', item: cart.items[0] %}
{%- endcomment -%}

{%- liquid
  assign item = item | default: blank
-%}

<div class="cart__item" data-key="{{ item.key }}">
  <div class="cart__image">
    {% if item.image != blank %}
      <a href="{{ item.url }}" class="image-wrap">
        {%- render 'image-element',
          img: item,
          sizes: '150px',
          alt: item.product.title,
          widths: '180, 360, 540',
          classes: 'animate-instant'
        -%}
      </a>
    {% endif %}
  </div>

  <div class="cart__item-title">
    <a href="{{ item.url }}" class="cart__item-name">
      {{ item.product.title }}
    </a>

    {%- unless item.product.has_only_default_variant -%}
      <div class="cart__item--variants">
        {%- for option in item.options_with_values -%}
          <div>
            <span>{{ option.name }}:</span> {{ option.value }}
          </div>
        {%- endfor -%}
      </div>
    {%- endunless -%}

    {%- if item.selling_plan_allocation != empty -%}
      <div class="cart__item--variants">
        {{ item.selling_plan_allocation.selling_plan.name }}
      </div>
    {%- endif -%}

    {%- assign property_size = item.properties | size -%}

    {% if property_size > 0 %}
      {% for p in item.properties %}
        {%- assign first_character_in_key = p.first | truncate: 1, '' -%}
        {% unless p.last == blank or first_character_in_key == '_' %}
          <div class="cart__item--properties">
            <span>{{ p.first }}:</span>
            {% if p.last contains '/uploads/' %}
              <a href="{{ p.last }}">{{ p.last | split: '/' | last }}</a>
            {% else %}
              {{ p.last }}
            {% endif %}
          </div>
        {% endunless %}
      {% endfor %}
    {% endif %}
  </div>

  <div class="cart__item-quantity">
    {%- assign quantity_selector_id = 'cart_updates_' | append: item.key -%}
    <label for="{{ quantity_selector_id }}" class="visually-hidden">
      {% render 't_with_fallback', key: 'labels.quantity', fallback: 'Quantity' %}
    </label>
    {%- render 'quantity-selector',
      id: quantity_selector_id,
      name: 'updates[]',
      value: item.quantity,
      min: 0,
      cart_item_key: item.key
    -%}
  </div>

  <div class="cart__item-remove">
    <a href="{{ routes.cart_change_url }}?id={{ item.key }}&amp;quantity=0" class="text-link">
      {% render 't_with_fallback', key: 'actions.remove', fallback: 'Remove' %}
    </a>
  </div>

  <div class="cart__item-price text-right">
    {% if item.original_price != item.final_price %}
      <span class="visually-hidden">
        {% render 't_with_fallback', key: 'labels.regular_price', fallback: 'Regular price' %}
      </span>
      <small class="cart__price cart__price--strikethrough">
        {%- render 'price', price: item.original_price -%}
      </small>
      <span class="visually-hidden">
        {% render 't_with_fallback', key: 'labels.sale_price', fallback: 'Sale price' %}
      </span>
      <span class="cart__price cart__discount">
        {%- render 'price', price: item.final_price -%}
      </span>
    {% else %}
      <span class="cart__price">
        {%- render 'price', price: item.original_price -%}
      </span>
    {% endif %}

    {%- if item.unit_price_measurement != blank -%}
      {%- capture unit_price_base_unit -%}
        <span class="product__unit-base--{{ section.id }}">
          {%- if item.unit_price_measurement -%}
            {%- if item.unit_price_measurement.reference_value != 1 -%}
              {{ item.unit_price_measurement.reference_value }}
            {%- endif -%}
            {{ item.unit_price_measurement.reference_unit }}
          {%- endif -%}
        </span>
      {%- endcapture -%}

      <span class="product__unit-price">{{ item.unit_price | money }}/{{ unit_price_base_unit }}</span>
    {%- endif -%}

    {%- if item.line_level_discount_allocations != blank -%}
      {%- for discount_allocation in item.line_level_discount_allocations -%}
        <small class="cart__discount">
          {{- discount_allocation.discount_application.title }} (-{{ discount_allocation.amount | money }})</small
        >
      {%- endfor -%}
    {%- endif -%}
  </div>
</div>
