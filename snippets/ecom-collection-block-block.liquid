{% comment %}
 EComposer (https://ecomposer.io)
 You SHOULD NOT modify source code in this page because
 It is automatically generated from EComposer
 At 2025-06-20 09:55:24
 {% endcomment %} 
 
 {% assign ecom_root_url = routes.root_url %}
 
 {% if ecom_root_url != '/'%}
 {% assign ecom_root_url = routes.root_url | append: '/' %}
 {% endif %}
 <link rel="stylesheet" media="print" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.9" /><noscript><link rel="stylesheet" media="all" onload="this.media='all'" id="ecom-vendors-slider_css" href="https://cdn.ecomposer.app/vendors/css/<EMAIL>" /><link rel="stylesheet" media="all" id="ecom-vendors-css_ecomposer_base" href="https://cdn.ecomposer.app/vendors/css/ecom-base.css?v=1.9" /></noscript><script type="text/javascript" asyc="async" id="ecom-vendors-shopify_option_selection_js" src="https://cdn.ecomposer.app/vendors/js/ecomposer_option_selection.js" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-countdown_js" src="https://cdn.ecomposer.app/vendors/js/ecom-countdown.js?ver=2.0" ></script><script type="text/javascript" asyc="async" id="ecom-vendors-slider_js" src="https://cdn.ecomposer.app/vendors/js/<EMAIL>" ></script>
<link href="https://fonts.googleapis.com/css?family=Playfair+Display:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Nunito+Sans:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Urbanist:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Jost:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=DM+Sans:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?family=Albert+Sans:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet"/>
{{'ecom-68107b5dcb8ec620f203e732.css' | asset_url | stylesheet_tag }}
<script src="{{'ecom-68107b5dcb8ec620f203e732.js' | asset_url }}" defer="defer"></script>
<script type="text/javascript" class="ecom-page-info">
 window.EComposer = window.EComposer || {};
 window.EComposer.BLOCK_ID="68107b5dcb8ec620f203e732";
 window.EComposer.BLOCK = {"template_id":"68107b5dcb8ec620f203e732","title":"Collection Block","type":"block","slug":"ecom-collection-block","plan_id":4};
 </script>
<div class="ecom-builder" id="ecom-collection-block"><div class="ecom-sections" ><section class="ecom-row ecom-core ecom-section ecom-3x1xqz2f9ao" data-id="ecom-3x1xqz2f9ao" style="z-index: inherit;"><div data-deep="1" class="core__row--columns core__row--full"><div class="ecom-column ecom-core ecom-t6a99h1b5d"><div class="core__column--wrapper"><div class="ecom-column__overlay"><div class="ecom-overlay"></div></div><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-liaxzs6akyg" data-core-is="block"><div class="ecom__element ecom-element element__text" data-stopdrag="" deep="1"><div class="text-content ecom-html has-drop-cap-view-framed">Discover our curated collections and find the perfect pieces to elevate your space</div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-zbpvjlvmig" data-core-is="group"><div class="ecom-core ecom-group core__block ecom-27hppmvlaul"> <div class="core__group--wrapper tabs__wrapper ecom__element ecom__tabs tabs__wrapper--horizontal" data-r="" style=""><div class="tabs__navs tabs__navs--horizontal"><div class="tabs__navs--items"><div class="ecom-items tabs__nav ecom-item-active" data-target=""><div class="tabs_nav--content_right"><div class="tabs_nav--content"><h3 class="tabs_nav--text ecom-items--text">Savanna</h3></div><div class="tabs_nav--sub-text"></div></div></div><div class="ecom-items tabs__nav" data-target=""><div class="tabs_nav--content_right"><div class="tabs_nav--content"><h3 class="tabs_nav--text ecom-items--text">Cas</h3></div><div class="tabs_nav--sub-text"></div></div></div><div class="ecom-items tabs__nav" data-target=""><div class="tabs_nav--content_right"><div class="tabs_nav--content"><h3 class="tabs_nav--text ecom-items--text">Helio</h3></div><div class="tabs_nav--sub-text"></div></div></div><div class="ecom-items tabs__nav" data-target=""><div class="tabs_nav--content_right"><div class="tabs_nav--content"><h3 class="tabs_nav--text ecom-items--text">Terra</h3></div><div class="tabs_nav--sub-text"></div></div></div><div class="ecom-items tabs__nav" data-target=""><div class="tabs_nav--content_right"><div class="tabs_nav--content"><h3 class="tabs_nav--text ecom-items--text">Stria</h3></div><div class="tabs_nav--sub-text"></div></div></div><div class="ecom-items tabs__nav" data-target=""><div class="tabs_nav--content_right"><div class="tabs_nav--content"><h3 class="tabs_nav--text ecom-items--text">Opus</h3></div><div class="tabs_nav--sub-text"></div></div></div></div></div><div class="core__group--items"><div id="ecom-hyfa5modpol" class="core__group--item tab__item ecom-item-active ecom-item-group-init" data-id="ecom-hyfa5modpol"><div class="core__group--body tabs__body"><div class="core__blocks" index="0"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-codw20hf0w4" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__product" deep="1"><div class="ecom-collection__product-wrapper"><div class="ecom-collection__product-container ecom-collection__product-container_featured"><div class="ecom-collection__product-main ecom-swiper-container ecom-collection_product_template_featured" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;loop&quot;:true,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1,&quot;spaceBetween&quot;:15},&quot;768&quot;:{&quot;slidesPerView&quot;:3,&quot;spaceBetween&quot;:15},&quot;1025&quot;:{&quot;slidesPerView&quot;:4,&quot;spaceBetween&quot;:14}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:0,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:200,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;}}" data-week="[%-W] week%!W" data-day="[%-d] day%!D" data-hour="[%-H] hr%!H" data-minute="[%-M] min%!M" data-second="[%-S] sec%!S" data-sale="Save {{sale}}" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{-review_platform-}}" data-countdown-shows="day,hour,minute,second" data-translate="false">
 {%- capture badge_tags -%}Hot,Best Selling,Trending Item{%- endcapture -%}
 {%- liquid
 assign colors = shop.metafields.ecomposer.colors
 assign badge_tags = badge_tags | strip | split: ','
 assign tmp_collection = collection
 assign tmp_product = product
 assign enable_hook = shop.metafields.ecomposer.enable_hook.value
 -%}
 
 
 {%- capture limit-%}4{% endcapture %}
 {%- if limit == blank -%}
 {% assign limit = 12 %}
 {% else %}
 {% assign limit = limit | plus: 0 %}
 {%- endif-%}
 
 {%- capture handle_collection -%}savanna-collections{% endcapture%}
 {% assign collection = collections[handle_collection] %}
 {%- if handle_collection and collection.handle != blank -%}
 {% assign products = collection.products | where: "available" %}
 {% else %}
 {% assign products = collections.all.products | where: "available" %}
 {% assign collection = collections.all%}
 {%- endif -%}
 {% assign is_collection = true %}
 
 {% if false and featured == product %}
 {% if true %}
 {% assign products = blank %}
 {% endif %}
 {%- if recommendations.performed? and recommendations.products_count > 0 -%}
 {% assign products = recommendations.products %}
 {%- endif -%}
 {% endif %}
 {% capture quickshop_layout%}full{% endcapture %}
 {% capture product_style%}vertical{% endcapture%}
 {%- assign view_more_only = undefined -%}
 {% capture product_layout %}slider{% endcapture %}
 {% assign check_min = 0%}
 {% if products and products.size > check_min and products != blank %}
 <div class="ecom-swiper-wrapper
 ecom-collection__product--wrapper-items ecom-collection-product__layout-slider
 "
 data-grid-column="4"
 data-grid-column-tablet="3"
 data-grid-column-mobile="1"
 
 data-icon-add='<svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>'
 >
 {% assign ecom_count = 0 %}
 {% for p in products %}
 {% assign product = p %}
 {% if p.handle %}
 {% assign product = p %}
 {% else %}
 {% assign product = all_products[p] %}
 {% endif %}
 {% if product.url == blank %} {% continue %} {% endif %}
 {% if ecom_count >= limit %}{% break %}{% endif %}
 
 
 {% assign ecom_count = ecom_count | plus: 1 %}
 {%- capture swatch_option -%}Color{%- endcapture -%}
 {% assign hide_quickshop = true %}
 {% assign other_option_layout = 'radio' %}
 {% capture product_picker%}
 
 {%- if product.has_only_default_variant == false-%}
 {% if quickshop_layout != 'full' and product.options.size > 1%}
 {% assign hide_quickshop = false %}
 {% endif %}
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <div class="ecom-collection__product-variants" data-picker-type="image">
 <button class="ecom-collection__product-close"></button>
 <form class="ecom-collection__product-form ecom-product-form" product_id="{{product.id}}" data-product_id="{{product.id}}" data-product-id="{{product.id}}" data-handle="{{product.handle}}">
 <div class="ecom-child-element" >
 {% assign variant_selected = product.first_available_variant%}
 
 {%- capture swatch_option_temp -%}Color{%- endcapture -%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {% assign swatch_option = swatch_option | split: ',' %}
 {% assign option_index = current_option.position | minus: 1 %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-main ecom-collection__product-picker-option-{{current_option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-collection__product-swatch-item ecom-collection__product-picker-images-item {%comment%}{% if option_value == variant_selected[index] %}ecom-product-swatch-item--active ecom-button-active{% endif %}{%endcomment%}" data-value="{{ option_value | escape }}">
 <span class="ecom-collection__product-swatch-item--wrapper"></span>
 <img src="{{ variant | img_url: "120x120", crop: 'center' }}" alt=" {{ option_value }}" />
 </li>
 {%- endfor -%}
 </ul>
 
 </div>
 {% endif %}
 {% endfor %}
 {%- if other_option_layout != 'hide' -%}
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}{% continue%}{%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-other ecom-collection__product-picker-option-{{option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-radio-list ecom-d-flex">
 {% for value in option.values %}
 <li class="ecom-collection__product-swatch-item ecom-collection__product-picker-radio-list-item {% if forloop.first %}ecom-product-swatch-item--active ecom-button-active{% endif %}" data-option-index="{{ option_index }}" data-value="{{ value | escape }}">
 {{value}}
 </li>
 {% endfor %}
 </ul>
 
 </div>
 {%- endfor -%}
 </div>
 {%- endif -%}
 
 
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 <div class="ecom-collection__product-picker-selection" style="display:none;">
 <select name="variant_id" data-product-id="{{product.id}}" id="ecom-variant-selector-{{product.id}}-ecom-codw20hf0w4">
 {% for variant in product.variants %}
 <option value="{{variant.id}}" {% if product.first_available_variant.id == variant.id%} selected {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div>
 </div>
 </div>
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions ">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 <div class="ecom-collection__product-quick-shop-add-to-cart-wrapper {% if hide_quickshop or view_more_only %} ecom-collection__product-quick-shop--force-show{% endif %} ">
 <div class="ecom-collection__product-form__actions ">
 
 <button type="button" class="ecom-child-element ecom-collection__product-submit ecom-ajax-cart-submit ecom-collection__product-simple-add-to-cart
 ecom-collection__product-add-cart-icon-before"
 data-text-add-cart="Add to cart "
 data-text-unavailable="Unavailable"
 data-text-sold-out="Sold out"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 data-text-pre-order="Pre order"
 
 >
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 Add to cart 
 </span>
 </button>
 </div>
 
 </div>
 {%- endif -%}
 </form> {% comment %} End form {% endcomment %}
 <script class="product-json" type="application/json">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% endif %}
 
 {% endcapture%}
 {% capture product_actions%}
 <div class="ecom-collection__product--actions" data-layout="{{quickshop_layout}}">
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions "
 
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% if view_more_only %}
 
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 
 {% elsif product.has_only_default_variant == true and product.available == false %}
 
 
 <div class="ecom-collection__product-form__actions--soldout ecom-collection__product-sold-out-before ecom-child-element"
 
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-sold-out-text">
 Sold out
 </span>
 </div>
 
 {% elsif product.has_only_default_variant %}
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 
 
 <a data-no-instant href="{{ecom_root_url}}cart/add?id={{ product.variants.first.id }}&quantity=1"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 class="ecom-collection__product-submit ecom-collection__product-form__actions--add ecom-collection__product-simple-add-to-cart ecom-ajax-cart-simple ecom-collection__product-add-cart-icon-before ecom-child-element" 
 data-id="{{ product.variants.first.id }}"
 data-handle="{{ product.handle }}" data-pid="{{ product.id }}" title="{{ product.title | escape }}">
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 {%- if product.variants.first.inventory_management and product.variants.first.inventory_quantity <= 0 and product.variants.first.inventory_policy == 'continue' -%}
 Pre order
 {%-else-%}
 Add to cart 
 {%- endif -%}
 </span>
 </a>
 
 {%- endif -%}
 {% else %}
 
 
 <button class="ecom-collection__product-form__actions--quickshop ecom-collection__product-quickshop-icon-before
 {% if hide_quickshop %} ecom-collection__product-quick-shop--force-hide{% endif %} ecom-child-element" type="button">
 
 <span class="ecom-collection__product-form__actions--quickshop-text">
 Quick Shop
 </span>
 </button>
 
 
 {% endif %}
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 </div>
 {% endcapture %}
 <div class="ecom-collection__product-item ecom-swiper-slide" data-product-handle="{{product.handle}}" data-style="{{product_style}}"{% unless product.has_only_default_variant %} ec-variant-init{% endunless %}>
 <div
 class="ecom-collection__product-item--wrapper {% if product_style == 'horizontal'%} ecom-d-flex {% else %} ecom-flex-column {% endif %}">
 <div class="ecom-collection__product-media-wrapper ecom-enable-hover--mobile {% if product_style == 'horizontal'%} ecom-d-flex{% endif %} "
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <a href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}" target="" title="{{product.title | escape }}" class="ecom-collection__product-item--inner ecom-image-default">
 {%- if product.featured_media -%}
 {%- liquid
 assign featured_media_aspect_ratio = product.featured_media.aspect_ratio
 if product.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <div class="ecom-collection__product-media--container">
 <div
 class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt
 {% if product.media[1] != nil %}ecom-collection__product-media--hover-effect{% endif %}"
 style="padding-bottom: {{ 1 | divided_by: featured_media_aspect_ratio | times: 100 }}%;"
 
 >
 <img srcset="{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.featured_media | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.featured_media.alt | escape }}"
 
 class="ecom-collection__product-media-image"
 width="{{ product.featured_media.width }}"
 height="{{ product.featured_media.height }}"
 />
 
 {%- if product.media[1] != nil -%}
 <img srcset="{%- if product.media[1].width >= 533 -%}{{ product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.media[1].width >= 720 -%}{{ product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.media[1].width >= 940 -%}{{ product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.media[1].width >= 1066 -%}{{ product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.media[1] | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.media[1].alt | escape }}"
 
 class="ecom-collection__product-secondary-media"
 width="{{ product.media[1].width }}"
 height="{{ product.media[1].height }}"
 />
 {%- endif -%}
 
 </div>
 </div>
 {% else %}
 <div class="ecom-collection__product-media--container">
 <div class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt"
 
 style="padding-bottom: 85%;"
 >
 {{ 'product-1' | placeholder_svg_tag: 'ecom-colection__product-svg-placeholder' }}
 </div>
 </div>
 {% endif %}
 
 
 
 
 </a>
 
 <div class="ecom-collection__product-group-button-action " style="justify-content: start">
 
 
 </div>
 </div>
 <div class="ecom-collection__product-item--information">
 <div class="ecom-collection__product-item--information-wrapper ecom-flex ecom-column">
 {% if product_style == 'absolute' %}
 {{product_actions}}
 {{ product_picker}}
 {% endif %}
 
 
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <h3
 class="ecom-collection__product-title-tag ecom-child-element"
 
 >
 <a
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{product.title | escape }}"
 target=""
 class="ecom-collection__product-item-information-title "
 >
 {{ product.title | strip_html}}
 </a>
 </h3>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 
 
 {% assign showPriceWhenNotLogin = true %}
 {% if undefined and customer == blank%}
 {% assign showPriceWhenNotLogin = false %}
 {% endif %}
 {% if showPriceWhenNotLogin == false and undefined %}
 {% assign showOnLive = true %}
 {% else %}
 {% assign showOnLive = false %}
 {% endif %}
 {% assign showPreview = false %}
 {% if showOnLive or showPreview %}
 <div class="ecom-collection__product-login-to-see">
 undefined
 </div>
 {% endif %}
 {% if true and showPriceWhenNotLogin %}
 <div class="ecom-collection__product-prices ecom-child-element" >
 {% capture ground_price %}
 
 {% endcapture %}
 {%- assign target = product.selected_or_first_available_variant -%}
 
 {%- assign target = product.selected_or_first_available_variant -%}
 <div class="ecom-collection__product-price-wrapper">
 <span
 class="ecom-collection__product-price--regular ecom-collection__product--compare-at-price"
 {%- if product.compare_at_price == nil or product.compare_at_price <= product.price -%} style="display:none" {% endif %}
 >
 {% if settings.currency_code_enabled == true %} {{ target.compare_at_price | money_with_currency }} {% else %} {{ target.compare_at_price | money }} {% endif %}
 </span>
 <span class="ecom-collection__product-price{% if target.compare_at_price > target.price %} ecom-collection__product-price--sale{% endif %}"
 {% if false %} bss-b2b-product-price bss-b2b-variant-price {% endif %}
 {% if false %}
 bss-b2b-product-id="{{ product.id }}"
 bss-b2b-variant-id="{{ product.selected_or_first_available_variant.id }}"
 {% endif %}
 >{% if settings.currency_code_enabled == true %} {{target.price | money_with_currency }} {% else %} {{target.price | money }} {% endif %}</span>
 </div>
 {{ ground_price }}
 
 </div>
 {% endif %}
 
 {% if product_style != 'absolute'%}
 {{ product_picker }}
 {% endif %}
 {% if product_style == 'horizontal' or product_style == 'vertical' or product_layout == 'list' %}
 {{product_actions}}
 {% endif %}
 
 </div>
 </div>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 {% endfor %}
 </div>
 {% else %}
 <div class="ecom-collection__product--wrapper-items ecom-collection__product--no-item ecom-collection-product__layout-slider" >
 <div class="ecom-collection__product-item" >
 There are no product yet!
 </div>
 </div>
 {% endif %}
 
 
 {% assign collection = tmp_collection %}
 {% assign product = tmp_product %}
 </div><div class="ecom-swiper-navigation-position"><button class="ecom-swiper-button ecom-swiper-button-prev" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M192 448c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25l160-160c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25L77.25 256l137.4 137.4c12.5 12.5 12.5 32.75 0 45.25C208.4 444.9 200.2 448 192 448z"></path></svg></button><button class="ecom-swiper-button ecom-swiper-button-next" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M64 448c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L178.8 256L41.38 118.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l160 160c12.5 12.5 12.5 32.75 0 45.25l-160 160C80.38 444.9 72.19 448 64 448z"></path></svg></button></div><div class="ecom-swiper-pagination-position ecom-swiper-pagination" style="display: none;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-5myogl0bn6y" data-core-is="block"><div class="ecom__element ecom-element-button ecom-button-default" deep="1"><a class="ecom__element--button ecom-flex ecom-fl_center ecom-al_center" href="https://sicotas.com/collections/savanna-collections"><span class="ecom__element--button-icon-text">View More</span><span class="ecom-button-icon ecom__element--button-icon"></span></a></div></div> </div></div></div></div><div id="ecom-i0qh2ttu3xc" class="core__group--item tab__item" data-id="ecom-i0qh2ttu3xc"><div class="core__group--body tabs__body"><div class="core__blocks" index="1"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block ecom-tddmbs31vqq" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__product" deep="1"><div class="ecom-collection__product-wrapper"><div class="ecom-collection__product-container ecom-collection__product-container_featured"><div class="ecom-collection__product-main ecom-swiper-container ecom-collection_product_template_featured" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;loop&quot;:true,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1,&quot;spaceBetween&quot;:15},&quot;768&quot;:{&quot;slidesPerView&quot;:3,&quot;spaceBetween&quot;:15},&quot;1025&quot;:{&quot;slidesPerView&quot;:4,&quot;spaceBetween&quot;:14}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:0,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:200,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;}}" data-week="[%-W] week%!W" data-day="[%-d] day%!D" data-hour="[%-H] hr%!H" data-minute="[%-M] min%!M" data-second="[%-S] sec%!S" data-sale="Save {{sale}}" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{-review_platform-}}" data-countdown-shows="day,hour,minute,second" data-translate="false">
 {%- capture badge_tags -%}Hot,Best Selling,Trending Item{%- endcapture -%}
 {%- liquid
 assign colors = shop.metafields.ecomposer.colors
 assign badge_tags = badge_tags | strip | split: ','
 assign tmp_collection = collection
 assign tmp_product = product
 assign enable_hook = shop.metafields.ecomposer.enable_hook.value
 -%}
 
 
 {%- capture limit-%}4{% endcapture %}
 {%- if limit == blank -%}
 {% assign limit = 12 %}
 {% else %}
 {% assign limit = limit | plus: 0 %}
 {%- endif-%}
 
 {%- capture handle_collection -%}cas-collections{% endcapture%}
 {% assign collection = collections[handle_collection] %}
 {%- if handle_collection and collection.handle != blank -%}
 {% assign products = collection.products | where: "available" %}
 {% else %}
 {% assign products = collections.all.products | where: "available" %}
 {% assign collection = collections.all%}
 {%- endif -%}
 {% assign is_collection = true %}
 
 {% if false and featured == product %}
 {% if true %}
 {% assign products = blank %}
 {% endif %}
 {%- if recommendations.performed? and recommendations.products_count > 0 -%}
 {% assign products = recommendations.products %}
 {%- endif -%}
 {% endif %}
 {% capture quickshop_layout%}full{% endcapture %}
 {% capture product_style%}vertical{% endcapture%}
 {%- assign view_more_only = undefined -%}
 {% capture product_layout %}slider{% endcapture %}
 {% assign check_min = 0%}
 {% if products and products.size > check_min and products != blank %}
 <div class="ecom-swiper-wrapper
 ecom-collection__product--wrapper-items ecom-collection-product__layout-slider
 "
 data-grid-column="4"
 data-grid-column-tablet="3"
 data-grid-column-mobile="1"
 
 data-icon-add='<svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>'
 >
 {% assign ecom_count = 0 %}
 {% for p in products %}
 {% assign product = p %}
 {% if p.handle %}
 {% assign product = p %}
 {% else %}
 {% assign product = all_products[p] %}
 {% endif %}
 {% if product.url == blank %} {% continue %} {% endif %}
 {% if ecom_count >= limit %}{% break %}{% endif %}
 
 
 {% assign ecom_count = ecom_count | plus: 1 %}
 {%- capture swatch_option -%}Color{%- endcapture -%}
 {% assign hide_quickshop = true %}
 {% assign other_option_layout = 'radio' %}
 {% capture product_picker%}
 
 {%- if product.has_only_default_variant == false-%}
 {% if quickshop_layout != 'full' and product.options.size > 1%}
 {% assign hide_quickshop = false %}
 {% endif %}
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <div class="ecom-collection__product-variants" data-picker-type="image">
 <button class="ecom-collection__product-close"></button>
 <form class="ecom-collection__product-form ecom-product-form" product_id="{{product.id}}" data-product_id="{{product.id}}" data-product-id="{{product.id}}" data-handle="{{product.handle}}">
 <div class="ecom-child-element" >
 {% assign variant_selected = product.first_available_variant%}
 
 {%- capture swatch_option_temp -%}Color{%- endcapture -%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {% assign swatch_option = swatch_option | split: ',' %}
 {% assign option_index = current_option.position | minus: 1 %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-main ecom-collection__product-picker-option-{{current_option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-collection__product-swatch-item ecom-collection__product-picker-images-item {%comment%}{% if option_value == variant_selected[index] %}ecom-product-swatch-item--active ecom-button-active{% endif %}{%endcomment%}" data-value="{{ option_value | escape }}">
 <span class="ecom-collection__product-swatch-item--wrapper"></span>
 <img src="{{ variant | img_url: "120x120", crop: 'center' }}" alt=" {{ option_value }}" />
 </li>
 {%- endfor -%}
 </ul>
 
 </div>
 {% endif %}
 {% endfor %}
 {%- if other_option_layout != 'hide' -%}
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}{% continue%}{%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-other ecom-collection__product-picker-option-{{option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-radio-list ecom-d-flex">
 {% for value in option.values %}
 <li class="ecom-collection__product-swatch-item ecom-collection__product-picker-radio-list-item {% if forloop.first %}ecom-product-swatch-item--active ecom-button-active{% endif %}" data-option-index="{{ option_index }}" data-value="{{ value | escape }}">
 {{value}}
 </li>
 {% endfor %}
 </ul>
 
 </div>
 {%- endfor -%}
 </div>
 {%- endif -%}
 
 
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 <div class="ecom-collection__product-picker-selection" style="display:none;">
 <select name="variant_id" data-product-id="{{product.id}}" id="ecom-variant-selector-{{product.id}}-ecom-tddmbs31vqq">
 {% for variant in product.variants %}
 <option value="{{variant.id}}" {% if product.first_available_variant.id == variant.id%} selected {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div>
 </div>
 </div>
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions ">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 <div class="ecom-collection__product-quick-shop-add-to-cart-wrapper {% if hide_quickshop or view_more_only %} ecom-collection__product-quick-shop--force-show{% endif %} ">
 <div class="ecom-collection__product-form__actions ">
 
 <button type="button" class="ecom-child-element ecom-collection__product-submit ecom-ajax-cart-submit ecom-collection__product-simple-add-to-cart
 ecom-collection__product-add-cart-icon-before"
 data-text-add-cart="Add to cart "
 data-text-unavailable="Unavailable"
 data-text-sold-out="Sold out"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 data-text-pre-order="Pre order"
 
 >
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 Add to cart 
 </span>
 </button>
 </div>
 
 </div>
 {%- endif -%}
 </form> {% comment %} End form {% endcomment %}
 <script class="product-json" type="application/json">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% endif %}
 
 {% endcapture%}
 {% capture product_actions%}
 <div class="ecom-collection__product--actions" data-layout="{{quickshop_layout}}">
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions "
 
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% if view_more_only %}
 
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 
 {% elsif product.has_only_default_variant == true and product.available == false %}
 
 
 <div class="ecom-collection__product-form__actions--soldout ecom-collection__product-sold-out-before ecom-child-element"
 
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-sold-out-text">
 Sold out
 </span>
 </div>
 
 {% elsif product.has_only_default_variant %}
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 
 
 <a data-no-instant href="{{ecom_root_url}}cart/add?id={{ product.variants.first.id }}&quantity=1"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 class="ecom-collection__product-submit ecom-collection__product-form__actions--add ecom-collection__product-simple-add-to-cart ecom-ajax-cart-simple ecom-collection__product-add-cart-icon-before ecom-child-element" 
 data-id="{{ product.variants.first.id }}"
 data-handle="{{ product.handle }}" data-pid="{{ product.id }}" title="{{ product.title | escape }}">
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 {%- if product.variants.first.inventory_management and product.variants.first.inventory_quantity <= 0 and product.variants.first.inventory_policy == 'continue' -%}
 Pre order
 {%-else-%}
 Add to cart 
 {%- endif -%}
 </span>
 </a>
 
 {%- endif -%}
 {% else %}
 
 
 <button class="ecom-collection__product-form__actions--quickshop ecom-collection__product-quickshop-icon-before
 {% if hide_quickshop %} ecom-collection__product-quick-shop--force-hide{% endif %} ecom-child-element" type="button">
 
 <span class="ecom-collection__product-form__actions--quickshop-text">
 Quick Shop
 </span>
 </button>
 
 
 {% endif %}
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 </div>
 {% endcapture %}
 <div class="ecom-collection__product-item ecom-swiper-slide" data-product-handle="{{product.handle}}" data-style="{{product_style}}"{% unless product.has_only_default_variant %} ec-variant-init{% endunless %}>
 <div
 class="ecom-collection__product-item--wrapper {% if product_style == 'horizontal'%} ecom-d-flex {% else %} ecom-flex-column {% endif %}">
 <div class="ecom-collection__product-media-wrapper ecom-enable-hover--mobile {% if product_style == 'horizontal'%} ecom-d-flex{% endif %} "
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <a href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}" target="" title="{{product.title | escape }}" class="ecom-collection__product-item--inner ecom-image-default">
 {%- if product.featured_media -%}
 {%- liquid
 assign featured_media_aspect_ratio = product.featured_media.aspect_ratio
 if product.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <div class="ecom-collection__product-media--container">
 <div
 class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt
 {% if product.media[1] != nil %}ecom-collection__product-media--hover-effect{% endif %}"
 style="padding-bottom: {{ 1 | divided_by: featured_media_aspect_ratio | times: 100 }}%;"
 
 >
 <img srcset="{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.featured_media | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.featured_media.alt | escape }}"
 
 class="ecom-collection__product-media-image"
 width="{{ product.featured_media.width }}"
 height="{{ product.featured_media.height }}"
 />
 
 {%- if product.media[1] != nil -%}
 <img srcset="{%- if product.media[1].width >= 533 -%}{{ product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.media[1].width >= 720 -%}{{ product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.media[1].width >= 940 -%}{{ product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.media[1].width >= 1066 -%}{{ product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.media[1] | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.media[1].alt | escape }}"
 
 class="ecom-collection__product-secondary-media"
 width="{{ product.media[1].width }}"
 height="{{ product.media[1].height }}"
 />
 {%- endif -%}
 
 </div>
 </div>
 {% else %}
 <div class="ecom-collection__product-media--container">
 <div class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt"
 
 style="padding-bottom: 85%;"
 >
 {{ 'product-1' | placeholder_svg_tag: 'ecom-colection__product-svg-placeholder' }}
 </div>
 </div>
 {% endif %}
 
 <div class="ecom-collection__product-badge">
 
 
 <span class="ecom-collection__product-badge--sold-out" aria-hidden="true" style="{%- if product.available == true -%}display: none; {%- endif -%}">
 Sold
 </span>
 <span class="ecom-collection__product-badge--sale" aria-hidden="true" style="{%- unless product.compare_at_price > product.price and product.available -%}display: none;{%- endunless -%}">
 Sale
 </span>
 
 {% if badge_tags %}
 {% for badge in badge_tags %}
 {% for original_tag in product.tags %}
 {% assign tag = original_tag | replace: ' ', ' ' %}
 {% if tag == badge %}
 <span class="ecom-collection__product-badge--custom ecom-collection__product-badge--{{ badge | handleize}}" aria-hidden="true">
 {{ badge }}
 </span>
 {% endif %}
 {% endfor %}
 {%- assign bad = badge | strip -%}
 {% endfor %}
 {% endif %}
 
 {%- if product.has_only_default_variant -%}
 {%- if product.compare_at_price != null and product.compare_at_price > product.price -%}
 {%- assign sale = product.compare_at_price | minus: product.price | money -%}
 <span class="ecom-collection__product-price--bage-sale">
 Save {{sale}}
 </span>
 {%- endif -%}
 {%- else -%}
 {%- if product.selected_or_first_available_variant.compare_at_price != null and product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
 {%- assign sale = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | money -%}
 {%else%}
 {%- assign sale = null %}
 {%- endif -%}
 <span class="ecom-collection__product-price--bage-sale" style="{% unless sale %}display:none{% endunless %}">
 Save {{sale}}
 </span>
 {%- endif -%}
 </div>
 </a>
 
 <div class="ecom-collection__product-group-button-action " style="justify-content: start">
 
 
 </div>
 </div>
 <div class="ecom-collection__product-item--information">
 <div class="ecom-collection__product-item--information-wrapper ecom-flex ecom-column">
 {% if product_style == 'absolute' %}
 {{product_actions}}
 {{ product_picker}}
 {% endif %}
 
 
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <h3
 class="ecom-collection__product-title-tag ecom-child-element"
 
 >
 <a
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{product.title | escape }}"
 target=""
 class="ecom-collection__product-item-information-title "
 >
 {{ product.title | strip_html}}
 </a>
 </h3>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 
 
 {% assign showPriceWhenNotLogin = true %}
 {% if undefined and customer == blank%}
 {% assign showPriceWhenNotLogin = false %}
 {% endif %}
 {% if showPriceWhenNotLogin == false and undefined %}
 {% assign showOnLive = true %}
 {% else %}
 {% assign showOnLive = false %}
 {% endif %}
 {% assign showPreview = false %}
 {% if showOnLive or showPreview %}
 <div class="ecom-collection__product-login-to-see">
 undefined
 </div>
 {% endif %}
 {% if true and showPriceWhenNotLogin %}
 <div class="ecom-collection__product-prices ecom-child-element" >
 {% capture ground_price %}
 
 {% endcapture %}
 {%- assign target = product.selected_or_first_available_variant -%}
 
 {%- assign target = product.selected_or_first_available_variant -%}
 <div class="ecom-collection__product-price-wrapper">
 <span
 class="ecom-collection__product-price--regular ecom-collection__product--compare-at-price"
 {%- if product.compare_at_price == nil or product.compare_at_price <= product.price -%} style="display:none" {% endif %}
 >
 {% if settings.currency_code_enabled == true %} {{ target.compare_at_price | money_with_currency }} {% else %} {{ target.compare_at_price | money }} {% endif %}
 </span>
 <span class="ecom-collection__product-price{% if target.compare_at_price > target.price %} ecom-collection__product-price--sale{% endif %}"
 {% if false %} bss-b2b-product-price bss-b2b-variant-price {% endif %}
 {% if false %}
 bss-b2b-product-id="{{ product.id }}"
 bss-b2b-variant-id="{{ product.selected_or_first_available_variant.id }}"
 {% endif %}
 >{% if settings.currency_code_enabled == true %} {{target.price | money_with_currency }} {% else %} {{target.price | money }} {% endif %}</span>
 </div>
 {{ ground_price }}
 
 </div>
 {% endif %}
 
 {% if product_style != 'absolute'%}
 {{ product_picker }}
 {% endif %}
 {% if product_style == 'horizontal' or product_style == 'vertical' or product_layout == 'list' %}
 {{product_actions}}
 {% endif %}
 
 {% if product.available and product.metafields.ecomposer.countdown %}
 <div
 class="ecom-collection__product-countdown ecom-child-element" 
 >
 <div
 class="ecom-collection__product-countdown-wrapper"
 >
 <div class="ecom-collection__product-countdown-wrapper--title"> </div>
 <div class="ecom-product-single__countdown-container" >
 {%- assign countdown_from = product.metafields.ecomposer.countdown_from -%}
 <div data-product-id="{{product.id}}" class="ecom-collection__product-countdown-time ecom-collection__product-countdown-time--metafields" data-ecom-countdown-from="{{ countdown_from }}" data-ecom-countdown="{{product.metafields.ecomposer.countdown}}"></div>
 </div>
 
 </div>
 </div>
 {% endif %}
 
 </div>
 </div>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 {% endfor %}
 </div>
 {% else %}
 <div class="ecom-collection__product--wrapper-items ecom-collection__product--no-item ecom-collection-product__layout-slider" >
 <div class="ecom-collection__product-item" >
 There are no product yet!
 </div>
 </div>
 {% endif %}
 
 
 {% assign collection = tmp_collection %}
 {% assign product = tmp_product %}
 </div><div class="ecom-swiper-navigation-position"><button class="ecom-swiper-button ecom-swiper-button-prev" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M192 448c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25l160-160c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25L77.25 256l137.4 137.4c12.5 12.5 12.5 32.75 0 45.25C208.4 444.9 200.2 448 192 448z"></path></svg></button><button class="ecom-swiper-button ecom-swiper-button-next" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M64 448c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L178.8 256L41.38 118.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l160 160c12.5 12.5 12.5 32.75 0 45.25l-160 160C80.38 444.9 72.19 448 64 448z"></path></svg></button></div><div class="ecom-swiper-pagination-position ecom-swiper-pagination" style="display: none;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-j6vl5vj1jue" data-core-is="block"><div class="ecom__element ecom-element-button ecom-button-default" deep="1"><a class="ecom__element--button ecom-flex ecom-fl_center ecom-al_center" href="https://sicotas.com/collections/cas-collections"><span class="ecom__element--button-icon-text">View More</span><span class="ecom-button-icon ecom__element--button-icon"></span></a></div></div> </div></div></div></div><div id="ecom-sphoq293gz" class="core__group--item tab__item" data-id="ecom-sphoq293gz"><div class="core__group--body tabs__body"><div class="core__blocks" index="2"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block ecom-u6zslnflk8h" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__product" deep="1"><div class="ecom-collection__product-wrapper"><div class="ecom-collection__product-container ecom-collection__product-container_featured"><div class="ecom-collection__product-main ecom-swiper-container ecom-collection_product_template_featured" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;loop&quot;:true,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1,&quot;spaceBetween&quot;:15},&quot;768&quot;:{&quot;slidesPerView&quot;:3,&quot;spaceBetween&quot;:15},&quot;1025&quot;:{&quot;slidesPerView&quot;:4,&quot;spaceBetween&quot;:14}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:0,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:200,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;}}" data-week="[%-W] week%!W" data-day="[%-d] day%!D" data-hour="[%-H] hr%!H" data-minute="[%-M] min%!M" data-second="[%-S] sec%!S" data-sale="Save {{sale}}" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{-review_platform-}}" data-countdown-shows="day,hour,minute,second" data-translate="false">
 {%- capture badge_tags -%}Hot,Best Selling,Trending Item{%- endcapture -%}
 {%- liquid
 assign colors = shop.metafields.ecomposer.colors
 assign badge_tags = badge_tags | strip | split: ','
 assign tmp_collection = collection
 assign tmp_product = product
 assign enable_hook = shop.metafields.ecomposer.enable_hook.value
 -%}
 
 
 {%- capture limit-%}8{% endcapture %}
 {%- if limit == blank -%}
 {% assign limit = 12 %}
 {% else %}
 {% assign limit = limit | plus: 0 %}
 {%- endif-%}
 
 {%- capture handle_collection -%}helio-collections{% endcapture%}
 {% assign collection = collections[handle_collection] %}
 {%- if handle_collection and collection.handle != blank -%}
 {% assign products = collection.products | where: "available" %}
 {% else %}
 {% assign products = collections.all.products | where: "available" %}
 {% assign collection = collections.all%}
 {%- endif -%}
 {% assign is_collection = true %}
 
 {% if false and featured == product %}
 {% if true %}
 {% assign products = blank %}
 {% endif %}
 {%- if recommendations.performed? and recommendations.products_count > 0 -%}
 {% assign products = recommendations.products %}
 {%- endif -%}
 {% endif %}
 {% capture quickshop_layout%}full{% endcapture %}
 {% capture product_style%}vertical{% endcapture%}
 {%- assign view_more_only = undefined -%}
 {% capture product_layout %}slider{% endcapture %}
 {% assign check_min = 0%}
 {% if products and products.size > check_min and products != blank %}
 <div class="ecom-swiper-wrapper
 ecom-collection__product--wrapper-items ecom-collection-product__layout-slider
 "
 data-grid-column="4"
 data-grid-column-tablet="3"
 data-grid-column-mobile="1"
 
 data-icon-add='<svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>'
 >
 {% assign ecom_count = 0 %}
 {% for p in products %}
 {% assign product = p %}
 {% if p.handle %}
 {% assign product = p %}
 {% else %}
 {% assign product = all_products[p] %}
 {% endif %}
 {% if product.url == blank %} {% continue %} {% endif %}
 {% if ecom_count >= limit %}{% break %}{% endif %}
 
 
 {% assign ecom_count = ecom_count | plus: 1 %}
 {%- capture swatch_option -%}Color{%- endcapture -%}
 {% assign hide_quickshop = true %}
 {% assign other_option_layout = 'radio' %}
 {% capture product_picker%}
 
 {%- if product.has_only_default_variant == false-%}
 {% if quickshop_layout != 'full' and product.options.size > 1%}
 {% assign hide_quickshop = false %}
 {% endif %}
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <div class="ecom-collection__product-variants" data-picker-type="image">
 <button class="ecom-collection__product-close"></button>
 <form class="ecom-collection__product-form ecom-product-form" product_id="{{product.id}}" data-product_id="{{product.id}}" data-product-id="{{product.id}}" data-handle="{{product.handle}}">
 <div class="ecom-child-element" >
 {% assign variant_selected = product.first_available_variant%}
 
 {%- capture swatch_option_temp -%}Color{%- endcapture -%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {% assign swatch_option = swatch_option | split: ',' %}
 {% assign option_index = current_option.position | minus: 1 %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-main ecom-collection__product-picker-option-{{current_option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-collection__product-swatch-item ecom-collection__product-picker-images-item {%comment%}{% if option_value == variant_selected[index] %}ecom-product-swatch-item--active ecom-button-active{% endif %}{%endcomment%}" data-value="{{ option_value | escape }}">
 <span class="ecom-collection__product-swatch-item--wrapper"></span>
 <img src="{{ variant | img_url: "120x120", crop: 'center' }}" alt=" {{ option_value }}" />
 </li>
 {%- endfor -%}
 </ul>
 
 </div>
 {% endif %}
 {% endfor %}
 {%- if other_option_layout != 'hide' -%}
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}{% continue%}{%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-other ecom-collection__product-picker-option-{{option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-radio-list ecom-d-flex">
 {% for value in option.values %}
 <li class="ecom-collection__product-swatch-item ecom-collection__product-picker-radio-list-item {% if forloop.first %}ecom-product-swatch-item--active ecom-button-active{% endif %}" data-option-index="{{ option_index }}" data-value="{{ value | escape }}">
 {{value}}
 </li>
 {% endfor %}
 </ul>
 
 </div>
 {%- endfor -%}
 </div>
 {%- endif -%}
 
 
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 <div class="ecom-collection__product-picker-selection" style="display:none;">
 <select name="variant_id" data-product-id="{{product.id}}" id="ecom-variant-selector-{{product.id}}-ecom-u6zslnflk8h">
 {% for variant in product.variants %}
 <option value="{{variant.id}}" {% if product.first_available_variant.id == variant.id%} selected {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div>
 </div>
 </div>
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions ">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 <div class="ecom-collection__product-quick-shop-add-to-cart-wrapper {% if hide_quickshop or view_more_only %} ecom-collection__product-quick-shop--force-show{% endif %} ">
 <div class="ecom-collection__product-form__actions ">
 
 <button type="button" class="ecom-child-element ecom-collection__product-submit ecom-ajax-cart-submit ecom-collection__product-simple-add-to-cart
 ecom-collection__product-add-cart-icon-before"
 data-text-add-cart="Add to cart "
 data-text-unavailable="Unavailable"
 data-text-sold-out="Sold out"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 data-text-pre-order="Pre order"
 
 >
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 Add to cart 
 </span>
 </button>
 </div>
 
 </div>
 {%- endif -%}
 </form> {% comment %} End form {% endcomment %}
 <script class="product-json" type="application/json">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% endif %}
 
 {% endcapture%}
 {% capture product_actions%}
 <div class="ecom-collection__product--actions" data-layout="{{quickshop_layout}}">
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions "
 
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% if view_more_only %}
 
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 
 {% elsif product.has_only_default_variant == true and product.available == false %}
 
 
 <div class="ecom-collection__product-form__actions--soldout ecom-collection__product-sold-out-before ecom-child-element"
 
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-sold-out-text">
 Sold out
 </span>
 </div>
 
 {% elsif product.has_only_default_variant %}
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 
 
 <a data-no-instant href="{{ecom_root_url}}cart/add?id={{ product.variants.first.id }}&quantity=1"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 class="ecom-collection__product-submit ecom-collection__product-form__actions--add ecom-collection__product-simple-add-to-cart ecom-ajax-cart-simple ecom-collection__product-add-cart-icon-before ecom-child-element" 
 data-id="{{ product.variants.first.id }}"
 data-handle="{{ product.handle }}" data-pid="{{ product.id }}" title="{{ product.title | escape }}">
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 {%- if product.variants.first.inventory_management and product.variants.first.inventory_quantity <= 0 and product.variants.first.inventory_policy == 'continue' -%}
 Pre order
 {%-else-%}
 Add to cart 
 {%- endif -%}
 </span>
 </a>
 
 {%- endif -%}
 {% else %}
 
 
 <button class="ecom-collection__product-form__actions--quickshop ecom-collection__product-quickshop-icon-before
 {% if hide_quickshop %} ecom-collection__product-quick-shop--force-hide{% endif %} ecom-child-element" type="button">
 
 <span class="ecom-collection__product-form__actions--quickshop-text">
 Quick Shop
 </span>
 </button>
 
 
 {% endif %}
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 </div>
 {% endcapture %}
 <div class="ecom-collection__product-item ecom-swiper-slide" data-product-handle="{{product.handle}}" data-style="{{product_style}}"{% unless product.has_only_default_variant %} ec-variant-init{% endunless %}>
 <div
 class="ecom-collection__product-item--wrapper {% if product_style == 'horizontal'%} ecom-d-flex {% else %} ecom-flex-column {% endif %}">
 <div class="ecom-collection__product-media-wrapper ecom-enable-hover--mobile {% if product_style == 'horizontal'%} ecom-d-flex{% endif %} "
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <a href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}" target="" title="{{product.title | escape }}" class="ecom-collection__product-item--inner ecom-image-default">
 {%- if product.featured_media -%}
 {%- liquid
 assign featured_media_aspect_ratio = product.featured_media.aspect_ratio
 if product.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <div class="ecom-collection__product-media--container">
 <div
 class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--square
 {% if product.media[1] != nil %}ecom-collection__product-media--hover-effect{% endif %}"
 
 
 >
 <img srcset="{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.featured_media | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.featured_media.alt | escape }}"
 
 class="ecom-collection__product-media-image"
 width="{{ product.featured_media.width }}"
 height="{{ product.featured_media.height }}"
 />
 
 {%- if product.media[1] != nil -%}
 <img srcset="{%- if product.media[1].width >= 533 -%}{{ product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.media[1].width >= 720 -%}{{ product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.media[1].width >= 940 -%}{{ product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.media[1].width >= 1066 -%}{{ product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.media[1] | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.media[1].alt | escape }}"
 
 class="ecom-collection__product-secondary-media"
 width="{{ product.media[1].width }}"
 height="{{ product.media[1].height }}"
 />
 {%- endif -%}
 
 </div>
 </div>
 {% else %}
 <div class="ecom-collection__product-media--container">
 <div class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--square"
 
 
 >
 {{ 'product-1' | placeholder_svg_tag: 'ecom-colection__product-svg-placeholder' }}
 </div>
 </div>
 {% endif %}
 
 <div class="ecom-collection__product-badge">
 
 
 <span class="ecom-collection__product-badge--sold-out" aria-hidden="true" style="{%- if product.available == true -%}display: none; {%- endif -%}">
 Sold
 </span>
 <span class="ecom-collection__product-badge--sale" aria-hidden="true" style="{%- unless product.compare_at_price > product.price and product.available -%}display: none;{%- endunless -%}">
 Sale
 </span>
 
 {% if badge_tags %}
 {% for badge in badge_tags %}
 {% for original_tag in product.tags %}
 {% assign tag = original_tag | replace: ' ', ' ' %}
 {% if tag == badge %}
 <span class="ecom-collection__product-badge--custom ecom-collection__product-badge--{{ badge | handleize}}" aria-hidden="true">
 {{ badge }}
 </span>
 {% endif %}
 {% endfor %}
 {%- assign bad = badge | strip -%}
 {% endfor %}
 {% endif %}
 
 {%- if product.has_only_default_variant -%}
 {%- if product.compare_at_price != null and product.compare_at_price > product.price -%}
 {%- assign sale = product.compare_at_price | minus: product.price | money -%}
 <span class="ecom-collection__product-price--bage-sale">
 Save {{sale}}
 </span>
 {%- endif -%}
 {%- else -%}
 {%- if product.selected_or_first_available_variant.compare_at_price != null and product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
 {%- assign sale = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | money -%}
 {%else%}
 {%- assign sale = null %}
 {%- endif -%}
 <span class="ecom-collection__product-price--bage-sale" style="{% unless sale %}display:none{% endunless %}">
 Save {{sale}}
 </span>
 {%- endif -%}
 </div>
 </a>
 
 <div class="ecom-collection__product-group-button-action " style="justify-content: start">
 
 
 </div>
 </div>
 <div class="ecom-collection__product-item--information">
 <div class="ecom-collection__product-item--information-wrapper ecom-flex ecom-column">
 {% if product_style == 'absolute' %}
 {{product_actions}}
 {{ product_picker}}
 {% endif %}
 
 
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <h3
 class="ecom-collection__product-title-tag ecom-child-element"
 
 >
 <a
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{product.title | escape }}"
 target=""
 class="ecom-collection__product-item-information-title "
 >
 {{ product.title | strip_html}}
 </a>
 </h3>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 
 
 {% assign showPriceWhenNotLogin = true %}
 {% if undefined and customer == blank%}
 {% assign showPriceWhenNotLogin = false %}
 {% endif %}
 {% if showPriceWhenNotLogin == false and undefined %}
 {% assign showOnLive = true %}
 {% else %}
 {% assign showOnLive = false %}
 {% endif %}
 {% assign showPreview = false %}
 {% if showOnLive or showPreview %}
 <div class="ecom-collection__product-login-to-see">
 undefined
 </div>
 {% endif %}
 {% if true and showPriceWhenNotLogin %}
 <div class="ecom-collection__product-prices ecom-child-element" >
 {% capture ground_price %}
 
 {% endcapture %}
 {%- assign target = product.selected_or_first_available_variant -%}
 
 {%- assign target = product.selected_or_first_available_variant -%}
 <div class="ecom-collection__product-price-wrapper">
 <span
 class="ecom-collection__product-price--regular ecom-collection__product--compare-at-price"
 {%- if product.compare_at_price == nil or product.compare_at_price <= product.price -%} style="display:none" {% endif %}
 >
 {% if settings.currency_code_enabled == true %} {{ target.compare_at_price | money_with_currency }} {% else %} {{ target.compare_at_price | money }} {% endif %}
 </span>
 <span class="ecom-collection__product-price{% if target.compare_at_price > target.price %} ecom-collection__product-price--sale{% endif %}"
 {% if false %} bss-b2b-product-price bss-b2b-variant-price {% endif %}
 {% if false %}
 bss-b2b-product-id="{{ product.id }}"
 bss-b2b-variant-id="{{ product.selected_or_first_available_variant.id }}"
 {% endif %}
 >{% if settings.currency_code_enabled == true %} {{target.price | money_with_currency }} {% else %} {{target.price | money }} {% endif %}</span>
 </div>
 {{ ground_price }}
 
 </div>
 {% endif %}
 
 {% if product_style != 'absolute'%}
 {{ product_picker }}
 {% endif %}
 {% if product_style == 'horizontal' or product_style == 'vertical' or product_layout == 'list' %}
 {{product_actions}}
 {% endif %}
 
 {% if product.available and product.metafields.ecomposer.countdown %}
 <div
 class="ecom-collection__product-countdown ecom-child-element" 
 >
 <div
 class="ecom-collection__product-countdown-wrapper"
 >
 <div class="ecom-collection__product-countdown-wrapper--title"> </div>
 <div class="ecom-product-single__countdown-container" >
 {%- assign countdown_from = product.metafields.ecomposer.countdown_from -%}
 <div data-product-id="{{product.id}}" class="ecom-collection__product-countdown-time ecom-collection__product-countdown-time--metafields" data-ecom-countdown-from="{{ countdown_from }}" data-ecom-countdown="{{product.metafields.ecomposer.countdown}}"></div>
 </div>
 
 </div>
 </div>
 {% endif %}
 
 </div>
 </div>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 {% endfor %}
 </div>
 {% else %}
 <div class="ecom-collection__product--wrapper-items ecom-collection__product--no-item ecom-collection-product__layout-slider" >
 <div class="ecom-collection__product-item" >
 There are no product yet!
 </div>
 </div>
 {% endif %}
 
 
 {% assign collection = tmp_collection %}
 {% assign product = tmp_product %}
 </div><div class="ecom-swiper-navigation-position"><button class="ecom-swiper-button ecom-swiper-button-prev" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M192 448c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25l160-160c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25L77.25 256l137.4 137.4c12.5 12.5 12.5 32.75 0 45.25C208.4 444.9 200.2 448 192 448z"></path></svg></button><button class="ecom-swiper-button ecom-swiper-button-next" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M64 448c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L178.8 256L41.38 118.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l160 160c12.5 12.5 12.5 32.75 0 45.25l-160 160C80.38 444.9 72.19 448 64 448z"></path></svg></button></div><div class="ecom-swiper-pagination-position ecom-swiper-pagination" style="display: none;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-r0se2rp0zgs" data-core-is="block"><div class="ecom__element ecom-element-button ecom-button-default" deep="1"><a class="ecom__element--button ecom-flex ecom-fl_center ecom-al_center" href="https://sicotas.com/collections/helio-collections"><span class="ecom__element--button-icon-text">View More</span><span class="ecom-button-icon ecom__element--button-icon"></span></a></div></div> </div></div></div></div><div id="ecom-r3no988wsdl" class="core__group--item tab__item" data-id="ecom-r3no988wsdl"><div class="core__group--body tabs__body"><div class="core__blocks" index="3"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block elmspace ecom-7jls8nlkouu" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__product" deep="1"><div class="ecom-collection__product-wrapper"><div class="ecom-collection__product-container ecom-collection__product-container_featured"><div class="ecom-collection__product-main ecom-swiper-container ecom-collection_product_template_featured" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;loop&quot;:true,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1,&quot;spaceBetween&quot;:15},&quot;768&quot;:{&quot;slidesPerView&quot;:3,&quot;spaceBetween&quot;:15},&quot;1025&quot;:{&quot;slidesPerView&quot;:4,&quot;spaceBetween&quot;:14}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:0,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:200,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;}}" data-week="[%-W] week%!W" data-day="[%-d] day%!D" data-hour="[%-H] hr%!H" data-minute="[%-M] min%!M" data-second="[%-S] sec%!S" data-sale="Save {{sale}}" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{-review_platform-}}" data-countdown-shows="day,hour,minute,second" data-translate="false">
 {%- capture badge_tags -%}Hot,Best Selling,Trending Item{%- endcapture -%}
 {%- liquid
 assign colors = shop.metafields.ecomposer.colors
 assign badge_tags = badge_tags | strip | split: ','
 assign tmp_collection = collection
 assign tmp_product = product
 assign enable_hook = shop.metafields.ecomposer.enable_hook.value
 -%}
 
 
 {%- capture limit-%}4{% endcapture %}
 {%- if limit == blank -%}
 {% assign limit = 12 %}
 {% else %}
 {% assign limit = limit | plus: 0 %}
 {%- endif-%}
 
 {%- capture handle_collection -%}terra-collection{% endcapture%}
 {% assign collection = collections[handle_collection] %}
 {%- if handle_collection and collection.handle != blank -%}
 {% assign products = collection.products | where: "available" %}
 {% else %}
 {% assign products = collections.all.products | where: "available" %}
 {% assign collection = collections.all%}
 {%- endif -%}
 {% assign is_collection = true %}
 
 {% if false and featured == product %}
 {% if true %}
 {% assign products = blank %}
 {% endif %}
 {%- if recommendations.performed? and recommendations.products_count > 0 -%}
 {% assign products = recommendations.products %}
 {%- endif -%}
 {% endif %}
 {% capture quickshop_layout%}full{% endcapture %}
 {% capture product_style%}vertical{% endcapture%}
 {%- assign view_more_only = undefined -%}
 {% capture product_layout %}slider{% endcapture %}
 {% assign check_min = 0%}
 {% if products and products.size > check_min and products != blank %}
 <div class="ecom-swiper-wrapper
 ecom-collection__product--wrapper-items ecom-collection-product__layout-slider
 "
 data-grid-column="4"
 data-grid-column-tablet="3"
 data-grid-column-mobile="1"
 
 data-icon-add='<svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>'
 >
 {% assign ecom_count = 0 %}
 {% for p in products %}
 {% assign product = p %}
 {% if p.handle %}
 {% assign product = p %}
 {% else %}
 {% assign product = all_products[p] %}
 {% endif %}
 {% if product.url == blank %} {% continue %} {% endif %}
 {% if ecom_count >= limit %}{% break %}{% endif %}
 
 
 {% assign ecom_count = ecom_count | plus: 1 %}
 {%- capture swatch_option -%}Color{%- endcapture -%}
 {% assign hide_quickshop = true %}
 {% assign other_option_layout = 'radio' %}
 {% capture product_picker%}
 
 {%- if product.has_only_default_variant == false-%}
 {% if quickshop_layout != 'full' and product.options.size > 1%}
 {% assign hide_quickshop = false %}
 {% endif %}
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <div class="ecom-collection__product-variants" data-picker-type="image">
 <button class="ecom-collection__product-close"></button>
 <form class="ecom-collection__product-form ecom-product-form" product_id="{{product.id}}" data-product_id="{{product.id}}" data-product-id="{{product.id}}" data-handle="{{product.handle}}">
 <div class="ecom-child-element" >
 {% assign variant_selected = product.first_available_variant%}
 
 {%- capture swatch_option_temp -%}Color{%- endcapture -%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {% assign swatch_option = swatch_option | split: ',' %}
 {% assign option_index = current_option.position | minus: 1 %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-main ecom-collection__product-picker-option-{{current_option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-collection__product-swatch-item ecom-collection__product-picker-images-item {%comment%}{% if option_value == variant_selected[index] %}ecom-product-swatch-item--active ecom-button-active{% endif %}{%endcomment%}" data-value="{{ option_value | escape }}">
 <span class="ecom-collection__product-swatch-item--wrapper"></span>
 <img src="{{ variant | img_url: "120x120", crop: 'center' }}" alt=" {{ option_value }}" />
 </li>
 {%- endfor -%}
 </ul>
 
 </div>
 {% endif %}
 {% endfor %}
 {%- if other_option_layout != 'hide' -%}
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}{% continue%}{%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-other ecom-collection__product-picker-option-{{option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-radio-list ecom-d-flex">
 {% for value in option.values %}
 <li class="ecom-collection__product-swatch-item ecom-collection__product-picker-radio-list-item {% if forloop.first %}ecom-product-swatch-item--active ecom-button-active{% endif %}" data-option-index="{{ option_index }}" data-value="{{ value | escape }}">
 {{value}}
 </li>
 {% endfor %}
 </ul>
 
 </div>
 {%- endfor -%}
 </div>
 {%- endif -%}
 
 
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 <div class="ecom-collection__product-picker-selection" style="display:none;">
 <select name="variant_id" data-product-id="{{product.id}}" id="ecom-variant-selector-{{product.id}}-ecom-7jls8nlkouu">
 {% for variant in product.variants %}
 <option value="{{variant.id}}" {% if product.first_available_variant.id == variant.id%} selected {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div>
 </div>
 </div>
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions ">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 <div class="ecom-collection__product-quick-shop-add-to-cart-wrapper {% if hide_quickshop or view_more_only %} ecom-collection__product-quick-shop--force-show{% endif %} ">
 <div class="ecom-collection__product-form__actions ">
 
 <button type="button" class="ecom-child-element ecom-collection__product-submit ecom-ajax-cart-submit ecom-collection__product-simple-add-to-cart
 ecom-collection__product-add-cart-icon-before"
 data-text-add-cart="Add to cart "
 data-text-unavailable="Unavailable"
 data-text-sold-out="Sold out"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 data-text-pre-order="Pre order"
 
 >
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 Add to cart 
 </span>
 </button>
 </div>
 
 </div>
 {%- endif -%}
 </form> {% comment %} End form {% endcomment %}
 <script class="product-json" type="application/json">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% endif %}
 
 {% endcapture%}
 {% capture product_actions%}
 <div class="ecom-collection__product--actions" data-layout="{{quickshop_layout}}">
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions "
 
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% if view_more_only %}
 
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 
 {% elsif product.has_only_default_variant == true and product.available == false %}
 
 
 <div class="ecom-collection__product-form__actions--soldout ecom-collection__product-sold-out-before ecom-child-element"
 
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-sold-out-text">
 Sold out
 </span>
 </div>
 
 {% elsif product.has_only_default_variant %}
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 
 
 <a data-no-instant href="{{ecom_root_url}}cart/add?id={{ product.variants.first.id }}&quantity=1"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 class="ecom-collection__product-submit ecom-collection__product-form__actions--add ecom-collection__product-simple-add-to-cart ecom-ajax-cart-simple ecom-collection__product-add-cart-icon-before ecom-child-element" 
 data-id="{{ product.variants.first.id }}"
 data-handle="{{ product.handle }}" data-pid="{{ product.id }}" title="{{ product.title | escape }}">
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 {%- if product.variants.first.inventory_management and product.variants.first.inventory_quantity <= 0 and product.variants.first.inventory_policy == 'continue' -%}
 Pre order
 {%-else-%}
 Add to cart 
 {%- endif -%}
 </span>
 </a>
 
 {%- endif -%}
 {% else %}
 
 
 <button class="ecom-collection__product-form__actions--quickshop ecom-collection__product-quickshop-icon-before
 {% if hide_quickshop %} ecom-collection__product-quick-shop--force-hide{% endif %} ecom-child-element" type="button">
 
 <span class="ecom-collection__product-form__actions--quickshop-text">
 Quick Shop
 </span>
 </button>
 
 
 {% endif %}
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 </div>
 {% endcapture %}
 <div class="ecom-collection__product-item ecom-swiper-slide" data-product-handle="{{product.handle}}" data-style="{{product_style}}"{% unless product.has_only_default_variant %} ec-variant-init{% endunless %}>
 <div
 class="ecom-collection__product-item--wrapper {% if product_style == 'horizontal'%} ecom-d-flex {% else %} ecom-flex-column {% endif %}">
 <div class="ecom-collection__product-media-wrapper ecom-enable-hover--mobile {% if product_style == 'horizontal'%} ecom-d-flex{% endif %} "
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <a href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}" target="" title="{{product.title | escape }}" class="ecom-collection__product-item--inner ecom-image-default">
 {%- if product.featured_media -%}
 {%- liquid
 assign featured_media_aspect_ratio = product.featured_media.aspect_ratio
 if product.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <div class="ecom-collection__product-media--container">
 <div
 class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt
 {% if product.media[1] != nil %}ecom-collection__product-media--hover-effect{% endif %}"
 style="padding-bottom: {{ 1 | divided_by: featured_media_aspect_ratio | times: 100 }}%;"
 
 >
 <img srcset="{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.featured_media | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.featured_media.alt | escape }}"
 
 class="ecom-collection__product-media-image"
 width="{{ product.featured_media.width }}"
 height="{{ product.featured_media.height }}"
 />
 
 {%- if product.media[1] != nil -%}
 <img srcset="{%- if product.media[1].width >= 533 -%}{{ product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.media[1].width >= 720 -%}{{ product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.media[1].width >= 940 -%}{{ product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.media[1].width >= 1066 -%}{{ product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.media[1] | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.media[1].alt | escape }}"
 
 class="ecom-collection__product-secondary-media"
 width="{{ product.media[1].width }}"
 height="{{ product.media[1].height }}"
 />
 {%- endif -%}
 
 </div>
 </div>
 {% else %}
 <div class="ecom-collection__product-media--container">
 <div class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt"
 
 style="padding-bottom: 85%;"
 >
 {{ 'product-1' | placeholder_svg_tag: 'ecom-colection__product-svg-placeholder' }}
 </div>
 </div>
 {% endif %}
 
 <div class="ecom-collection__product-badge">
 
 
 <span class="ecom-collection__product-badge--sold-out" aria-hidden="true" style="{%- if product.available == true -%}display: none; {%- endif -%}">
 Sold
 </span>
 <span class="ecom-collection__product-badge--sale" aria-hidden="true" style="{%- unless product.compare_at_price > product.price and product.available -%}display: none;{%- endunless -%}">
 Sale
 </span>
 
 {% if badge_tags %}
 {% for badge in badge_tags %}
 {% for original_tag in product.tags %}
 {% assign tag = original_tag | replace: ' ', ' ' %}
 {% if tag == badge %}
 <span class="ecom-collection__product-badge--custom ecom-collection__product-badge--{{ badge | handleize}}" aria-hidden="true">
 {{ badge }}
 </span>
 {% endif %}
 {% endfor %}
 {%- assign bad = badge | strip -%}
 {% endfor %}
 {% endif %}
 
 {%- if product.has_only_default_variant -%}
 {%- if product.compare_at_price != null and product.compare_at_price > product.price -%}
 {%- assign sale = product.compare_at_price | minus: product.price | money -%}
 <span class="ecom-collection__product-price--bage-sale">
 Save {{sale}}
 </span>
 {%- endif -%}
 {%- else -%}
 {%- if product.selected_or_first_available_variant.compare_at_price != null and product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
 {%- assign sale = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | money -%}
 {%else%}
 {%- assign sale = null %}
 {%- endif -%}
 <span class="ecom-collection__product-price--bage-sale" style="{% unless sale %}display:none{% endunless %}">
 Save {{sale}}
 </span>
 {%- endif -%}
 </div>
 </a>
 
 <div class="ecom-collection__product-group-button-action " style="justify-content: start">
 
 
 </div>
 </div>
 <div class="ecom-collection__product-item--information">
 <div class="ecom-collection__product-item--information-wrapper ecom-flex ecom-column">
 {% if product_style == 'absolute' %}
 {{product_actions}}
 {{ product_picker}}
 {% endif %}
 
 
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <h3
 class="ecom-collection__product-title-tag ecom-child-element"
 
 >
 <a
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{product.title | escape }}"
 target=""
 class="ecom-collection__product-item-information-title "
 >
 {{ product.title | strip_html}}
 </a>
 </h3>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 
 
 {% assign showPriceWhenNotLogin = true %}
 {% if false and customer == blank%}
 {% assign showPriceWhenNotLogin = false %}
 {% endif %}
 {% if showPriceWhenNotLogin == false and false %}
 {% assign showOnLive = true %}
 {% else %}
 {% assign showOnLive = false %}
 {% endif %}
 {% assign showPreview = false %}
 {% if showOnLive or showPreview %}
 <div class="ecom-collection__product-login-to-see">
 Login to see price
 </div>
 {% endif %}
 {% if true and showPriceWhenNotLogin %}
 <div class="ecom-collection__product-prices ecom-child-element" >
 {% capture ground_price %}
 
 {% endcapture %}
 {%- assign target = product.selected_or_first_available_variant -%}
 
 {%- assign target = product.selected_or_first_available_variant -%}
 <div class="ecom-collection__product-price-wrapper">
 <span
 class="ecom-collection__product-price--regular ecom-collection__product--compare-at-price"
 {%- if product.compare_at_price == nil or product.compare_at_price <= product.price -%} style="display:none" {% endif %}
 >
 {% if settings.currency_code_enabled == true %} {{ target.compare_at_price | money_with_currency }} {% else %} {{ target.compare_at_price | money }} {% endif %}
 </span>
 <span class="ecom-collection__product-price{% if target.compare_at_price > target.price %} ecom-collection__product-price--sale{% endif %}"
 {% if false %} bss-b2b-product-price bss-b2b-variant-price {% endif %}
 {% if false %}
 bss-b2b-product-id="{{ product.id }}"
 bss-b2b-variant-id="{{ product.selected_or_first_available_variant.id }}"
 {% endif %}
 >{% if settings.currency_code_enabled == true %} {{target.price | money_with_currency }} {% else %} {{target.price | money }} {% endif %}</span>
 </div>
 {{ ground_price }}
 
 </div>
 {% endif %}
 
 {% if product_style != 'absolute'%}
 {{ product_picker }}
 {% endif %}
 {% if product_style == 'horizontal' or product_style == 'vertical' or product_layout == 'list' %}
 {{product_actions}}
 {% endif %}
 
 {% if product.available and product.metafields.ecomposer.countdown %}
 <div
 class="ecom-collection__product-countdown ecom-child-element" 
 >
 <div
 class="ecom-collection__product-countdown-wrapper"
 >
 <div class="ecom-collection__product-countdown-wrapper--title"> </div>
 <div class="ecom-product-single__countdown-container" >
 {%- assign countdown_from = product.metafields.ecomposer.countdown_from -%}
 <div data-product-id="{{product.id}}" class="ecom-collection__product-countdown-time ecom-collection__product-countdown-time--metafields" data-ecom-countdown-from="{{ countdown_from }}" data-ecom-countdown="{{product.metafields.ecomposer.countdown}}"></div>
 </div>
 
 </div>
 </div>
 {% endif %}
 
 </div>
 </div>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 {% endfor %}
 </div>
 {% else %}
 <div class="ecom-collection__product--wrapper-items ecom-collection__product--no-item ecom-collection-product__layout-slider" >
 <div class="ecom-collection__product-item" >
 There are no product yet!
 </div>
 </div>
 {% endif %}
 
 
 {% assign collection = tmp_collection %}
 {% assign product = tmp_product %}
 </div><div class="ecom-swiper-navigation-position"><button class="ecom-swiper-button ecom-swiper-button-prev" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M192 448c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25l160-160c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25L77.25 256l137.4 137.4c12.5 12.5 12.5 32.75 0 45.25C208.4 444.9 200.2 448 192 448z"></path></svg></button><button class="ecom-swiper-button ecom-swiper-button-next" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M64 448c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L178.8 256L41.38 118.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l160 160c12.5 12.5 12.5 32.75 0 45.25l-160 160C80.38 444.9 72.19 448 64 448z"></path></svg></button></div><div class="ecom-swiper-pagination-position ecom-swiper-pagination" style="display: none;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-niwrtfloizs" data-core-is="block"><div class="ecom__element ecom-element-button ecom-button-default" deep="1"><a class="ecom__element--button ecom-flex ecom-fl_center ecom-al_center" href="https://sicotas.com/collections/andy-collections-1"><span class="ecom__element--button-icon-text">View More</span><span class="ecom-button-icon ecom__element--button-icon"></span></a></div></div> </div></div></div></div><div id="ecom-ww68pw02v4" class="core__group--item tab__item" data-id="ecom-ww68pw02v4"><div class="core__group--body tabs__body"><div class="core__blocks" index="4"><div class="core__blocks--body"><div class="ecom-block ecom-core core__block ecom-fkeu614q7uv" data-core-is="block"><div class="ecom-element ecom-collection ecom-collection__product" deep="1"><div class="ecom-collection__product-wrapper"><div class="ecom-collection__product-container ecom-collection__product-container_featured"><div class="ecom-collection__product-main ecom-swiper-container ecom-collection_product_template_featured" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;loop&quot;:true,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1,&quot;spaceBetween&quot;:15},&quot;768&quot;:{&quot;slidesPerView&quot;:3,&quot;spaceBetween&quot;:15},&quot;1025&quot;:{&quot;slidesPerView&quot;:4,&quot;spaceBetween&quot;:14}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:0,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:200,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;}}" data-week="[%-W] week%!W" data-day="[%-d] day%!D" data-hour="[%-H] hr%!H" data-minute="[%-M] min%!M" data-second="[%-S] sec%!S" data-sale="Save {{sale}}" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{-review_platform-}}" data-countdown-shows="day,hour,minute,second" data-translate="false">
 {%- capture badge_tags -%}Hot,Best Selling,Trending Item{%- endcapture -%}
 {%- liquid
 assign colors = shop.metafields.ecomposer.colors
 assign badge_tags = badge_tags | strip | split: ','
 assign tmp_collection = collection
 assign tmp_product = product
 assign enable_hook = shop.metafields.ecomposer.enable_hook.value
 -%}
 
 
 {%- capture limit-%}8{% endcapture %}
 {%- if limit == blank -%}
 {% assign limit = 12 %}
 {% else %}
 {% assign limit = limit | plus: 0 %}
 {%- endif-%}
 
 {%- capture handle_collection -%}stria-collections{% endcapture%}
 {% assign collection = collections[handle_collection] %}
 {%- if handle_collection and collection.handle != blank -%}
 {% assign products = collection.products | where: "available" %}
 {% else %}
 {% assign products = collections.all.products | where: "available" %}
 {% assign collection = collections.all%}
 {%- endif -%}
 {% assign is_collection = true %}
 
 {% if false and featured == product %}
 {% if true %}
 {% assign products = blank %}
 {% endif %}
 {%- if recommendations.performed? and recommendations.products_count > 0 -%}
 {% assign products = recommendations.products %}
 {%- endif -%}
 {% endif %}
 {% capture quickshop_layout%}full{% endcapture %}
 {% capture product_style%}vertical{% endcapture%}
 {%- assign view_more_only = undefined -%}
 {% capture product_layout %}slider{% endcapture %}
 {% assign check_min = 0%}
 {% if products and products.size > check_min and products != blank %}
 <div class="ecom-swiper-wrapper
 ecom-collection__product--wrapper-items ecom-collection-product__layout-slider
 "
 data-grid-column="4"
 data-grid-column-tablet="3"
 data-grid-column-mobile="1"
 
 data-icon-add='<svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>'
 >
 {% assign ecom_count = 0 %}
 {% for p in products %}
 {% assign product = p %}
 {% if p.handle %}
 {% assign product = p %}
 {% else %}
 {% assign product = all_products[p] %}
 {% endif %}
 {% if product.url == blank %} {% continue %} {% endif %}
 {% if ecom_count >= limit %}{% break %}{% endif %}
 
 
 {% assign ecom_count = ecom_count | plus: 1 %}
 {%- capture swatch_option -%}Color{%- endcapture -%}
 {% assign hide_quickshop = true %}
 {% assign other_option_layout = 'radio' %}
 {% capture product_picker%}
 
 {%- if product.has_only_default_variant == false-%}
 {% if quickshop_layout != 'full' and product.options.size > 1%}
 {% assign hide_quickshop = false %}
 {% endif %}
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <div class="ecom-collection__product-variants" data-picker-type="image">
 <button class="ecom-collection__product-close"></button>
 <form class="ecom-collection__product-form ecom-product-form" product_id="{{product.id}}" data-product_id="{{product.id}}" data-product-id="{{product.id}}" data-handle="{{product.handle}}">
 <div class="ecom-child-element" >
 {% assign variant_selected = product.first_available_variant%}
 
 {%- capture swatch_option_temp -%}Color{%- endcapture -%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {% assign swatch_option = swatch_option | split: ',' %}
 {% assign option_index = current_option.position | minus: 1 %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-main ecom-collection__product-picker-option-{{current_option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-collection__product-swatch-item ecom-collection__product-picker-images-item {%comment%}{% if option_value == variant_selected[index] %}ecom-product-swatch-item--active ecom-button-active{% endif %}{%endcomment%}" data-value="{{ option_value | escape }}">
 <span class="ecom-collection__product-swatch-item--wrapper"></span>
 <img src="{{ variant | img_url: "120x120", crop: 'center' }}" alt=" {{ option_value }}" />
 </li>
 {%- endfor -%}
 </ul>
 
 </div>
 {% endif %}
 {% endfor %}
 {%- if other_option_layout != 'hide' -%}
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}{% continue%}{%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-other ecom-collection__product-picker-option-{{option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-radio-list ecom-d-flex">
 {% for value in option.values %}
 <li class="ecom-collection__product-swatch-item ecom-collection__product-picker-radio-list-item {% if forloop.first %}ecom-product-swatch-item--active ecom-button-active{% endif %}" data-option-index="{{ option_index }}" data-value="{{ value | escape }}">
 {{value}}
 </li>
 {% endfor %}
 </ul>
 
 </div>
 {%- endfor -%}
 </div>
 {%- endif -%}
 
 
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 <div class="ecom-collection__product-picker-selection" style="display:none;">
 <select name="variant_id" data-product-id="{{product.id}}" id="ecom-variant-selector-{{product.id}}-ecom-fkeu614q7uv">
 {% for variant in product.variants %}
 <option value="{{variant.id}}" {% if product.first_available_variant.id == variant.id%} selected {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div>
 </div>
 </div>
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions ">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 <div class="ecom-collection__product-quick-shop-add-to-cart-wrapper {% if hide_quickshop or view_more_only %} ecom-collection__product-quick-shop--force-show{% endif %} ">
 <div class="ecom-collection__product-form__actions ">
 
 <button type="button" class="ecom-child-element ecom-collection__product-submit ecom-ajax-cart-submit ecom-collection__product-simple-add-to-cart
 ecom-collection__product-add-cart-icon-before"
 data-text-add-cart="Add to cart "
 data-text-unavailable="Unavailable"
 data-text-sold-out="Sold out"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 data-text-pre-order="Pre order"
 
 >
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 Add to cart 
 </span>
 </button>
 </div>
 
 </div>
 {%- endif -%}
 </form> {% comment %} End form {% endcomment %}
 <script class="product-json" type="application/json">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% endif %}
 
 {% endcapture%}
 {% capture product_actions%}
 <div class="ecom-collection__product--actions" data-layout="{{quickshop_layout}}">
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions "
 
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% if view_more_only %}
 
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 
 {% elsif product.has_only_default_variant == true and product.available == false %}
 
 
 <div class="ecom-collection__product-form__actions--soldout ecom-collection__product-sold-out-before ecom-child-element"
 
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-sold-out-text">
 Sold out
 </span>
 </div>
 
 {% elsif product.has_only_default_variant %}
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 
 
 <a data-no-instant href="{{ecom_root_url}}cart/add?id={{ product.variants.first.id }}&quantity=1"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 class="ecom-collection__product-submit ecom-collection__product-form__actions--add ecom-collection__product-simple-add-to-cart ecom-ajax-cart-simple ecom-collection__product-add-cart-icon-before ecom-child-element" 
 data-id="{{ product.variants.first.id }}"
 data-handle="{{ product.handle }}" data-pid="{{ product.id }}" title="{{ product.title | escape }}">
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 {%- if product.variants.first.inventory_management and product.variants.first.inventory_quantity <= 0 and product.variants.first.inventory_policy == 'continue' -%}
 Pre order
 {%-else-%}
 Add to cart 
 {%- endif -%}
 </span>
 </a>
 
 {%- endif -%}
 {% else %}
 
 
 <button class="ecom-collection__product-form__actions--quickshop ecom-collection__product-quickshop-icon-before
 {% if hide_quickshop %} ecom-collection__product-quick-shop--force-hide{% endif %} ecom-child-element" type="button">
 
 <span class="ecom-collection__product-form__actions--quickshop-text">
 Quick Shop
 </span>
 </button>
 
 
 {% endif %}
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 </div>
 {% endcapture %}
 <div class="ecom-collection__product-item ecom-swiper-slide" data-product-handle="{{product.handle}}" data-style="{{product_style}}"{% unless product.has_only_default_variant %} ec-variant-init{% endunless %}>
 <div
 class="ecom-collection__product-item--wrapper {% if product_style == 'horizontal'%} ecom-d-flex {% else %} ecom-flex-column {% endif %}">
 <div class="ecom-collection__product-media-wrapper ecom-enable-hover--mobile {% if product_style == 'horizontal'%} ecom-d-flex{% endif %} "
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <a href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}" target="" title="{{product.title | escape }}" class="ecom-collection__product-item--inner ecom-image-default">
 {%- if product.featured_media -%}
 {%- liquid
 assign featured_media_aspect_ratio = product.featured_media.aspect_ratio
 if product.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <div class="ecom-collection__product-media--container">
 <div
 class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt
 {% if product.media[1] != nil %}ecom-collection__product-media--hover-effect{% endif %}"
 style="padding-bottom: {{ 1 | divided_by: featured_media_aspect_ratio | times: 100 }}%;"
 
 >
 <img srcset="{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.featured_media | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.featured_media.alt | escape }}"
 
 class="ecom-collection__product-media-image"
 width="{{ product.featured_media.width }}"
 height="{{ product.featured_media.height }}"
 />
 
 {%- if product.media[1] != nil -%}
 <img srcset="{%- if product.media[1].width >= 533 -%}{{ product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.media[1].width >= 720 -%}{{ product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.media[1].width >= 940 -%}{{ product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.media[1].width >= 1066 -%}{{ product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.media[1] | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.media[1].alt | escape }}"
 
 class="ecom-collection__product-secondary-media"
 width="{{ product.media[1].width }}"
 height="{{ product.media[1].height }}"
 />
 {%- endif -%}
 
 </div>
 </div>
 {% else %}
 <div class="ecom-collection__product-media--container">
 <div class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt"
 
 style="padding-bottom: 85%;"
 >
 {{ 'product-1' | placeholder_svg_tag: 'ecom-colection__product-svg-placeholder' }}
 </div>
 </div>
 {% endif %}
 
 <div class="ecom-collection__product-badge">
 
 
 <span class="ecom-collection__product-badge--sold-out" aria-hidden="true" style="{%- if product.available == true -%}display: none; {%- endif -%}">
 Sold
 </span>
 <span class="ecom-collection__product-badge--sale" aria-hidden="true" style="{%- unless product.compare_at_price > product.price and product.available -%}display: none;{%- endunless -%}">
 Sale
 </span>
 
 {% if badge_tags %}
 {% for badge in badge_tags %}
 {% for original_tag in product.tags %}
 {% assign tag = original_tag | replace: ' ', ' ' %}
 {% if tag == badge %}
 <span class="ecom-collection__product-badge--custom ecom-collection__product-badge--{{ badge | handleize}}" aria-hidden="true">
 {{ badge }}
 </span>
 {% endif %}
 {% endfor %}
 {%- assign bad = badge | strip -%}
 {% endfor %}
 {% endif %}
 
 {%- if product.has_only_default_variant -%}
 {%- if product.compare_at_price != null and product.compare_at_price > product.price -%}
 {%- assign sale = product.compare_at_price | minus: product.price | money -%}
 <span class="ecom-collection__product-price--bage-sale">
 Save {{sale}}
 </span>
 {%- endif -%}
 {%- else -%}
 {%- if product.selected_or_first_available_variant.compare_at_price != null and product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
 {%- assign sale = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | money -%}
 {%else%}
 {%- assign sale = null %}
 {%- endif -%}
 <span class="ecom-collection__product-price--bage-sale" style="{% unless sale %}display:none{% endunless %}">
 Save {{sale}}
 </span>
 {%- endif -%}
 </div>
 </a>
 
 <div class="ecom-collection__product-group-button-action " style="justify-content: start">
 
 
 </div>
 </div>
 <div class="ecom-collection__product-item--information">
 <div class="ecom-collection__product-item--information-wrapper ecom-flex ecom-column">
 {% if product_style == 'absolute' %}
 {{product_actions}}
 {{ product_picker}}
 {% endif %}
 
 
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <h3
 class="ecom-collection__product-title-tag ecom-child-element"
 
 >
 <a
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{product.title | escape }}"
 target=""
 class="ecom-collection__product-item-information-title "
 >
 {{ product.title | strip_html}}
 </a>
 </h3>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 
 
 {% assign showPriceWhenNotLogin = true %}
 {% if false and customer == blank%}
 {% assign showPriceWhenNotLogin = false %}
 {% endif %}
 {% if showPriceWhenNotLogin == false and false %}
 {% assign showOnLive = true %}
 {% else %}
 {% assign showOnLive = false %}
 {% endif %}
 {% assign showPreview = false %}
 {% if showOnLive or showPreview %}
 <div class="ecom-collection__product-login-to-see">
 Login to see price
 </div>
 {% endif %}
 {% if true and showPriceWhenNotLogin %}
 <div class="ecom-collection__product-prices ecom-child-element" >
 {% capture ground_price %}
 
 {% endcapture %}
 {%- assign target = product.selected_or_first_available_variant -%}
 
 {%- assign target = product.selected_or_first_available_variant -%}
 <div class="ecom-collection__product-price-wrapper">
 <span
 class="ecom-collection__product-price--regular ecom-collection__product--compare-at-price"
 {%- if product.compare_at_price == nil or product.compare_at_price <= product.price -%} style="display:none" {% endif %}
 >
 {% if settings.currency_code_enabled == true %} {{ target.compare_at_price | money_with_currency }} {% else %} {{ target.compare_at_price | money }} {% endif %}
 </span>
 <span class="ecom-collection__product-price{% if target.compare_at_price > target.price %} ecom-collection__product-price--sale{% endif %}"
 {% if false %} bss-b2b-product-price bss-b2b-variant-price {% endif %}
 {% if false %}
 bss-b2b-product-id="{{ product.id }}"
 bss-b2b-variant-id="{{ product.selected_or_first_available_variant.id }}"
 {% endif %}
 >{% if settings.currency_code_enabled == true %} {{target.price | money_with_currency }} {% else %} {{target.price | money }} {% endif %}</span>
 </div>
 {{ ground_price }}
 
 </div>
 {% endif %}
 
 {% if product_style != 'absolute'%}
 {{ product_picker }}
 {% endif %}
 {% if product_style == 'horizontal' or product_style == 'vertical' or product_layout == 'list' %}
 {{product_actions}}
 {% endif %}
 
 {% if product.available and product.metafields.ecomposer.countdown %}
 <div
 class="ecom-collection__product-countdown ecom-child-element" 
 >
 <div
 class="ecom-collection__product-countdown-wrapper"
 >
 <div class="ecom-collection__product-countdown-wrapper--title"> </div>
 <div class="ecom-product-single__countdown-container" >
 {%- assign countdown_from = product.metafields.ecomposer.countdown_from -%}
 <div data-product-id="{{product.id}}" class="ecom-collection__product-countdown-time ecom-collection__product-countdown-time--metafields" data-ecom-countdown-from="{{ countdown_from }}" data-ecom-countdown="{{product.metafields.ecomposer.countdown}}"></div>
 </div>
 
 </div>
 </div>
 {% endif %}
 
 </div>
 </div>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 {% endfor %}
 </div>
 {% else %}
 <div class="ecom-collection__product--wrapper-items ecom-collection__product--no-item ecom-collection-product__layout-slider" >
 <div class="ecom-collection__product-item" >
 There are no product yet!
 </div>
 </div>
 {% endif %}
 
 
 {% assign collection = tmp_collection %}
 {% assign product = tmp_product %}
 </div><div class="ecom-swiper-navigation-position"><button class="ecom-swiper-button ecom-swiper-button-prev" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M192 448c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25l160-160c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25L77.25 256l137.4 137.4c12.5 12.5 12.5 32.75 0 45.25C208.4 444.9 200.2 448 192 448z"></path></svg></button><button class="ecom-swiper-button ecom-swiper-button-next" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M64 448c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L178.8 256L41.38 118.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l160 160c12.5 12.5 12.5 32.75 0 45.25l-160 160C80.38 444.9 72.19 448 64 448z"></path></svg></button></div><div class="ecom-swiper-pagination-position ecom-swiper-pagination" style="display: none;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-8hy6luod42" data-core-is="block"><div class="ecom__element ecom-element-button ecom-button-default" deep="1"><a class="ecom__element--button ecom-flex ecom-fl_center ecom-al_center" href="https://sicotas.com/collections/stria-collections"><span class="ecom__element--button-icon-text">View More</span><span class="ecom-button-icon ecom__element--button-icon"></span></a></div></div> </div></div></div></div><div id="ecom-vfy8siwm47r" class="core__group--item tab__item" data-id="ecom-vfy8siwm47r"><div class="core__group--body tabs__body"><div class="core__blocks" index="5"><div class="core__blocks--body"><div class="ecom-animation ecom-block ecom-core core__block ecom-5qdsg2bx02v" data-core-is="block" ecom-scrolling-animation="[{&quot;css&quot;:&quot;translateX&quot;,&quot;value&quot;:[]}]"><div class="ecom-element ecom-collection ecom-collection__product" deep="1"><div class="ecom-collection__product-wrapper"><div class="ecom-collection__product-container ecom-collection__product-container_featured"><div class="ecom-collection__product-main ecom-swiper-container ecom-collection_product_template_featured" data-option-swiper="{&quot;direction&quot;:&quot;horizontal&quot;,&quot;freeMode&quot;:false,&quot;loop&quot;:true,&quot;breakpoints&quot;:{&quot;0&quot;:{&quot;slidesPerView&quot;:1,&quot;spaceBetween&quot;:15},&quot;768&quot;:{&quot;slidesPerView&quot;:3,&quot;spaceBetween&quot;:15},&quot;1025&quot;:{&quot;slidesPerView&quot;:4,&quot;spaceBetween&quot;:14}},&quot;autoplay&quot;:{&quot;enabled&quot;:false,&quot;delay&quot;:0,&quot;pauseOnMouseEnter&quot;:false,&quot;disableOnInteraction&quot;:false,&quot;reverseDirection&quot;:false},&quot;speed&quot;:200,&quot;grabCursor&quot;:true,&quot;slideActiveClass&quot;:&quot;ecom-box-active&quot;,&quot;centeredSlides&quot;:false,&quot;watchOverflow&quot;:true,&quot;autoHeight&quot;:true,&quot;cubeEffect&quot;:{&quot;slideShadows&quot;:true,&quot;shadowOffset&quot;:20},&quot;creativeEffect&quot;:{},&quot;fadeEffect&quot;:{&quot;crossFade&quot;:true},&quot;pagination&quot;:{&quot;el&quot;:&quot;.ecom-swiper-pagination&quot;,&quot;type&quot;:&quot;bullets&quot;,&quot;clickable&quot;:true},&quot;navigation&quot;:{&quot;nextEl&quot;:&quot;.ecom-swiper-button-next&quot;,&quot;prevEl&quot;:&quot;.ecom-swiper-button-prev&quot;}}" data-week="[%-W] week%!W" data-day="[%-d] day%!D" data-hour="[%-H] hr%!H" data-minute="[%-M] min%!M" data-second="[%-S] sec%!S" data-sale="Save {{sale}}" data-review-platform="{%- assign review_platform = shop.metafields.ecomposer.app_review.value -%}{{-review_platform-}}" data-countdown-shows="day,hour,minute,second" data-translate="false">
 {%- capture badge_tags -%}Hot,Best Selling,Trending Item{%- endcapture -%}
 {%- liquid
 assign colors = shop.metafields.ecomposer.colors
 assign badge_tags = badge_tags | strip | split: ','
 assign tmp_collection = collection
 assign tmp_product = product
 assign enable_hook = shop.metafields.ecomposer.enable_hook.value
 -%}
 
 
 {%- capture limit-%}4{% endcapture %}
 {%- if limit == blank -%}
 {% assign limit = 12 %}
 {% else %}
 {% assign limit = limit | plus: 0 %}
 {%- endif-%}
 
 {%- capture handle_collection -%}white-fluted-furniture-set{% endcapture%}
 {% assign collection = collections[handle_collection] %}
 {%- if handle_collection and collection.handle != blank -%}
 {% assign products = collection.products | where: "available" %}
 {% else %}
 {% assign products = collections.all.products | where: "available" %}
 {% assign collection = collections.all%}
 {%- endif -%}
 {% assign is_collection = true %}
 
 {% if false and featured == product %}
 {% if true %}
 {% assign products = blank %}
 {% endif %}
 {%- if recommendations.performed? and recommendations.products_count > 0 -%}
 {% assign products = recommendations.products %}
 {%- endif -%}
 {% endif %}
 {% capture quickshop_layout%}full{% endcapture %}
 {% capture product_style%}vertical{% endcapture%}
 {%- assign view_more_only = undefined -%}
 {% capture product_layout %}slider{% endcapture %}
 {% assign check_min = 0%}
 {% if products and products.size > check_min and products != blank %}
 <div class="ecom-swiper-wrapper
 ecom-collection__product--wrapper-items ecom-collection-product__layout-slider
 "
 data-grid-column="4"
 data-grid-column-tablet="3"
 data-grid-column-mobile="1"
 
 data-icon-add='<svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>'
 >
 {% assign ecom_count = 0 %}
 {% for p in products %}
 {% assign product = p %}
 {% if p.handle %}
 {% assign product = p %}
 {% else %}
 {% assign product = all_products[p] %}
 {% endif %}
 {% if product.url == blank %} {% continue %} {% endif %}
 {% if ecom_count >= limit %}{% break %}{% endif %}
 
 
 {% assign ecom_count = ecom_count | plus: 1 %}
 {%- capture swatch_option -%}Color{%- endcapture -%}
 {% assign hide_quickshop = true %}
 {% assign other_option_layout = 'radio' %}
 {% capture product_picker%}
 
 {%- if product.has_only_default_variant == false-%}
 {% if quickshop_layout != 'full' and product.options.size > 1%}
 {% assign hide_quickshop = false %}
 {% endif %}
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <div class="ecom-collection__product-variants" data-picker-type="image">
 <button class="ecom-collection__product-close"></button>
 <form class="ecom-collection__product-form ecom-product-form" product_id="{{product.id}}" data-product_id="{{product.id}}" data-product-id="{{product.id}}" data-handle="{{product.handle}}">
 <div class="ecom-child-element" >
 {% assign variant_selected = product.first_available_variant%}
 
 {%- capture swatch_option_temp -%}Color{%- endcapture -%}
 {%- assign swatch_option_temp = swatch_option_temp | split: ',' -%}
 {% assign swatch_option = "" %}
 {% for item in swatch_option_temp %}
 {% assign normalizedItem = item | strip %}
 {% assign swatch_option = swatch_option | append: normalizedItem %}
 {% unless forloop.last %}
 {% assign swatch_option = swatch_option | append: ',' %}
 {% endunless %}
 {% endfor %}
 {% assign swatch_option = swatch_option | split: ',' %}
 {% assign option_index = current_option.position | minus: 1 %}
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}
 {% assign variant_selected = product.selected_or_first_available_variant %}
 {% assign current_option = option %}
 {% assign option_index = current_option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-main ecom-collection__product-picker-option-{{current_option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-images-list">
 {%- assign values = "" -%}
 {%- assign index = current_option.position | prepend: 'option' -%}
 {%- for variant in product.variants -%}
 {%- assign option_value = variant[index] -%}
 {%- assign option_value_downcase = variant[index] | downcase -%}
 {%- if values != ""%}
 {%- assign tmp = values | split: '|' -%}
 {%- if tmp contains option_value_downcase -%} {%- continue-%}{%- endif -%}
 {%- assign values = values | append: '|' | append: option_value_downcase -%}
 {%- else -%}
 {%- assign values = option_value_downcase -%}
 {%- endif -%}
 <li data-option-index="{{ option_index }}" class="ecom-collection__product-swatch-item ecom-collection__product-picker-images-item {%comment%}{% if option_value == variant_selected[index] %}ecom-product-swatch-item--active ecom-button-active{% endif %}{%endcomment%}" data-value="{{ option_value | escape }}">
 <span class="ecom-collection__product-swatch-item--wrapper"></span>
 <img src="{{ variant | img_url: "120x120", crop: 'center' }}" alt=" {{ option_value }}" />
 </li>
 {%- endfor -%}
 </ul>
 
 </div>
 {% endif %}
 {% endfor %}
 {%- if other_option_layout != 'hide' -%}
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 {%- for option in product.options_with_values -%}
 {%- if swatch_option contains option.name -%}{% continue%}{%-endif-%}
 {%- assign index = option.position | prepend: 'option' -%}
 {% assign option_index = option.position | minus: 1 %}
 <div class="ecom-collection__product-picker-other ecom-collection__product-picker-option-{{option.name | handleize }}">
 
 
 <ul class="ecom-collection__product-picker-radio-list ecom-d-flex">
 {% for value in option.values %}
 <li class="ecom-collection__product-swatch-item ecom-collection__product-picker-radio-list-item {% if forloop.first %}ecom-product-swatch-item--active ecom-button-active{% endif %}" data-option-index="{{ option_index }}" data-value="{{ value | escape }}">
 {{value}}
 </li>
 {% endfor %}
 </ul>
 
 </div>
 {%- endfor -%}
 </div>
 {%- endif -%}
 
 
 <div class="ecom-collection__product-quick-shop-wrapper {% if hide_quickshop %} ecom-collection__product-quick-shop--force-show{% endif %}">
 <div class="ecom-collection__product-picker-selection" style="display:none;">
 <select name="variant_id" data-product-id="{{product.id}}" id="ecom-variant-selector-{{product.id}}-ecom-5qdsg2bx02v">
 {% for variant in product.variants %}
 <option value="{{variant.id}}" {% if product.first_available_variant.id == variant.id%} selected {% endif %}>{{variant.title}}</option>
 {% endfor %}
 </select>
 </div>
 </div>
 </div>
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions ">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 <div class="ecom-collection__product-quick-shop-add-to-cart-wrapper {% if hide_quickshop or view_more_only %} ecom-collection__product-quick-shop--force-show{% endif %} ">
 <div class="ecom-collection__product-form__actions ">
 
 <button type="button" class="ecom-child-element ecom-collection__product-submit ecom-ajax-cart-submit ecom-collection__product-simple-add-to-cart
 ecom-collection__product-add-cart-icon-before"
 data-text-add-cart="Add to cart "
 data-text-unavailable="Unavailable"
 data-text-sold-out="Sold out"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 data-text-pre-order="Pre order"
 
 >
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 Add to cart 
 </span>
 </button>
 </div>
 
 </div>
 {%- endif -%}
 </form> {% comment %} End form {% endcomment %}
 <script class="product-json" type="application/json">
 {%- render "ecom_product_json", product: product -%}
 </script>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_variant', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% endif %}
 
 {% endcapture%}
 {% capture product_actions%}
 <div class="ecom-collection__product--actions" data-layout="{{quickshop_layout}}">
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions "
 
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 {% if view_more_only %}
 
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 
 {% elsif product.has_only_default_variant == true and product.available == false %}
 
 
 <div class="ecom-collection__product-form__actions--soldout ecom-collection__product-sold-out-before ecom-child-element"
 
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-sold-out-text">
 Sold out
 </span>
 </div>
 
 {% elsif product.has_only_default_variant %}
 {%- if product.requires_selling_plan == true and view_more_only != true -%}
 
 <div
 class="ecom-button-default ecom-collection__product-form__actions">
 <a class="ecom-collection__product-form__actions--view-more ecom-collection__product-view-more-before ecom-child-element"
 
 target=""
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{ product.title | escape }}">
 
 <span class="ecom-collection__product-view-more-text">
 View more
 </span>
 </a>
 </div>
 
 {%- else -%}
 
 
 <a data-no-instant href="{{ecom_root_url}}cart/add?id={{ product.variants.first.id }}&quantity=1"
 data-action="popup"
 data-text-added-cart="Added to cart"
 data-message-added-cart="Added to cart"
 data-href="#"
 data-target="_blank"
 class="ecom-collection__product-submit ecom-collection__product-form__actions--add ecom-collection__product-simple-add-to-cart ecom-ajax-cart-simple ecom-collection__product-add-cart-icon-before ecom-child-element" 
 data-id="{{ product.variants.first.id }}"
 data-handle="{{ product.handle }}" data-pid="{{ product.id }}" title="{{ product.title | escape }}">
 <span class="ecom-collection__product-add-cart-icon"><svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></span>
 <span class="ecom-add-to-cart-text">
 {%- if product.variants.first.inventory_management and product.variants.first.inventory_quantity <= 0 and product.variants.first.inventory_policy == 'continue' -%}
 Pre order
 {%-else-%}
 Add to cart 
 {%- endif -%}
 </span>
 </a>
 
 {%- endif -%}
 {% else %}
 
 
 <button class="ecom-collection__product-form__actions--quickshop ecom-collection__product-quickshop-icon-before
 {% if hide_quickshop %} ecom-collection__product-quick-shop--force-hide{% endif %} ecom-child-element" type="button">
 
 <span class="ecom-collection__product-form__actions--quickshop-text">
 Quick Shop
 </span>
 </button>
 
 
 {% endif %}
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_cart_button', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 </div>
 {% endcapture %}
 <div class="ecom-collection__product-item ecom-swiper-slide" data-product-handle="{{product.handle}}" data-style="{{product_style}}"{% unless product.has_only_default_variant %} ec-variant-init{% endunless %}>
 <div
 class="ecom-collection__product-item--wrapper {% if product_style == 'horizontal'%} ecom-d-flex {% else %} ecom-flex-column {% endif %}">
 <div class="ecom-collection__product-media-wrapper ecom-enable-hover--mobile {% if product_style == 'horizontal'%} ecom-d-flex{% endif %} "
 >
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <a href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}" target="" title="{{product.title | escape }}" class="ecom-collection__product-item--inner ecom-image-default">
 {%- if product.featured_media -%}
 {%- liquid
 assign featured_media_aspect_ratio = product.featured_media.aspect_ratio
 if product.featured_media.aspect_ratio == nil
 assign featured_media_aspect_ratio = 1
 endif
 -%}
 <div class="ecom-collection__product-media--container">
 <div
 class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt
 {% if product.media[1] != nil %}ecom-collection__product-media--hover-effect{% endif %}"
 style="padding-bottom: {{ 1 | divided_by: featured_media_aspect_ratio | times: 100 }}%;"
 
 >
 <img srcset="{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.featured_media | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.featured_media.alt | escape }}"
 
 class="ecom-collection__product-media-image"
 width="{{ product.featured_media.width }}"
 height="{{ product.featured_media.height }}"
 />
 
 {%- if product.media[1] != nil -%}
 <img srcset="{%- if product.media[1].width >= 533 -%}{{ product.media[1] | img_url: '533x' }} 533w,{%- endif -%}
 {%- if product.media[1].width >= 720 -%}{{ product.media[1] | img_url: '720x' }} 720w,{%- endif -%}
 {%- if product.media[1].width >= 940 -%}{{ product.media[1] | img_url: '940x' }} 940w,{%- endif -%}
 {%- if product.media[1].width >= 1066 -%}{{ product.media[1] | img_url: '1066x' }} 1066w{%- endif -%}"
 src="{{ product.media[1] | img_url: '533x' }}"
 sizes="(min-width: 1100px) 535px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
 alt="{{ product.media[1].alt | escape }}"
 
 class="ecom-collection__product-secondary-media"
 width="{{ product.media[1].width }}"
 height="{{ product.media[1].height }}"
 />
 {%- endif -%}
 
 </div>
 </div>
 {% else %}
 <div class="ecom-collection__product-media--container">
 <div class="ecom-child-element ecom-collection__product-media ecom-collection__product-media--adapt"
 
 style="padding-bottom: 85%;"
 >
 {{ 'product-1' | placeholder_svg_tag: 'ecom-colection__product-svg-placeholder' }}
 </div>
 </div>
 {% endif %}
 
 <div class="ecom-collection__product-badge">
 
 
 <span class="ecom-collection__product-badge--sold-out" aria-hidden="true" style="{%- if product.available == true -%}display: none; {%- endif -%}">
 Sold
 </span>
 <span class="ecom-collection__product-badge--sale" aria-hidden="true" style="{%- unless product.compare_at_price > product.price and product.available -%}display: none;{%- endunless -%}">
 Sale
 </span>
 
 {% if badge_tags %}
 {% for badge in badge_tags %}
 {% for original_tag in product.tags %}
 {% assign tag = original_tag | replace: ' ', ' ' %}
 {% if tag == badge %}
 <span class="ecom-collection__product-badge--custom ecom-collection__product-badge--{{ badge | handleize}}" aria-hidden="true">
 {{ badge }}
 </span>
 {% endif %}
 {% endfor %}
 {%- assign bad = badge | strip -%}
 {% endfor %}
 {% endif %}
 
 {%- if product.has_only_default_variant -%}
 {%- if product.compare_at_price != null and product.compare_at_price > product.price -%}
 {%- assign sale = product.compare_at_price | minus: product.price | money -%}
 <span class="ecom-collection__product-price--bage-sale">
 Save {{sale}}
 </span>
 {%- endif -%}
 {%- else -%}
 {%- if product.selected_or_first_available_variant.compare_at_price != null and product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
 {%- assign sale = product.selected_or_first_available_variant.compare_at_price | minus: product.selected_or_first_available_variant.price | money -%}
 {%else%}
 {%- assign sale = null %}
 {%- endif -%}
 <span class="ecom-collection__product-price--bage-sale" style="{% unless sale %}display:none{% endunless %}">
 Save {{sale}}
 </span>
 {%- endif -%}
 </div>
 </a>
 
 <div class="ecom-collection__product-group-button-action " style="justify-content: start">
 
 
 </div>
 </div>
 <div class="ecom-collection__product-item--information">
 <div class="ecom-collection__product-item--information-wrapper ecom-flex ecom-column">
 {% if product_style == 'absolute' %}
 {{product_actions}}
 {{ product_picker}}
 {% endif %}
 
 
 
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_before_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 <h3
 class="ecom-collection__product-title-tag ecom-child-element"
 
 >
 <a
 href="{%- if is_collection-%}{{- product.url | within: collection -}}{% else %}{{- product.url -}}{%-endif-%}"
 title="{{product.title | escape }}"
 target=""
 class="ecom-collection__product-item-information-title "
 >
 {{ product.title | strip_html}}
 </a>
 </h3>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after_title', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 
 
 {% assign showPriceWhenNotLogin = true %}
 {% if false and customer == blank%}
 {% assign showPriceWhenNotLogin = false %}
 {% endif %}
 {% if showPriceWhenNotLogin == false and false %}
 {% assign showOnLive = true %}
 {% else %}
 {% assign showOnLive = false %}
 {% endif %}
 {% assign showPreview = false %}
 {% if showOnLive or showPreview %}
 <div class="ecom-collection__product-login-to-see">
 Login to see price
 </div>
 {% endif %}
 {% if true and showPriceWhenNotLogin %}
 <div class="ecom-collection__product-prices ecom-child-element" >
 {% capture ground_price %}
 
 {% endcapture %}
 {%- assign target = product.selected_or_first_available_variant -%}
 
 {%- assign target = product.selected_or_first_available_variant -%}
 <div class="ecom-collection__product-price-wrapper">
 <span
 class="ecom-collection__product-price--regular ecom-collection__product--compare-at-price"
 {%- if product.compare_at_price == nil or product.compare_at_price <= product.price -%} style="display:none" {% endif %}
 >
 {% if settings.currency_code_enabled == true %} {{ target.compare_at_price | money_with_currency }} {% else %} {{ target.compare_at_price | money }} {% endif %}
 </span>
 <span class="ecom-collection__product-price{% if target.compare_at_price > target.price %} ecom-collection__product-price--sale{% endif %}"
 {% if false %} bss-b2b-product-price bss-b2b-variant-price {% endif %}
 {% if false %}
 bss-b2b-product-id="{{ product.id }}"
 bss-b2b-variant-id="{{ product.selected_or_first_available_variant.id }}"
 {% endif %}
 >{% if settings.currency_code_enabled == true %} {{target.price | money_with_currency }} {% else %} {{target.price | money }} {% endif %}</span>
 </div>
 {{ ground_price }}
 
 </div>
 {% endif %}
 
 {% if product_style != 'absolute'%}
 {{ product_picker }}
 {% endif %}
 {% if product_style == 'horizontal' or product_style == 'vertical' or product_layout == 'list' %}
 {{product_actions}}
 {% endif %}
 
 {% if product.available and product.metafields.ecomposer.countdown %}
 <div
 class="ecom-collection__product-countdown ecom-child-element" 
 >
 <div
 class="ecom-collection__product-countdown-wrapper"
 >
 <div class="ecom-collection__product-countdown-wrapper--title"> </div>
 <div class="ecom-product-single__countdown-container" >
 {%- assign countdown_from = product.metafields.ecomposer.countdown_from -%}
 <div data-product-id="{{product.id}}" class="ecom-collection__product-countdown-time ecom-collection__product-countdown-time--metafields" data-ecom-countdown-from="{{ countdown_from }}" data-ecom-countdown="{{product.metafields.ecomposer.countdown}}"></div>
 </div>
 
 </div>
 </div>
 {% endif %}
 
 </div>
 </div>
 </div>
 {%- if enable_hook -%}
 {% capture the_ecom_hook %}
 {% render 'ecom_product_loop_after', product: product %}
 {% endcapture %}
 {% unless the_ecom_hook contains 'Liquid error' %}
 {{ the_ecom_hook }}
 {% endunless %}
 {%- endif -%}
 </div>
 {% endfor %}
 </div>
 {% else %}
 <div class="ecom-collection__product--wrapper-items ecom-collection__product--no-item ecom-collection-product__layout-slider" >
 <div class="ecom-collection__product-item" >
 There are no product yet!
 </div>
 </div>
 {% endif %}
 
 
 {% assign collection = tmp_collection %}
 {% assign product = tmp_product %}
 </div><div class="ecom-swiper-navigation-position"><button class="ecom-swiper-button ecom-swiper-button-prev" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M192 448c-8.188 0-16.38-3.125-22.62-9.375l-160-160c-12.5-12.5-12.5-32.75 0-45.25l160-160c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25L77.25 256l137.4 137.4c12.5 12.5 12.5 32.75 0 45.25C208.4 444.9 200.2 448 192 448z"></path></svg></button><button class="ecom-swiper-button ecom-swiper-button-next" style="--ecom-position: unset; --ecom-position__tablet: absolute; --ecom-position__mobile: absolute;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" fill="currentColor"><path d="M64 448c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L178.8 256L41.38 118.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l160 160c12.5 12.5 12.5 32.75 0 45.25l-160 160C80.38 444.9 72.19 448 64 448z"></path></svg></button></div><div class="ecom-swiper-pagination-position ecom-swiper-pagination" style="display: none;"></div></div></div></div></div> <div class="ecom-block ecom-core core__block elmspace ecom-6b0elm12swx" data-core-is="block"><div class="ecom__element ecom-element-button ecom-button-default" deep="1"><a class="ecom__element--button ecom-flex ecom-fl_center ecom-al_center" href="https://sicotas.com/collections/white-fluted-furniture-set"><span class="ecom__element--button-icon-text">View More</span><span class="ecom-button-icon ecom__element--button-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor"><path d="M 18.71875 6.78125 L 17.28125 8.21875 L 24.0625 15 L 4 15 L 4 17 L 24.0625 17 L 17.28125 23.78125 L 18.71875 25.21875 L 27.21875 16.71875 L 27.90625 16 L 27.21875 15.28125 Z"></path></svg></span></a></div></div> </div></div></div></div></div></div> </div></div> </div></div></div></div> </div><div class="ecom-section__overlay"><div class="ecom-overlay"></div></div> </section>
</div></div>