{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the list of customer addresses.

  Accepts:
  - hydration {string} - The hydration strategy

  Usage:
  {% render 'section-main-addresses' %}
{%- endcomment -%}

{%- liquid
  assign hydration = hydration | default: 'on:visible'
-%}

<is-land {{ hydration }}>
  <section-main-addresses>
    {%- paginate customer.addresses by 5 -%}
      <div class="page-width page-width--narrow page-content">
        {%- if settings.show_breadcrumbs -%}
          <nav class="breadcrumb" role="navigation" aria-label="breadcrumbs">
            <a href="{{ routes.account_url }}">
              {% render 't_with_fallback', key: 'labels.my_account', fallback: 'My account' -%}
            </a>
            <span class="divider" aria-hidden="true">/</span>
            {% render 't_with_fallback', key: 'labels.addresses', fallback: 'Addresses' %}
          </nav>
        {%- endif -%}

        <header class="section-header section-header--with-link">
          <h1 class="section-header__title">
            {% render 't_with_fallback', key: 'labels.addresses', fallback: 'Addresses' %}
          </h1>
          <button type="button" class="section-header__link address-new-toggle">
            {% render 't_with_fallback', key: 'actions.add_address', fallback: 'Add address' %}
          </button>
        </header>

        <div id="AddressNewForm" class="form-vertical js-address-form text-left hide">
          {%- form 'customer_address', customer.new_address -%}
            <h2>
              {% render 't_with_fallback', key: 'actions.add_address', fallback: 'Add address' %}
            </h2>

            <div class="float-grid grid--small clearfix">
              <div class="grid__item one-half small--one-whole">
                <label for="AddressFirstNameNew">
                  {% render 't_with_fallback', key: 'labels.first_name', fallback: 'First name' -%}
                </label>
                <input
                  type="text"
                  id="AddressFirstNameNew"
                  class="input-full"
                  name="address[first_name]"
                  value="{{form.first_name}}"
                  autocapitalize="words"
                >
              </div>

              <div class="grid__item one-half small--one-whole">
                <label for="AddressLastNameNew">
                  {% render 't_with_fallback', key: 'labels.last_name', fallback: 'Last name' -%}
                </label>
                <input
                  type="text"
                  id="AddressLastNameNew"
                  class="input-full"
                  name="address[last_name]"
                  value="{{form.last_name}}"
                  autocapitalize="words"
                >
              </div>
            </div>

            <label for="AddressCompanyNew">
              {% render 't_with_fallback', key: 'labels.company', fallback: 'Company' -%}
            </label>
            <input
              type="text"
              id="AddressCompanyNew"
              class="input-full"
              name="address[company]"
              value="{{form.company}}"
              autocapitalize="words"
            >

            <label for="AddressAddress1New">
              {% render 't_with_fallback', key: 'labels.address1', fallback: 'Address1' -%}
            </label>
            <input
              type="text"
              id="AddressAddress1New"
              class="input-full"
              name="address[address1]"
              value="{{form.address1}}"
              autocapitalize="words"
            >

            <label for="AddressAddress2New">
              {% render 't_with_fallback', key: 'labels.address2', fallback: 'Address2' -%}
            </label>
            <input
              type="text"
              id="AddressAddress2New"
              class="input-full"
              name="address[address2]"
              value="{{form.address2}}"
              autocapitalize="words"
            >

            <div class="float-grid grid--small clearfix">
              <div class="grid__item medium-up--one-half">
                <label for="AddressCityNew">
                  {% render 't_with_fallback', key: 'labels.city', fallback: 'City' -%}
                </label>
                <input
                  type="text"
                  id="AddressCityNew"
                  class="input-full"
                  name="address[city]"
                  value="{{form.city}}"
                  autocapitalize="words"
                >
              </div>

              <div
                class="grid__item medium-up--one-half js-address-country"
                data-country-id="AddressCountryNew"
                data-province-container-id="AddressProvinceContainerNew"
                data-province-id="AddressProvinceNew"
              >
                <label for="AddressCountryNew">
                  {% render 't_with_fallback', key: 'labels.country', fallback: 'Country' -%}
                </label>
                <select
                  id="AddressCountryNew"
                  class="input-full"
                  name="address[country]"
                  data-default="{{form.country}}"
                >
                  {{ country_option_tags }}
                </select>
              </div>

              <div class="grid__item" id="AddressProvinceContainerNew" style="display: none;">
                <label for="AddressProvinceNew">
                  {% render 't_with_fallback', key: 'labels.province', fallback: 'Province' -%}
                </label>
                <select
                  id="AddressProvinceNew"
                  class="input-full"
                  name="address[province]"
                  data-default="{{form.province}}"
                ></select>
              </div>

              <div class="grid__item medium-up--one-half">
                <label for="AddressZipNew">
                  {% render 't_with_fallback', key: 'labels.zip', fallback: 'Zip' -%}
                </label>
                <input
                  type="text"
                  id="AddressZipNew"
                  class="input-full"
                  name="address[zip]"
                  value="{{form.zip}}"
                  autocapitalize="characters"
                >
              </div>

              <div class="grid__item medium-up--one-half">
                <label for="AddressPhoneNew">
                  {% render 't_with_fallback', key: 'labels.phone', fallback: 'Phone' -%}
                </label>
                <input type="tel" id="AddressPhoneNew" class="input-full" name="address[phone]" value="{{form.phone}}">
              </div>
            </div>

            <p>
              {{ form.set_as_default_checkbox }}
              <label for="address_default_address_new" class="inline">
                {% render 't_with_fallback', key: 'actions.set_default_address', fallback: 'Set default address' -%}
              </label>
            </p>

            <p>
              <label for="addresses-add-submit" class="visually-hidden">
                {% render 't_with_fallback', key: 'actions.add_address', fallback: 'Add address' -%}
              </label>
              <button type="submit" id="addresses-add-submit" class="btn">
                {% render 't_with_fallback', key: 'actions.add_address', fallback: 'Add address' %}
              </button>
            </p>
            <p>
              <label for="addresses-cancel-submit" class="visually-hidden">
                {% render 't_with_fallback', key: 'actions.cancel', fallback: 'Cancel' -%}
              </label>
              <button type="button" id="addresses-cancel-submit" class="text-link address-new-toggle">
                {% render 't_with_fallback', key: 'actions.cancel', fallback: 'Cancel' %}
              </button>
            </p>
          {%- endform -%}
        </div>

        {%- for address in customer.addresses -%}
          {%- if address == customer.default_address -%}
            <p class="h4">
              {% render 't_with_fallback', key: 'labels.default', fallback: 'Default' %}
            </p>
          {%- endif -%}

          {{ address | format_address }}

          <p>
            <button type="button" class="btn btn--small address-edit-toggle" data-form-id="{{ address.id }}">
              {% render 't_with_fallback', key: 'actions.edit', fallback: 'Edit' %}
            </button>
            <button
              type="button"
              class="btn btn--secondary btn--small address-delete"
              data-form-id="{{ address.id }}"
              data-confirm-message="{% render 't_with_fallback', key: 'actions.confirm_address_delete', fallback: 'Are you sure you wish to delete this address?' -%}"
            >
              {% render 't_with_fallback', key: 'actions.delete', fallback: 'Delete' %}
            </button>
          </p>

          <div id="EditAddress_{{ address.id }}" class="form-vertical js-address-form text-left hide">
            {%- form 'customer_address', address -%}
              <hr class="hr--large">
              <h2>
                {% render 't_with_fallback', key: 'actions.edit_address', fallback: 'Edit address' %}
              </h2>

              <div class="float-grid grid--small clearfix">
                <div class="grid__item one-half small--one-whole">
                  <label for="AddressFirstName_{{form.id}}">
                    {% render 't_with_fallback', key: 'labels.first_name', fallback: 'First name' -%}
                  </label>
                  <input
                    type="text"
                    id="AddressFirstName_{{form.id}}"
                    class="input-full"
                    name="address[first_name]"
                    value="{{form.first_name}}"
                    autocapitalize="words"
                  >
                </div>

                <div class="grid__item one-half small--one-whole">
                  <label for="AddressLastName_{{form.id}}">
                    {% render 't_with_fallback', key: 'labels.last_name', fallback: 'Last name' -%}
                  </label>
                  <input
                    type="text"
                    id="AddressLastName_{{form.id}}"
                    class="input-full"
                    name="address[last_name]"
                    value="{{form.last_name}}"
                    autocapitalize="words"
                  >
                </div>
              </div>

              <label for="AddressCompany_{{form.id}}">
                {% render 't_with_fallback', key: 'labels.company', fallback: 'Company' -%}
              </label>
              <input
                type="text"
                id="AddressCompany_{{form.id}}"
                class="input-full"
                name="address[company]"
                value="{{form.company}}"
                autocapitalize="words"
              >

              <label for="AddressAddress1_{{form.id}}">
                {% render 't_with_fallback', key: 'labels.address1', fallback: 'Address1' -%}
              </label>
              <input
                type="text"
                id="AddressAddress1_{{form.id}}"
                class="input-full"
                name="address[address1]"
                value="{{form.address1}}"
                autocapitalize="words"
              >

              <label for="AddressAddress2_{{form.id}}">
                {% render 't_with_fallback', key: 'labels.address2', fallback: 'Address2' -%}
              </label>
              <input
                type="text"
                id="AddressAddress2_{{form.id}}"
                class="input-full"
                name="address[address2]"
                value="{{form.address2}}"
                autocapitalize="words"
              >

              <label for="AddressCity_{{form.id}}">
                {% render 't_with_fallback', key: 'labels.city', fallback: 'City' -%}
              </label>
              <input
                type="text"
                id="AddressCity_{{form.id}}"
                class="input-full"
                name="address[city]"
                value="{{form.city}}"
                autocapitalize="words"
              >

              <div
                class="js-address-country"
                data-country-id="AddressCountry_{{form.id}}"
                data-province-container-id="AddressProvinceContainer_{{form.id}}"
                data-province-id="AddressProvince_{{form.id}}"
              >
                <label for="AddressCountry_{{form.id}}">
                  {% render 't_with_fallback', key: 'labels.country', fallback: 'Country' -%}
                </label>
                <select
                  id="AddressCountry_{{form.id}}"
                  class="input-full"
                  name="address[country]"
                  data-default="{{form.country}}"
                >
                  {{ country_option_tags }}
                </select>
              </div>

              <div id="AddressProvinceContainer_{{form.id}}" style="display: none;">
                <label for="AddressProvince_{{form.id}}">
                  {% render 't_with_fallback', key: 'labels.province', fallback: 'Province' -%}
                </label>
                <select
                  id="AddressProvince_{{form.id}}"
                  class="input-full"
                  name="address[province]"
                  data-default="{{form.province}}"
                ></select>
              </div>

              <div class="float-grid grid--small clearfix">
                <div class="grid__item one-half small--one-whole">
                  <label for="AddressZip_{{form.id}}">
                    {% render 't_with_fallback', key: 'labels.zip', fallback: 'Zip' -%}
                  </label>
                  <input
                    type="text"
                    id="AddressZip_{{form.id}}"
                    class="input-full"
                    name="address[zip]"
                    value="{{form.zip}}"
                    autocapitalize="characters"
                  >
                </div>

                <div class="grid__item one-half small--one-whole">
                  <label for="AddressPhone_{{form.id}}">
                    {% render 't_with_fallback', key: 'labels.phone', fallback: 'Phone' -%}
                  </label>
                  <input
                    type="tel"
                    id="AddressPhone_{{form.id}}"
                    class="input-full"
                    name="address[phone]"
                    value="{{form.phone}}"
                  >
                </div>
              </div>

              <p>
                {{ form.set_as_default_checkbox }}
                <label for="address_default_address_{{form.id}}" class="inline">
                  {% render 't_with_fallback', key: 'actions.set_default_address', fallback: 'Set as default address' %}
                </label>
              </p>

              <p>
                <label for="addresses-update-submit" class="visually-hidden">
                  {% render 't_with_fallback', key: 'actions.update_address', fallback: 'Update address' -%}
                </label>
                <button type="submit" id="addresses-update-submit" class="btn">
                  {% render 't_with_fallback', key: 'actions.update_address', fallback: 'Update address' %}
                </button>
              </p>
              <p>
                <label for="address-edit-toggle" class="visually-hidden">
                  {% render 't_with_fallback', key: 'actions.cancel', fallback: 'Cancel' -%}
                </label>
                <button
                  type="button"
                  id="address-edit-toggle"
                  class="text-link address-edit-toggle"
                  data-form-id="{{ form.id }}"
                >
                  {% render 't_with_fallback', key: 'actions.cancel', fallback: 'Cancel' %}
                </button>
              </p>
            {%- endform -%}
          </div>

          <hr class="hr--medium">
        {%- endfor -%}

        {%- liquid
          if paginate.pages > 1
            render 'pagination', paginate: paginate
          endif
        -%}
      </div>
    {%- endpaginate -%}
  </section-main-addresses>

  <template data-island>
    <script type="module">
      import 'components/section-main-addresses'
    </script>
  </template>
</is-land>
