{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the footer contact block.

  Accepts:
  - block {block} - The block object
  - phone {string} - The phone number
  - contact_page {page} - The contact page object
  - chat_page {page} - The chat page object
  - blocks_heading_size - The heading size for the block

  Usage:
  {% render 'footer-contact', block: block %}
{%- endcomment -%}
 
{%- liquid
  assign phone = phone | default: block.settings.phone
  assign contact_page = contact_page | default: block.settings.contact
  assign chat_page = chat_page | default: block.settings.chat
  assign enable_social = enable_social | default: block.settings.enable_social, allow_false: true | default: true, allow_false: true
  assign blocks_heading_size = blocks_heading_size | default: block.settings.heading_size | default: 'h4'
-%}

<div class="footer__mobile-section">
  <div class="footer__blocks--mobile">
    <div class="footer__block--mobile">
      <h2 class="footer__title {{ blocks_heading_size }}">
        {% assign actions_get_in_touch = 'actions.get_in_touch' | t %}
        {% render 't_with_fallback', t: actions_get_in_touch, fallback: 'Get in touch' %}
      </h2>

      <ul class="footer__menu footer__menu--underline">
        {%- if phone != blank -%}
          <li>
            <a href="tel:{{ phone }}">
              <span class="icon-and-text">
                {% render 'icon', name: 'phone' %}
                <span>{{ phone }}</span>
              </span>
            </a>
          </li>
        {%- endif -%}

        {%- if contact_page != blank -%}
          <li>
            <a href="{{ contact_page.url }}">
              <span class="icon-and-text">
                {% render 'icon', name: 'email' %}
                <span>
                  {% assign actions_email_us = 'actions.email_us' | t %}
                  {% render 't_with_fallback', t: actions_email_us, fallback: 'Email us' -%}
                </span>
              </span>
            </a>
          </li>
        {%- endif -%}

        {%- if chat_page != blank -%}
          <li>
            <a href="{{ chat_page.url }}">
              <span class="icon-and-text">
                {% render 'icon', name: 'chat' %}
                <span>
                  {% assign actions_live_chat = 'actions.live_chat' | t %}
                  {% render 't_with_fallback', t: actions_live_chat, fallback: 'Live chat' -%}
                </span>
              </span>
            </a>
          </li>
        {%- endif -%}
      </ul>
    </div>


    <div class="footer__newsletter block_newsletter">
        {% comment %} {%- if newsletter_richtext != blank -%} {% endcomment %}
          <div class="footer__subscribe rte rte--nomargin clearfix">
            {% comment %} {{ newsletter_richtext }} {% endcomment %}
            Subscribe now for exclusive offers, exciting giveaways,and limited-time deals!
          </div>
        {% comment %} {%- endif -%} {% endcomment %}

        {%- render 'newsletter-form', context: 'footer_block' -%}
    </div>

    {%- if enable_social -%}
      <div class="footer__block--mobile">
        {% assign actions_follow_us = 'actions.follow_us' | t %}
        <div class="footer__title">
          {% render 't_with_fallback', t: actions_follow_us, fallback: 'Follow us' %}
        </div>

        {% render 'social-icons', wrapper_class: 'footer__social' %}
      </div>
    {%- endif -%}
  </div>
</div>
