{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the footer section.

  Accepts:
  - show_newsletter {boolean} - Whether to show the newsletter form
  - newsletter_richtext {string} - The newsletter text
  - footer_main_menu {boolean} - Whether to show the main menu in the footer on mobile
  - show_copyright {boolean} - Whether to show the copyright text
  - copyright_text {string} - The copyright text
  - policies_menu {menu} - The policies menu
  - show_wave {boolean} - Whether to show the wave background

  Usage:
  {% render 'section-footer' %}
{%- endcomment -%} 

{%- liquid
  assign show_newsletter = show_newsletter | default: section.settings.show_newsletter, allow_false: true | default: true, allow_false: true
  assign newsletter_richtext = newsletter_richtext | default: section.settings.newsletter_richtext
  assign footer_main_menu = footer_main_menu | default: section.settings.footer_main_menu, allow_false: true | default: true, allow_false: true
  assign show_copyright = show_copyright | default: section.settings.show_copyright, allow_false: true | default: true, allow_false: true
  assign copyright_text = copyright_text | default: section.settings.copyright_text
  assign policies_menu = policies_menu | default: section.settings.policies_menu
  assign show_wave = show_wave | default: section.settings.show_wave, allow_false: true | default: false, allow_false: true
  assign hydration = hydration | default: 'on:visible'
-%}

{%- if show_newsletter -%}
  <div class="footer__section footer__section--border">
    <div class="page-width">
      <div class="footer__newsletter">
        {%- if newsletter_richtext != blank -%}
          <div class="footer__subscribe rte rte--nomargin clearfix">
            {{ newsletter_richtext }}
          </div>
        {%- endif -%}

        {%- render 'newsletter-form', context: 'footer' -%}
      </div>
    </div>
  </div>
{%- endif -%}

{% comment %}
  Main navigation is copied here with JS for mobile users
{% endcomment %}
{%- if footer_main_menu -%}
  <div id="FooterMobileNavWrap" class="footer__section footer__section--border medium-up--hide">
    <div id="FooterMobileNav" class="page-width"></div>
  </div>
{%- endif -%}

<style>
  .footer__title{
    line-height: 1.4;
  }
  .footer__menu a{
    font-family: PingFang SC;
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
    leading-trim: NONE;
    line-height: 1.6;
    letter-spacing: 0px;
    color: #666666;
  }
  .block_newsletter{
    flex-direction: column;
    padding: 0;
  }
  .footer__block--contact .footer__block--mobile .footer__title,.footer__block--contact .footer__blocks--mobile .footer__block--mobile:first-child{
    display: none;
  }
  .block_newsletter.footer__newsletter>*{
    padding: 0;
  }
  .block_newsletter form{
    width: 100%;
    margin-bottom: 40px;
  }
  .footer__newsletter.block_newsletter .newsletter__input-group{
    width: 100%;
    margin: 0;
  }
  .footer__newsletter.block_newsletter .footer__subscribe{
    font-family: PingFang SC;
    font-weight: 500;
    font-style: Medium;
    font-size: 16px;
    leading-trim: NONE;
    line-height: 1.4;
    letter-spacing: 0px;
    margin-bottom: 14px;
  }
  .block_newsletter .input-group.newsletter__input-group input.newsletter__input{
    color: #6D4C41;
    font-family: PingFang SC;
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
    leading-trim: NONE;
    line-height: 1.4;
    letter-spacing: 0px;
    width: 280px;
    height: 48px;
    angle: 0 deg;
    opacity: 1;
    top: 105px;
    left: 1307px;
    border-width: 1px;
    border-top-left-radius: 30px;
    border-bottom-left-radius: 30px;
    padding: 14px 0 14px 16px;
    margin-bottom: 0;
  }
  .block_newsletter .newsletter__input-group input.newsletter__input:focus,.block_newsletter .newsletter__input-group input.newsletter__input:active,.block_newsletter .newsletter__input-group input.newsletter__input:focus-visible{
    background: #FCF7F2;
    border: 1px solid #6D4C41;
    border-color: #6D4C41;
    outline: none;
  }
  .block_newsletter .newsletter__input-group input.newsletter__input::placeholder{
    color: #999999;
  
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
   
    line-height: 100%;
    letter-spacing: 0px;


  }
  .block_newsletter .input-group .input-group-btn:last-child .btn{
    width: 145px;
    height: 48px;
    angle: 0 deg;
    opacity: 1;
    top: 105px;
    left: 1587px;
    border-top-right-radius: 27px;
    border-bottom-right-radius: 27px;
    background: #6D4C41;
  }
  .block_newsletter .input-group .input-group-btn:last-child .btn span{
    font-family: PingFang SC;
    font-weight: 600;
    font-style: Semibold;
    font-size: 14px;
    leading-trim: NONE;
    line-height: 1.4;
    letter-spacing: 0px;
    color: #F3E8DD;
  }
  .block_newsletter .input-group .input-group-btn:last-child .btn:hover{
    background: #502212;
  }
  .footer__mobile-section .footer__social li{
    margin: 0;
  }
  @media only screen and (max-width: 768px)  {
    .footer__blocks{
      gap: 20px;
    flex-direction: column;
    }
    .footer__block, .footer__block--mobile {
        max-width: 100%;
        flex: 1 1 50%;
    }
    .block_newsletter .footer__title{
      margin-bottom: 10px;
    }
    .pc-div-hr{
      display: none;
    }
    .footer__block[data-type='contact']{
      order: -1;
      margin-bottom: 10px;
    }
    .block_newsletter .input-group.newsletter__input-group input.newsletter__input{
      width: 100%;
    min-width: 239px;
    }
    .footer__newsletter.block_newsletter .footer__subscribe{
      margin-bottom: 0;
    }
    .footer__newsletter.block_newsletter .footer__social .icon{
      width: 24px;
      height: 24px;
    }
    .block_newsletter .input-group .input-group-btn:last-child .btn{
      width: 100%;
      min-width: 105px;
    }
    .block_newsletter form{
      margin: 16px 0 26px;
      
    }
    .footer__mobile-section .footer__social{
      display: flex;
    justify-content: center;
    column-gap: 40px;
    padding: 0;
    }
    .block_newsletter .form__submit--small{
      display: none;
    }
    .block_newsletter .form__submit--large{
      display: block;
    }
  }
</style>

<is-land {{ hydration }}>
  <footer-section class="site-footer" data-section-id="{{ section.id }}" data-section-type="footer-section">
    <div
      id="FooterMenus"
      class="footer__section footer__section--menus {% if show_wave %}background-svg--wave-reverse{% endif %}"
    >
      <div class="page-width">
        <div class="footer__blocks">
          {%- for block in section.blocks -%}
            <div
              class="footer__block footer__block--{{ block.type }}"
              data-type="{{ block.type }}"
              {{ block.shopify_attributes }}
            >
              {%- liquid
                case block.type
                  when 'menu'
                    render 'footer-menu', block: block
                  when 'payment'
                    render 'footer-payments', block: block
                  when 'contact'
                    render 'footer-contact', block: block
                  when 'image'
                    render 'footer-image', block: block
                  when 'follow_shop_cta'
                    render 'follow-shop-cta'
                    when 'hr'
                  render 'div-hr'
                endcase
              -%}
            </div>
          {%- endfor -%}
        </div>
      </div>
    </div>

    <div class="footer__section">
      <div class="page-width text-center small--text-left">
        <div class="footer__base-links">
          {%- if show_copyright -%}
            <span>
              &copy; {{ 'now' | date: '%Y' }}
              {{ shop.name }}
              {%- if copyright_text != blank -%}
                {{ copyright_text }}
              {%- endif -%}
            </span>
          {%- endif -%}

          {%- if policies_menu.links.size > 0 -%}
            {%- for link in policies_menu.links -%}
              <a href="{{ link.url }}">{{ link.title }}</a>
            {%- endfor -%}
          {%- endif -%}

          {{ powered_by_link }}
        </div>
      </div>
    </div>
  </footer-section>

  <template data-island>
    <script type="module">
      import 'components/section-footer'
    </script>
  </template>
</is-land>
