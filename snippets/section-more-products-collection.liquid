{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a section with a list of products from a collection.

  Accepts:
  - collection {collection} - Collection to display
  - max_products {number} - Maximum number of products to display

  Usage:
  {% render 'section-more-products-collection', max_products: 10 %}
{%- endcomment -%}

{%- liquid
  assign collection = section.settings.collection | default: collection
  assign max_products = max_products | default: section.settings.max_products | default: 5
-%}

{%- if collection and collection.handle != blank -%}
  {%- capture collection_link -%}
    <a href="{{ collection.url }}">{{ collection.title }}</a>
  {%- endcapture -%}

  <div
    data-subsection
    data-section-id="{{ section.id }}"
    data-section-type="collection-template"
  >
    <div class="index-section index-section--sub-product">
      <div class="page-width">
        <header class="section-header">
          <div class="h3 section-header__title">
            {% assign labels_more_from_html = 'labels.more_from_html' | t: link: collection_link %}
            {%- capture fallback_labels_more_from_html -%}
              More from {{ collection_link }}
            {%- endcapture -%}
            {% render 't_with_fallback', t: labels_more_from_html, fallback: fallback_labels_more_from_html %}
          </div>
        </header>
      </div>

      <div class="page-width page-width--flush-small">
        <div
          class="new-grid product-grid scrollable-grid--small"
          {% render 'grid-view-type', products_per_row: products_per_row %}
        >
          {%- liquid
            for product in collection.products limit: max_products
              render 'product-grid-item', product: product, collection: collection, sizeVariable: products_per_row, fallback: '45vw'
            endfor
          -%}
        </div>
      </div>
    </div>
  </div>
{%- endif -%}
