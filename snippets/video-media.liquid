{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a video media element

  Accepts:
  - video {video} - Video object
  - external_video {external_video} - External video object
  - autoplay {boolean} - Autoplay the video
  - loop {boolean} - Loop the video
  - hide_controls {boolean} - Hide the video controls
  - muted {boolean} - Mute the video
  - background {boolean} - Set the video as a background (scales to fit, cropping may occur)
  - media_crop {'16-9'|'portrait'|'landscape'|'square'} - Crop type of the media
  - hydration {'on:visible'|'on:idle'|'on:interaction'|'on:media'} - Hydration strategy

  Usage:
  {% render 'video-media', autoplay: true %}
{%- endcomment -%}

{% liquid
  assign video = video | default: section.settings.video
  assign external_video = external_video | default: section.settings.external_video
  assign autoplay = autoplay | default: section.settings.autoplay, allow_false: true | default: false, allow_false: true
  assign loop = loop | default: section.settings.loop, allow_false: true | default: false, allow_false: true
  assign show_controls = show_controls | default: section.settings.show_controls, allow_false: true | default: false, allow_false: true
  assign muted = muted | default: section.settings.muted, allow_false: true | default: false, allow_false: true
  assign background = background | default: section.settings.background, allow_false: true | default: false, allow_false: true
  assign media_crop = media_crop | default: section.settings.media_crop | default: '16-9'
  assign hydration = hydration | default: 'on:visible'

  if external_video != blank
    assign external_video_host = external_video.host | default: external_video.type
    assign external_video_id = external_video.external_id | default: external_video.id
  endif
%}

<is-land {{ hydration }}>
  <div
    class="video-media aspect-ratio--{{ media_crop }}"
    data-background="{{ background }}"
  >
    {%- if video != blank -%}
      {%- if autoplay -%}
        {%- assign show_controls = false -%}
      {%- else -%}
        {%- assign show_controls = true -%}
      {%- endif -%}

      <video-media
        {% if autoplay %}
          autoplay
        {% endif %}
        defer-hydration
      >
        {{
          video
          | video_tag:
            playsinline: true,
            controls: show_controls,
            loop: autoplay,
            muted: autoplay,
            image_size: '500x',
            preload: 'metadata'
        }}
      </video-media>
    {%- elsif external_video != blank -%}
      <video-media
        host="{{ external_video_host }}"
        {% if autoplay %}
          autoplay
        {% endif %}
        defer-hydration
      >
        <template>
          {%- if external_video_host == 'youtube' -%}
            <iframe
              src="https://www.youtube.com/embed/{{ external_video_id }}?playsinline=1&{% if autoplay %}autoplay=1&controls=0&mute=1&loop=1&{% endif %}playlist={{ external_video_id }}&enablejsapi=1&rel=0&modestbranding=1&origin={{ 'https://' | append: request.host | url_encode }}"
              allow="autoplay; encrypted-media"
              allowfullscreen="allowfullscreen"
            ></iframe>
          {%- elsif external_video_host == 'vimeo' -%}
            <iframe
              src="https://player.vimeo.com/video/{{ external_video_id }}?autopause=1&{% if autoplay %}background=1&autoplay=1&loop=1&muted=1&{% endif %}transparent=0&responsive=1&portrait=0&title=0&byline=0"
              allow="autoplay; encrypted-media;"
              allowfullscreen="allowfullscreen"
            ></iframe>
          {%- endif -%}
        </template>
      </video-media>
    {%- else -%}
      {{ 'hero-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
    {%- endif -%}
  </div>

  {% if video != blank or external_video != blank %}
    <template data-island>
      <script type="module">
        import 'components/video-media'
      </script>
    </template>
  {% endif %}
</is-land>
