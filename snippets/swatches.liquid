{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a product's color swatches.

  Accepts:
  - product {product} - The product object

  Usage:
  {% render 'swatches', product: product %}
{%- endcomment -%}

{%- liquid
  capture swatch_trigger
    assign trigger_color_swatch = 'trigger.color_swatch' | t | downcase
    render 't_with_fallback', t: trigger_color_swatch, fallback: 'color'
  endcapture

  assign swatch_file_extension = 'png'
  assign color_count = 0
  assign max_colors_show = 4
  assign more_than_max = false
  assign hydration = hydration | default: 'on:idle'
-%}

{%- for option in product.options_with_values -%}
  {%- liquid
    assign option_name = option.name | downcase
    assign is_color = false
    if swatch_trigger contains option_name
      assign is_color = true
    elsif swatch_trigger == 'color' and option_name contains 'colour'
      assign is_color = true
    endif
  -%}
  {%- if is_color -%}
    {%- assign option_index = forloop.index0 -%}
    {%- assign values = '' -%}
    <is-land {{ hydration }}>
      <color-swatches class="grid-product__colors">
        {%- for variant in product.variants -%}
          {%- assign value = variant.options[option_index] %}
          {%- unless values contains value -%}
            {%- liquid
              assign values = values | join: ',' | append: ',' | append: value | split: ','

              assign color_file_name = value | handle | append: '.' | append: swatch_file_extension
              assign color_image = color_file_name | file_img_url: '50x50' | prepend: 'https:' | split: '?' | first
              assign color_swatch_fallback = value | split: ' ' | last | handle
              assign color_count = color_count | plus: 1
            -%}

            {%- if color_count <= max_colors_show -%}
              <span
                class="color-swatch color-swatch--{{ value | handle }}{% if variant.image %} color-swatch--with-image{% endif %}"
                data-url="{{ variant.url | within: collection }}"
                {% if variant.image %}
                  data-variant-id="{{ variant.id }}"
                  data-variant-image="{{ variant.image | image_url: width: 400 }}"
                {% endif %}
                style="background-color: {{ color_swatch_fallback }};{% if images[color_file_name] != blank %}  background-image: url({{ color_image }});{% endif %}"
              >
                <span class="visually-hidden">{{ value }}</span>
              </span>
            {%- else -%}
              {%- assign more_than_max = true -%}
            {%- endif -%}
          {%- endunless -%}
        {%- endfor -%}

        {%- if more_than_max -%}
          {%- assign more_colors = color_count | minus: max_colors_show -%}
          <small class="color-swatch__more">+{{ more_colors }}</small>
        {%- endif -%}

        <template data-island>
          <script type="module">
            import '@archetype-themes/custom-elements/swatches'
          </script>
        </template>
      </color-swatches>
    </is-land>
  {%- endif -%}
{%- endfor -%}
