{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a password header section.

  Accepts:
  - overlay_header {boolean} - Whether to add an overlay to the header
  - logo {image} - The logo object
  - desktop_logo_height {80-180} - The height of the desktop logo
  - mobile_logo_height {20-120} - The height of the mobile logo
  - hydration {string} - The hydration strategy for the section

  Usage:
  {% render 'section-password-header' %}
{%- endcomment -%}

{%- liquid
  assign overlay_header = overlay_header | default: section.settings.overlay_header, allow_false: true | default: true, allow_false: true
  assign logo = logo | default: section.settings.logo
  assign desktop_logo_height = desktop_logo_height | default: section.settings.desktop_logo_height | default: 80
  assign mobile_logo_height = mobile_logo_height | default: section.settings.mobile_logo_height | default: 50
  assign hydration = hydration | default: 'on:visible'
-%}

<is-land {{ hydration }}>
  <password-header data-section-id="{{ section.id }}" data-section-type="password-header">
    <header-nav
      id="HeaderWrapper"
      class="header-wrapper{% if overlay_header %} header-wrapper--overlay is-light{% endif %}"
    >
      {%- unless shop.password_message == blank -%}
        <div class="password-page__toolbar toolbar">
          <div class="page-width">
            <div class="toolbar__content">
              <div class="toolbar__item toolbar__item--announcements">
                <div class="announcement-bar text-center">
                  <span class="announcement__text">{{ shop.password_message }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      {%- endunless -%}

      <header id="SiteHeader" class="site-header site-header--password">
        <div class="site-header__element site-header__element--top">
          <div class="page-width">
            <div class="header-layout">
              <div class="header-item header-item--logo">
                <h1 itemscope itemtype="http://schema.org/Organization" class="site-header__logo">
                  {%- if logo != blank -%}
                    {%- style -%}
                      .password-page__logo h1 {
                        height: {{ mobile_logo_height }}px;
                      }
                      @media only screen and (min-width: 769px) {
                        .password-page__logo h1 {
                          height: {{ desktop_logo_height }}px;
                        }
                      }
                    {%- endstyle -%}

                    {% comment %} Desktop logo {% endcomment %}
                    {%- assign width = desktop_logo_height | times: logo.aspect_ratio | times: 2 -%}
                    {%- assign height = desktop_logo_height -%}
                    {%- capture sizes -%}{{ desktop_logo_height | times: logo.aspect_ratio }}px{%- endcapture -%}
                    {%- capture widths -%}{{ desktop_logo_height | times: logo.aspect_ratio }}, {{ desktop_logo_height | times: logo.aspect_ratio | times: 2 }}{%- endcapture -%}
                    {%- capture style -%}max-width: {{ desktop_logo_height | times: logo.aspect_ratio }}px;max-height: {{ desktop_logo_height }}px;{%- endcapture -%}

                    {% comment %} Mobile logo {% endcomment %}
                    {%- assign mobile_width = mobile_logo_height | times: logo.aspect_ratio | times: 2 -%}
                    {%- assign mobile_height = mobile_logo_height -%}
                    {%- capture mobile_sizes -%}{{ mobile_logo_height | times: logo.aspect_ratio }}px{%- endcapture -%}
                    {%- capture mobile_widths -%}{{ mobile_logo_height | times: logo.aspect_ratio }}, {{ mobile_logo_height | times: logo.aspect_ratio | times: 2 }}{%- endcapture -%}
                    {%- capture mobile_style -%}max-width: {{ mobile_logo_height | times: logo.aspect_ratio }}px;max-height: {{ mobile_logo_height }}px;{%- endcapture -%}

                    {%-
                      render 'image-element',
                      img: logo,
                      img_width: width,
                      img_height: height,
                      sizes: sizes,
                      widths: widths,
                      style: style,
                      classes: 'small--hide',
                      alt: logo.alt | default: shop.name,
                      loading: 'eager',
                      itemprop: 'logo',
                    -%}
                    {%-
                      render 'image-element',
                      img: logo,
                      img_width: mobile_width,
                      img_height: mobile_height,
                      sizes: mobile_sizes,
                      widths: mobile_widths,
                      style: mobile_style,
                      classes: 'medium-up--hide',
                      loading: 'eager',
                      alt: logo.alt | default: shop.name,
                    -%}
                  {%- else -%}
                    <span>{{ shop.name }}</span>
                  {%- endif -%}
                </h1>
              </div>

              <div class="header-item header-item--icons">
                <div class="site-nav">
                  <div class="site-nav__icons">
                    <a href="#LoginModal" class="js-modal-open-login-modal password-login">
                      <span class="password__lock">
                        {% render 'icon', name: 'lock' %}
                      </span>
                      {% render 't_with_fallback', key: 'labels.password', fallback: 'Password' %}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
    </header-nav>
    <script type="module">
      import '@archetype-themes/custom-elements/header-nav'
    </script>
  </password-header>

  <template data-island>
    <script type="module">
      import 'components/section-password-header'
    </script>
  </template>
</is-land>
