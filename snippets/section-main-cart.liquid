{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders the main cart section.

  Accepts:
  - cart_notes_enable {boolean} - Enable cart notes
  - cart_terms_conditions_enable {boolean} - Enable terms and conditions
  - cart_terms_conditions_page {page} - Terms and conditions page
  - cart_additional_buttons {boolean} - Additional checkout buttons

  Usage:
  {% render 'section-main-cart' %}
{%- endcomment -%}

{%- liquid
  assign cart_notes_enable = cart_notes_enable | default: settings.cart_notes_enable, allow_false: true | default: true, allow_false: true
  assign cart_terms_conditions_enable = cart_terms_conditions_enable | default: settings.cart_terms_conditions_enable, allow_false: true | default: true, allow_false: true
  assign cart_terms_conditions_page = cart_terms_conditions_page | default: settings.cart_terms_conditions_page
  assign cart_additional_buttons = cart_additional_buttons | default: settings.cart_additional_buttons, allow_false: true | default: true, allow_false: true
  assign hydration = hydration | default: 'on:visible'
  assign money_format = shop.money_format
  capture superscript
    render 'enable-superscript'
  endcapture
-%}

<is-land {{ hydration }}>
  <div class="page-width page-width--cart page-content">
    {%- render 'breadcrumbs' -%}

    <header class="section-header text-center{% if cart.item_count == 0 %} section-header--404{% endif %}">
      <h1 class="section-header__title">
        {% render 't_with_fallback', key: 'labels.cart', fallback: 'Cart' %}
      </h1>
      <div class="rte text-spacing clearfix">
        {%- if cart.item_count == 0 -%}
          <p>
            {% render 't_with_fallback', key: 'info.cart_empty', fallback: 'Your cart is currently empty.' %}
          </p>
          <hr class="hr--clear hr--small">
          <p>
            <a href="{{ routes.root_url }}" class="btn">
              {% render 't_with_fallback', key: 'actions.continue_shopping', fallback: 'Continue shopping' -%}
            </a>
          </p>
        {%- endif -%}
      </div>
    </header>

    {%- if cart.item_count > 0 -%}
      <form
        action="{{ routes.cart_url }}"
        method="post"
        novalidate
        data-location="page"
        id="CartPageForm"
        data-money-format="{{ money_format }}"
        data-super-script="{{ superscript | strip }}"
      >
        <div class="cart__page">
          <div class="cart__page-col">
            <div data-products>
              {% for item in cart.items %}
                {%- render 'cart-item', item: item -%}
              {% endfor %}
            </div>

            {%- render 'cart-recommendations', context: 'page' -%}
          </div>

          <div class="cart__page-col medium-up--overlay">
            {% if cart_notes_enable %}
              <div class="cart__item-row">
                {%- render 'cart-note' -%}
              </div>
            {% endif %}

            <div class="cart__item-sub cart__item-row cart__item--subtotal">
              <div>
                {% render 't_with_fallback', key: 'labels.subtotal', fallback: 'Subtotal' %}
              </div>
              <div data-subtotal>
                {%- render 'price', price: cart.total_price -%}
              </div>
            </div>

            <div data-discounts>
              {% if cart.cart_level_discount_applications != blank %}
                <div class="cart__discounts text-right">
                  <div>
                    {% for cart_discount in cart.cart_level_discount_applications %}
                      {%- assign savings = cart_discount.total_allocated_amount | money -%}
                      <div class="cart__discount">
                        {% assign info_you_save_amount = 'info.you_save_amount' | t: saved_amount: savings %}
                        {%- capture fallback_info_you_save_amount -%}
                          You save {{ savings }}
                        {%- endcapture -%}
                        {% render 't_with_fallback', t: info_you_save_amount, fallback: fallback_info_you_save_amount %}
                        ({{ cart_discount.title }})
                      </div>
                    {% endfor %}
                  </div>
                </div>
              {% endif %}
            </div>

            {% if cart_terms_conditions_enable %}
              <div class="cart__item-row cart__terms">
                <input type="checkbox" id="CartTerms" class="cart__terms-checkbox">
                <label for="CartTerms" class="text-label">
                  <small>
                    {% if cart_terms_conditions_page != blank %}
                      {% assign actions_terms_i_agree_html = 'actions.terms_i_agree_html'
                        | t: url: cart_terms_conditions_page.url
                      %}
                      {%- capture fallback_actions_terms_i_agree_html -%}
                        I agree with the <a href='{{ cart_terms_conditions_page.url }}' target='_blank'>terms and conditions</a>
                      {%- endcapture -%}
                      {% render 't_with_fallback',
                        t: actions_terms_i_agree_html,
                        fallback: fallback_actions_terms_i_agree_html
                      %}
                    {% else %}
                      {% assign actions_terms_i_agree = 'actions.terms_i_agree' | t %}
                      {% render 't_with_fallback',
                        t: actions_terms_i_agree,
                        fallback: 'I agree with the terms and conditions'
                      %}
                    {% endif %}
                  </small>
                </label>
              </div>
            {% endif %}

            <div class="cart__item-row cart__checkout-wrapper">
              <button
                type="submit"
                name="checkout"
                data-terms-required="{{ cart_terms_conditions_enable }}"
                class="btn cart__checkout"
              >
                {% render 't_with_fallback', key: 'actions.checkout', fallback: 'Check out' %}
              </button>

              {% if additional_checkout_buttons and cart_additional_buttons %}
                <div class="additional-checkout-buttons">{{ content_for_additional_checkout_buttons }}</div>
              {% endif %}

              <a href="{{ routes.all_products_collection_url }}" class="btn btn--secondary cart__continue">
                {% render 't_with_fallback', key: 'actions.continue_shopping', fallback: 'Continue shopping' %}
              </a>
            </div>

            <div class="cart__item-row text-center">
              <small>
                {%- if cart.taxes_included -%}
                  {% assign info_shipping_at_checkout_taxes_included = 'info.shipping_at_checkout_taxes_included' | t %}
                  {% render 't_with_fallback',
                    t: info_shipping_at_checkout_taxes_included,
                    fallback: 'Taxes included. Shipping and discount codes calculated at checkout.'
                  %}
                {%- else -%}
                  {% assign info_shipping_at_checkout = 'info.shipping_at_checkout' | t -%}
                  {%- render 't_with_fallback',
                    t: info_shipping_at_checkout,
                    fallback: 'Shipping, taxes, and discount codes calculated at checkout.'
                  %}
                {%- endif -%}
              </small>
            </div>
          </div>
        </div>
        <script type="application/json" data-locales>
          {
            "cartTermsConfirmation":
              {% render 't_with_fallback', key: 'info.you_must_agree', fallback: 'You must agree with the terms and conditions of sales to check out', as_json: true -%}
            ,
            "cartSavings":
              {%- assign info_save_amount = 'info.save_amount' | t: saved_amount: "[savings]" -%}
              {%- render 't_with_fallback', t: info_save_amount, fallback: 'Save [savings]', as_json: true -%}
          }
        </script>
      </form>
    {%- endif -%}
  </div>

  <template data-island>
    <script type="module">
      import 'components/section-main-cart'
    </script>
  </template>
</is-land>
