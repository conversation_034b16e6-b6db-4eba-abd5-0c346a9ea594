function on(event,callback,options){if(!this.namespaces)
this.namespaces={}
this.namespaces[event]=callback
options=options||!1
this.addEventListener(event.split('.')[0],callback,options)
return this}
function off(event){if(!this.namespaces){return}
this.removeEventListener(event.split('.')[0],this.namespaces[event])
delete this.namespaces[event]
return this}
window.on=Element.prototype.on=on
window.off=Element.prototype.off=off