(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ 5:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeEditorEventHandlerMixin: () => (/* binding */ ThemeEditorEventHandlerMixin)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
var ThemeEditorEventHandlerMixin = function ThemeEditorEventHandlerMixin(Base) {
  return /*#__PURE__*/function (_Base) {
    function _class() {
      _classCallCheck(this, _class);
      return _callSuper(this, _class, arguments);
    }
    _inherits(_class, _Base);
    return _createClass(_class, [{
      key: "connectedCallback",
      value: function connectedCallback() {
        var _this = this;
        if (Shopify.designMode) {
          this.boundEventMap = this.createThemeEditorEventMap();
          Object.entries(this.boundEventMap).forEach(function (_ref) {
            var _ref2 = _slicedToArray(_ref, 2),
              event = _ref2[0],
              boundHandler = _ref2[1];
            var target = event.includes('section') ? document : _this;
            target.addEventListener(event, boundHandler);
          });
          if (typeof this.onSectionLoad === 'function') {
            this.onSectionLoad();
          }
        }
      }
    }, {
      key: "disconnectedCallback",
      value: function disconnectedCallback() {
        if (Shopify.designMode) {
          Object.entries(this.boundEventMap).forEach(function (_ref3) {
            var _ref4 = _slicedToArray(_ref3, 2),
              event = _ref4[0],
              boundHandler = _ref4[1];
            if (event.includes('section')) {
              document.removeEventListener(event, boundHandler);
            }
          });
          if (typeof this.onSectionUnload === 'function') {
            this.onSectionUnload();
          }
        }
      }
    }, {
      key: "createThemeEditorEventMap",
      value: function createThemeEditorEventMap() {
        var _this2 = this;
        var events = ['shopify:section:select', 'shopify:section:deselect', 'shopify:section:reorder', 'shopify:block:select', 'shopify:block:deselect'];
        return events.reduce(function (acc, eventName) {
          var methodName = _this2.convertEventToMethodName(eventName);
          if (typeof _this2[methodName] === 'function') {
            acc[eventName] = _this2.createEventBoundHandler(_this2[methodName]);
          }
          return acc;
        }, {});
      }
    }, {
      key: "convertEventToMethodName",
      value: function convertEventToMethodName(eventName) {
        var capitalize = function capitalize(str) {
          return str.charAt(0).toUpperCase() + str.slice(1);
        };
        return "on".concat(eventName.split(':').slice(1).map(capitalize).join(''));
      }
    }, {
      key: "createEventBoundHandler",
      value: function createEventBoundHandler(handler) {
        var _this3 = this;
        return function (event) {
          if (event.detail && event.detail.sectionId === _this3.sectionId) {
            handler.call(_this3, event);
          }
        };
      }
    }]);
  }(Base);
};

/***/ }),

/***/ 123:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   debounce: () => (/* binding */ debounce),
/* harmony export */   defaultTo: () => (/* binding */ defaultTo),
/* harmony export */   executeJSmodules: () => (/* binding */ executeJSmodules),
/* harmony export */   prepareTransition: () => (/* binding */ prepareTransition),
/* harmony export */   throttle: () => (/* binding */ throttle),
/* harmony export */   wrap: () => (/* binding */ wrap)
/* harmony export */ });
function prepareTransition(el, callback) {
  el.addEventListener('transitionend', removeClass);
  function removeClass(evt) {
    el.classList.remove('is-transitioning');
    el.removeEventListener('transitionend', removeClass);
  }
  el.classList.add('is-transitioning');
  el.offsetWidth;
  if (typeof callback === 'function') {
    callback();
  }
}
function defaultTo(value, defaultValue) {
  return value == null || value !== value ? defaultValue : value;
}
function wrap(el, wrapper) {
  el.parentNode.insertBefore(wrapper, el);
  wrapper.appendChild(el);
}
function executeJSmodules(scripts) {
  for (var i = 0; i < scripts.length; i++) {
    var script = document.createElement('script');
    script.type = 'module';
    script.textContent = scripts[i].textContent;
    scripts[i].parentNode.replaceChild(script, scripts[i]);
  }
}
function debounce(wait, callback, immediate) {
  var timeout;
  return function () {
    var context = this,
      args = arguments;
    var later = function later() {
      timeout = null;
      if (!immediate) callback.apply(context, args);
    };
    var callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) callback.apply(context, args);
  };
}
function throttle(limit, callback) {
  var waiting = !1;
  return function () {
    if (!waiting) {
      callback.apply(this, arguments);
      waiting = !0;
      setTimeout(function () {
        waiting = !1;
      }, limit);
    }
  };
}

/***/ }),

/***/ 129:
/***/ (function() {

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
/*!
 * Flickity PACKAGED v2.3.0
 * Touch, responsive, flickable carousels
 *
 * Licensed GPLv3 for open source use
 * or Flickity Commercial License for commercial use
 *
 * https://flickity.metafizzy.co
 * Copyright 2015-2021 Metafizzy
 */
;
(function (global, factory) {
  global.EvEmitter = factory();
})(typeof window != 'undefined' ? window : this, function () {
  function EvEmitter() {}
  var proto = EvEmitter.prototype;
  proto.on = function (eventName, listener) {
    if (!eventName || !listener) {
      return;
    }
    var events = this._events = this._events || {};
    var listeners = events[eventName] = events[eventName] || [];
    if (listeners.indexOf(listener) == -1) {
      listeners.push(listener);
    }
    return this;
  };
  proto.once = function (eventName, listener) {
    if (!eventName || !listener) {
      return;
    }
    this.on(eventName, listener);
    var onceEvents = this._onceEvents = this._onceEvents || {};
    var onceListeners = onceEvents[eventName] = onceEvents[eventName] || {};
    onceListeners[listener] = !0;
    return this;
  };
  proto.off = function (eventName, listener) {
    var listeners = this._events && this._events[eventName];
    if (!listeners || !listeners.length) {
      return;
    }
    var index = listeners.indexOf(listener);
    if (index != -1) {
      listeners.splice(index, 1);
    }
    return this;
  };
  proto.emitEvent = function (eventName, args) {
    var listeners = this._events && this._events[eventName];
    if (!listeners || !listeners.length) {
      return;
    }
    listeners = listeners.slice(0);
    args = args || [];
    var onceListeners = this._onceEvents && this._onceEvents[eventName];
    for (var i = 0; i < listeners.length; i++) {
      var listener = listeners[i];
      var isOnce = onceListeners && onceListeners[listener];
      if (isOnce) {
        this.off(eventName, listener);
        delete onceListeners[listener];
      }
      listener.apply(this, args);
    }
    return this;
  };
  proto.allOff = function () {
    delete this._events;
    delete this._onceEvents;
  };
  return EvEmitter;
})
/*!
 * getSize v2.0.3
 * measure size of elements
 * MIT license
 */;
(function (window, factory) {
  window.getSize = factory();
})(window, function factory() {
  'use strict';

  function getStyleSize(value) {
    var num = parseFloat(value);
    var isValid = value.indexOf('%') == -1 && !isNaN(num);
    return isValid && num;
  }
  function noop() {}
  var measurements = ['paddingLeft', 'paddingRight', 'paddingTop', 'paddingBottom', 'marginLeft', 'marginRight', 'marginTop', 'marginBottom', 'borderLeftWidth', 'borderRightWidth', 'borderTopWidth', 'borderBottomWidth'];
  var measurementsLength = measurements.length;
  function getZeroSize() {
    var size = {
      width: 0,
      height: 0,
      innerWidth: 0,
      innerHeight: 0,
      outerWidth: 0,
      outerHeight: 0
    };
    for (var i = 0; i < measurementsLength; i++) {
      var measurement = measurements[i];
      size[measurement] = 0;
    }
    return size;
  }
  function getStyle(elem) {
    var style = getComputedStyle(elem);
    return style;
  }
  var isSetup = !1;
  var isBoxSizeOuter;
  function setup() {
    if (isSetup) {
      return;
    }
    isSetup = !0;
    var div = document.createElement('div');
    div.style.width = '200px';
    div.style.padding = '1px 2px 3px 4px';
    div.style.borderStyle = 'solid';
    div.style.borderWidth = '1px 2px 3px 4px';
    div.style.boxSizing = 'border-box';
    var body = document.body || document.documentElement;
    body.appendChild(div);
    var style = getStyle(div);
    isBoxSizeOuter = Math.round(getStyleSize(style.width)) == 200;
    getSize.isBoxSizeOuter = isBoxSizeOuter;
    body.removeChild(div);
  }
  function getSize(elem) {
    setup();
    if (typeof elem == 'string') {
      elem = document.querySelector(elem);
    }
    if (!elem || _typeof(elem) != 'object' || !elem.nodeType) {
      return;
    }
    var style = getStyle(elem);
    if (style.display == 'none') {
      return getZeroSize();
    }
    var size = {};
    size.width = elem.offsetWidth;
    size.height = elem.offsetHeight;
    var isBorderBox = size.isBorderBox = style.boxSizing == 'border-box';
    for (var i = 0; i < measurementsLength; i++) {
      var measurement = measurements[i];
      var value = style[measurement];
      var num = parseFloat(value);
      size[measurement] = !isNaN(num) ? num : 0;
    }
    var paddingWidth = size.paddingLeft + size.paddingRight;
    var paddingHeight = size.paddingTop + size.paddingBottom;
    var marginWidth = size.marginLeft + size.marginRight;
    var marginHeight = size.marginTop + size.marginBottom;
    var borderWidth = size.borderLeftWidth + size.borderRightWidth;
    var borderHeight = size.borderTopWidth + size.borderBottomWidth;
    var isBorderBoxSizeOuter = isBorderBox && isBoxSizeOuter;
    var styleWidth = getStyleSize(style.width);
    if (styleWidth !== !1) {
      size.width = styleWidth + (isBorderBoxSizeOuter ? 0 : paddingWidth + borderWidth);
    }
    var styleHeight = getStyleSize(style.height);
    if (styleHeight !== !1) {
      size.height = styleHeight + (isBorderBoxSizeOuter ? 0 : paddingHeight + borderHeight);
    }
    size.innerWidth = size.width - (paddingWidth + borderWidth);
    size.innerHeight = size.height - (paddingHeight + borderHeight);
    size.outerWidth = size.width + marginWidth;
    size.outerHeight = size.height + marginHeight;
    return size;
  }
  return getSize;
});
(function (window, factory) {
  'use strict';

  window.matchesSelector = factory();
})(window, function factory() {
  'use strict';

  var matchesMethod = function () {
    var ElemProto = window.Element.prototype;
    if (ElemProto.matches) {
      return 'matches';
    }
    if (ElemProto.matchesSelector) {
      return 'matchesSelector';
    }
    var prefixes = ['webkit', 'moz', 'ms', 'o'];
    for (var i = 0; i < prefixes.length; i++) {
      var prefix = prefixes[i];
      var method = prefix + 'MatchesSelector';
      if (ElemProto[method]) {
        return method;
      }
    }
  }();
  return function matchesSelector(elem, selector) {
    return elem[matchesMethod](selector);
  };
});
(function (window, factory) {
  window.fizzyUIUtils = factory(window, window.matchesSelector);
})(window, function factory(window, matchesSelector) {
  var utils = {};
  utils.extend = function (a, b) {
    for (var prop in b) {
      a[prop] = b[prop];
    }
    return a;
  };
  utils.modulo = function (num, div) {
    return (num % div + div) % div;
  };
  var arraySlice = Array.prototype.slice;
  utils.makeArray = function (obj) {
    if (Array.isArray(obj)) {
      return obj;
    }
    if (obj === null || obj === undefined) {
      return [];
    }
    var isArrayLike = _typeof(obj) == 'object' && typeof obj.length == 'number';
    if (isArrayLike) {
      return arraySlice.call(obj);
    }
    return [obj];
  };
  utils.removeFrom = function (ary, obj) {
    var index = ary.indexOf(obj);
    if (index != -1) {
      ary.splice(index, 1);
    }
  };
  utils.getParent = function (elem, selector) {
    while (elem.parentNode && elem != document.body) {
      elem = elem.parentNode;
      if (matchesSelector(elem, selector)) {
        return elem;
      }
    }
  };
  utils.getQueryElement = function (elem) {
    if (typeof elem == 'string') {
      return document.querySelector(elem);
    }
    return elem;
  };
  utils.handleEvent = function (event) {
    var method = 'on' + event.type;
    if (this[method]) {
      this[method](event);
    }
  };
  utils.filterFindElements = function (elems, selector) {
    elems = utils.makeArray(elems);
    var ffElems = [];
    elems.forEach(function (elem) {
      if (!(elem instanceof HTMLElement)) {
        return;
      }
      if (!selector) {
        ffElems.push(elem);
        return;
      }
      if (matchesSelector(elem, selector)) {
        ffElems.push(elem);
      }
      var childElems = elem.querySelectorAll(selector);
      for (var i = 0; i < childElems.length; i++) {
        ffElems.push(childElems[i]);
      }
    });
    return ffElems;
  };
  utils.debounceMethod = function (_class, methodName, threshold) {
    threshold = threshold || 100;
    var method = _class.prototype[methodName];
    var timeoutName = methodName + 'Timeout';
    _class.prototype[methodName] = function () {
      var timeout = this[timeoutName];
      clearTimeout(timeout);
      var args = arguments;
      var _this = this;
      this[timeoutName] = setTimeout(function () {
        method.apply(_this, args);
        delete _this[timeoutName];
      }, threshold);
    };
  };
  utils.docReady = function (callback) {
    var readyState = document.readyState;
    if (readyState == 'complete' || readyState == 'interactive') {
      setTimeout(callback);
    } else {
      document.addEventListener('DOMContentLoaded', callback);
    }
  };
  utils.toDashed = function (str) {
    return str.replace(/(.)([A-Z])/g, function (match, $1, $2) {
      return $1 + '-' + $2;
    }).toLowerCase();
  };
  utils.htmlInit = function (WidgetClass, namespace) {
    utils.docReady(function () {
      var dashedNamespace = utils.toDashed(namespace);
      var dataAttr = 'data-' + dashedNamespace;
      var dataAttrElems = document.querySelectorAll('[' + dataAttr + ']');
      var jsDashElems = document.querySelectorAll('.js-' + dashedNamespace);
      var elems = utils.makeArray(dataAttrElems).concat(utils.makeArray(jsDashElems));
      var dataOptionsAttr = dataAttr + '-options';
      elems.forEach(function (elem) {
        var attr = elem.getAttribute(dataAttr) || elem.getAttribute(dataOptionsAttr);
        var options;
        try {
          options = attr && JSON.parse(attr);
        } catch (error) {
          return;
        }
        var instance = new WidgetClass(elem, options);
      });
    });
  };
  return utils;
});
(function (window, factory) {
  window.Flickity = window.Flickity || {};
  window.Flickity.Cell = factory(window, window.getSize);
})(window, function factory(window, getSize) {
  function Cell(elem, parent) {
    this.element = elem;
    this.parent = parent;
    this.create();
  }
  var proto = Cell.prototype;
  proto.create = function () {
    this.element.style.position = 'absolute';
    this.element.setAttribute('aria-hidden', 'true');
    this.x = 0;
    this.shift = 0;
    this.element.style[this.parent.originSide] = 0;
  };
  proto.destroy = function () {
    this.unselect();
    this.element.style.position = '';
    var side = this.parent.originSide;
    this.element.style[side] = '';
    this.element.style.transform = '';
    this.element.removeAttribute('aria-hidden');
  };
  proto.getSize = function () {
    this.size = getSize(this.element);
  };
  proto.setPosition = function (x) {
    this.x = x;
    this.updateTarget();
    this.renderPosition(x);
  };
  proto.updateTarget = proto.setDefaultTarget = function () {
    var marginProperty = this.parent.originSide == 'left' ? 'marginLeft' : 'marginRight';
    this.target = this.x + this.size[marginProperty] + this.size.width * this.parent.cellAlign;
  };
  proto.renderPosition = function (x) {
    var sideOffset = this.parent.originSide === 'left' ? 1 : -1;
    var adjustedX = this.parent.options.percentPosition ? x * sideOffset * (this.parent.size.innerWidth / this.size.width) : x * sideOffset;
    this.element.style.transform = 'translateX(' + this.parent.getPositionValue(adjustedX) + ')';
  };
  proto.select = function () {
    this.element.classList.add('is-selected');
    this.element.removeAttribute('aria-hidden');
  };
  proto.unselect = function () {
    this.element.classList.remove('is-selected');
    this.element.setAttribute('aria-hidden', 'true');
  };
  proto.wrapShift = function (shift) {
    this.shift = shift;
    this.renderPosition(this.x + this.parent.slideableWidth * shift);
  };
  proto.remove = function () {
    this.element.parentNode.removeChild(this.element);
  };
  return Cell;
});
(function (window, factory) {
  window.Flickity = window.Flickity || {};
  window.Flickity.Slide = factory();
})(window, function factory() {
  'use strict';

  function Slide(parent) {
    this.parent = parent;
    this.isOriginLeft = parent.originSide == 'left';
    this.cells = [];
    this.outerWidth = 0;
    this.height = 0;
  }
  var proto = Slide.prototype;
  proto.addCell = function (cell) {
    this.cells.push(cell);
    this.outerWidth += cell.size.outerWidth;
    this.height = Math.max(cell.size.outerHeight, this.height);
    if (this.cells.length == 1) {
      this.x = cell.x;
      var beginMargin = this.isOriginLeft ? 'marginLeft' : 'marginRight';
      this.firstMargin = cell.size[beginMargin];
    }
  };
  proto.updateTarget = function () {
    var endMargin = this.isOriginLeft ? 'marginRight' : 'marginLeft';
    var lastCell = this.getLastCell();
    var lastMargin = lastCell ? lastCell.size[endMargin] : 0;
    var slideWidth = this.outerWidth - (this.firstMargin + lastMargin);
    this.target = this.x + this.firstMargin + slideWidth * this.parent.cellAlign;
  };
  proto.getLastCell = function () {
    return this.cells[this.cells.length - 1];
  };
  proto.select = function () {
    this.cells.forEach(function (cell) {
      cell.select();
    });
  };
  proto.unselect = function () {
    this.cells.forEach(function (cell) {
      cell.unselect();
    });
  };
  proto.getCellElements = function () {
    return this.cells.map(function (cell) {
      return cell.element;
    });
  };
  return Slide;
});
(function (window, factory) {
  window.Flickity = window.Flickity || {};
  window.Flickity.animatePrototype = factory(window, window.fizzyUIUtils);
})(window, function factory(window, utils) {
  var proto = {};
  proto.startAnimation = function () {
    if (this.isAnimating) {
      return;
    }
    this.isAnimating = !0;
    this.restingFrames = 0;
    this.animate();
  };
  proto.animate = function () {
    this.applyDragForce();
    this.applySelectedAttraction();
    var previousX = this.x;
    this.integratePhysics();
    this.positionSlider();
    this.settle(previousX);
    if (this.isAnimating) {
      var _this = this;
      requestAnimationFrame(function animateFrame() {
        _this.animate();
      });
    }
  };
  proto.positionSlider = function () {
    var x = this.x;
    if (this.options.wrapAround && this.cells.length > 1) {
      x = utils.modulo(x, this.slideableWidth);
      x -= this.slideableWidth;
      this.shiftWrapCells(x);
    }
    this.setTranslateX(x, this.isAnimating);
    this.dispatchScrollEvent();
  };
  proto.setTranslateX = function (x, is3d) {
    x += this.cursorPosition;
    x = this.options.rightToLeft ? -x : x;
    var translateX = this.getPositionValue(x);
    this.slider.style.transform = is3d ? 'translate3d(' + translateX + ',0,0)' : 'translateX(' + translateX + ')';
  };
  proto.dispatchScrollEvent = function () {
    var firstSlide = this.slides[0];
    if (!firstSlide) {
      return;
    }
    var positionX = -this.x - firstSlide.target;
    var progress = positionX / this.slidesWidth;
    this.dispatchEvent('scroll', null, [progress, positionX]);
  };
  proto.positionSliderAtSelected = function () {
    if (!this.cells.length) {
      return;
    }
    this.x = -this.selectedSlide.target;
    this.velocity = 0;
    this.positionSlider();
  };
  proto.getPositionValue = function (position) {
    if (this.options.percentPosition) {
      return Math.round(position / this.size.innerWidth * 10000) * 0.01 + '%';
    } else {
      return Math.round(position) + 'px';
    }
  };
  proto.settle = function (previousX) {
    var isResting = !this.isPointerDown && Math.round(this.x * 100) == Math.round(previousX * 100);
    if (isResting) {
      this.restingFrames++;
    }
    if (this.restingFrames > 2) {
      this.isAnimating = !1;
      delete this.isFreeScrolling;
      this.positionSlider();
      this.dispatchEvent('settle', null, [this.selectedIndex]);
    }
  };
  proto.shiftWrapCells = function (x) {
    var beforeGap = this.cursorPosition + x;
    this._shiftCells(this.beforeShiftCells, beforeGap, -1);
    var afterGap = this.size.innerWidth - (x + this.slideableWidth + this.cursorPosition);
    this._shiftCells(this.afterShiftCells, afterGap, 1);
  };
  proto._shiftCells = function (cells, gap, shift) {
    for (var i = 0; i < cells.length; i++) {
      var cell = cells[i];
      var cellShift = gap > 0 ? shift : 0;
      cell.wrapShift(cellShift);
      gap -= cell.size.outerWidth;
    }
  };
  proto._unshiftCells = function (cells) {
    if (!cells || !cells.length) {
      return;
    }
    for (var i = 0; i < cells.length; i++) {
      cells[i].wrapShift(0);
    }
  };
  proto.integratePhysics = function () {
    this.x += this.velocity;
    this.velocity *= this.getFrictionFactor();
  };
  proto.applyForce = function (force) {
    this.velocity += force;
  };
  proto.getFrictionFactor = function () {
    return 1 - this.options[this.isFreeScrolling ? 'freeScrollFriction' : 'friction'];
  };
  proto.getRestingPosition = function () {
    return this.x + this.velocity / (1 - this.getFrictionFactor());
  };
  proto.applyDragForce = function () {
    if (!this.isDraggable || !this.isPointerDown) {
      return;
    }
    var dragVelocity = this.dragX - this.x;
    var dragForce = dragVelocity - this.velocity;
    this.applyForce(dragForce);
  };
  proto.applySelectedAttraction = function () {
    var dragDown = this.isDraggable && this.isPointerDown;
    if (dragDown || this.isFreeScrolling || !this.slides.length) {
      return;
    }
    var distance = this.selectedSlide.target * -1 - this.x;
    var force = distance * this.options.selectedAttraction;
    this.applyForce(force);
  };
  return proto;
});
(function (window, factory) {
  var _Flickity = window.Flickity;
  window.Flickity = factory(window, window.EvEmitter, window.getSize, window.fizzyUIUtils, _Flickity.Cell, _Flickity.Slide, _Flickity.animatePrototype);
})(window, function factory(window, EvEmitter, getSize, utils, Cell, Slide, animatePrototype) {
  var getComputedStyle = window.getComputedStyle;
  function moveElements(elems, toElem) {
    elems = utils.makeArray(elems);
    while (elems.length) {
      toElem.appendChild(elems.shift());
    }
  }
  var GUID = 0;
  var instances = {};
  function Flickity(element, options) {
    var queryElement = utils.getQueryElement(element);
    if (!queryElement) {
      return;
    }
    this.element = queryElement;
    if (this.element.flickityGUID) {
      var instance = instances[this.element.flickityGUID];
      if (instance) instance.option(options);
      return instance;
    }
    this.options = utils.extend({}, this.constructor.defaults);
    this.option(options);
    this._create();
  }
  Flickity.defaults = {
    accessibility: !0,
    adaptiveHeight: !1,
    cellAlign: 'center',
    freeScrollFriction: 0.075,
    friction: 0.28,
    initialIndex: 0,
    percentPosition: !0,
    resize: !0,
    selectedAttraction: 0.025,
    setGallerySize: !0,
    wrapAround: !1
  };
  Flickity.createMethods = [];
  var proto = Flickity.prototype;
  utils.extend(proto, EvEmitter.prototype);
  proto._create = function () {
    var id = this.guid = ++GUID;
    this.element.flickityGUID = id;
    instances[id] = this;
    this.selectedIndex = 0;
    this.restingFrames = 0;
    this.x = 0;
    this.velocity = 0;
    this.originSide = this.options.rightToLeft ? 'right' : 'left';
    this.viewport = document.createElement('div');
    this.viewport.className = 'flickity-viewport';
    this._createSlider();
    for (var eventName in this.options.on) {
      var listener = this.options.on[eventName];
      this.on(eventName, listener);
    }
    Flickity.createMethods.forEach(function (method) {
      this[method]();
    }, this);
    this.activate();
  };
  proto.option = function (opts) {
    utils.extend(this.options, opts);
  };
  proto.activate = function () {
    if (this.isActive) {
      return;
    }
    this.isActive = !0;
    this.element.classList.add('flickity-enabled');
    if (this.options.rightToLeft) {
      this.element.classList.add('flickity-rtl');
    }
    this.getSize();
    var cellElems = this._filterFindCellElements(this.element.children);
    moveElements(cellElems, this.slider);
    this.viewport.appendChild(this.slider);
    this.element.appendChild(this.viewport);
    this.reloadCells();
    if (this.options.accessibility) {
      this.element.tabIndex = 0;
      this.element.addEventListener('keydown', this);
    }
    this.emitEvent('activate');
    this.selectInitialIndex();
    this.isInitActivated = !0;
    this.dispatchEvent('ready', null, [this.element]);
  };
  proto._createSlider = function () {
    var slider = document.createElement('div');
    slider.className = 'flickity-slider';
    slider.style[this.originSide] = 0;
    this.slider = slider;
  };
  proto._filterFindCellElements = function (elems) {
    return utils.filterFindElements(elems, this.options.cellSelector);
  };
  proto.reloadCells = function () {
    this.cells = this._makeCells(this.slider.children);
    this.positionCells();
    this._getWrapShiftCells();
    this.setGallerySize();
  };
  proto._makeCells = function (elems) {
    var cellElems = this._filterFindCellElements(elems);
    var cells = cellElems.map(function (cellElem) {
      return new Cell(cellElem, this);
    }, this);
    return cells;
  };
  proto.getLastCell = function () {
    return this.cells[this.cells.length - 1];
  };
  proto.getLastSlide = function () {
    return this.slides[this.slides.length - 1];
  };
  proto.positionCells = function () {
    this._sizeCells(this.cells);
    this._positionCells(0);
  };
  proto._positionCells = function (index) {
    index = index || 0;
    this.maxCellHeight = index ? this.maxCellHeight || 0 : 0;
    var cellX = 0;
    if (index > 0) {
      var startCell = this.cells[index - 1];
      cellX = startCell.x + startCell.size.outerWidth;
    }
    var len = this.cells.length;
    for (var i = index; i < len; i++) {
      var cell = this.cells[i];
      cell.setPosition(cellX);
      cellX += cell.size.outerWidth;
      this.maxCellHeight = Math.max(cell.size.outerHeight, this.maxCellHeight);
    }
    this.slideableWidth = cellX;
    this.updateSlides();
    this._containSlides();
    this.slidesWidth = len ? this.getLastSlide().target - this.slides[0].target : 0;
  };
  proto._sizeCells = function (cells) {
    cells.forEach(function (cell) {
      cell.getSize();
    });
  };
  proto.updateSlides = function () {
    this.slides = [];
    if (!this.cells.length) {
      return;
    }
    var slide = new Slide(this);
    this.slides.push(slide);
    var isOriginLeft = this.originSide == 'left';
    var nextMargin = isOriginLeft ? 'marginRight' : 'marginLeft';
    var canCellFit = this._getCanCellFit();
    this.cells.forEach(function (cell, i) {
      if (!slide.cells.length) {
        slide.addCell(cell);
        return;
      }
      var slideWidth = slide.outerWidth - slide.firstMargin + (cell.size.outerWidth - cell.size[nextMargin]);
      if (canCellFit.call(this, i, slideWidth)) {
        slide.addCell(cell);
      } else {
        slide.updateTarget();
        slide = new Slide(this);
        this.slides.push(slide);
        slide.addCell(cell);
      }
    }, this);
    slide.updateTarget();
    this.updateSelectedSlide();
  };
  proto._getCanCellFit = function () {
    var groupCells = this.options.groupCells;
    if (!groupCells) {
      return function () {
        return !1;
      };
    } else if (typeof groupCells == 'number') {
      var number = parseInt(groupCells, 10);
      return function (i) {
        return i % number !== 0;
      };
    }
    var percentMatch = typeof groupCells == 'string' && groupCells.match(/^(\d+)%$/);
    var percent = percentMatch ? parseInt(percentMatch[1], 10) / 100 : 1;
    return function (i, slideWidth) {
      return slideWidth <= (this.size.innerWidth + 1) * percent;
    };
  };
  proto.reposition = function () {
    this.positionCells();
    this.positionSliderAtSelected();
  };
  proto.getSize = function () {
    this.size = getSize(this.element);
    this.setCellAlign();
    this.cursorPosition = this.size.innerWidth * this.cellAlign;
  };
  var cellAlignShorthands = {
    center: {
      left: 0.5,
      right: 0.5
    },
    left: {
      left: 0,
      right: 1
    },
    right: {
      right: 0,
      left: 1
    }
  };
  proto.setCellAlign = function () {
    var shorthand = cellAlignShorthands[this.options.cellAlign];
    this.cellAlign = shorthand ? shorthand[this.originSide] : this.options.cellAlign;
  };
  proto.setGallerySize = function () {
    if (this.options.setGallerySize) {
      var height = this.options.adaptiveHeight && this.selectedSlide ? this.selectedSlide.height : this.maxCellHeight;
      this.viewport.style.height = height + 'px';
    }
  };
  proto._getWrapShiftCells = function () {
    if (!this.options.wrapAround) {
      return;
    }
    this._unshiftCells(this.beforeShiftCells);
    this._unshiftCells(this.afterShiftCells);
    var gapX = this.cursorPosition;
    var cellIndex = this.cells.length - 1;
    this.beforeShiftCells = this._getGapCells(gapX, cellIndex, -1);
    gapX = this.size.innerWidth - this.cursorPosition;
    this.afterShiftCells = this._getGapCells(gapX, 0, 1);
  };
  proto._getGapCells = function (gapX, cellIndex, increment) {
    var cells = [];
    while (gapX > 0) {
      var cell = this.cells[cellIndex];
      if (!cell) {
        break;
      }
      cells.push(cell);
      cellIndex += increment;
      gapX -= cell.size.outerWidth;
    }
    return cells;
  };
  proto._containSlides = function () {
    if (!this.options.contain || this.options.wrapAround || !this.cells.length) {
      return;
    }
    var isRightToLeft = this.options.rightToLeft;
    var beginMargin = isRightToLeft ? 'marginRight' : 'marginLeft';
    var endMargin = isRightToLeft ? 'marginLeft' : 'marginRight';
    var contentWidth = this.slideableWidth - this.getLastCell().size[endMargin];
    var isContentSmaller = contentWidth < this.size.innerWidth;
    var beginBound = this.cursorPosition + this.cells[0].size[beginMargin];
    var endBound = contentWidth - this.size.innerWidth * (1 - this.cellAlign);
    this.slides.forEach(function (slide) {
      if (isContentSmaller) {
        slide.target = contentWidth * this.cellAlign;
      } else {
        slide.target = Math.max(slide.target, beginBound);
        slide.target = Math.min(slide.target, endBound);
      }
    }, this);
  };
  proto.dispatchEvent = function (type, event, args) {
    var emitArgs = event ? [event].concat(args) : args;
    this.emitEvent(type, emitArgs);
  };
  proto.select = function (index, isWrap, isInstant) {
    if (!this.isActive) {
      return;
    }
    index = parseInt(index, 10);
    this._wrapSelect(index);
    if (this.options.wrapAround || isWrap) {
      index = utils.modulo(index, this.slides.length);
    }
    if (!this.slides[index]) {
      return;
    }
    var prevIndex = this.selectedIndex;
    this.selectedIndex = index;
    this.updateSelectedSlide();
    if (isInstant) {
      this.positionSliderAtSelected();
    } else {
      this.startAnimation();
    }
    if (this.options.adaptiveHeight) {
      this.setGallerySize();
    }
    this.dispatchEvent('select', null, [index]);
    if (index != prevIndex) {
      this.dispatchEvent('change', null, [index]);
    }
    this.dispatchEvent('cellSelect');
  };
  proto._wrapSelect = function (index) {
    var len = this.slides.length;
    var isWrapping = this.options.wrapAround && len > 1;
    if (!isWrapping) {
      return index;
    }
    var wrapIndex = utils.modulo(index, len);
    var delta = Math.abs(wrapIndex - this.selectedIndex);
    var backWrapDelta = Math.abs(wrapIndex + len - this.selectedIndex);
    var forewardWrapDelta = Math.abs(wrapIndex - len - this.selectedIndex);
    if (!this.isDragSelect && backWrapDelta < delta) {
      index += len;
    } else if (!this.isDragSelect && forewardWrapDelta < delta) {
      index -= len;
    }
    if (index < 0) {
      this.x -= this.slideableWidth;
    } else if (index >= len) {
      this.x += this.slideableWidth;
    }
  };
  proto.previous = function (isWrap, isInstant) {
    this.select(this.selectedIndex - 1, isWrap, isInstant);
  };
  proto.next = function (isWrap, isInstant) {
    this.select(this.selectedIndex + 1, isWrap, isInstant);
  };
  proto.updateSelectedSlide = function () {
    var slide = this.slides[this.selectedIndex];
    if (!slide) {
      return;
    }
    this.unselectSelectedSlide();
    this.selectedSlide = slide;
    slide.select();
    this.selectedCells = slide.cells;
    this.selectedElements = slide.getCellElements();
    this.selectedCell = slide.cells[0];
    this.selectedElement = this.selectedElements[0];
  };
  proto.unselectSelectedSlide = function () {
    if (this.selectedSlide) {
      this.selectedSlide.unselect();
    }
  };
  proto.selectInitialIndex = function () {
    var initialIndex = this.options.initialIndex;
    if (this.isInitActivated) {
      this.select(this.selectedIndex, !1, !0);
      return;
    }
    if (initialIndex && typeof initialIndex == 'string') {
      var cell = this.queryCell(initialIndex);
      if (cell) {
        this.selectCell(initialIndex, !1, !0);
        return;
      }
    }
    var index = 0;
    if (initialIndex && this.slides[initialIndex]) {
      index = initialIndex;
    }
    this.select(index, !1, !0);
  };
  proto.selectCell = function (value, isWrap, isInstant) {
    var cell = this.queryCell(value);
    if (!cell) {
      return;
    }
    var index = this.getCellSlideIndex(cell);
    this.select(index, isWrap, isInstant);
  };
  proto.getCellSlideIndex = function (cell) {
    for (var i = 0; i < this.slides.length; i++) {
      var slide = this.slides[i];
      var index = slide.cells.indexOf(cell);
      if (index != -1) {
        return i;
      }
    }
  };
  proto.getCell = function (elem) {
    for (var i = 0; i < this.cells.length; i++) {
      var cell = this.cells[i];
      if (cell.element == elem) {
        return cell;
      }
    }
  };
  proto.getCells = function (elems) {
    elems = utils.makeArray(elems);
    var cells = [];
    elems.forEach(function (elem) {
      var cell = this.getCell(elem);
      if (cell) {
        cells.push(cell);
      }
    }, this);
    return cells;
  };
  proto.getCellElements = function () {
    return this.cells.map(function (cell) {
      return cell.element;
    });
  };
  proto.getParentCell = function (elem) {
    var cell = this.getCell(elem);
    if (cell) {
      return cell;
    }
    elem = utils.getParent(elem, '.flickity-slider > *');
    return this.getCell(elem);
  };
  proto.getAdjacentCellElements = function (adjCount, index) {
    if (!adjCount) {
      return this.selectedSlide.getCellElements();
    }
    index = index === undefined ? this.selectedIndex : index;
    var len = this.slides.length;
    if (1 + adjCount * 2 >= len) {
      return this.getCellElements();
    }
    var cellElems = [];
    for (var i = index - adjCount; i <= index + adjCount; i++) {
      var slideIndex = this.options.wrapAround ? utils.modulo(i, len) : i;
      var slide = this.slides[slideIndex];
      if (slide) {
        cellElems = cellElems.concat(slide.getCellElements());
      }
    }
    return cellElems;
  };
  proto.queryCell = function (selector) {
    if (typeof selector == 'number') {
      return this.cells[selector];
    }
    if (typeof selector == 'string') {
      if (selector.match(/^[#.]?[\d/]/)) {
        return;
      }
      selector = this.element.querySelector(selector);
    }
    return this.getCell(selector);
  };
  proto.uiChange = function () {
    this.emitEvent('uiChange');
  };
  proto.childUIPointerDown = function (event) {
    if (event.type != 'touchstart') {
      event.preventDefault();
    }
    this.focus();
  };
  proto.onresize = function () {
    this.resize();
  };
  utils.debounceMethod(Flickity, 'onresize', 150);
  proto.resize = function () {
    if (!this.isActive || this.isAnimating || this.isDragging) {
      return;
    }
    this.getSize();
    if (this.options.wrapAround) {
      this.x = utils.modulo(this.x, this.slideableWidth);
    }
    this.positionCells();
    this._getWrapShiftCells();
    this.setGallerySize();
    this.emitEvent('resize');
    var selectedElement = this.selectedElements && this.selectedElements[0];
    this.selectCell(selectedElement, !1, !0);
  };
  proto.onkeydown = function (event) {
    var isNotFocused = document.activeElement && document.activeElement != this.element;
    if (!this.options.accessibility || isNotFocused) {
      return;
    }
    var handler = Flickity.keyboardHandlers[event.keyCode];
    if (handler) {
      handler.call(this);
    }
  };
  Flickity.keyboardHandlers = {
    37: function _() {
      var leftMethod = this.options.rightToLeft ? 'next' : 'previous';
      this.uiChange();
      this[leftMethod]();
    },
    39: function _() {
      var rightMethod = this.options.rightToLeft ? 'previous' : 'next';
      this.uiChange();
      this[rightMethod]();
    }
  };
  proto.focus = function () {
    var prevScrollY = window.pageYOffset;
    this.element.focus({
      preventScroll: !0
    });
    if (window.pageYOffset != prevScrollY) {
      window.scrollTo(window.pageXOffset, prevScrollY);
    }
  };
  proto.deactivate = function () {
    if (!this.isActive) {
      return;
    }
    this.element.classList.remove('flickity-enabled');
    this.element.classList.remove('flickity-rtl');
    this.unselectSelectedSlide();
    this.cells.forEach(function (cell) {
      cell.destroy();
    });
    this.element.removeChild(this.viewport);
    moveElements(this.slider.children, this.element);
    if (this.options.accessibility) {
      this.element.removeAttribute('tabIndex');
      this.element.removeEventListener('keydown', this);
    }
    this.isActive = !1;
    this.emitEvent('deactivate');
  };
  proto.destroy = function () {
    this.deactivate();
    window.removeEventListener('resize', this);
    this.allOff();
    this.emitEvent('destroy');
    delete this.element.flickityGUID;
    delete instances[this.guid];
  };
  utils.extend(proto, animatePrototype);
  Flickity.data = function (elem) {
    elem = utils.getQueryElement(elem);
    var id = elem && elem.flickityGUID;
    return id && instances[id];
  };
  utils.htmlInit(Flickity, 'flickity');
  Flickity.Cell = Cell;
  Flickity.Slide = Slide;
  return Flickity;
})
/*!
 * Unipointer v2.4.0
 * base class for doing one thing with pointer event
 * MIT license
 */;
(function (window, factory) {
  window.Unipointer = factory(window, window.EvEmitter);
})(window, function factory(window, EvEmitter) {
  function noop() {}
  function Unipointer() {}
  var proto = Unipointer.prototype = Object.create(EvEmitter.prototype);
  proto.bindStartEvent = function (elem) {
    this._bindStartEvent(elem, !0);
  };
  proto.unbindStartEvent = function (elem) {
    this._bindStartEvent(elem, !1);
  };
  proto._bindStartEvent = function (elem, isAdd) {
    isAdd = isAdd === undefined ? !0 : isAdd;
    var bindMethod = isAdd ? 'addEventListener' : 'removeEventListener';
    var startEvent = 'mousedown';
    if ('ontouchstart' in window) {
      startEvent = 'touchstart';
    } else if (window.PointerEvent) {
      startEvent = 'pointerdown';
    }
    elem[bindMethod](startEvent, this);
  };
  proto.handleEvent = function (event) {
    var method = 'on' + event.type;
    if (this[method]) {
      this[method](event);
    }
  };
  proto.getTouch = function (touches) {
    for (var i = 0; i < touches.length; i++) {
      var touch = touches[i];
      if (touch.identifier == this.pointerIdentifier) {
        return touch;
      }
    }
  };
  proto.onmousedown = function (event) {
    var button = event.button;
    if (button && button !== 0 && button !== 1) {
      return;
    }
    this._pointerDown(event, event);
  };
  proto.ontouchstart = function (event) {
    this._pointerDown(event, event.changedTouches[0]);
  };
  proto.onpointerdown = function (event) {
    this._pointerDown(event, event);
  };
  proto._pointerDown = function (event, pointer) {
    if (event.button || this.isPointerDown) {
      return;
    }
    this.isPointerDown = !0;
    this.pointerIdentifier = pointer.pointerId !== undefined ? pointer.pointerId : pointer.identifier;
    this.pointerDown(event, pointer);
  };
  proto.pointerDown = function (event, pointer) {
    this._bindPostStartEvents(event);
    this.emitEvent('pointerDown', [event, pointer]);
  };
  var postStartEvents = {
    mousedown: ['mousemove', 'mouseup'],
    touchstart: ['touchmove', 'touchend', 'touchcancel'],
    pointerdown: ['pointermove', 'pointerup', 'pointercancel']
  };
  proto._bindPostStartEvents = function (event) {
    if (!event) {
      return;
    }
    var events = postStartEvents[event.type];
    events.forEach(function (eventName) {
      window.addEventListener(eventName, this);
    }, this);
    this._boundPointerEvents = events;
  };
  proto._unbindPostStartEvents = function () {
    if (!this._boundPointerEvents) {
      return;
    }
    this._boundPointerEvents.forEach(function (eventName) {
      window.removeEventListener(eventName, this);
    }, this);
    delete this._boundPointerEvents;
  };
  proto.onmousemove = function (event) {
    this._pointerMove(event, event);
  };
  proto.onpointermove = function (event) {
    if (event.pointerId == this.pointerIdentifier) {
      this._pointerMove(event, event);
    }
  };
  proto.ontouchmove = function (event) {
    var touch = this.getTouch(event.changedTouches);
    if (touch) {
      this._pointerMove(event, touch);
    }
  };
  proto._pointerMove = function (event, pointer) {
    this.pointerMove(event, pointer);
  };
  proto.pointerMove = function (event, pointer) {
    this.emitEvent('pointerMove', [event, pointer]);
  };
  proto.onmouseup = function (event) {
    this._pointerUp(event, event);
  };
  proto.onpointerup = function (event) {
    if (event.pointerId == this.pointerIdentifier) {
      this._pointerUp(event, event);
    }
  };
  proto.ontouchend = function (event) {
    var touch = this.getTouch(event.changedTouches);
    if (touch) {
      this._pointerUp(event, touch);
    }
  };
  proto._pointerUp = function (event, pointer) {
    this._pointerDone();
    this.pointerUp(event, pointer);
  };
  proto.pointerUp = function (event, pointer) {
    this.emitEvent('pointerUp', [event, pointer]);
  };
  proto._pointerDone = function () {
    this._pointerReset();
    this._unbindPostStartEvents();
    this.pointerDone();
  };
  proto._pointerReset = function () {
    this.isPointerDown = !1;
    delete this.pointerIdentifier;
  };
  proto.pointerDone = noop;
  proto.onpointercancel = function (event) {
    if (event.pointerId == this.pointerIdentifier) {
      this._pointerCancel(event, event);
    }
  };
  proto.ontouchcancel = function (event) {
    var touch = this.getTouch(event.changedTouches);
    if (touch) {
      this._pointerCancel(event, touch);
    }
  };
  proto._pointerCancel = function (event, pointer) {
    this._pointerDone();
    this.pointerCancel(event, pointer);
  };
  proto.pointerCancel = function (event, pointer) {
    this.emitEvent('pointerCancel', [event, pointer]);
  };
  Unipointer.getPointerPoint = function (pointer) {
    return {
      x: pointer.pageX,
      y: pointer.pageY
    };
  };
  return Unipointer;
})
/*!
 * Unidragger v2.4.0
 * Draggable base class
 * MIT license
 */;
(function (window, factory) {
  window.Unidragger = factory(window, window.Unipointer);
})(window, function factory(window, Unipointer) {
  function Unidragger() {}
  var proto = Unidragger.prototype = Object.create(Unipointer.prototype);
  proto.bindHandles = function () {
    this._bindHandles(!0);
  };
  proto.unbindHandles = function () {
    this._bindHandles(!1);
  };
  proto._bindHandles = function (isAdd) {
    isAdd = isAdd === undefined ? !0 : isAdd;
    var bindMethod = isAdd ? 'addEventListener' : 'removeEventListener';
    var touchAction = isAdd ? this._touchActionValue : '';
    for (var i = 0; i < this.handles.length; i++) {
      var handle = this.handles[i];
      this._bindStartEvent(handle, isAdd);
      handle[bindMethod]('click', this);
      if (window.PointerEvent) {
        handle.style.touchAction = touchAction;
      }
    }
  };
  proto._touchActionValue = 'none';
  proto.pointerDown = function (event, pointer) {
    var isOkay = this.okayPointerDown(event);
    if (!isOkay) {
      return;
    }
    this.pointerDownPointer = {
      pageX: pointer.pageX,
      pageY: pointer.pageY
    };
    event.preventDefault();
    this.pointerDownBlur();
    this._bindPostStartEvents(event);
    this.emitEvent('pointerDown', [event, pointer]);
  };
  var cursorNodes = {
    TEXTAREA: !0,
    INPUT: !0,
    SELECT: !0,
    OPTION: !0
  };
  var clickTypes = {
    radio: !0,
    checkbox: !0,
    button: !0,
    submit: !0,
    image: !0,
    file: !0
  };
  proto.okayPointerDown = function (event) {
    var isCursorNode = cursorNodes[event.target.nodeName];
    var isClickType = clickTypes[event.target.type];
    var isOkay = !isCursorNode || isClickType;
    if (!isOkay) {
      this._pointerReset();
    }
    return isOkay;
  };
  proto.pointerDownBlur = function () {
    var focused = document.activeElement;
    var canBlur = focused && focused.blur && focused != document.body;
    if (canBlur) {
      focused.blur();
    }
  };
  proto.pointerMove = function (event, pointer) {
    var moveVector = this._dragPointerMove(event, pointer);
    this.emitEvent('pointerMove', [event, pointer, moveVector]);
    this._dragMove(event, pointer, moveVector);
  };
  proto._dragPointerMove = function (event, pointer) {
    var moveVector = {
      x: pointer.pageX - this.pointerDownPointer.pageX,
      y: pointer.pageY - this.pointerDownPointer.pageY
    };
    if (!this.isDragging && this.hasDragStarted(moveVector)) {
      this._dragStart(event, pointer);
    }
    return moveVector;
  };
  proto.hasDragStarted = function (moveVector) {
    return Math.abs(moveVector.x) > 3 || Math.abs(moveVector.y) > 3;
  };
  proto.pointerUp = function (event, pointer) {
    this.emitEvent('pointerUp', [event, pointer]);
    this._dragPointerUp(event, pointer);
  };
  proto._dragPointerUp = function (event, pointer) {
    if (this.isDragging) {
      this._dragEnd(event, pointer);
    } else {
      this._staticClick(event, pointer);
    }
  };
  proto._dragStart = function (event, pointer) {
    this.isDragging = !0;
    this.isPreventingClicks = !0;
    this.dragStart(event, pointer);
  };
  proto.dragStart = function (event, pointer) {
    this.emitEvent('dragStart', [event, pointer]);
  };
  proto._dragMove = function (event, pointer, moveVector) {
    if (!this.isDragging) {
      return;
    }
    this.dragMove(event, pointer, moveVector);
  };
  proto.dragMove = function (event, pointer, moveVector) {
    event.preventDefault();
    this.emitEvent('dragMove', [event, pointer, moveVector]);
  };
  proto._dragEnd = function (event, pointer) {
    this.isDragging = !1;
    setTimeout(function () {
      delete this.isPreventingClicks;
    }.bind(this));
    this.dragEnd(event, pointer);
  };
  proto.dragEnd = function (event, pointer) {
    this.emitEvent('dragEnd', [event, pointer]);
  };
  proto.onclick = function (event) {
    if (this.isPreventingClicks) {
      event.preventDefault();
    }
  };
  proto._staticClick = function (event, pointer) {
    if (this.isIgnoringMouseUp && event.type == 'mouseup') {
      return;
    }
    this.staticClick(event, pointer);
    if (event.type != 'mouseup') {
      this.isIgnoringMouseUp = !0;
      setTimeout(function () {
        delete this.isIgnoringMouseUp;
      }.bind(this), 400);
    }
  };
  proto.staticClick = function (event, pointer) {
    this.emitEvent('staticClick', [event, pointer]);
  };
  Unidragger.getPointerPoint = Unipointer.getPointerPoint;
  return Unidragger;
});
(function (window, factory) {
  window.Flickity = factory(window, window.Flickity, window.Unidragger, window.fizzyUIUtils);
})(window, function factory(window, Flickity, Unidragger, utils) {
  utils.extend(Flickity.defaults, {
    draggable: '>1',
    dragThreshold: 3
  });
  Flickity.createMethods.push('_createDrag');
  var proto = Flickity.prototype;
  utils.extend(proto, Unidragger.prototype);
  proto._touchActionValue = 'pan-y';
  proto._createDrag = function () {
    this.on('activate', this.onActivateDrag);
    this.on('uiChange', this._uiChangeDrag);
    this.on('deactivate', this.onDeactivateDrag);
    this.on('cellChange', this.updateDraggable);
  };
  proto.onActivateDrag = function () {
    this.handles = [this.viewport];
    this.bindHandles();
    this.updateDraggable();
  };
  proto.onDeactivateDrag = function () {
    this.unbindHandles();
    this.element.classList.remove('is-draggable');
  };
  proto.updateDraggable = function () {
    if (this.options.draggable == '>1') {
      this.isDraggable = this.slides.length > 1;
    } else {
      this.isDraggable = this.options.draggable;
    }
    if (this.isDraggable) {
      this.element.classList.add('is-draggable');
    } else {
      this.element.classList.remove('is-draggable');
    }
  };
  proto.bindDrag = function () {
    this.options.draggable = !0;
    this.updateDraggable();
  };
  proto.unbindDrag = function () {
    this.options.draggable = !1;
    this.updateDraggable();
  };
  proto._uiChangeDrag = function () {
    delete this.isFreeScrolling;
  };
  proto.pointerDown = function (event, pointer) {
    if (!this.isDraggable) {
      this._pointerDownDefault(event, pointer);
      return;
    }
    var isOkay = this.okayPointerDown(event);
    if (!isOkay) {
      return;
    }
    this._pointerDownPreventDefault(event);
    this.pointerDownFocus(event);
    if (document.activeElement != this.element) {
      this.pointerDownBlur();
    }
    this.dragX = this.x;
    this.viewport.classList.add('is-pointer-down');
    this.pointerDownScroll = getScrollPosition();
    window.addEventListener('scroll', this);
    this._pointerDownDefault(event, pointer);
  };
  proto._pointerDownDefault = function (event, pointer) {
    this.pointerDownPointer = {
      pageX: pointer.pageX,
      pageY: pointer.pageY
    };
    this._bindPostStartEvents(event);
    this.dispatchEvent('pointerDown', event, [pointer]);
  };
  var focusNodes = {
    INPUT: !0,
    TEXTAREA: !0,
    SELECT: !0
  };
  proto.pointerDownFocus = function (event) {
    var isFocusNode = focusNodes[event.target.nodeName];
    if (!isFocusNode) {
      this.focus();
    }
  };
  proto._pointerDownPreventDefault = function (event) {
    var isTouchStart = event.type == 'touchstart';
    var isTouchPointer = event.pointerType == 'touch';
    var isFocusNode = focusNodes[event.target.nodeName];
    if (!isTouchStart && !isTouchPointer && !isFocusNode) {
      event.preventDefault();
    }
  };
  proto.hasDragStarted = function (moveVector) {
    return Math.abs(moveVector.x) > this.options.dragThreshold;
  };
  proto.pointerUp = function (event, pointer) {
    delete this.isTouchScrolling;
    this.viewport.classList.remove('is-pointer-down');
    this.dispatchEvent('pointerUp', event, [pointer]);
    this._dragPointerUp(event, pointer);
  };
  proto.pointerDone = function () {
    window.removeEventListener('scroll', this);
    delete this.pointerDownScroll;
  };
  proto.dragStart = function (event, pointer) {
    if (!this.isDraggable) {
      return;
    }
    this.dragStartPosition = this.x;
    this.startAnimation();
    window.removeEventListener('scroll', this);
    this.dispatchEvent('dragStart', event, [pointer]);
  };
  proto.pointerMove = function (event, pointer) {
    var moveVector = this._dragPointerMove(event, pointer);
    this.dispatchEvent('pointerMove', event, [pointer, moveVector]);
    this._dragMove(event, pointer, moveVector);
  };
  proto.dragMove = function (event, pointer, moveVector) {
    if (!this.isDraggable) {
      return;
    }
    event.preventDefault();
    this.previousDragX = this.dragX;
    var direction = this.options.rightToLeft ? -1 : 1;
    if (this.options.wrapAround) {
      moveVector.x %= this.slideableWidth;
    }
    var dragX = this.dragStartPosition + moveVector.x * direction;
    if (!this.options.wrapAround && this.slides.length) {
      var originBound = Math.max(-this.slides[0].target, this.dragStartPosition);
      dragX = dragX > originBound ? (dragX + originBound) * 0.5 : dragX;
      var endBound = Math.min(-this.getLastSlide().target, this.dragStartPosition);
      dragX = dragX < endBound ? (dragX + endBound) * 0.5 : dragX;
    }
    this.dragX = dragX;
    this.dragMoveTime = new Date();
    this.dispatchEvent('dragMove', event, [pointer, moveVector]);
  };
  proto.dragEnd = function (event, pointer) {
    if (!this.isDraggable) {
      return;
    }
    if (this.options.freeScroll) {
      this.isFreeScrolling = !0;
    }
    var index = this.dragEndRestingSelect();
    if (this.options.freeScroll && !this.options.wrapAround) {
      var restingX = this.getRestingPosition();
      this.isFreeScrolling = -restingX > this.slides[0].target && -restingX < this.getLastSlide().target;
    } else if (!this.options.freeScroll && index == this.selectedIndex) {
      index += this.dragEndBoostSelect();
    }
    delete this.previousDragX;
    this.isDragSelect = this.options.wrapAround;
    this.select(index);
    delete this.isDragSelect;
    this.dispatchEvent('dragEnd', event, [pointer]);
  };
  proto.dragEndRestingSelect = function () {
    var restingX = this.getRestingPosition();
    var distance = Math.abs(this.getSlideDistance(-restingX, this.selectedIndex));
    var positiveResting = this._getClosestResting(restingX, distance, 1);
    var negativeResting = this._getClosestResting(restingX, distance, -1);
    var index = positiveResting.distance < negativeResting.distance ? positiveResting.index : negativeResting.index;
    return index;
  };
  proto._getClosestResting = function (restingX, distance, increment) {
    var index = this.selectedIndex;
    var minDistance = Infinity;
    var condition = this.options.contain && !this.options.wrapAround ? function (dist, minDist) {
      return dist <= minDist;
    } : function (dist, minDist) {
      return dist < minDist;
    };
    while (condition(distance, minDistance)) {
      index += increment;
      minDistance = distance;
      distance = this.getSlideDistance(-restingX, index);
      if (distance === null) {
        break;
      }
      distance = Math.abs(distance);
    }
    return {
      distance: minDistance,
      index: index - increment
    };
  };
  proto.getSlideDistance = function (x, index) {
    var len = this.slides.length;
    var isWrapAround = this.options.wrapAround && len > 1;
    var slideIndex = isWrapAround ? utils.modulo(index, len) : index;
    var slide = this.slides[slideIndex];
    if (!slide) {
      return null;
    }
    var wrap = isWrapAround ? this.slideableWidth * Math.floor(index / len) : 0;
    return x - (slide.target + wrap);
  };
  proto.dragEndBoostSelect = function () {
    if (this.previousDragX === undefined || !this.dragMoveTime || new Date() - this.dragMoveTime > 100) {
      return 0;
    }
    var distance = this.getSlideDistance(-this.dragX, this.selectedIndex);
    var delta = this.previousDragX - this.dragX;
    if (distance > 0 && delta > 0) {
      return 1;
    } else if (distance < 0 && delta < 0) {
      return -1;
    }
    return 0;
  };
  proto.staticClick = function (event, pointer) {
    var clickedCell = this.getParentCell(event.target);
    var cellElem = clickedCell && clickedCell.element;
    var cellIndex = clickedCell && this.cells.indexOf(clickedCell);
    this.dispatchEvent('staticClick', event, [pointer, cellElem, cellIndex]);
  };
  proto.onscroll = function () {
    var scroll = getScrollPosition();
    var scrollMoveX = this.pointerDownScroll.x - scroll.x;
    var scrollMoveY = this.pointerDownScroll.y - scroll.y;
    if (Math.abs(scrollMoveX) > 3 || Math.abs(scrollMoveY) > 3) {
      this._pointerDone();
    }
  };
  function getScrollPosition() {
    return {
      x: window.pageXOffset,
      y: window.pageYOffset
    };
  }
  return Flickity;
});
(function (window, factory) {
  factory(window, window.Flickity, window.Unipointer, window.fizzyUIUtils);
})(window, function factory(window, Flickity, Unipointer, utils) {
  'use strict';

  var svgURI = 'http://www.w3.org/2000/svg';
  function PrevNextButton(direction, parent) {
    this.direction = direction;
    this.parent = parent;
    this._create();
  }
  PrevNextButton.prototype = Object.create(Unipointer.prototype);
  PrevNextButton.prototype._create = function () {
    this.isEnabled = !0;
    this.isPrevious = this.direction == -1;
    var leftDirection = this.parent.options.rightToLeft ? 1 : -1;
    this.isLeft = this.direction == leftDirection;
    var element = this.element = document.createElement('button');
    element.className = 'flickity-button flickity-prev-next-button';
    element.className += this.isPrevious ? ' flickity-previous' : ' flickity-next';
    element.setAttribute('type', 'button');
    this.disable();
    element.setAttribute('aria-label', this.isPrevious ? 'Previous' : 'Next');
    var svg = this.createSVG();
    element.appendChild(svg);
    this.parent.on('select', this.update.bind(this));
    this.on('pointerDown', this.parent.childUIPointerDown.bind(this.parent));
  };
  PrevNextButton.prototype.activate = function () {
    this.bindStartEvent(this.element);
    this.element.addEventListener('click', this);
    this.parent.element.appendChild(this.element);
  };
  PrevNextButton.prototype.deactivate = function () {
    this.parent.element.removeChild(this.element);
    this.unbindStartEvent(this.element);
    this.element.removeEventListener('click', this);
  };
  PrevNextButton.prototype.createSVG = function () {
    var svg = document.createElementNS(svgURI, 'svg');
    svg.setAttribute('class', 'flickity-button-icon');
    svg.setAttribute('viewBox', '0 0 100 100');
    var path = document.createElementNS(svgURI, 'path');
    var pathMovements = getArrowMovements(this.parent.options.arrowShape);
    path.setAttribute('d', pathMovements);
    path.setAttribute('class', 'arrow');
    if (!this.isLeft) {
      path.setAttribute('transform', 'translate(100, 100) rotate(180) ');
    }
    svg.appendChild(path);
    return svg;
  };
  function getArrowMovements(shape) {
    if (typeof shape == 'string') {
      return shape;
    }
    return 'M ' + shape.x0 + ',50' + ' L ' + shape.x1 + ',' + (shape.y1 + 50) + ' L ' + shape.x2 + ',' + (shape.y2 + 50) + ' L ' + shape.x3 + ',50 ' + ' L ' + shape.x2 + ',' + (50 - shape.y2) + ' L ' + shape.x1 + ',' + (50 - shape.y1) + ' Z';
  }
  PrevNextButton.prototype.handleEvent = utils.handleEvent;
  PrevNextButton.prototype.onclick = function () {
    if (!this.isEnabled) {
      return;
    }
    this.parent.uiChange();
    var method = this.isPrevious ? 'previous' : 'next';
    this.parent[method]();
  };
  PrevNextButton.prototype.enable = function () {
    if (this.isEnabled) {
      return;
    }
    this.element.disabled = !1;
    this.isEnabled = !0;
  };
  PrevNextButton.prototype.disable = function () {
    if (!this.isEnabled) {
      return;
    }
    this.element.disabled = !0;
    this.isEnabled = !1;
  };
  PrevNextButton.prototype.update = function () {
    var slides = this.parent.slides;
    if (this.parent.options.wrapAround && slides.length > 1) {
      this.enable();
      return;
    }
    var lastIndex = slides.length ? slides.length - 1 : 0;
    var boundIndex = this.isPrevious ? 0 : lastIndex;
    var method = this.parent.selectedIndex == boundIndex ? 'disable' : 'enable';
    this[method]();
  };
  PrevNextButton.prototype.destroy = function () {
    this.deactivate();
    this.allOff();
  };
  utils.extend(Flickity.defaults, {
    prevNextButtons: !0,
    arrowShape: {
      x0: 10,
      x1: 60,
      y1: 50,
      x2: 70,
      y2: 40,
      x3: 30
    }
  });
  Flickity.createMethods.push('_createPrevNextButtons');
  var proto = Flickity.prototype;
  proto._createPrevNextButtons = function () {
    if (!this.options.prevNextButtons) {
      return;
    }
    this.prevButton = new PrevNextButton(-1, this);
    this.nextButton = new PrevNextButton(1, this);
    this.on('activate', this.activatePrevNextButtons);
  };
  proto.activatePrevNextButtons = function () {
    this.prevButton.activate();
    this.nextButton.activate();
    this.on('deactivate', this.deactivatePrevNextButtons);
  };
  proto.deactivatePrevNextButtons = function () {
    this.prevButton.deactivate();
    this.nextButton.deactivate();
    this.off('deactivate', this.deactivatePrevNextButtons);
  };
  Flickity.PrevNextButton = PrevNextButton;
  return Flickity;
});
(function (window, factory) {
  factory(window, window.Flickity, window.Unipointer, window.fizzyUIUtils);
})(window, function factory(window, Flickity, Unipointer, utils) {
  function PageDots(parent) {
    this.parent = parent;
    this._create();
  }
  PageDots.prototype = Object.create(Unipointer.prototype);
  PageDots.prototype._create = function () {
    this.holder = document.createElement('ol');
    this.holder.className = 'flickity-page-dots';
    this.dots = [];
    this.handleClick = this.onClick.bind(this);
    this.on('pointerDown', this.parent.childUIPointerDown.bind(this.parent));
  };
  PageDots.prototype.activate = function () {
    this.setDots();
    this.holder.addEventListener('click', this.handleClick);
    this.bindStartEvent(this.holder);
    this.parent.element.appendChild(this.holder);
  };
  PageDots.prototype.deactivate = function () {
    this.holder.removeEventListener('click', this.handleClick);
    this.unbindStartEvent(this.holder);
    this.parent.element.removeChild(this.holder);
  };
  PageDots.prototype.setDots = function () {
    var delta = this.parent.slides.length - this.dots.length;
    if (delta > 0) {
      this.addDots(delta);
    } else if (delta < 0) {
      this.removeDots(-delta);
    }
  };
  PageDots.prototype.addDots = function (count) {
    var fragment = document.createDocumentFragment();
    var newDots = [];
    var length = this.dots.length;
    var max = length + count;
    for (var i = length; i < max; i++) {
      var dot = document.createElement('li');
      dot.className = 'dot';
      dot.setAttribute('aria-label', 'Page dot ' + (i + 1));
      fragment.appendChild(dot);
      newDots.push(dot);
    }
    this.holder.appendChild(fragment);
    this.dots = this.dots.concat(newDots);
  };
  PageDots.prototype.removeDots = function (count) {
    var removeDots = this.dots.splice(this.dots.length - count, count);
    removeDots.forEach(function (dot) {
      this.holder.removeChild(dot);
    }, this);
  };
  PageDots.prototype.updateSelected = function () {
    if (this.selectedDot) {
      this.selectedDot.className = 'dot';
      this.selectedDot.removeAttribute('aria-current');
    }
    if (!this.dots.length) {
      return;
    }
    this.selectedDot = this.dots[this.parent.selectedIndex];
    this.selectedDot.className = 'dot is-selected';
    this.selectedDot.setAttribute('aria-current', 'step');
  };
  PageDots.prototype.onTap = PageDots.prototype.onClick = function (event) {
    var target = event.target;
    if (target.nodeName != 'LI') {
      return;
    }
    this.parent.uiChange();
    var index = this.dots.indexOf(target);
    this.parent.select(index);
  };
  PageDots.prototype.destroy = function () {
    this.deactivate();
    this.allOff();
  };
  Flickity.PageDots = PageDots;
  utils.extend(Flickity.defaults, {
    pageDots: !0
  });
  Flickity.createMethods.push('_createPageDots');
  var proto = Flickity.prototype;
  proto._createPageDots = function () {
    if (!this.options.pageDots) {
      return;
    }
    this.pageDots = new PageDots(this);
    this.on('activate', this.activatePageDots);
    this.on('select', this.updateSelectedPageDots);
    this.on('cellChange', this.updatePageDots);
    this.on('resize', this.updatePageDots);
    this.on('deactivate', this.deactivatePageDots);
  };
  proto.activatePageDots = function () {
    this.pageDots.activate();
  };
  proto.updateSelectedPageDots = function () {
    this.pageDots.updateSelected();
  };
  proto.updatePageDots = function () {
    this.pageDots.setDots();
  };
  proto.deactivatePageDots = function () {
    this.pageDots.deactivate();
  };
  Flickity.PageDots = PageDots;
  return Flickity;
});
(function (window, factory) {
  factory(window.EvEmitter, window.fizzyUIUtils, window.Flickity);
})(window, function factory(EvEmitter, utils, Flickity) {
  function Player(parent) {
    this.parent = parent;
    this.state = 'stopped';
    this.onVisibilityChange = this.visibilityChange.bind(this);
    this.onVisibilityPlay = this.visibilityPlay.bind(this);
  }
  Player.prototype = Object.create(EvEmitter.prototype);
  Player.prototype.play = function () {
    if (this.state == 'playing') {
      return;
    }
    var isPageHidden = document.hidden;
    if (isPageHidden) {
      document.addEventListener('visibilitychange', this.onVisibilityPlay);
      return;
    }
    this.state = 'playing';
    document.addEventListener('visibilitychange', this.onVisibilityChange);
    this.tick();
  };
  Player.prototype.tick = function () {
    if (this.state != 'playing') {
      return;
    }
    var time = this.parent.options.autoPlay;
    time = typeof time == 'number' ? time : 3000;
    var _this = this;
    this.clear();
    this.timeout = setTimeout(function () {
      _this.parent.next(!0);
      _this.tick();
    }, time);
  };
  Player.prototype.stop = function () {
    this.state = 'stopped';
    this.clear();
    document.removeEventListener('visibilitychange', this.onVisibilityChange);
  };
  Player.prototype.clear = function () {
    clearTimeout(this.timeout);
  };
  Player.prototype.pause = function () {
    if (this.state == 'playing') {
      this.state = 'paused';
      this.clear();
    }
  };
  Player.prototype.unpause = function () {
    if (this.state == 'paused') {
      this.play();
    }
  };
  Player.prototype.visibilityChange = function () {
    var isPageHidden = document.hidden;
    this[isPageHidden ? 'pause' : 'unpause']();
  };
  Player.prototype.visibilityPlay = function () {
    this.play();
    document.removeEventListener('visibilitychange', this.onVisibilityPlay);
  };
  utils.extend(Flickity.defaults, {
    pauseAutoPlayOnHover: !0
  });
  Flickity.createMethods.push('_createPlayer');
  var proto = Flickity.prototype;
  proto._createPlayer = function () {
    this.player = new Player(this);
    this.on('activate', this.activatePlayer);
    this.on('uiChange', this.stopPlayer);
    this.on('pointerDown', this.stopPlayer);
    this.on('deactivate', this.deactivatePlayer);
  };
  proto.activatePlayer = function () {
    if (!this.options.autoPlay) {
      return;
    }
    this.player.play();
    this.element.addEventListener('mouseenter', this);
  };
  proto.playPlayer = function () {
    this.player.play();
  };
  proto.stopPlayer = function () {
    this.player.stop();
  };
  proto.pausePlayer = function () {
    this.player.pause();
  };
  proto.unpausePlayer = function () {
    this.player.unpause();
  };
  proto.deactivatePlayer = function () {
    this.player.stop();
    this.element.removeEventListener('mouseenter', this);
  };
  proto.onmouseenter = function () {
    if (!this.options.pauseAutoPlayOnHover) {
      return;
    }
    this.player.pause();
    this.element.addEventListener('mouseleave', this);
  };
  proto.onmouseleave = function () {
    this.player.unpause();
    this.element.removeEventListener('mouseleave', this);
  };
  Flickity.Player = Player;
  return Flickity;
});
(function (window, factory) {
  factory(window, window.Flickity, window.fizzyUIUtils);
})(window, function factory(window, Flickity, utils) {
  function getCellsFragment(cells) {
    var fragment = document.createDocumentFragment();
    cells.forEach(function (cell) {
      fragment.appendChild(cell.element);
    });
    return fragment;
  }
  var proto = Flickity.prototype;
  proto.insert = function (elems, index) {
    var cells = this._makeCells(elems);
    if (!cells || !cells.length) {
      return;
    }
    var len = this.cells.length;
    index = index === undefined ? len : index;
    var fragment = getCellsFragment(cells);
    var isAppend = index == len;
    if (isAppend) {
      this.slider.appendChild(fragment);
    } else {
      var insertCellElement = this.cells[index].element;
      this.slider.insertBefore(fragment, insertCellElement);
    }
    if (index === 0) {
      this.cells = cells.concat(this.cells);
    } else if (isAppend) {
      this.cells = this.cells.concat(cells);
    } else {
      var endCells = this.cells.splice(index, len - index);
      this.cells = this.cells.concat(cells).concat(endCells);
    }
    this._sizeCells(cells);
    this.cellChange(index, !0);
  };
  proto.append = function (elems) {
    this.insert(elems, this.cells.length);
  };
  proto.prepend = function (elems) {
    this.insert(elems, 0);
  };
  proto.remove = function (elems) {
    var cells = this.getCells(elems);
    if (!cells || !cells.length) {
      return;
    }
    var minCellIndex = this.cells.length - 1;
    cells.forEach(function (cell) {
      cell.remove();
      var index = this.cells.indexOf(cell);
      minCellIndex = Math.min(index, minCellIndex);
      utils.removeFrom(this.cells, cell);
    }, this);
    this.cellChange(minCellIndex, !0);
  };
  proto.cellSizeChange = function (elem) {
    var cell = this.getCell(elem);
    if (!cell) {
      return;
    }
    cell.getSize();
    var index = this.cells.indexOf(cell);
    this.cellChange(index);
  };
  proto.cellChange = function (changedCellIndex, isPositioningSlider) {
    var prevSelectedElem = this.selectedElement;
    this._positionCells(changedCellIndex);
    this._getWrapShiftCells();
    this.setGallerySize();
    var cell = this.getCell(prevSelectedElem);
    if (cell) {
      this.selectedIndex = this.getCellSlideIndex(cell);
    }
    this.selectedIndex = Math.min(this.slides.length - 1, this.selectedIndex);
    this.emitEvent('cellChange', [changedCellIndex]);
    this.select(this.selectedIndex);
    if (isPositioningSlider) {
      this.positionSliderAtSelected();
    }
  };
  return Flickity;
})
/*!
 * Flickity v2.3.0
 * Touch, responsive, flickable carousels
 *
 * Licensed GPLv3 for open source use
 * or Flickity Commercial License for commercial use
 *
 * https://flickity.metafizzy.co
 * Copyright 2015-2021 Metafizzy
 */;
(function (window, factory) {})(window, function factory(Flickity) {
  return Flickity;
});

/***/ }),

/***/ 136:
/***/ (() => {

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _wrapNativeSuper(t) { var r = "function" == typeof Map ? new Map() : void 0; return _wrapNativeSuper = function _wrapNativeSuper(t) { if (null === t || !_isNativeFunction(t)) return t; if ("function" != typeof t) throw new TypeError("Super expression must either be null or a function"); if (void 0 !== r) { if (r.has(t)) return r.get(t); r.set(t, Wrapper); } function Wrapper() { return _construct(t, arguments, _getPrototypeOf(this).constructor); } return Wrapper.prototype = Object.create(t.prototype, { constructor: { value: Wrapper, enumerable: !1, writable: !0, configurable: !0 } }), _setPrototypeOf(Wrapper, t); }, _wrapNativeSuper(t); }
function _construct(t, e, r) { if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments); var o = [null]; o.push.apply(o, e); var p = new (t.bind.apply(t, o))(); return r && _setPrototypeOf(p, r.prototype), p; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _isNativeFunction(t) { try { return -1 !== Function.toString.call(t).indexOf("[native code]"); } catch (n) { return "function" == typeof t; } }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
var ColorSwatches = /*#__PURE__*/function (_HTMLElement) {
  function ColorSwatches() {
    _classCallCheck(this, ColorSwatches);
    return _callSuper(this, ColorSwatches, arguments);
  }
  _inherits(ColorSwatches, _HTMLElement);
  return _createClass(ColorSwatches, [{
    key: "connectedCallback",
    value: function connectedCallback() {
      this.selectors = {
        colorSwatchImage: '.grid-product__color-image',
        colorSwatch: '.color-swatch--with-image',
        gridItemLink: '.grid-item__link',
        gridProductImageWrap: '.grid-product__image-wrap'
      };
      this.gridItemLink = this.closest(this.selectors.gridItemLink);
      this.gridProductImageWrap = this.gridItemLink.querySelector(this.selectors.gridProductImageWrap);
      this.colorImages = this.gridProductImageWrap.querySelectorAll(this.selectors.colorSwatchImage);
      if (this.colorImages.length) {
        this.swatches = this.querySelectorAll(this.selectors.colorSwatch);
        this.colorSwatchHovering();
      }
    }
  }, {
    key: "colorSwatchHovering",
    value: function colorSwatchHovering() {
      var _this = this;
      this.swatches.forEach(function (swatch) {
        swatch.addEventListener('mouseenter', function () {
          return _this.setActiveColorImage(swatch);
        });
        swatch.addEventListener('touchstart', function (evt) {
          evt.preventDefault();
          _this.setActiveColorImage(swatch);
        }, {
          passive: !0
        });
        swatch.addEventListener('mouseleave', function () {
          return _this.removeActiveColorImage(swatch);
        });
      });
    }
  }, {
    key: "setActiveColorImage",
    value: function setActiveColorImage(swatch) {
      var id = swatch.dataset.variantId;
      var image = swatch.dataset.variantImage;
      this.colorImages.forEach(function (el) {
        el.classList.remove('is-active');
      });
      this.swatches.forEach(function (el) {
        el.classList.remove('is-active');
      });
      var imageEl = this.gridProductImageWrap.querySelector('.grid-product__color-image--' + id);
      imageEl.style.backgroundImage = 'url(' + image + ')';
      imageEl.classList.add('is-active');
      swatch.classList.add('is-active');
      var variantUrl = swatch.dataset.url;
      var gridItem = swatch.closest('.grid-item__link');
      gridItem.setAttribute('href', variantUrl);
    }
  }, {
    key: "removeActiveColorImage",
    value: function removeActiveColorImage(swatch) {
      var id = swatch.dataset.variantId;
      this.gridProductImageWrap.querySelector(".grid-product__color-image--".concat(id)).classList.remove('is-active');
      swatch.classList.remove('is-active');
    }
  }]);
}(/*#__PURE__*/_wrapNativeSuper(HTMLElement));
if (!customElements.get("color-swatches")) {
  customElements.define('color-swatches', ColorSwatches);
}

/***/ }),

/***/ 156:
/***/ (() => {

(function (window, factory) {
  factory(window.Flickity, window.fizzyUIUtils);
})(window, function factory(Flickity, utils) {
  var Slide = Flickity.Slide;
  var slideUpdateTarget = Slide.prototype.updateTarget;
  Slide.prototype.updateTarget = function () {
    slideUpdateTarget.apply(this, arguments);
    if (!this.parent.options.fade) {
      return;
    }
    var slideTargetX = this.target - this.x;
    var firstCellX = this.cells[0].x;
    this.cells.forEach(function (cell) {
      var targetX = cell.x - firstCellX - slideTargetX;
      cell.renderPosition(targetX);
    });
  };
  var proto = Flickity.prototype;
  Flickity.createMethods.push('_createFade');
  proto._createFade = function () {
    this.fadeIndex = this.selectedIndex;
    this.prevSelectedIndex = this.selectedIndex;
    this.on('select', this.onSelectFade);
    this.on('dragEnd', this.onDragEndFade);
    this.on('settle', this.onSettleFade);
    this.on('activate', this.onActivateFade);
    this.on('deactivate', this.onDeactivateFade);
  };
  var updateSlides = proto.updateSlides;
  proto.updateSlides = function () {
    updateSlides.apply(this, arguments);
    if (!this.options.fade) {
      return;
    }
  };
  proto.onSelectFade = function () {
    this.fadeIndex = Math.min(this.prevSelectedIndex, this.slides.length - 1);
    this.prevSelectedIndex = this.selectedIndex;
  };
  proto.onSettleFade = function () {
    delete this.didDragEnd;
    if (!this.options.fade) {
      return;
    }
    var fadedSlide = this.slides[this.fadeIndex];
  };
  proto.onDragEndFade = function () {
    this.didDragEnd = !0;
  };
  proto.onActivateFade = function () {
    if (this.options.fade) {
      this.element.classList.add('is-fade');
    }
  };
  proto.onDeactivateFade = function () {
    if (!this.options.fade) {
      return;
    }
    this.element.classList.remove('is-fade');
  };
  var positionSlider = proto.positionSlider;
  proto.positionSlider = function () {
    if (!this.options.fade) {
      positionSlider.apply(this, arguments);
      return;
    }
    this.fadeSlides();
    this.dispatchScrollEvent();
  };
  var positionSliderAtSelected = proto.positionSliderAtSelected;
  proto.positionSliderAtSelected = function () {
    if (this.options.fade) {
      this.setTranslateX(0);
    }
    positionSliderAtSelected.apply(this, arguments);
  };
  proto.fadeSlides = function () {
    if (this.slides.length < 2) {
      return;
    }
    var indexes = this.getFadeIndexes();
    var fadeSlideA = this.slides[indexes.a];
    var fadeSlideB = this.slides[indexes.b];
    var distance = this.wrapDifference(fadeSlideA.target, fadeSlideB.target);
    var progress = this.wrapDifference(fadeSlideA.target, -this.x);
    progress = progress / distance;
    var fadeHideIndex = indexes.a;
    if (this.isDragging) {
      fadeHideIndex = progress > 0.5 ? indexes.a : indexes.b;
    }
    var isNewHideIndex = this.fadeHideIndex != undefined && this.fadeHideIndex != fadeHideIndex && this.fadeHideIndex != indexes.a && this.fadeHideIndex != indexes.b;
    this.fadeHideIndex = fadeHideIndex;
  };
  proto.getFadeIndexes = function () {
    if (!this.isDragging && !this.didDragEnd) {
      return {
        a: this.fadeIndex,
        b: this.selectedIndex
      };
    }
    if (this.options.wrapAround) {
      return this.getFadeDragWrapIndexes();
    } else {
      return this.getFadeDragLimitIndexes();
    }
  };
  proto.getFadeDragWrapIndexes = function () {
    var distances = this.slides.map(function (slide, i) {
      return this.getSlideDistance(-this.x, i);
    }, this);
    var absDistances = distances.map(function (distance) {
      return Math.abs(distance);
    });
    var minDistance = Math.min.apply(Math, absDistances);
    var closestIndex = absDistances.indexOf(minDistance);
    var distance = distances[closestIndex];
    var len = this.slides.length;
    var delta = distance >= 0 ? 1 : -1;
    return {
      a: closestIndex,
      b: utils.modulo(closestIndex + delta, len)
    };
  };
  proto.getFadeDragLimitIndexes = function () {
    var dragIndex = 0;
    for (var i = 0; i < this.slides.length - 1; i++) {
      var slide = this.slides[i];
      if (-this.x < slide.target) {
        break;
      }
      dragIndex = i;
    }
    return {
      a: dragIndex,
      b: dragIndex + 1
    };
  };
  proto.wrapDifference = function (a, b) {
    var diff = b - a;
    if (!this.options.wrapAround) {
      return diff;
    }
    var diffPlus = diff + this.slideableWidth;
    var diffMinus = diff - this.slideableWidth;
    if (Math.abs(diffPlus) < Math.abs(diff)) {
      diff = diffPlus;
    }
    if (Math.abs(diffMinus) < Math.abs(diff)) {
      diff = diffMinus;
    }
    return diff;
  };
  var _getWrapShiftCells = proto._getWrapShiftCells;
  proto._getWrapShiftCells = function () {
    if (!this.options.fade) {
      _getWrapShiftCells.apply(this, arguments);
    }
  };
  var shiftWrapCells = proto.shiftWrapCells;
  proto.shiftWrapCells = function () {
    if (!this.options.fade) {
      shiftWrapCells.apply(this, arguments);
    }
  };
  return Flickity;
});

/***/ }),

/***/ 180:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _default)
/* harmony export */ });
/* harmony import */ var _archetype_themes_vendors_in_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(757);
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = "function" == typeof Symbol ? Symbol : {}, a = i.iterator || "@@iterator", c = i.asyncIterator || "@@asyncIterator", u = i.toStringTag || "@@toStringTag"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, ""); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, "_invoke", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: "normal", arg: t.call(e, r) }; } catch (t) { return { type: "throw", arg: t }; } } e.wrap = wrap; var h = "suspendedStart", l = "suspendedYield", f = "executing", s = "completed", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { ["next", "throw", "return"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if ("throw" !== c.type) { var u = c.arg, h = u.value; return h && "object" == _typeof(h) && n.call(h, "__await") ? e.resolve(h.__await).then(function (t) { invoke("next", t, i, a); }, function (t) { invoke("throw", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke("throw", t, i, a); }); } a(c.arg); } var r; o(this, "_invoke", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error("Generator is already running"); if (o === s) { if ("throw" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if ("next" === n.method) n.sent = n._sent = n.arg;else if ("throw" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else "return" === n.method && n.abrupt("return", n.arg); o = f; var p = tryCatch(e, r, n); if ("normal" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } "throw" === p.type && (o = s, n.method = "throw", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, "throw" === n && e.iterator["return"] && (r.method = "return", r.arg = t, maybeInvokeDelegate(e, r), "throw" === r.method) || "return" !== n && (r.method = "throw", r.arg = new TypeError("The iterator does not provide a '" + n + "' method")), y; var i = tryCatch(o, e.iterator, r.arg); if ("throw" === i.type) return r.method = "throw", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, "return" !== r.method && (r.method = "next", r.arg = t), r.delegate = null, y) : a : (r.method = "throw", r.arg = new TypeError("iterator result is not an object"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = "normal", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: "root" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || "" === e) { var r = e[a]; if (r) return r.call(e); if ("function" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + " is not iterable"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, "GeneratorFunction"), e.isGeneratorFunction = function (t) { var e = "function" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || "GeneratorFunction" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, "GeneratorFunction")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, "Generator"), define(g, a, function () { return this; }), define(g, "toString", function () { return "[object Generator]"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = "next", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) "t" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if ("throw" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = "throw", a.arg = e, r.next = n, o && (r.method = "next", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if ("root" === i.tryLoc) return handle("end"); if (i.tryLoc <= this.prev) { var c = n.call(i, "catchLoc"), u = n.call(i, "finallyLoc"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error("try statement without catch or finally"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) { var i = o; break; } } i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if ("throw" === t.type) throw t.arg; return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, "catch": function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if ("throw" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error("illegal catch attempt"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, "next" === this.method && (this.arg = t), y; } }, e; }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _wrapNativeSuper(t) { var r = "function" == typeof Map ? new Map() : void 0; return _wrapNativeSuper = function _wrapNativeSuper(t) { if (null === t || !_isNativeFunction(t)) return t; if ("function" != typeof t) throw new TypeError("Super expression must either be null or a function"); if (void 0 !== r) { if (r.has(t)) return r.get(t); r.set(t, Wrapper); } function Wrapper() { return _construct(t, arguments, _getPrototypeOf(this).constructor); } return Wrapper.prototype = Object.create(t.prototype, { constructor: { value: Wrapper, enumerable: !1, writable: !0, configurable: !0 } }), _setPrototypeOf(Wrapper, t); }, _wrapNativeSuper(t); }
function _construct(t, e, r) { if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments); var o = [null]; o.push.apply(o, e); var p = new (t.bind.apply(t, o))(); return r && _setPrototypeOf(p, r.prototype), p; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _isNativeFunction(t) { try { return -1 !== Function.toString.call(t).indexOf("[native code]"); } catch (n) { return "function" == typeof t; } }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }

console.log('base-media.js', '我是test数据');
var _default = /*#__PURE__*/function (_HTMLElement) {
  function _default() {
    var _this;
    _classCallCheck(this, _default);
    _this = _callSuper(this, _default);
    _this.player = null;
    _this.pausedByUser = !1;
    _this.playingWhenLastViewed = !1;
    return _this;
  }
  _inherits(_default, _HTMLElement);
  return _createClass(_default, [{
    key: "connectedCallback",
    value: function connectedCallback() {
      if (!this.hasAttribute('defer-hydration')) {
        this.hydrate();
      }
    }
  }, {
    key: "hydrate",
    value: function () {
      var _hydrate = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {
        var _this2 = this;
        var playerTarget, handler;
        return _regeneratorRuntime().wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              playerTarget = this.getPlayerTarget();
              handler = {
                get: function get(target, prop) {
                  return /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {
                    var resolvedTarget;
                    return _regeneratorRuntime().wrap(function _callee$(_context) {
                      while (1) switch (_context.prev = _context.next) {
                        case 0:
                          _context.next = 2;
                          return target;
                        case 2:
                          resolvedTarget = _context.sent;
                          _this2.playerHandler(resolvedTarget, prop);
                        case 4:
                        case "end":
                          return _context.stop();
                      }
                    }, _callee);
                  }));
                }
              };
              this.player = new Proxy(playerTarget, handler);
              this.setupInViewHandler();
            case 4:
            case "end":
              return _context2.stop();
          }
        }, _callee2, this);
      }));
      function hydrate() {
        return _hydrate.apply(this, arguments);
      }
      return hydrate;
    }()
  }, {
    key: "setupInViewHandler",
    value: function setupInViewHandler() {
      var _this3 = this;
      (0,_archetype_themes_vendors_in_view__WEBPACK_IMPORTED_MODULE_0__["default"])(this, function () {
        if (_this3.autoplay && !_this3.pausedByUser || _this3.playingWhenLastViewed) {
          _this3.play();
        }
        return function () {
          _this3.playingWhenLastViewed = _this3.playing;
          _this3.pause();
        };
      });
    }
  }, {
    key: "attributeChangedCallback",
    value: function attributeChangedCallback(name, oldValue, newValue) {
      if (name === 'defer-hydration' && newValue === null) {
        this.hydrate();
      } else if (name === 'playing') {
        this.handlePlayingAttributeChange(oldValue, newValue);
      }
    }
  }, {
    key: "handlePlayingAttributeChange",
    value: function handlePlayingAttributeChange(oldValue, newValue) {
      if (oldValue === null && newValue === '') {
        this.dispatchEvent(new CustomEvent('media:play', {
          bubbles: !0
        }));
      } else if (newValue === null) {
        this.dispatchEvent(new CustomEvent('media:pause', {
          bubbles: !0
        }));
      }
    }
  }, {
    key: "play",
    value: function play() {
      this.pausedByUser = !1;
      if (this.playing) return;
      this.player.play();
      this.playingWhenLastViewed = !0;
    }
  }, {
    key: "pause",
    value: function pause() {
      this.pausedByUser = !0;
      if (!this.playing) return;
      this.player.pause();
    }
  }, {
    key: "getPlayerTarget",
    value: function getPlayerTarget() {
      throw new Error('getPlayerTarget must be implemented in a subclass');
    }
  }, {
    key: "playerHandler",
    value: function playerHandler(target, prop) {
      throw new Error('playerHandler must be implemented in a subclass');
    }
  }, {
    key: "playing",
    get: function get() {
      return this.hasAttribute('playing');
    }
  }, {
    key: "autoplay",
    get: function get() {
      return this.hasAttribute('autoplay');
    }
  }], [{
    key: "observedAttributes",
    get: function get() {
      return ['defer-hydration', 'playing'];
    }
  }]);
}(/*#__PURE__*/_wrapNativeSuper(HTMLElement));


/***/ }),

/***/ 233:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EVENTS: () => (/* binding */ EVENTS)
/* harmony export */ });
var EVENTS = {
  sortSelected: 'sort:selected',
  variantChange: 'variant:change',
  ajaxProductError: 'ajaxProduct:error',
  ajaxProductAdded: 'ajaxProduct:added',
  mobileNavOpen: 'mobileNav:open',
  mobileNavClose: 'mobileNav:close',
  predictiveSearchOpen: 'predictiveSearch:open',
  predictiveSearchClose: 'predictiveSearch:close',
  predictiveSearchCloseAll: 'predictiveSearch:close-all',
  headerStickyCheck: 'headerSticky:check',
  overlayHeaderChange: 'overlayHeader:change',
  headerOverlayDisable: 'headerOverlay:disable',
  headerOverlayRemoveClass: 'headerOverlay:remove-class',
  cartDrawerChange: 'CartDrawer:change',
  youtubeReady: 'youtube:ready',
  vimeoReady: 'vimeo:ready',
  cartOpen: 'cart:open',
  cartClose: 'cart:close',
  sizeDrawer: 'size:drawer',
  cartUpdated: 'cart:updated',
  headerDrawerOpened: 'headerDrawer:opened',
  headerDrawerClosed: 'headerDrawer:closed',
  headerSearchOpen: 'headerSearch:open',
  headerSearchClose: 'headerSearch:close',
  toggleMobileFilters: 'toggleMobileFilters'
};

/***/ }),

/***/ 350:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HTMLThemeElement: () => (/* binding */ HTMLThemeElement),
/* harmony export */   ThemeElement: () => (/* binding */ ThemeElement)
/* harmony export */ });
/* harmony import */ var _archetype_themes_utils_theme_editor_event_handler_mixin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5);
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _wrapNativeSuper(t) { var r = "function" == typeof Map ? new Map() : void 0; return _wrapNativeSuper = function _wrapNativeSuper(t) { if (null === t || !_isNativeFunction(t)) return t; if ("function" != typeof t) throw new TypeError("Super expression must either be null or a function"); if (void 0 !== r) { if (r.has(t)) return r.get(t); r.set(t, Wrapper); } function Wrapper() { return _construct(t, arguments, _getPrototypeOf(this).constructor); } return Wrapper.prototype = Object.create(t.prototype, { constructor: { value: Wrapper, enumerable: !1, writable: !0, configurable: !0 } }), _setPrototypeOf(Wrapper, t); }, _wrapNativeSuper(t); }
function _construct(t, e, r) { if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments); var o = [null]; o.push.apply(o, e); var p = new (t.bind.apply(t, o))(); return r && _setPrototypeOf(p, r.prototype), p; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _isNativeFunction(t) { try { return -1 !== Function.toString.call(t).indexOf("[native code]"); } catch (n) { return "function" == typeof t; } }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }

var ThemeElement = /*#__PURE__*/function (_HTMLElement) {
  function ThemeElement() {
    _classCallCheck(this, ThemeElement);
    return _callSuper(this, ThemeElement, arguments);
  }
  _inherits(ThemeElement, _HTMLElement);
  return _createClass(ThemeElement, [{
    key: "sectionId",
    get: function get() {
      this._sectionId = this._sectionId || this.getAttribute('section-id');
      if (!this._sectionId) {
        throw new Error("The section-id attribute must be specified for ".concat(this.tagName));
      }
      return this._sectionId;
    }
  }, {
    key: "locales",
    get: function get() {
      this._locales = this._locales || JSON.parse(this.querySelector('script[type="application/json"][data-locales]').textContent);
      return this._locales;
    }
  }]);
}(/*#__PURE__*/_wrapNativeSuper(HTMLElement));
var HTMLThemeElement = /*#__PURE__*/function (_ThemeEditorEventHand) {
  function HTMLThemeElement() {
    _classCallCheck(this, HTMLThemeElement);
    return _callSuper(this, HTMLThemeElement, arguments);
  }
  _inherits(HTMLThemeElement, _ThemeEditorEventHand);
  return _createClass(HTMLThemeElement);
}((0,_archetype_themes_utils_theme_editor_event_handler_mixin__WEBPACK_IMPORTED_MODULE_0__.ThemeEditorEventHandlerMixin)(ThemeElement));


/***/ }),

/***/ 453:
/***/ (() => {

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _wrapNativeSuper(t) { var r = "function" == typeof Map ? new Map() : void 0; return _wrapNativeSuper = function _wrapNativeSuper(t) { if (null === t || !_isNativeFunction(t)) return t; if ("function" != typeof t) throw new TypeError("Super expression must either be null or a function"); if (void 0 !== r) { if (r.has(t)) return r.get(t); r.set(t, Wrapper); } function Wrapper() { return _construct(t, arguments, _getPrototypeOf(this).constructor); } return Wrapper.prototype = Object.create(t.prototype, { constructor: { value: Wrapper, enumerable: !1, writable: !0, configurable: !0 } }), _setPrototypeOf(Wrapper, t); }, _wrapNativeSuper(t); }
function _construct(t, e, r) { if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments); var o = [null]; o.push.apply(o, e); var p = new (t.bind.apply(t, o))(); return r && _setPrototypeOf(p, r.prototype), p; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _isNativeFunction(t) { try { return -1 !== Function.toString.call(t).indexOf("[native code]"); } catch (n) { return "function" == typeof t; } }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
var selectors = {
  disclosureForm: '[data-disclosure-form]',
  disclosureList: '[data-disclosure-list]',
  disclosureToggle: '[data-disclosure-toggle]',
  disclosureInput: '[data-disclosure-input]',
  disclosureOptions: '[data-disclosure-option]'
};
var classes = {
  listVisible: 'disclosure-list--visible'
};
var Disclosure = /*#__PURE__*/function (_HTMLElement) {
  function Disclosure() {
    _classCallCheck(this, Disclosure);
    return _callSuper(this, Disclosure, arguments);
  }
  _inherits(Disclosure, _HTMLElement);
  return _createClass(Disclosure, [{
    key: "connectedCallback",
    value: function connectedCallback() {
      this.container = this;
      this._cacheSelectors();
      this._setupListeners();
    }
  }, {
    key: "disconnectedCallback",
    value: function disconnectedCallback() {
      this.destroy();
    }
  }, {
    key: "_cacheSelectors",
    value: function _cacheSelectors() {
      this.cache = {
        disclosureForm: this.container.closest(selectors.disclosureForm),
        disclosureList: this.container.querySelector(selectors.disclosureList),
        disclosureToggle: this.container.querySelector(selectors.disclosureToggle),
        disclosureInput: this.container.querySelector(selectors.disclosureInput),
        disclosureOptions: this.container.querySelectorAll(selectors.disclosureOptions)
      };
    }
  }, {
    key: "_setupListeners",
    value: function _setupListeners() {
      this.eventHandlers = this._setupEventHandlers();
      this.cache.disclosureToggle.addEventListener('click', this.eventHandlers.toggleList);
      this.cache.disclosureOptions.forEach(function (disclosureOption) {
        disclosureOption.addEventListener('click', this.eventHandlers.connectOptions);
      }, this);
      this.container.addEventListener('keyup', this.eventHandlers.onDisclosureKeyUp);
      this.cache.disclosureList.addEventListener('focusout', this.eventHandlers.onDisclosureListFocusOut);
      this.cache.disclosureToggle.addEventListener('focusout', this.eventHandlers.onDisclosureToggleFocusOut);
      document.body.addEventListener('click', this.eventHandlers.onBodyClick);
    }
  }, {
    key: "_setupEventHandlers",
    value: function _setupEventHandlers() {
      return {
        connectOptions: this._connectOptions.bind(this),
        toggleList: this._toggleList.bind(this),
        onBodyClick: this._onBodyClick.bind(this),
        onDisclosureKeyUp: this._onDisclosureKeyUp.bind(this),
        onDisclosureListFocusOut: this._onDisclosureListFocusOut.bind(this),
        onDisclosureToggleFocusOut: this._onDisclosureToggleFocusOut.bind(this)
      };
    }
  }, {
    key: "_connectOptions",
    value: function _connectOptions(event) {
      event.preventDefault();
      this._submitForm(event.currentTarget.dataset.value);
    }
  }, {
    key: "_onDisclosureToggleFocusOut",
    value: function _onDisclosureToggleFocusOut(event) {
      var disclosureLostFocus = this.container.contains(event.relatedTarget) === !1;
      if (disclosureLostFocus) {
        this._hideList();
      }
    }
  }, {
    key: "_onDisclosureListFocusOut",
    value: function _onDisclosureListFocusOut(event) {
      var childInFocus = event.currentTarget.contains(event.relatedTarget);
      var isVisible = this.cache.disclosureList.classList.contains(classes.listVisible);
      if (isVisible && !childInFocus) {
        this._hideList();
      }
    }
  }, {
    key: "_onDisclosureKeyUp",
    value: function _onDisclosureKeyUp(event) {
      if (event.which !== 27) return;
      this._hideList();
      this.cache.disclosureToggle.focus();
    }
  }, {
    key: "_onBodyClick",
    value: function _onBodyClick(event) {
      var isOption = this.container.contains(event.target);
      var isVisible = this.cache.disclosureList.classList.contains(classes.listVisible);
      if (isVisible && !isOption) {
        this._hideList();
      }
    }
  }, {
    key: "_submitForm",
    value: function _submitForm(value) {
      this.cache.disclosureInput.value = value;
      this.cache.disclosureForm.submit();
    }
  }, {
    key: "_hideList",
    value: function _hideList() {
      this.cache.disclosureList.classList.remove(classes.listVisible);
      this.cache.disclosureToggle.setAttribute('aria-expanded', !1);
    }
  }, {
    key: "_toggleList",
    value: function _toggleList() {
      var ariaExpanded = this.cache.disclosureToggle.getAttribute('aria-expanded') === 'true';
      this.cache.disclosureList.classList.toggle(classes.listVisible);
      this.cache.disclosureToggle.setAttribute('aria-expanded', !ariaExpanded);
    }
  }, {
    key: "destroy",
    value: function destroy() {
      this.cache.disclosureToggle.removeEventListener('click', this.eventHandlers.toggleList);
      this.cache.disclosureOptions.forEach(function (disclosureOption) {
        disclosureOption.removeEventListener('click', this.eventHandlers.connectOptions);
      }, this);
      this.container.removeEventListener('keyup', this.eventHandlers.onDisclosureKeyUp);
      this.cache.disclosureList.removeEventListener('focusout', this.eventHandlers.onDisclosureListFocusOut);
      this.cache.disclosureToggle.removeEventListener('focusout', this.eventHandlers.onDisclosureToggleFocusOut);
      document.body.removeEventListener('click', this.eventHandlers.onBodyClick);
    }
  }]);
}(/*#__PURE__*/_wrapNativeSuper(HTMLElement));
if (!customElements.get("at-disclosure")) {
  customElements.define('at-disclosure', Disclosure);
}

/***/ }),

/***/ 484:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _archetype_themes_modules_slideshow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(950);
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _wrapNativeSuper(t) { var r = "function" == typeof Map ? new Map() : void 0; return _wrapNativeSuper = function _wrapNativeSuper(t) { if (null === t || !_isNativeFunction(t)) return t; if ("function" != typeof t) throw new TypeError("Super expression must either be null or a function"); if (void 0 !== r) { if (r.has(t)) return r.get(t); r.set(t, Wrapper); } function Wrapper() { return _construct(t, arguments, _getPrototypeOf(this).constructor); } return Wrapper.prototype = Object.create(t.prototype, { constructor: { value: Wrapper, enumerable: !1, writable: !0, configurable: !0 } }), _setPrototypeOf(Wrapper, t); }, _wrapNativeSuper(t); }
function _construct(t, e, r) { if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments); var o = [null]; o.push.apply(o, e); var p = new (t.bind.apply(t, o))(); return r && _setPrototypeOf(p, r.prototype), p; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _isNativeFunction(t) { try { return -1 !== Function.toString.call(t).indexOf("[native code]"); } catch (n) { return "function" == typeof t; } }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }

var ProductRecommendations = /*#__PURE__*/function (_HTMLElement) {
  function ProductRecommendations() {
    _classCallCheck(this, ProductRecommendations);
    return _callSuper(this, ProductRecommendations, arguments);
  }
  _inherits(ProductRecommendations, _HTMLElement);
  return _createClass(ProductRecommendations, [{
    key: "connectedCallback",
    value: function connectedCallback() {
      var _this = this;
      this.el = this;
      this.url = this.dataset.url;
      this.intent = this.dataset.intent;
      this.placeholder = this.querySelector('.product-recommendations-placeholder');
      this.productResults = this.querySelector('.grid-product');
      this.sectionId = this.dataset.sectionId;
      this.blockId = this.dataset.blockId;
      fetch(this.url).then(function (response) {
        return response.text();
      }).then(function (text) {
        var html = document.createElement('div');
        html.innerHTML = text;
        var recommendations = html.querySelector('.product-recommendations');
        if (!recommendations) {
          _this.el.classList.add('hide');
          return;
        }
        _this.placeholder.innerHTML = '';
        _this.placeholder.innerHTML = recommendations.innerHTML;
        _this.slideshow = _this.querySelector('[data-slideshow]');
        if (_this.slideshow) {
          _this.setupSlider();
        }
      })["catch"](function (e) {
        console.error(e);
      });
    }
  }, {
    key: "setupSlider",
    value: function setupSlider() {
      var controlType = this.slideshow.dataset.controls;
      var perSlide = parseFloat(this.slideshow.dataset.perSlide);
      var count = parseFloat(this.slideshow.dataset.count);
      var prevNextButtons = !1;
      var pageDots = !0;
      if (controlType === 'arrows') {
        pageDots = !1;
        prevNextButtons = !0;
      }
      if (perSlide < count) {
        this.flickity = new _archetype_themes_modules_slideshow__WEBPACK_IMPORTED_MODULE_0__.Slideshow(this.slideshow, {
          prevNextButtons: prevNextButtons,
          pageDots: pageDots,
          adaptiveHeight: !0,
          wrapAround: !1
        });
      }
    }
  }]);
}(/*#__PURE__*/_wrapNativeSuper(HTMLElement));
if (!customElements.get("product-recommendations")) {
  customElements.define('product-recommendations', ProductRecommendations);
}

/***/ }),

/***/ 757:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ inView)
/* harmony export */ });
var thresholds = {
  any: 0,
  all: 1
};
function inView(elementOrSelector, onStart) {
  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},
    root = _ref.root,
    rootMargin = _ref.margin,
    _ref$amount = _ref.amount,
    amount = _ref$amount === void 0 ? 'any' : _ref$amount;
  if (typeof IntersectionObserver === 'undefined') {
    return function () {};
  }
  var elements;
  if (typeof elementOrSelector === 'string') {
    elements = document.querySelectorAll(elementOrSelector);
  } else if (elementOrSelector instanceof Element) {
    elements = [elementOrSelector];
  } else {
    elements = Array.from(elementOrSelector || []);
  }
  var activeIntersections = new WeakMap();
  var onIntersectionChange = function onIntersectionChange(entries) {
    entries.forEach(function (entry) {
      var onEnd = activeIntersections.get(entry.target);
      if (entry.isIntersecting === Boolean(onEnd)) return;
      if (entry.isIntersecting) {
        var newOnEnd = onStart(entry);
        if (typeof newOnEnd === 'function') {
          activeIntersections.set(entry.target, newOnEnd);
        } else {
          observer.unobserve(entry.target);
        }
      } else if (onEnd) {
        onEnd(entry);
        activeIntersections["delete"](entry.target);
      }
    });
  };
  var observer = new IntersectionObserver(onIntersectionChange, {
    root: root,
    rootMargin: rootMargin,
    threshold: typeof amount === 'number' ? amount : thresholds[amount]
  });
  elements.forEach(function (element) {
    return observer.observe(element);
  });
  return function () {
    return observer.disconnect();
  };
}

/***/ }),

/***/ 762:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _archetype_themes_utils_events__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(233);
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _wrapNativeSuper(t) { var r = "function" == typeof Map ? new Map() : void 0; return _wrapNativeSuper = function _wrapNativeSuper(t) { if (null === t || !_isNativeFunction(t)) return t; if ("function" != typeof t) throw new TypeError("Super expression must either be null or a function"); if (void 0 !== r) { if (r.has(t)) return r.get(t); r.set(t, Wrapper); } function Wrapper() { return _construct(t, arguments, _getPrototypeOf(this).constructor); } return Wrapper.prototype = Object.create(t.prototype, { constructor: { value: Wrapper, enumerable: !1, writable: !0, configurable: !0 } }), _setPrototypeOf(Wrapper, t); }, _wrapNativeSuper(t); }
function _construct(t, e, r) { if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments); var o = [null]; o.push.apply(o, e); var p = new (t.bind.apply(t, o))(); return r && _setPrototypeOf(p, r.prototype), p; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _isNativeFunction(t) { try { return -1 !== Function.toString.call(t).indexOf("[native code]"); } catch (n) { return "function" == typeof t; } }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }

var HeaderSearch = /*#__PURE__*/function (_HTMLElement) {
  function HeaderSearch() {
    _classCallCheck(this, HeaderSearch);
    return _callSuper(this, HeaderSearch, arguments);
  }
  _inherits(HeaderSearch, _HTMLElement);
  return _createClass(HeaderSearch, [{
    key: "connectedCallback",
    value: function connectedCallback() {
      this.abortController = new AbortController();
      this.boundDocumentClick = this.handleDocumentClick.bind(this);
      this.boundCloseAll = this.handleCloseAll.bind(this);
      document.addEventListener(_archetype_themes_utils_events__WEBPACK_IMPORTED_MODULE_0__.EVENTS.headerSearchOpen, this.openInlineSearch.bind(this), {
        signal: this.abortController.signal
      });
    }
  }, {
    key: "disconnectedCallback",
    value: function disconnectedCallback() {
      this.abortController.abort();
    }
  }, {
    key: "openInlineSearch",
    value: function openInlineSearch(evt) {
      evt.preventDefault();
      evt.stopImmediatePropagation();
      this.classList.add('is-active');
      this.dispatchEvent(new CustomEvent(_archetype_themes_utils_events__WEBPACK_IMPORTED_MODULE_0__.EVENTS.predictiveSearchOpen, {
        bubbles: !0,
        detail: {
          context: 'header'
        }
      }));
      this.enableCloseListeners();
    }
  }, {
    key: "enableCloseListeners",
    value: function enableCloseListeners() {
      var _this = this;
      setTimeout(function () {
        document.addEventListener('click', _this.boundDocumentClick, {
          signal: _this.abortController.signal
        });
      }, 0);
      document.addEventListener('predictiveSearch:close-all', this.boundCloseAll, {
        signal: this.abortController.signal
      });
    }
  }, {
    key: "close",
    value: function close(evt) {
      if (evt && evt.target.closest) {
        if (evt.target.closest('.site-header__element--sub')) {
          return;
        } else if (evt.target.closest('#SearchResultsWrapper')) {
          return;
        } else if (evt.target.closest('.site-header__search-container')) {
          return;
        }
      }
      this.classList.remove('is-active');
      document.removeEventListener('click', this.boundDocumentClick);
      this.dispatchEvent(new CustomEvent(_archetype_themes_utils_events__WEBPACK_IMPORTED_MODULE_0__.EVENTS.headerSearchClose, {
        bubbles: !0
      }));
    }
  }, {
    key: "handleDocumentClick",
    value: function handleDocumentClick(evt) {
      this.close(evt);
    }
  }, {
    key: "handleCloseAll",
    value: function handleCloseAll() {
      document.removeEventListener('predictiveSearch:close-all', this.boundCloseAll);
      this.close();
    }
  }]);
}(/*#__PURE__*/_wrapNativeSuper(HTMLElement));
if (!customElements.get("header-search")) {
  customElements.define('header-search', HeaderSearch);
}

/***/ }),

/***/ 950:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Slideshow: () => (/* binding */ Slideshow),
/* harmony export */   SlideshowSection: () => (/* binding */ SlideshowSection)
/* harmony export */ });
/* harmony import */ var _archetype_themes_vendors_flickity__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(129);
/* harmony import */ var _archetype_themes_vendors_flickity__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_archetype_themes_vendors_flickity__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _archetype_themes_vendors_flickity_fade__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(156);
/* harmony import */ var _archetype_themes_vendors_flickity_fade__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_archetype_themes_vendors_flickity_fade__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _archetype_themes_utils_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(123);
/* harmony import */ var _archetype_themes_custom_elements_theme_element__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(350);
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
function _get() { return _get = "undefined" != typeof Reflect && Reflect.get ? Reflect.get.bind() : function (e, t, r) { var p = _superPropBase(e, t); if (p) { var n = Object.getOwnPropertyDescriptor(p, t); return n.get ? n.get.call(arguments.length < 3 ? e : r) : n.value; } }, _get.apply(null, arguments); }
function _superPropBase(t, o) { for (; !{}.hasOwnProperty.call(t, o) && null !== (t = _getPrototypeOf(t));); return t; }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }




var Slideshow = /*#__PURE__*/function () {
  function Slideshow(el, args) {
    var _this = this;
    _classCallCheck(this, Slideshow);
    this.el = el;
    var defaults = {
      adaptiveHeight: !1,
      autoPlay: !1,
      avoidReflow: !1,
      childNav: null,
      childNavScroller: null,
      childVertical: !1,
      dragThreshold: 7,
      fade: !1,
      friction: 0.8,
      initialIndex: 0,
      pageDots: !1,
      pauseAutoPlayOnHover: !1,
      prevNextButtons: !1,
      rightToLeft: document.documentElement.dir === 'rtl',
      selectedAttraction: 0.14,
      setGallerySize: !0,
      wrapAround: !0
    };
    this.args = Object.assign({}, defaults, args);
    this.classes = {
      animateOut: 'animate-out',
      isPaused: 'is-paused',
      isActive: 'is-active'
    };
    this.selectors = {
      allSlides: '.slideshow__slide',
      currentSlide: '.is-selected',
      wrapper: '.slideshow-wrapper',
      pauseButton: '.slideshow__pause'
    };
    this.productSelectors = {
      thumb: '.product__thumb-item:not(.hide)',
      links: '.product__thumb-item:not(.hide) a',
      arrow: '.product__thumb-arrow'
    };
    this.args.on = {
      ready: this.init.bind(this),
      change: this.slideChange.bind(this),
      settle: this.afterChange.bind(this)
    };
    if (this.args.childNav) {
      this.childNavEls = this.args.childNav.querySelectorAll(this.productSelectors.thumb);
      this.childNavLinks = this.args.childNav.querySelectorAll(this.productSelectors.links);
      this.arrows = this.args.childNav.querySelectorAll(this.productSelectors.arrow);
      if (this.childNavLinks.length) {
        this.initChildNav();
      }
    }
    if (this.args.avoidReflow) {
      avoidReflow(el);
    }
    this.slideshow = new Flickity(el, this.args);
    if (el.dataset.zoom && el.dataset.zoom === 'true') {
      this.slideshow.on('dragStart', function () {
        _this.slideshow.slider.style.pointerEvents = 'none';
        if (_this.slideshow.options.fade) {
          _this.slideshow.slider.querySelector('.is-selected').style.pointerEvents = 'none';
        }
      });
      this.slideshow.on('dragEnd', function () {
        _this.slideshow.slider.style.pointerEvents = 'auto';
        if (_this.slideshow.options.fade) {
          _this.slideshow.slider.querySelector('.is-selected').style.pointerEvents = 'auto';
        }
      });
    }
    if (this.args.autoPlay) {
      var wrapper = el.closest(this.selectors.wrapper);
      this.pauseBtn = wrapper.querySelector(this.selectors.pauseButton);
      if (this.pauseBtn) {
        this.pauseBtn.addEventListener('click', this._togglePause.bind(this));
      }
    }
    window.addEventListener('resize', (0,_archetype_themes_utils_utils__WEBPACK_IMPORTED_MODULE_3__.debounce)(300, function () {
      this.resize();
    }.bind(this)));
    function avoidReflow(el) {
      if (!el.id) return;
      var firstChild = el.firstChild;
      while (firstChild != null && firstChild.nodeType == 3) {
        firstChild = firstChild.nextSibling;
      }
      var style = document.createElement('style');
      style.innerHTML = "#".concat(el.id, " .flickity-viewport{height:").concat(firstChild.offsetHeight, "px}");
      document.head.appendChild(style);
    }
  }
  return _createClass(Slideshow, [{
    key: "init",
    value: function init() {
      this.currentSlide = this.el.querySelector(this.selectors.currentSlide);
      if (this.args.callbacks && this.args.callbacks.onInit) {
        if (typeof this.args.callbacks.onInit === 'function') {
          this.args.callbacks.onInit(this.currentSlide);
        }
      }
    }
  }, {
    key: "slideChange",
    value: function slideChange(index) {
      if (this.currentSlide) {
        document.dispatchEvent(new CustomEvent('slideshow-component:slide-changed', {
          detail: {
            previousSlide: this.currentSlide.previousElementSibling,
            currentSlide: this.currentSlide,
            nextSlide: this.currentSlide.nextElementSibling
          }
        }));
      }
      if (this.args.fade && this.currentSlide) {
        this.currentSlide.classList.add(this.classes.animateOut);
        this.currentSlide.addEventListener('transitionend', function () {
          this.currentSlide.classList.remove(this.classes.animateOut);
        }.bind(this));
      }
      if (this.args.childNav) {
        this.childNavGoTo(index);
      }
      if (this.args.callbacks && this.args.callbacks.onChange) {
        if (typeof this.args.callbacks.onChange === 'function') {
          this.args.callbacks.onChange(index);
        }
      }
      if (this.arrows && this.arrows.length) {
        this.arrows[0].classList.toggle('hide', index === 0);
        this.arrows[1].classList.toggle('hide', index === this.childNavLinks.length - 1);
      }
    }
  }, {
    key: "afterChange",
    value: function afterChange() {
      var _this2 = this;
      if (this.args.fade) {
        this.el.querySelectorAll(this.selectors.allSlides).forEach(function (slide) {
          slide.classList.remove(_this2.classes.animateOut);
        });
      }
      this.currentSlide = this.el.querySelector(this.selectors.currentSlide);
      if (this.args.childNav) {
        this.childNavGoTo(this.slideshow.selectedIndex);
      }
    }
  }, {
    key: "destroy",
    value: function destroy() {
      var _this3 = this;
      if (this.args.childNav && this.childNavLinks.length) {
        this.childNavLinks.forEach(function (a) {
          a.classList.remove(_this3.classes.isActive);
        });
      }
      this.slideshow.destroy();
    }
  }, {
    key: "reposition",
    value: function reposition() {
      this.slideshow.reposition();
    }
  }, {
    key: "_togglePause",
    value: function _togglePause() {
      if (this.pauseBtn.classList.contains(this.classes.isPaused)) {
        this.pauseBtn.classList.remove(this.classes.isPaused);
        this.slideshow.playPlayer();
      } else {
        this.pauseBtn.classList.add(this.classes.isPaused);
        this.slideshow.pausePlayer();
      }
    }
  }, {
    key: "resize",
    value: function resize() {
      this.slideshow.resize();
    }
  }, {
    key: "play",
    value: function play() {
      this.slideshow.playPlayer();
    }
  }, {
    key: "pause",
    value: function pause() {
      this.slideshow.pausePlayer();
    }
  }, {
    key: "goToSlide",
    value: function goToSlide(i) {
      this.slideshow.select(i);
    }
  }, {
    key: "setDraggable",
    value: function setDraggable(enable) {
      this.slideshow.options.draggable = enable;
      this.slideshow.updateDraggable();
    }
  }, {
    key: "initChildNav",
    value: function initChildNav() {
      var _this4 = this;
      this.childNavLinks[this.args.initialIndex].classList.add('is-active');
      this.childNavLinks.forEach(function (link, i) {
        link.setAttribute('data-index', i);
        link.addEventListener('click', function (evt) {
          evt.preventDefault();
          this.goToSlide(this.getChildIndex(evt.currentTarget));
        }.bind(_this4));
        link.addEventListener('focus', function (evt) {
          this.goToSlide(this.getChildIndex(evt.currentTarget));
        }.bind(_this4));
        link.addEventListener('keydown', function (evt) {
          if (evt.keyCode === 13) {
            this.goToSlide(this.getChildIndex(evt.currentTarget));
          }
        }.bind(_this4));
      });
      if (this.arrows.length) {
        this.arrows.forEach(function (arrow) {
          arrow.addEventListener('click', _this4.arrowClick.bind(_this4));
        });
      }
    }
  }, {
    key: "getChildIndex",
    value: function getChildIndex(target) {
      return parseInt(target.dataset.index);
    }
  }, {
    key: "childNavGoTo",
    value: function childNavGoTo(index) {
      var _this5 = this;
      this.childNavLinks.forEach(function (a) {
        a.blur();
        a.classList.remove(_this5.classes.isActive);
      });
      var el = this.childNavLinks[index];
      el.classList.add(this.classes.isActive);
      if (!this.args.childNavScroller) {
        return;
      }
      if (this.args.childVertical) {
        var elTop = el.offsetTop;
        this.args.childNavScroller.scrollTop = elTop - 100;
      } else {
        var elLeft = el.offsetLeft;
        this.args.childNavScroller.scrollLeft = elLeft - 100;
      }
    }
  }, {
    key: "arrowClick",
    value: function arrowClick(evt) {
      if (evt.currentTarget.classList.contains('product__thumb-arrow--prev')) {
        this.slideshow.previous();
      } else {
        this.slideshow.next();
      }
    }
  }]);
}();
var SlideshowSection = /*#__PURE__*/function (_HTMLThemeElement) {
  function SlideshowSection() {
    _classCallCheck(this, SlideshowSection);
    return _callSuper(this, SlideshowSection, arguments);
  }
  _inherits(SlideshowSection, _HTMLThemeElement);
  return _createClass(SlideshowSection, [{
    key: "connectedCallback",
    value: function connectedCallback() {
      _superPropGet(SlideshowSection, "connectedCallback", this, 3)([]);
      this.container = this.querySelector("#SlideshowWrapper-".concat(this.sectionId));
      this.slideshow = this.container.querySelector('#Slideshow-' + this.sectionId);
      this.initialIndex = 0;
      if (!this.slideshow) {
        return;
      }
      var sectionEl = this.container.parentElement;
      var sectionIndex = [].indexOf.call(sectionEl.parentElement.children, sectionEl);
      this.init();
    }
  }, {
    key: "init",
    value: function init() {
      var slides = this.slideshow.querySelectorAll('.slideshow__slide');
      this.slideshow.classList.remove('loading', 'loading--delayed');
      this.slideshow.classList.add('loaded');
      if (slides.length > 1) {
        var sliderArgs = {
          prevNextButtons: this.slideshow.hasAttribute('data-arrows'),
          pageDots: this.slideshow.hasAttribute('data-dots'),
          fade: !0,
          setGallerySize: !1,
          initialIndex: this.initialIndex,
          autoPlay: this.slideshow.dataset.autoplay === 'true' ? parseInt(this.slideshow.dataset.speed) : !1
        };
        this.flickity = new Slideshow(this.slideshow, sliderArgs);
      } else {
        slides[0].classList.add('is-selected');
      }
    }
  }, {
    key: "forceReload",
    value: function forceReload() {
      this.onSectionUnload();
      this.init();
    }
  }, {
    key: "onSectionUnload",
    value: function onSectionUnload() {
      if (this.flickity && typeof this.flickity.destroy === 'function') {
        this.flickity.destroy();
      }
    }
  }, {
    key: "onSectionSelect",
    value: function onSectionSelect() {
      this.forceReload();
    }
  }, {
    key: "onSectionReorder",
    value: function onSectionReorder() {
      this.forceReload();
    }
  }, {
    key: "onSectionDeselect",
    value: function onSectionDeselect() {
      if (this.flickity && typeof this.flickity.play === 'function') {
        this.flickity.play();
      }
    }
  }, {
    key: "onBlockSelect",
    value: function onBlockSelect(_ref) {
      var blockId = _ref.detail.blockId;
      this.forceReload();
      var slide = this.slideshow.querySelector('.slideshow__slide--' + blockId);
      var index = parseInt(slide.dataset.index);
      if (this.flickity && typeof this.flickity.pause === 'function') {
        this.flickity.goToSlide(index);
        this.flickity.pause();
      } else {
        this.initialIndex = index;
        setTimeout(function () {
          if (this.flickity && typeof this.flickity.pause === 'function') {
            this.flickity.pause();
          }
        }.bind(this), 1000);
      }
    }
  }, {
    key: "onBlockDeselect",
    value: function onBlockDeselect() {
      if (this.flickity && typeof this.flickity.play === 'function') {
        if (this.flickity.args.autoPlay) {
          this.flickity.play();
        }
      }
    }
  }]);
}(_archetype_themes_custom_elements_theme_element__WEBPACK_IMPORTED_MODULE_2__.HTMLThemeElement);
customElements.define('slideshow-section', SlideshowSection);



/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be in strict mode.
(() => {
"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (custom_elements_common)
/* harmony export */ });
/* harmony import */ var custom_elements_common_archetype_themes_custom_elements_base_media_WEBPACK_IMPORTED_MODULE_0_ = __webpack_require__(180);
/* harmony import */ var custom_elements_common_archetype_themes_custom_elements_disclosure_WEBPACK_IMPORTED_MODULE_1_ = __webpack_require__(453);
/* harmony import */ var custom_elements_common_archetype_themes_custom_elements_disclosure_WEBPACK_IMPORTED_MODULE_1_default = /*#__PURE__*/__webpack_require__.n(custom_elements_common_archetype_themes_custom_elements_disclosure_WEBPACK_IMPORTED_MODULE_1_);
/* harmony import */ var custom_elements_common_archetype_themes_custom_elements_header_search_WEBPACK_IMPORTED_MODULE_2_ = __webpack_require__(762);
/* harmony import */ var custom_elements_common_archetype_themes_custom_elements_product_recommendations_WEBPACK_IMPORTED_MODULE_3_ = __webpack_require__(484);
/* harmony import */ var custom_elements_common_archetype_themes_custom_elements_swatches_WEBPACK_IMPORTED_MODULE_4_ = __webpack_require__(136);
/* harmony import */ var custom_elements_common_archetype_themes_custom_elements_swatches_WEBPACK_IMPORTED_MODULE_4_default = /*#__PURE__*/__webpack_require__.n(custom_elements_common_archetype_themes_custom_elements_swatches_WEBPACK_IMPORTED_MODULE_4_);





// export {ThemeElement,HTMLThemeElement} from "@archetype-themes/custom-elements/theme-element"
/* harmony default export */ const custom_elements_common = (custom_elements_common_archetype_themes_custom_elements_base_media_WEBPACK_IMPORTED_MODULE_0_["default"]);
})();

/******/ 	return __webpack_exports__;
/******/ })()
;
});