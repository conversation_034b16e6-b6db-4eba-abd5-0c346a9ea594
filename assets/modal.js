import{trapFocus,removeTrapFocus}from '@archetype-themes/utils/a11y'
export default class Modals{constructor(id,name,options){this.id=id
this.modal=document.getElementById(id)
this.boundClose=this.close.bind(this)
if(!this.modal){return!1}
this.modalContent=this.modal.querySelector('.modal__inner')
const defaults={close:'.js-modal-close',open:'.js-modal-open-'+name,openClass:'modal--is-active',closingClass:'modal--is-closing',bodyOpenClass:['modal-open'],bodyOpenSolidClass:'modal-open--solid',bodyClosingClass:'modal-closing',closeOffContentClick:!0,trapFocus:!0}
this.config=Object.assign(defaults,options)
this.modalIsOpen=!1
this.focusOnOpen=this.config.focusIdOnOpen?document.getElementById(this.config.focusIdOnOpen):this.modal
this.isSolid=this.config.solid
this.init()}
init=function(){document.querySelectorAll(this.config.open).forEach((btn)=>{btn.setAttribute('aria-expanded','false')
btn.addEventListener('click',this.open.bind(this))})
this.modal.querySelectorAll(this.config.close).forEach((btn)=>{btn.addEventListener('click',this.close.bind(this))})
document.addEventListener('drawerOpen',function(){this.close()}.bind(this))}
open=function(evt){let externalCall=!1
if(this.modalIsOpen){return}
if(evt){evt.preventDefault()}else{externalCall=!0}
if(evt&&evt.stopPropagation){evt.stopPropagation()
this.activeSource=evt.currentTarget.setAttribute('aria-expanded','true')}
if(this.modalIsOpen&&!externalCall){this.close()}
this.modal.classList.add(this.config.openClass)
document.documentElement.classList.add(...this.config.bodyOpenClass)
if(this.isSolid){document.documentElement.classList.add(this.config.bodyOpenSolidClass)}
this.modalIsOpen=!0
setTimeout(()=>{if(!this.config.trapFocus)return
trapFocus(this.modal,{elementToFocus:this.focusOnOpen})},100)
document.dispatchEvent(new CustomEvent('modalOpen'))
document.dispatchEvent(new CustomEvent('modalOpen.'+this.id))
this.bindEvents()}
close=function(evt){if(!this.modalIsOpen){return}
if(evt){if(evt.target.closest('.js-modal-close')){}else if(evt.target.closest('.modal__inner')){return}}
document.activeElement.blur()
this.modal.classList.remove(this.config.openClass)
this.modal.classList.add(this.config.closingClass)
document.documentElement.classList.remove(...this.config.bodyOpenClass)
document.documentElement.classList.add(this.config.bodyClosingClass)
window.setTimeout(function(){document.documentElement.classList.remove(this.config.bodyClosingClass)
this.modal.classList.remove(this.config.closingClass)
if(this.activeSource&&this.activeSource.getAttribute('aria-expanded')){this.activeSource.setAttribute('aria-expanded','false').focus()}}.bind(this),500)
if(this.isSolid){document.documentElement.classList.remove(this.config.bodyOpenSolidClass)}
this.modalIsOpen=!1
if(this.config.trapFocus)removeTrapFocus()
document.dispatchEvent(new CustomEvent('modalClose.'+this.id))
this.unbindEvents()}
bindEvents(){window.addEventListener('keyup',function(evt){if(evt.keyCode===27){this.close()}}.bind(this))
if(this.config.closeOffContentClick){this.modal.addEventListener('click',this.boundClose)}}
unbindEvents(){if(this.config.closeOffContentClick){this.modal.removeEventListener('click',this.boundClose)}}}