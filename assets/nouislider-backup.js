var e="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:global;var t={};(function(e,r){r(t)})(0,(function(t){t.PipsMode=void 0;(function(e){e.Range="range";e.Steps="steps";e.Positions="positions";e.Count="count";e.Values="values"})(t.PipsMode||(t.PipsMode={}));t.PipsType=void 0;(function(e){e[e.None=-1]="None";e[e.NoValue=0]="NoValue";e[e.LargeValue=1]="LargeValue";e[e.SmallValue=2]="SmallValue"})(t.PipsType||(t.PipsType={}));function isValidFormatter(e){return isValidPartialFormatter(e)&&"function"===typeof e.from}function isValidPartialFormatter(e){return"object"===typeof e&&"function"===typeof e.to}function removeElement(e){e.parentElement.removeChild(e)}function isSet(e){return null!==e&&void 0!==e}function preventDefault(e){e.preventDefault()}function unique(t){return t.filter((function(t){return!(this||e)[t]&&((this||e)[t]=true)}),{})}function closest(e,t){return Math.round(e/t)*t}function offset(e,t){var r=e.getBoundingClientRect();var a=e.ownerDocument;var n=a.documentElement;var i=getPageOffset(a);/webkit.*Chrome.*Mobile/i.test(navigator.userAgent)&&(i.x=0);return t?r.top+i.y-n.clientTop:r.left+i.x-n.clientLeft}function isNumeric(e){return"number"===typeof e&&!isNaN(e)&&isFinite(e)}function addClassFor(e,t,r){if(r>0){addClass(e,t);setTimeout((function(){removeClass(e,t)}),r)}}function limit(e){return Math.max(Math.min(e,100),0)}function asArray(e){return Array.isArray(e)?e:[e]}function countDecimals(e){e=String(e);var t=e.split(".");return t.length>1?t[1].length:0}function addClass(e,t){e.classList&&!/\s/.test(t)?e.classList.add(t):e.className+=" "+t}function removeClass(e,t){e.classList&&!/\s/.test(t)?e.classList.remove(t):e.className=e.className.replace(new RegExp("(^|\\b)"+t.split(" ").join("|")+"(\\b|$)","gi")," ")}function hasClass(e,t){return e.classList?e.classList.contains(t):new RegExp("\\b"+t+"\\b").test(e.className)}function getPageOffset(e){var t=void 0!==window.pageXOffset;var r="CSS1Compat"===(e.compatMode||"");var a=t?window.pageXOffset:r?e.documentElement.scrollLeft:e.body.scrollLeft;var n=t?window.pageYOffset:r?e.documentElement.scrollTop:e.body.scrollTop;return{x:a,y:n}}function getActions(){return window.navigator.pointerEnabled?{start:"pointerdown",move:"pointermove",end:"pointerup"}:window.navigator.msPointerEnabled?{start:"MSPointerDown",move:"MSPointerMove",end:"MSPointerUp"}:{start:"mousedown touchstart",move:"mousemove touchmove",end:"mouseup touchend"}}function getSupportsPassive(){var e=false;try{var t=Object.defineProperty({},"passive",{get:function(){e=true}});window.addEventListener("test",null,t)}catch(e){}return e}function getSupportsTouchActionNone(){return window.CSS&&CSS.supports&&CSS.supports("touch-action","none")}function subRangeRatio(e,t){return 100/(t-e)}function fromPercentage(e,t,r){return 100*t/(e[r+1]-e[r])}function toPercentage(e,t){return fromPercentage(e,e[0]<0?t+Math.abs(e[0]):t-e[0],0)}function isPercentage(e,t){return t*(e[1]-e[0])/100+e[0]}function getJ(e,t){var r=1;while(e>=t[r])r+=1;return r}function toStepping(e,t,r){if(r>=e.slice(-1)[0])return 100;var a=getJ(r,e);var n=e[a-1];var i=e[a];var s=t[a-1];var o=t[a];return s+toPercentage([n,i],r)/subRangeRatio(s,o)}function fromStepping(e,t,r){if(r>=100)return e.slice(-1)[0];var a=getJ(r,t);var n=e[a-1];var i=e[a];var s=t[a-1];var o=t[a];return isPercentage([n,i],(r-s)*subRangeRatio(s,o))}function getStep(e,t,r,a){if(100===a)return a;var n=getJ(a,e);var i=e[n-1];var s=e[n];return r?a-i>(s-i)/2?s:i:t[n-1]?e[n-1]+closest(a-e[n-1],t[n-1]):a}var r=function(){function Spectrum(t,r,a){(this||e).xPct=[];(this||e).xVal=[];(this||e).xSteps=[];(this||e).xNumSteps=[];(this||e).xHighestCompleteStep=[];(this||e).xSteps=[a||false];(this||e).xNumSteps=[false];(this||e).snap=r;var n;var i=[];Object.keys(t).forEach((function(e){i.push([asArray(t[e]),e])}));i.sort((function(e,t){return e[0][0]-t[0][0]}));for(n=0;n<i.length;n++)this.handleEntryPoint(i[n][1],i[n][0]);(this||e).xNumSteps=(this||e).xSteps.slice(0);for(n=0;n<(this||e).xNumSteps.length;n++)this.handleStepPoint(n,(this||e).xNumSteps[n])}Spectrum.prototype.getDistance=function(t){var r;var a=[];for(r=0;r<(this||e).xNumSteps.length-1;r++){var n=(this||e).xNumSteps[r];if(n&&t/n%1!==0)throw new Error("noUiSlider: 'limit', 'margin' and 'padding' of "+(this||e).xPct[r]+"% range must be divisible by step.");a[r]=fromPercentage((this||e).xVal,t,r)}return a};Spectrum.prototype.getAbsoluteDistance=function(t,r,a){var n=0;if(t<(this||e).xPct[(this||e).xPct.length-1])while(t>(this||e).xPct[n+1])n++;else t===(this||e).xPct[(this||e).xPct.length-1]&&(n=(this||e).xPct.length-2);a||t!==(this||e).xPct[n+1]||n++;null===r&&(r=[]);var i;var s=1;var o=r[n];var l=0;var u=0;var c=0;var p=0;i=a?(t-(this||e).xPct[n])/((this||e).xPct[n+1]-(this||e).xPct[n]):((this||e).xPct[n+1]-t)/((this||e).xPct[n+1]-(this||e).xPct[n]);while(o>0){l=(this||e).xPct[n+1+p]-(this||e).xPct[n+p];if(r[n+p]*s+100-100*i>100){u=l*i;s=(o-100*i)/r[n+p];i=1}else{u=r[n+p]*l/100*s;s=0}if(a){c-=u;(this||e).xPct.length+p>=1&&p--}else{c+=u;(this||e).xPct.length-p>=1&&p++}o=r[n+p]*s}return t+c};Spectrum.prototype.toStepping=function(t){t=toStepping((this||e).xVal,(this||e).xPct,t);return t};Spectrum.prototype.fromStepping=function(t){return fromStepping((this||e).xVal,(this||e).xPct,t)};Spectrum.prototype.getStep=function(t){t=getStep((this||e).xPct,(this||e).xSteps,(this||e).snap,t);return t};Spectrum.prototype.getDefaultStep=function(t,r,a){var n=getJ(t,(this||e).xPct);(100===t||r&&t===(this||e).xPct[n-1])&&(n=Math.max(n-1,1));return((this||e).xVal[n]-(this||e).xVal[n-1])/a};Spectrum.prototype.getNearbySteps=function(t){var r=getJ(t,(this||e).xPct);return{stepBefore:{startValue:(this||e).xVal[r-2],step:(this||e).xNumSteps[r-2],highestStep:(this||e).xHighestCompleteStep[r-2]},thisStep:{startValue:(this||e).xVal[r-1],step:(this||e).xNumSteps[r-1],highestStep:(this||e).xHighestCompleteStep[r-1]},stepAfter:{startValue:(this||e).xVal[r],step:(this||e).xNumSteps[r],highestStep:(this||e).xHighestCompleteStep[r]}}};Spectrum.prototype.countStepDecimals=function(){var t=(this||e).xNumSteps.map(countDecimals);return Math.max.apply(null,t)};Spectrum.prototype.convert=function(e){return this.getStep(this.toStepping(e))};Spectrum.prototype.handleEntryPoint=function(t,r){var a;a="min"===t?0:"max"===t?100:parseFloat(t);if(!isNumeric(a)||!isNumeric(r[0]))throw new Error("noUiSlider: 'range' value isn't numeric.");(this||e).xPct.push(a);(this||e).xVal.push(r[0]);var n=Number(r[1]);a?(this||e).xSteps.push(!isNaN(n)&&n):isNaN(n)||((this||e).xSteps[0]=n);(this||e).xHighestCompleteStep.push(0)};Spectrum.prototype.handleStepPoint=function(t,r){if(r)if((this||e).xVal[t]!==(this||e).xVal[t+1]){(this||e).xSteps[t]=fromPercentage([(this||e).xVal[t],(this||e).xVal[t+1]],r,0)/subRangeRatio((this||e).xPct[t],(this||e).xPct[t+1]);var a=((this||e).xVal[t+1]-(this||e).xVal[t])/(this||e).xNumSteps[t];var n=Math.ceil(Number(a.toFixed(3))-1);var i=(this||e).xVal[t]+(this||e).xNumSteps[t]*n;(this||e).xHighestCompleteStep[t]=i}else(this||e).xSteps[t]=(this||e).xHighestCompleteStep[t]=(this||e).xVal[t]};return Spectrum}();var a={to:function(e){return void 0===e?"":e.toFixed(2)},from:Number};var n={target:"target",base:"base",origin:"origin",handle:"handle",handleLower:"handle-lower",handleUpper:"handle-upper",touchArea:"touch-area",horizontal:"horizontal",vertical:"vertical",background:"background",connect:"connect",connects:"connects",ltr:"ltr",rtl:"rtl",textDirectionLtr:"txt-dir-ltr",textDirectionRtl:"txt-dir-rtl",draggable:"draggable",drag:"state-drag",tap:"state-tap",active:"active",tooltip:"tooltip",pips:"pips",pipsHorizontal:"pips-horizontal",pipsVertical:"pips-vertical",marker:"marker",markerHorizontal:"marker-horizontal",markerVertical:"marker-vertical",markerNormal:"marker-normal",markerLarge:"marker-large",markerSub:"marker-sub",value:"value",valueHorizontal:"value-horizontal",valueVertical:"value-vertical",valueNormal:"value-normal",valueLarge:"value-large",valueSub:"value-sub"};var i={tooltips:".__tooltips",aria:".__aria"};function testStep(e,t){if(!isNumeric(t))throw new Error("noUiSlider: 'step' is not numeric.");e.singleStep=t}function testKeyboardPageMultiplier(e,t){if(!isNumeric(t))throw new Error("noUiSlider: 'keyboardPageMultiplier' is not numeric.");e.keyboardPageMultiplier=t}function testKeyboardDefaultStep(e,t){if(!isNumeric(t))throw new Error("noUiSlider: 'keyboardDefaultStep' is not numeric.");e.keyboardDefaultStep=t}function testRange(e,t){if("object"!==typeof t||Array.isArray(t))throw new Error("noUiSlider: 'range' is not an object.");if(void 0===t.min||void 0===t.max)throw new Error("noUiSlider: Missing 'min' or 'max' in 'range'.");if(t.min===t.max)throw new Error("noUiSlider: 'range' 'min' and 'max' cannot be equal.");e.spectrum=new r(t,e.snap||false,e.singleStep)}function testStart(e,t){t=asArray(t);if(!Array.isArray(t)||!t.length)throw new Error("noUiSlider: 'start' option is incorrect.");e.handles=t.length;e.start=t}function testSnap(e,t){if("boolean"!==typeof t)throw new Error("noUiSlider: 'snap' option must be a boolean.");e.snap=t}function testAnimate(e,t){if("boolean"!==typeof t)throw new Error("noUiSlider: 'animate' option must be a boolean.");e.animate=t}function testAnimationDuration(e,t){if("number"!==typeof t)throw new Error("noUiSlider: 'animationDuration' option must be a number.");e.animationDuration=t}function testConnect(e,t){var r=[false];var a;"lower"===t?t=[true,false]:"upper"===t&&(t=[false,true]);if(true===t||false===t){for(a=1;a<e.handles;a++)r.push(t);r.push(false)}else{if(!Array.isArray(t)||!t.length||t.length!==e.handles+1)throw new Error("noUiSlider: 'connect' option doesn't match handle count.");r=t}e.connect=r}function testOrientation(e,t){switch(t){case"horizontal":e.ort=0;break;case"vertical":e.ort=1;break;default:throw new Error("noUiSlider: 'orientation' option is invalid.")}}function testMargin(e,t){if(!isNumeric(t))throw new Error("noUiSlider: 'margin' option must be numeric.");0!==t&&(e.margin=e.spectrum.getDistance(t))}function testLimit(e,t){if(!isNumeric(t))throw new Error("noUiSlider: 'limit' option must be numeric.");e.limit=e.spectrum.getDistance(t);if(!e.limit||e.handles<2)throw new Error("noUiSlider: 'limit' option is only supported on linear sliders with 2 or more handles.")}function testPadding(e,t){var r;if(!isNumeric(t)&&!Array.isArray(t))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(Array.isArray(t)&&!(2===t.length||isNumeric(t[0])||isNumeric(t[1])))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(0!==t){Array.isArray(t)||(t=[t,t]);e.padding=[e.spectrum.getDistance(t[0]),e.spectrum.getDistance(t[1])];for(r=0;r<e.spectrum.xNumSteps.length-1;r++)if(e.padding[0][r]<0||e.padding[1][r]<0)throw new Error("noUiSlider: 'padding' option must be a positive number(s).");var a=t[0]+t[1];var n=e.spectrum.xVal[0];var i=e.spectrum.xVal[e.spectrum.xVal.length-1];if(a/(i-n)>1)throw new Error("noUiSlider: 'padding' option must not exceed 100% of the range.")}}function testDirection(e,t){switch(t){case"ltr":e.dir=0;break;case"rtl":e.dir=1;break;default:throw new Error("noUiSlider: 'direction' option was not recognized.")}}function testBehaviour(e,t){if("string"!==typeof t)throw new Error("noUiSlider: 'behaviour' must be a string containing options.");var r=t.indexOf("tap")>=0;var a=t.indexOf("drag")>=0;var n=t.indexOf("fixed")>=0;var i=t.indexOf("snap")>=0;var s=t.indexOf("hover")>=0;var o=t.indexOf("unconstrained")>=0;if(n){if(2!==e.handles)throw new Error("noUiSlider: 'fixed' behaviour must be used with 2 handles");testMargin(e,e.start[1]-e.start[0])}if(o&&(e.margin||e.limit))throw new Error("noUiSlider: 'unconstrained' behaviour cannot be used with margin or limit");e.events={tap:r||i,drag:a,fixed:n,snap:i,hover:s,unconstrained:o}}function testTooltips(e,t){if(false!==t)if(true===t||isValidPartialFormatter(t)){e.tooltips=[];for(var r=0;r<e.handles;r++)e.tooltips.push(t)}else{t=asArray(t);if(t.length!==e.handles)throw new Error("noUiSlider: must pass a formatter for all handles.");t.forEach((function(e){if("boolean"!==typeof e&&!isValidPartialFormatter(e))throw new Error("noUiSlider: 'tooltips' must be passed a formatter or 'false'.")}));e.tooltips=t}}function testAriaFormat(e,t){if(!isValidPartialFormatter(t))throw new Error("noUiSlider: 'ariaFormat' requires 'to' method.");e.ariaFormat=t}function testFormat(e,t){if(!isValidFormatter(t))throw new Error("noUiSlider: 'format' requires 'to' and 'from' methods.");e.format=t}function testKeyboardSupport(e,t){if("boolean"!==typeof t)throw new Error("noUiSlider: 'keyboardSupport' option must be a boolean.");e.keyboardSupport=t}function testDocumentElement(e,t){e.documentElement=t}function testCssPrefix(e,t){if("string"!==typeof t&&false!==t)throw new Error("noUiSlider: 'cssPrefix' must be a string or `false`.");e.cssPrefix=t}function testCssClasses(e,t){if("object"!==typeof t)throw new Error("noUiSlider: 'cssClasses' must be an object.");if("string"===typeof e.cssPrefix){e.cssClasses={};Object.keys(t).forEach((function(r){e.cssClasses[r]=e.cssPrefix+t[r]}))}else e.cssClasses=t}function testOptions(e){var t={margin:null,limit:null,padding:null,animate:true,animationDuration:300,ariaFormat:a,format:a};var r={step:{r:false,t:testStep},keyboardPageMultiplier:{r:false,t:testKeyboardPageMultiplier},keyboardDefaultStep:{r:false,t:testKeyboardDefaultStep},start:{r:true,t:testStart},connect:{r:true,t:testConnect},direction:{r:true,t:testDirection},snap:{r:false,t:testSnap},animate:{r:false,t:testAnimate},animationDuration:{r:false,t:testAnimationDuration},range:{r:true,t:testRange},orientation:{r:false,t:testOrientation},margin:{r:false,t:testMargin},limit:{r:false,t:testLimit},padding:{r:false,t:testPadding},behaviour:{r:true,t:testBehaviour},ariaFormat:{r:false,t:testAriaFormat},format:{r:false,t:testFormat},tooltips:{r:false,t:testTooltips},keyboardSupport:{r:true,t:testKeyboardSupport},documentElement:{r:false,t:testDocumentElement},cssPrefix:{r:true,t:testCssPrefix},cssClasses:{r:true,t:testCssClasses}};var i={connect:false,direction:"ltr",behaviour:"tap",orientation:"horizontal",keyboardSupport:true,cssPrefix:"noUi-",cssClasses:n,keyboardPageMultiplier:5,keyboardDefaultStep:10};e.format&&!e.ariaFormat&&(e.ariaFormat=e.format);Object.keys(r).forEach((function(a){if(isSet(e[a])||void 0!==i[a])r[a].t(t,isSet(e[a])?e[a]:i[a]);else if(r[a].r)throw new Error("noUiSlider: '"+a+"' is required.")}));t.pips=e.pips;var s=document.createElement("div");var o=void 0!==s.style.msTransform;var l=void 0!==s.style.transform;t.transformRule=l?"transform":o?"msTransform":"webkitTransform";var u=[["left","top"],["right","bottom"]];t.style=u[t.dir][t.ort];return t}function scope(e,r,a){var n=getActions();var s=getSupportsTouchActionNone();var o=s&&getSupportsPassive();var l=e;var u;var c;var p;var f;var d;var v=r.spectrum;var m=[];var h=[];var g=[];var S=0;var b={};var x=e.ownerDocument;var y=r.documentElement||x.documentElement;var E=x.body;var P="rtl"===x.dir||1===r.ort?0:100;function addNodeTo(e,t){var r=x.createElement("div");t&&addClass(r,t);e.appendChild(r);return r}function addOrigin(e,t){var a=addNodeTo(e,r.cssClasses.origin);var n=addNodeTo(a,r.cssClasses.handle);addNodeTo(n,r.cssClasses.touchArea);n.setAttribute("data-handle",String(t));if(r.keyboardSupport){n.setAttribute("tabindex","0");n.addEventListener("keydown",(function(e){return eventKeydown(e,t)}))}n.setAttribute("role","slider");n.setAttribute("aria-orientation",r.ort?"vertical":"horizontal");0===t?addClass(n,r.cssClasses.handleLower):t===r.handles-1&&addClass(n,r.cssClasses.handleUpper);return a}function addConnect(e,t){return!!t&&addNodeTo(e,r.cssClasses.connect)}function addElements(e,t){var a=addNodeTo(t,r.cssClasses.connects);c=[];p=[];p.push(addConnect(a,e[0]));for(var n=0;n<r.handles;n++){c.push(addOrigin(t,n));g[n]=n;p.push(addConnect(a,e[n+1]))}}function addSlider(e){addClass(e,r.cssClasses.target);0===r.dir?addClass(e,r.cssClasses.ltr):addClass(e,r.cssClasses.rtl);0===r.ort?addClass(e,r.cssClasses.horizontal):addClass(e,r.cssClasses.vertical);var t=getComputedStyle(e).direction;addClass(e,"rtl"===t?r.cssClasses.textDirectionRtl:r.cssClasses.textDirectionLtr);return addNodeTo(e,r.cssClasses.base)}function addTooltip(e,t){return!(!r.tooltips||!r.tooltips[t])&&addNodeTo(e.firstChild,r.cssClasses.tooltip)}function isSliderDisabled(){return l.hasAttribute("disabled")}function isHandleDisabled(e){var t=c[e];return t.hasAttribute("disabled")}function removeTooltips(){if(d){removeEvent("update"+i.tooltips);d.forEach((function(e){e&&removeElement(e)}));d=null}}function tooltips(){removeTooltips();d=c.map(addTooltip);bindEvent("update"+i.tooltips,(function(e,t,a){if(d&&r.tooltips&&false!==d[t]){var n=e[t];true!==r.tooltips[t]&&(n=r.tooltips[t].to(a[t]));d[t].innerHTML=n}}))}function aria(){removeEvent("update"+i.aria);bindEvent("update"+i.aria,(function(e,t,a,n,i){g.forEach((function(e){var t=c[e];var n=checkHandlePosition(h,e,0,true,true,true);var s=checkHandlePosition(h,e,100,true,true,true);var o=i[e];var l=String(r.ariaFormat.to(a[e]));n=v.fromStepping(n).toFixed(1);s=v.fromStepping(s).toFixed(1);o=v.fromStepping(o).toFixed(1);t.children[0].setAttribute("aria-valuemin",n);t.children[0].setAttribute("aria-valuemax",s);t.children[0].setAttribute("aria-valuenow",o);t.children[0].setAttribute("aria-valuetext",l)}))}))}function getGroup(e){if(e.mode===t.PipsMode.Range||e.mode===t.PipsMode.Steps)return v.xVal;if(e.mode===t.PipsMode.Count){if(e.values<2)throw new Error("noUiSlider: 'values' (>= 2) required for mode 'count'.");var r=e.values-1;var a=100/r;var n=[];while(r--)n[r]=r*a;n.push(100);return mapToRange(n,e.stepped)}return e.mode===t.PipsMode.Positions?mapToRange(e.values,e.stepped):e.mode===t.PipsMode.Values?e.stepped?e.values.map((function(e){return v.fromStepping(v.getStep(v.toStepping(e)))})):e.values:[]}function mapToRange(e,t){return e.map((function(e){return v.fromStepping(t?v.getStep(e):e)}))}function generateSpread(e){function safeIncrement(e,t){return Number((e+t).toFixed(7))}var r=getGroup(e);var a={};var n=v.xVal[0];var i=v.xVal[v.xVal.length-1];var s=false;var o=false;var l=0;r=unique(r.slice().sort((function(e,t){return e-t})));if(r[0]!==n){r.unshift(n);s=true}if(r[r.length-1]!==i){r.push(i);o=true}r.forEach((function(n,i){var u;var c;var p;var f=n;var d=r[i+1];var m;var h;var g;var S;var b;var x;var y;var E=e.mode===t.PipsMode.Steps;E&&(u=v.xNumSteps[i]);u||(u=d-f);void 0===d&&(d=f);u=Math.max(u,1e-7);for(c=f;c<=d;c=safeIncrement(c,u)){m=v.toStepping(c);h=m-l;b=h/(e.density||1);x=Math.round(b);y=h/x;for(p=1;p<=x;p+=1){g=l+p*y;a[g.toFixed(5)]=[v.fromStepping(g),0]}S=r.indexOf(c)>-1?t.PipsType.LargeValue:E?t.PipsType.SmallValue:t.PipsType.NoValue;!i&&s&&c!==d&&(S=0);c===d&&o||(a[m.toFixed(5)]=[c,S]);l=m}}));return a}function addMarking(e,a,n){var i,s;var o=x.createElement("div");var l=(i={},i[t.PipsType.None]="",i[t.PipsType.NoValue]=r.cssClasses.valueNormal,i[t.PipsType.LargeValue]=r.cssClasses.valueLarge,i[t.PipsType.SmallValue]=r.cssClasses.valueSub,i);var u=(s={},s[t.PipsType.None]="",s[t.PipsType.NoValue]=r.cssClasses.markerNormal,s[t.PipsType.LargeValue]=r.cssClasses.markerLarge,s[t.PipsType.SmallValue]=r.cssClasses.markerSub,s);var c=[r.cssClasses.valueHorizontal,r.cssClasses.valueVertical];var p=[r.cssClasses.markerHorizontal,r.cssClasses.markerVertical];addClass(o,r.cssClasses.pips);addClass(o,0===r.ort?r.cssClasses.pipsHorizontal:r.cssClasses.pipsVertical);function getClasses(e,t){var a=t===r.cssClasses.value;var n=a?c:p;var i=a?l:u;return t+" "+n[r.ort]+" "+i[e]}function addSpread(e,i,s){s=a?a(i,s):s;if(s!==t.PipsType.None){var l=addNodeTo(o,false);l.className=getClasses(s,r.cssClasses.marker);l.style[r.style]=e+"%";if(s>t.PipsType.NoValue){l=addNodeTo(o,false);l.className=getClasses(s,r.cssClasses.value);l.setAttribute("data-value",String(i));l.style[r.style]=e+"%";l.innerHTML=String(n.to(i))}}}Object.keys(e).forEach((function(t){addSpread(t,e[t][0],e[t][1])}));return o}function removePips(){if(f){removeElement(f);f=null}}function pips(e){removePips();var t=generateSpread(e);var r=e.filter;var a=e.format||{to:function(e){return String(Math.round(e))}};f=l.appendChild(addMarking(t,r,a));return f}function baseSize(){var e=u.getBoundingClientRect();var t="offset"+["Width","Height"][r.ort];return 0===r.ort?e.width||u[t]:e.height||u[t]}function attachEvent(e,t,a,i){var method=function(s){var u=fixEvent(s,i.pageOffset,i.target||t);if(!u)return false;if(isSliderDisabled()&&!i.doNotReject)return false;if(hasClass(l,r.cssClasses.tap)&&!i.doNotReject)return false;if(e===n.start&&void 0!==u.buttons&&u.buttons>1)return false;if(i.hover&&u.buttons)return false;o||u.preventDefault();u.calcPoint=u.points[r.ort];a(u,i)};var s=[];e.split(" ").forEach((function(e){t.addEventListener(e,method,!!o&&{passive:true});s.push([e,method])}));return s}function fixEvent(e,t,r){var a=0===e.type.indexOf("touch");var n=0===e.type.indexOf("mouse");var i=0===e.type.indexOf("pointer");var s=0;var o=0;0===e.type.indexOf("MSPointer")&&(i=true);if("mousedown"===e.type&&!e.buttons&&!e.touches)return false;if(a){var isTouchOnTarget=function(t){var a=t.target;return a===r||r.contains(a)||e.composed&&e.composedPath().shift()===r};if("touchstart"===e.type){var l=Array.prototype.filter.call(e.touches,isTouchOnTarget);if(l.length>1)return false;s=l[0].pageX;o=l[0].pageY}else{var u=Array.prototype.find.call(e.changedTouches,isTouchOnTarget);if(!u)return false;s=u.pageX;o=u.pageY}}t=t||getPageOffset(x);if(n||i){s=e.clientX+t.x;o=e.clientY+t.y}e.pageOffset=t;e.points=[s,o];e.cursor=n||i;return e}function calcPointToPercentage(e){var t=e-offset(u,r.ort);var a=100*t/baseSize();a=limit(a);return r.dir?100-a:a}function getClosestHandle(e){var t=100;var r=false;c.forEach((function(a,n){if(!isHandleDisabled(n)){var i=h[n];var s=Math.abs(i-e);var o=100===s&&100===t;var l=s<t;var u=s<=t&&e>i;if(l||u||o){r=n;t=s}}}));return r}function documentLeave(e,t){"mouseout"===e.type&&"HTML"===e.target.nodeName&&null===e.relatedTarget&&eventEnd(e,t)}function eventMove(e,t){if(-1===navigator.appVersion.indexOf("MSIE 9")&&0===e.buttons&&0!==t.buttonsProperty)return eventEnd(e,t);var a=(r.dir?-1:1)*(e.calcPoint-t.startCalcPoint);var n=100*a/t.baseSize;moveHandles(a>0,n,t.locations,t.handleNumbers,t.connect)}function eventEnd(e,t){if(t.handle){removeClass(t.handle,r.cssClasses.active);S-=1}t.listeners.forEach((function(e){y.removeEventListener(e[0],e[1])}));if(0===S){removeClass(l,r.cssClasses.drag);setZindex();if(e.cursor){E.style.cursor="";E.removeEventListener("selectstart",preventDefault)}}t.handleNumbers.forEach((function(e){fireEvent("change",e);fireEvent("set",e);fireEvent("end",e)}))}function eventStart(e,t){if(!t.handleNumbers.some(isHandleDisabled)){var a;if(1===t.handleNumbers.length){var i=c[t.handleNumbers[0]];a=i.children[0];S+=1;addClass(a,r.cssClasses.active)}e.stopPropagation();var s=[];var o=attachEvent(n.move,y,eventMove,{target:e.target,handle:a,connect:t.connect,listeners:s,startCalcPoint:e.calcPoint,baseSize:baseSize(),pageOffset:e.pageOffset,handleNumbers:t.handleNumbers,buttonsProperty:e.buttons,locations:h.slice()});var u=attachEvent(n.end,y,eventEnd,{target:e.target,handle:a,listeners:s,doNotReject:true,handleNumbers:t.handleNumbers});var p=attachEvent("mouseout",y,documentLeave,{target:e.target,handle:a,listeners:s,doNotReject:true,handleNumbers:t.handleNumbers});s.push.apply(s,o.concat(u,p));if(e.cursor){E.style.cursor=getComputedStyle(e.target).cursor;c.length>1&&addClass(l,r.cssClasses.drag);E.addEventListener("selectstart",preventDefault,false)}t.handleNumbers.forEach((function(e){fireEvent("start",e)}))}}function eventTap(e){e.stopPropagation();var t=calcPointToPercentage(e.calcPoint);var a=getClosestHandle(t);if(false!==a){r.events.snap||addClassFor(l,r.cssClasses.tap,r.animationDuration);setHandle(a,t,true,true);setZindex();fireEvent("slide",a,true);fireEvent("update",a,true);fireEvent("change",a,true);fireEvent("set",a,true);r.events.snap&&eventStart(e,{handleNumbers:[a]})}}function eventHover(e){var t=calcPointToPercentage(e.calcPoint);var r=v.getStep(t);var a=v.fromStepping(r);Object.keys(b).forEach((function(e){"hover"===e.split(".")[0]&&b[e].forEach((function(e){e.call(C,a)}))}))}function eventKeydown(e,t){if(isSliderDisabled()||isHandleDisabled(t))return false;var a=["Left","Right"];var n=["Down","Up"];var i=["PageDown","PageUp"];var s=["Home","End"];if(r.dir&&!r.ort)a.reverse();else if(r.ort&&!r.dir){n.reverse();i.reverse()}var o=e.key.replace("Arrow","");var l=o===i[0];var u=o===i[1];var c=o===n[0]||o===a[0]||l;var p=o===n[1]||o===a[1]||u;var f=o===s[0];var d=o===s[1];if(!c&&!p&&!f&&!d)return true;e.preventDefault();var g;if(p||c){var S=r.keyboardPageMultiplier;var b=c?0:1;var x=getNextStepsForHandle(t);var y=x[b];if(null===y)return false;false===y&&(y=v.getDefaultStep(h[t],c,r.keyboardDefaultStep));(u||l)&&(y*=S);y=Math.max(y,1e-7);y*=c?-1:1;g=m[t]+y}else g=d?r.spectrum.xVal[r.spectrum.xVal.length-1]:r.spectrum.xVal[0];setHandle(t,v.toStepping(g),true,true);fireEvent("slide",t);fireEvent("update",t);fireEvent("change",t);fireEvent("set",t);return false}function bindSliderEvents(e){e.fixed||c.forEach((function(e,t){attachEvent(n.start,e.children[0],eventStart,{handleNumbers:[t]})}));e.tap&&attachEvent(n.start,u,eventTap,{});e.hover&&attachEvent(n.move,u,eventHover,{hover:true});e.drag&&p.forEach((function(t,a){if(false!==t&&0!==a&&a!==p.length-1){var i=c[a-1];var s=c[a];var o=[t];addClass(t,r.cssClasses.draggable);if(e.fixed){o.push(i.children[0]);o.push(s.children[0])}o.forEach((function(e){attachEvent(n.start,e,eventStart,{handles:[i,s],handleNumbers:[a-1,a],connect:t})}))}}))}function bindEvent(e,t){b[e]=b[e]||[];b[e].push(t);"update"===e.split(".")[0]&&c.forEach((function(e,t){fireEvent("update",t)}))}function isInternalNamespace(e){return e===i.aria||e===i.tooltips}function removeEvent(e){var t=e&&e.split(".")[0];var r=t?e.substring(t.length):e;Object.keys(b).forEach((function(e){var a=e.split(".")[0];var n=e.substring(a.length);t&&t!==a||r&&r!==n||isInternalNamespace(n)&&r!==n||delete b[e]}))}function fireEvent(e,t,a){Object.keys(b).forEach((function(n){var i=n.split(".")[0];e===i&&b[n].forEach((function(e){e.call(C,m.map(r.format.to),t,m.slice(),a||false,h.slice(),C)}))}))}function checkHandlePosition(e,t,a,n,i,s){var o;if(c.length>1&&!r.events.unconstrained){if(n&&t>0){o=v.getAbsoluteDistance(e[t-1],r.margin,false);a=Math.max(a,o)}if(i&&t<c.length-1){o=v.getAbsoluteDistance(e[t+1],r.margin,true);a=Math.min(a,o)}}if(c.length>1&&r.limit){if(n&&t>0){o=v.getAbsoluteDistance(e[t-1],r.limit,false);a=Math.min(a,o)}if(i&&t<c.length-1){o=v.getAbsoluteDistance(e[t+1],r.limit,true);a=Math.max(a,o)}}if(r.padding){if(0===t){o=v.getAbsoluteDistance(0,r.padding[0],false);a=Math.max(a,o)}if(t===c.length-1){o=v.getAbsoluteDistance(100,r.padding[1],true);a=Math.min(a,o)}}a=v.getStep(a);a=limit(a);return!(a===e[t]&&!s)&&a}function inRuleOrder(e,t){var a=r.ort;return(a?t:e)+", "+(a?e:t)}function moveHandles(e,t,r,a,n){var i=r.slice();var s=a[0];var o=[!e,e];var l=[e,!e];a=a.slice();e&&a.reverse();a.length>1?a.forEach((function(e,r){var a=checkHandlePosition(i,e,i[e]+t,o[r],l[r],false);if(false===a)t=0;else{t=a-i[e];i[e]=a}})):o=l=[true];var u=false;a.forEach((function(e,a){u=setHandle(e,r[e]+t,o[a],l[a])||u}));if(u){a.forEach((function(e){fireEvent("update",e);fireEvent("slide",e)}));void 0!=n&&fireEvent("drag",s)}}function transformDirection(e,t){return r.dir?100-e-t:e}function updateHandlePosition(e,t){h[e]=t;m[e]=v.fromStepping(t);var a=10*(transformDirection(t,0)-P);var n="translate("+inRuleOrder(a+"%","0")+")";c[e].style[r.transformRule]=n;updateConnect(e);updateConnect(e+1)}function setZindex(){g.forEach((function(e){var t=h[e]>50?-1:1;var r=3+(c.length+t*e);c[e].style.zIndex=String(r)}))}function setHandle(e,t,r,a,n){n||(t=checkHandlePosition(h,e,t,r,a,false));if(false===t)return false;updateHandlePosition(e,t);return true}function updateConnect(e){if(p[e]){var t=0;var a=100;0!==e&&(t=h[e-1]);e!==p.length-1&&(a=h[e]);var n=a-t;var i="translate("+inRuleOrder(transformDirection(t,n)+"%","0")+")";var s="scale("+inRuleOrder(n/100,"1")+")";p[e].style[r.transformRule]=i+" "+s}}function resolveToValue(e,t){if(null===e||false===e||void 0===e)return h[t];"number"===typeof e&&(e=String(e));e=r.format.from(e);false!==e&&(e=v.toStepping(e));return false===e||isNaN(e)?h[t]:e}function valueSet(e,t,a){var n=asArray(e);var i=void 0===h[0];t=void 0===t||t;r.animate&&!i&&addClassFor(l,r.cssClasses.tap,r.animationDuration);g.forEach((function(e){setHandle(e,resolveToValue(n[e],e),true,false,a)}));var s=1===g.length?0:1;for(;s<g.length;++s)g.forEach((function(e){setHandle(e,h[e],true,true,a)}));setZindex();g.forEach((function(e){fireEvent("update",e);null!==n[e]&&t&&fireEvent("set",e)}))}function valueReset(e){valueSet(r.start,e)}function valueSetHandle(e,t,r,a){e=Number(e);if(!(e>=0&&e<g.length))throw new Error("noUiSlider: invalid handle number, got: "+e);setHandle(e,resolveToValue(t,e),true,true,a);fireEvent("update",e);r&&fireEvent("set",e)}function valueGet(e){void 0===e&&(e=false);if(e)return 1===m.length?m[0]:m.slice(0);var t=m.map(r.format.to);return 1===t.length?t[0]:t}function destroy(){removeEvent(i.aria);removeEvent(i.tooltips);Object.keys(r.cssClasses).forEach((function(e){removeClass(l,r.cssClasses[e])}));while(l.firstChild)l.removeChild(l.firstChild);delete l.noUiSlider}function getNextStepsForHandle(e){var t=h[e];var a=v.getNearbySteps(t);var n=m[e];var i=a.thisStep.step;var s=null;if(r.snap)return[n-a.stepBefore.startValue||null,a.stepAfter.startValue-n||null];false!==i&&n+i>a.stepAfter.startValue&&(i=a.stepAfter.startValue-n);s=n>a.thisStep.startValue?a.thisStep.step:false!==a.stepBefore.step&&n-a.stepBefore.highestStep;100===t?i=null:0===t&&(s=null);var o=v.countStepDecimals();null!==i&&false!==i&&(i=Number(i.toFixed(o)));null!==s&&false!==s&&(s=Number(s.toFixed(o)));return[s,i]}function getNextSteps(){return g.map(getNextStepsForHandle)}function updateOptions(e,t){var n=valueGet();var i=["margin","limit","padding","range","animate","snap","step","format","pips","tooltips"];i.forEach((function(t){void 0!==e[t]&&(a[t]=e[t])}));var s=testOptions(a);i.forEach((function(t){void 0!==e[t]&&(r[t]=s[t])}));v=s.spectrum;r.margin=s.margin;r.limit=s.limit;r.padding=s.padding;r.pips?pips(r.pips):removePips();r.tooltips?tooltips():removeTooltips();h=[];valueSet(isSet(e.start)?e.start:n,t)}function setupSlider(){u=addSlider(l);addElements(r.connect,u);bindSliderEvents(r.events);valueSet(r.start);r.pips&&pips(r.pips);r.tooltips&&tooltips();aria()}setupSlider();var C={destroy:destroy,steps:getNextSteps,on:bindEvent,off:removeEvent,get:valueGet,set:valueSet,setHandle:valueSetHandle,reset:valueReset,__moveHandles:function(e,t,r){moveHandles(e,t,h,r)},options:a,updateOptions:updateOptions,target:l,removePips:removePips,removeTooltips:removeTooltips,getTooltips:function(){return d},getOrigins:function(){return c},pips:pips};return C}function initialize(e,t){if(!e||!e.nodeName)throw new Error("noUiSlider: create requires a single element, got: "+e);if(e.noUiSlider)throw new Error("noUiSlider: Slider was already initialized.");var r=testOptions(t);var a=scope(e,r,t);e.noUiSlider=a;return a}var s={__spectrum:r,cssClasses:n,create:initialize};t.create=initialize;t.cssClasses=n;t.default=s;Object.defineProperty(t,"__esModule",{value:true})}));const r=t.PipsMode,a=t.PipsType,n=t.create,i=t.cssClasses,s=t.__esModule;export default t;export{r as PipsMode,a as PipsType,s as __esModule,n as create,i as cssClasses};

//# sourceMappingURL=nouislider.js.map