import{EVENTS}from '@archetype-themes/utils/events'
let hasLoadedBefore=!1
class CollectionHeader extends HTMLElement{constructor(){super()
this.overlayHeader=!1
this.heroImageContainer=this.querySelector('.collection-hero')
this.handleOverlayHeaderChange=this.handleOverlayHeaderChange.bind(this)}
connectedCallback(){this.abortController=new AbortController()
document.addEventListener(EVENTS.overlayHeaderChange,this.handleOverlayHeaderChange,{signal:this.abortController.signal})
if(this.heroImageContainer){if(hasLoadedBefore){this.checkIfNeedReload()}
this.heroImageContainer.classList.remove('loading','loading--delayed')
this.heroImageContainer.classList.add('loaded')}else if(this.overlayHeader){this.dispatchEvent(new CustomEvent(EVENTS.headerOverlayDisable),{bubbles:!0})}
hasLoadedBefore=!0}
disconnectedCallback(){this.abortController.abort()}
handleOverlayHeaderChange(event){this.overlayHeader=event.detail.overlayHeader
if(!this.overlayHeader&&!this.heroImageContainer){this.dispatchEvent(new CustomEvent(EVENTS.headerOverlayDisable),{bubbles:!0})}}
checkIfNeedReload(){if(!Shopify.designMode){return}
if(this.overlayHeader){const header=document.querySelector('.header-wrapper')
if(!header.classList.contains('header-wrapper--overlay')){location.reload()}}}}
customElements.define('section-collection-header',CollectionHeader)