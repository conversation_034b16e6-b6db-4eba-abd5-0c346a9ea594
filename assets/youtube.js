import{loadScript}from '@archetype-themes/utils/resource-loader'
import{EVENTS,publish,subscribe}from '@archetype-themes/utils/pubsub'
window.onYouTubeIframeAPIReady=function(){publish(EVENTS.youtubeReady)}
const classes={loading:'loading',loaded:'loaded',interactable:'video-interactable'}
const defaults={width:1280,height:720,playerVars:{autohide:0,autoplay:1,cc_load_policy:0,controls:0,fs:0,iv_load_policy:3,modestbranding:1,playsinline:1,rel:0}}
export default class YouTube{constructor(divId,options){this.divId=divId
this.iframe=null
this.attemptedToPlay=!1
defaults.events={onReady:this.onVideoPlayerReady.bind(this),onStateChange:this.onVideoStateChange.bind(this)}
this.options=Object.assign({},defaults,options)
if(this.options){if(this.options.videoParent){this.parent=document.getElementById(this.divId).closest(this.options.videoParent)}
if(!this.options.autoplay){this.options.playerVars.autoplay=this.options.autoplay}
if(this.options.style==='sound'){this.options.playerVars.controls=1
this.options.playerVars.autoplay=0}}
this.setAsLoading()
this.checkYouTubeReady()}
async checkYouTubeReady(){if(window.YT&&window.YT.Player){this.init()}else{await loadScript('https://www.youtube.com/iframe_api')
this.youtubeReadyUnsubscriber=subscribe(EVENTS.youtubeReady,()=>{this.init()
this.youtubeReadyUnsubscriber()})}}
init(){this.videoPlayer=new YT.Player(this.divId,this.options)}
onVideoPlayerReady(evt){this.iframe=document.getElementById(this.divId)
this.iframe.setAttribute('tabindex','-1')
if(this.options.style!=='sound'){evt.target.mute()}
var observer=new IntersectionObserver((entries,observer)=>{entries.forEach((entry)=>{if(entry.isIntersecting){this.play()}else{this.pause()}})},{rootMargin:'0px 0px 50px 0px'})
observer.observe(this.iframe)}
onVideoStateChange(evt){switch(evt.data){case-1:if(this.attemptedToPlay){this.setAsLoaded()
this.enableInteraction()}
break
case 0:this.play(evt)
break
case 1:this.setAsLoaded()
break
case 3:this.attemptedToPlay=!0
break}}
setAsLoading(){if(!this.parent)return
this.parent.classList.add(classes.loading)}
setAsLoaded(){if(!this.parent)return
this.parent.classList.remove(classes.loading)
this.parent.classList.add(classes.loaded)}
enableInteraction(){if(!this.parent)return
this.parent.classList.add(classes.interactable)}
play(){if(this.videoPlayer&&typeof this.videoPlayer.playVideo==='function'){this.videoPlayer.playVideo()}}
pause(){if(this.videoPlayer&&typeof this.videoPlayer.pauseVideo==='function'){this.videoPlayer.pauseVideo()}}
destroy(){if(this.videoPlayer&&typeof this.videoPlayer.destroy==='function'){this.videoPlayer.destroy()}
this.youtubeReadyUnsubscriber?.()}}