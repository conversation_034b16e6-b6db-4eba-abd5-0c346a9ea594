import '@archetype-themes/scripts/config'
import '@archetype-themes/scripts/modules/collection-sidebar'
import '@archetype-themes/scripts/helpers/ajax-renderer'
import '@archetype-themes/scripts/modules/cart-api'
import{debounce}from '@archetype-themes/scripts/helpers/utils'
import '@archetype-themes/scripts/helpers/delegate'
import{init as collapsiblesInit}from '@archetype-themes/scripts/modules/collapsibles'
class Collection extends HTMLElement{constructor(){super()
this.isAnimating=!1
this.selectors={sortSelect:'#SortBy',sortBtn:'.filter-sort',viewChange:'.grid-view-btn',productGrid:'.product-grid',collectionGrid:'.collection-grid__wrapper',sidebar:'#CollectionSidebar',activeTagList:'.tag-list--active-tags',tags:'.tag-list input',activeTags:'.tag-list a',tagsForm:'.filter-form',filterBar:'.collection-filter',priceRange:'.price-range',trigger:'.collapsible-trigger',filters:'.filter-wrapper',sidebarWrapper:'#CollectionSidebarFilterWrap',inlineWrapper:'#CollectionInlineFilterWrap'}
this.config={mobileFiltersInPlace:!1}
this.classes={activeTag:'tag--active',removeTagParent:'tag--remove',collapsibleContent:'collapsible-content',isOpen:'is-open'}
this.container=this
this.containerId=this.container.id
this.sectionId=this.container.getAttribute('data-section-id')
this.namespace='.collection-'+this.sectionId
this.isCollectionTemplate=this.container.dataset.collectionTemplate
this.ajaxRenderer=new theme.AjaxRenderer({sections:[{sectionId:this.sectionId,nodeId:'CollectionAjaxContent'}],onReplace:this.onReplaceAjaxContent.bind(this)})
document.dispatchEvent(new CustomEvent('collection-component:loaded',{detail:{sectionId:this.sectionId}}))
this.init(this.container)}
init(){this.config.mobileFiltersInPlace=!1
if(!this.container){this.container=document.getElementById(this.containerId)}
if(this.isCollectionTemplate){this.cloneFiltersOnMobile()
this.initSort()
this.initFilters()
this.initPriceRange()
this.initGridOptions()
this.sidebar=new theme.CollectionSidebar()}}
initSort(){this.queryParams=new URLSearchParams(window.location.search)
this.sortSelect=document.querySelector(this.selectors.sortSelect)
this.sortBtns=document.querySelectorAll(this.selectors.sortBtn)
if(this.sortSelect){this.defaultSort=this.getDefaultSortValue()
this.sortSelect.on('change'+this.namespace,()=>{this.onSortChange()})}
if(this.sortBtns.length){this.sortBtns.forEach((btn)=>{btn.addEventListener('click',function(){document.dispatchEvent(new Event('filter:selected'))
const sortValue=btn.dataset.value
this.onSortChange(sortValue)}.bind(this))})}}
getSortValue(){return this.sortSelect.value||this.defaultSort}
getDefaultSortValue(){return this.sortSelect.getAttribute('data-default-sortby')}
onSortChange(sortValue=null){this.queryParams=new URLSearchParams(window.location.search)
if(sortValue){this.queryParams.set('sort_by',sortValue)}else{this.queryParams.set('sort_by',this.getSortValue())}
this.queryParams.delete('page')
window.location.search=this.queryParams.toString()}
initGridOptions(){var grid=this.container.querySelector(this.selectors.productGrid)
var viewBtns=this.container.querySelectorAll(this.selectors.viewChange)
this.container.querySelectorAll(this.selectors.viewChange).forEach((btn)=>{btn.addEventListener('click',function(){viewBtns.forEach((el)=>{el.classList.remove('is-active')})
btn.classList.add('is-active')
var newView=btn.dataset.view
grid.dataset.view=newView
theme.cart.updateAttribute('product_view',newView)
window.dispatchEvent(new Event('resize'))})})}
initFilters(){var filterBar=document.querySelectorAll(this.selectors.filterBar)
if(!filterBar.length){return}
document.addEventListener('matchSmall',this.cloneFiltersOnMobile.bind(this))
this.bindBackButton()
if(theme.config.stickyHeader){this.setFilterStickyPosition()
document.addEventListener('headerStickyChange',debounce(500,this.setFilterStickyPosition).bind(this))
window.on('resize',debounce(500,this.setFilterStickyPosition).bind(this))}
document.querySelectorAll(this.selectors.activeTags).forEach((tag)=>{tag.addEventListener('click',this.onTagClick.bind(this))})
document.querySelectorAll(this.selectors.tagsForm).forEach((form)=>{form.addEventListener('input',this.onFormSubmit.bind(this))})}
initPriceRange(){document.addEventListener('price-range:change',this.onPriceRangeChange.bind(this),{once:!0})}
onPriceRangeChange(){this.renderFromFormData(event.detail)}
cloneFiltersOnMobile(){if(this.config.mobileFiltersInPlace){return}
var sidebarWrapper=document.querySelector(this.selectors.sidebarWrapper)
if(!sidebarWrapper){return}
var filters=sidebarWrapper.querySelector(this.selectors.filters).cloneNode(!0)
var inlineWrapper=document.querySelector(this.selectors.inlineWrapper)
inlineWrapper.innerHTML=''
inlineWrapper.append(theme.filtersPrime??filters)
theme.filtersPrime=null
collapsiblesInit(inlineWrapper)
this.config.mobileFiltersInPlace=!0}
renderActiveTag(parent,el){const textEl=parent.querySelector('.tag__text')
if(parent.classList.contains(this.classes.activeTag)){parent.classList.remove(this.classes.activeTag)}else{parent.classList.add(this.classes.activeTag)
if(el.closest('li').classList.contains(this.classes.removeTagParent)){parent.remove()}else{document.querySelectorAll(this.selectors.activeTagList).forEach((list)=>{const newTag=document.createElement('li')
const newTagLink=document.createElement('a')
newTag.classList.add('tag','tag--remove')
newTagLink.classList.add('btn','btn--small')
newTagLink.innerText=textEl.innerText
newTag.appendChild(newTagLink)
list.appendChild(newTag)})}}}
onTagClick(evt){const el=evt.currentTarget
document.dispatchEvent(new Event('filter:selected'))
if(el.classList.contains('no-ajax')){return}
evt.preventDefault()
if(this.isAnimating){return}
this.isAnimating=!0
const parent=el.parentNode
const newUrl=new URL(el.href)
this.renderActiveTag(parent,el)
this.updateScroll(!0)
this.startLoading()
this.renderCollectionPage(newUrl.searchParams)}
onFormSubmit(evt){const el=evt.target
document.dispatchEvent(new Event('filter:selected'))
if(el.classList.contains('no-ajax')){return}
evt.preventDefault()
if(this.isAnimating){return}
this.isAnimating=!0
const parent=el.closest('li')
const formEl=el.closest('form')
const formData=new FormData(formEl)
this.renderActiveTag(parent,el)
this.updateScroll(!0)
this.startLoading()
this.renderFromFormData(formData)}
onReplaceAjaxContent(newDom,section){const openCollapsibleIds=this.fetchOpenCollasibleFilters()
openCollapsibleIds.forEach((selector)=>{newDom.querySelectorAll(`[data-collapsible-id=${selector}]`).forEach(this.openCollapsible.bind(this))})
var newContentEl=newDom.getElementById(section.nodeId)
if(!newContentEl){return}
document.getElementById(section.nodeId).innerHTML=newContentEl.innerHTML
var page=document.getElementById(section.nodeId)
var countEl=page.querySelector('.collection-filter__item--count')
if(countEl){var count=countEl.innerText
document.querySelectorAll('[data-collection-count]').forEach((el)=>{el.innerText=count})}}
renderFromFormData(formData){const searchParams=new URLSearchParams(formData)
this.renderCollectionPage(searchParams)}
renderCollectionPage(searchParams,updateURLHash=!0){this.ajaxRenderer.renderPage(window.location.pathname,searchParams,updateURLHash).then(()=>{this.init(this.container)
this.updateScroll(!1)
collapsiblesInit()
document.dispatchEvent(new CustomEvent('collection:reloaded'))
this.isAnimating=!1})}
updateScroll(animate){var scrollTo=document.getElementById('CollectionAjaxContent').offsetTop
if(theme.config.stickyHeader){scrollTo=scrollTo-document.querySelector('#SiteHeader').offsetHeight}
if(!theme.config.bpSmall){scrollTo-=10}
if(animate){window.scrollTo({top:scrollTo,behavior:'smooth'})}else{window.scrollTo({top:scrollTo})}}
bindBackButton(){window.off('popstate'+this.namespace)
window.on('popstate'+this.namespace,function(state){if(state){const newUrl=new URL(window.location.href)
this.renderCollectionPage(newUrl.searchParams,!1)}}.bind(this))}
fetchOpenCollasibleFilters(){const openDesktopCollapsible=Array.from(document.querySelectorAll(`${this.selectors.sidebar} ${this.selectors.trigger}.${this.classes.isOpen}`))
const openMobileCollapsible=Array.from(document.querySelectorAll(`${this.selectors.inlineWrapper} ${this.selectors.trigger}.${this.classes.isOpen}`))
return[...openDesktopCollapsible,...openMobileCollapsible].map((trigger)=>trigger.dataset.collapsibleId)}
openCollapsible(el){if(el.classList.contains(this.classes.collapsibleContent)){el.style.height='auto'}
el.classList.add(this.classes.isOpen)}
setFilterStickyPosition(){var headerHeight=document.querySelector('.site-header').offsetHeight-1
document.querySelector(this.selectors.filterBar).style.top=headerHeight+'px'
var stickySidebar=document.querySelector('.grid__item--sidebar')
if(stickySidebar){stickySidebar.style.top=headerHeight+30+'px'}}
startLoading(){document.querySelector(this.selectors.collectionGrid).classList.add('unload')}
forceReload(){this.init(this.container)}}
customElements.define('collection-template',Collection)