import{debounce}from '@archetype-themes/utils/utils'
class ToolTipTrigger extends HTMLElement{constructor(){super()
this.el=this
this.toolTipContent=this.querySelector('[data-tool-tip-trigger-content]')
this.trigger=this.dataset.toolTip.includes('Quick')?this.el.closest('[data-product-grid-item]'):this.el
this.init()}
init(){const toolTipOpen=new CustomEvent('tooltip:open',{detail:{context:this.dataset.toolTip,content:this.toolTipContent?.innerHTML,tool_tip_classes:this.dataset.toolTipClasses},bubbles:!0})
const toolTipInteract=new CustomEvent('tooltip:interact',{detail:{context:this.dataset.toolTip,content:this.toolTipContent?.innerHTML,tool_tip_classes:this.dataset.toolTipClasses},bubbles:!0})
const debouncedMouseOverHandler=debounce(500,(e)=>{e.stopPropagation()
this.dispatchEvent(toolTipInteract)},!0)
const debouncedFocusInHandler=debounce(500,(e)=>{e.stopPropagation()
this.dispatchEvent(toolTipInteract)})
this.trigger.addEventListener('mouseover',debouncedMouseOverHandler)
this.trigger.addEventListener('focusin',debouncedFocusInHandler)
this.el.addEventListener('click',(e)=>{e.stopPropagation()
this.dispatchEvent(toolTipOpen)})}}
customElements.define('tool-tip-trigger',ToolTipTrigger)