;(function () {
  const e = typeof window !== 'undefined'
  const t = typeof document !== 'undefined'
  const noop = () => {}
  const r = t ? document.querySelector('script[type=esms-options]') : void 0
  const s = r ? JSON.parse(r.innerHTML) : {}
  Object.assign(s, self.esmsInitOptions || {})
  let n = !t || !!s.shimMode
  const a = globalHook(n && s.onimport)
  const i = globalHook(n && s.resolve)
  let c = s.fetch ? globalHook(s.fetch) : fetch
  const f = s.meta ? globalHook(n && s.meta) : noop
  const ne = s.mapOverrides
  let oe = s.nonce
  if (!oe && t) {
    const e = document.querySelector('script[nonce]')
    e && (oe = e.nonce || e.getAttribute('nonce'))
  }
  const ce = globalHook(s.onerror || noop)
  const le = s.onpolyfill
    ? globalHook(s.onpolyfill)
    : () => {
        console.log('%c^^ Module TypeError above is polyfilled and can be ignored ^^', 'font-weight:900;color:#391')
      }
  const { revokeBlobURLs: ue, noLoadEventRetriggers: de, enforceIntegrity: pe } = s
  function globalHook(e) {
    return typeof e === 'string' ? self[e] : e
  }
  const he = Array.isArray(s.polyfillEnable) ? s.polyfillEnable : []
  const me = he.includes('css-modules')
  const be = he.includes('json-modules')
  const ke = !navigator.userAgentData && !!navigator.userAgent.match(/Edge\/\d+\.\d+/)
  const we = t
    ? document.baseURI
    : `${location.protocol}//${location.host}${location.pathname.includes('/') ? location.pathname.slice(0, location.pathname.lastIndexOf('/') + 1) : location.pathname}`
  const createBlob = (e, t = 'text/javascript') => URL.createObjectURL(new Blob([e], { type: t }))
  let { skip: ge } = s
  if (Array.isArray(ge)) {
    const e = ge.map((e) => new URL(e, we).href)
    ge = (t) => e.some((e) => (e[e.length - 1] === '/' && t.startsWith(e)) || t === e)
  } else if (typeof ge === 'string') {
    const e = new RegExp(ge)
    ge = (t) => e.test(t)
  } else ge instanceof RegExp && (ge = (e) => ge.test(e))
  const eoop = (e) =>
    setTimeout(() => {
      throw e
    })
  const throwError = (t) => {
    ;(self.reportError || (e && window.safari && console.error) || eoop)(t), void ce(t)
  }
  function fromParent(e) {
    return e ? ` imported from ${e}` : ''
  }
  let ve = false
  function setImportMapSrcOrLazy() {
    ve = true
  }
  if (!n)
    if (
      document.querySelectorAll('script[type=module-shim],script[type=importmap-shim],link[rel=modulepreload-shim]')
        .length
    )
      n = true
    else {
      let e = false
      for (const t of document.querySelectorAll('script[type=module],script[type=importmap]'))
        if (e) {
          if (t.type === 'importmap' && e) {
            ve = true
            break
          }
        } else t.type !== 'module' || t.ep || (e = true)
    }
  const ye = /\\/g
  function asURL(e) {
    try {
      if (e.indexOf(':') !== -1) return new URL(e).href
    } catch (e) {}
  }
  function resolveUrl(e, t) {
    return resolveIfNotPlainOrUrl(e, t) || asURL(e) || resolveIfNotPlainOrUrl('./' + e, t)
  }
  function resolveIfNotPlainOrUrl(e, t) {
    const r = t.indexOf('#'),
      s = t.indexOf('?')
    r + s > -2 && (t = t.slice(0, r === -1 ? s : s === -1 || s > r ? r : s))
    e.indexOf('\\') !== -1 && (e = e.replace(ye, '/'))
    if (e[0] === '/' && e[1] === '/') return t.slice(0, t.indexOf(':') + 1) + e
    if (
      (e[0] === '.' &&
        (e[1] === '/' ||
          (e[1] === '.' && (e[2] === '/' || (e.length === 2 && (e += '/')))) ||
          (e.length === 1 && (e += '/')))) ||
      e[0] === '/'
    ) {
      const r = t.slice(0, t.indexOf(':') + 1)
      if (r === 'blob:')
        throw new TypeError(
          `Failed to resolve module specifier "${e}". Invalid relative url or base scheme isn't hierarchical.`
        )
      let s
      if (t[r.length + 1] === '/')
        if (r !== 'file:') {
          s = t.slice(r.length + 2)
          s = s.slice(s.indexOf('/') + 1)
        } else s = t.slice(8)
      else s = t.slice(r.length + (t[r.length] === '/'))
      if (e[0] === '/') return t.slice(0, t.length - s.length - 1) + e
      const n = s.slice(0, s.lastIndexOf('/') + 1) + e
      const a = []
      let i = -1
      for (let e = 0; e < n.length; e++)
        if (i === -1) {
          if (n[e] === '.') {
            if (n[e + 1] === '.' && (n[e + 2] === '/' || e + 2 === n.length)) {
              a.pop()
              e += 2
              continue
            }
            if (n[e + 1] === '/' || e + 1 === n.length) {
              e += 1
              continue
            }
          }
          while (n[e] === '/') e++
          i = e
        } else if (n[e] === '/') {
          a.push(n.slice(i, e + 1))
          i = -1
        }
      i !== -1 && a.push(n.slice(i))
      return t.slice(0, t.length - s.length) + a.join('')
    }
  }
  function resolveAndComposeImportMap(e, t, r) {
    const s = { imports: Object.assign({}, r.imports), scopes: Object.assign({}, r.scopes) }
    e.imports && resolveAndComposePackages(e.imports, s.imports, t, r)
    if (e.scopes)
      for (let n in e.scopes) {
        const a = resolveUrl(n, t)
        resolveAndComposePackages(e.scopes[n], s.scopes[a] || (s.scopes[a] = {}), t, r)
      }
    return s
  }
  function getMatch(e, t) {
    if (t[e]) return e
    let r = e.length
    do {
      const s = e.slice(0, r + 1)
      if (s in t) return s
    } while ((r = e.lastIndexOf('/', r - 1)) !== -1)
  }
  function applyPackages(e, t) {
    const r = getMatch(e, t)
    if (r) {
      const s = t[r]
      if (s === null) return
      return s + e.slice(r.length)
    }
  }
  function resolveImportMap(e, t, r) {
    let s = r && getMatch(r, e.scopes)
    while (s) {
      const r = applyPackages(t, e.scopes[s])
      if (r) return r
      s = getMatch(s.slice(0, s.lastIndexOf('/')), e.scopes)
    }
    return applyPackages(t, e.imports) || (t.indexOf(':') !== -1 && t)
  }
  function resolveAndComposePackages(e, t, r, s) {
    for (let a in e) {
      const i = resolveIfNotPlainOrUrl(a, r) || a
      if ((!n || !ne) && t[i] && t[i] !== e[i]) throw Error(`Rejected map override "${i}" from ${t[i]} to ${e[i]}.`)
      let c = e[a]
      if (typeof c !== 'string') continue
      const f = resolveImportMap(s, resolveIfNotPlainOrUrl(c, r) || c, r)
      f ? (t[i] = f) : console.warn(`Mapping "${a}" -> "${e[a]}" does not resolve`)
    }
  }
  let $e = !t && (0, eval)('u=>import(u)')
  let Se
  const Oe =
    t &&
    new Promise((e) => {
      const t = Object.assign(document.createElement('script'), { src: createBlob('self._d=u=>import(u)'), ep: true })
      t.setAttribute('nonce', oe)
      t.addEventListener('load', () => {
        if (!(Se = !!($e = self._d))) {
          let e
          window.addEventListener('error', (t) => (e = t))
          $e = (t, r) =>
            new Promise((s, n) => {
              const a = Object.assign(document.createElement('script'), {
                type: 'module',
                src: createBlob(`import*as m from'${t}';self._esmsi=m`)
              })
              e = void 0
              a.ep = true
              oe && a.setAttribute('nonce', oe)
              a.addEventListener('error', cb)
              a.addEventListener('load', cb)
              function cb(i) {
                document.head.removeChild(a)
                if (self._esmsi) {
                  s(self._esmsi, we)
                  self._esmsi = void 0
                } else {
                  n(
                    (!(i instanceof Event) && i) ||
                      (e && e.error) ||
                      new Error(`Error loading ${(r && r.errUrl) || t} (${a.src}).`)
                  )
                  e = void 0
                }
              }
              document.head.appendChild(a)
            })
        }
        document.head.removeChild(t)
        delete self._d
        e()
      })
      document.head.appendChild(t)
    })
  let Le = false
  let xe = false
  const Ae = t && HTMLScriptElement.supports
  let Ce = Ae && Ae.name === 'supports' && Ae('importmap')
  let Ue = Se
  const Ee = 'import.meta'
  const Pe = 'import"x"assert{type:"css"}'
  const Ie = 'import"x"assert{type:"json"}'
  let Me = Promise.resolve(Oe).then(() => {
    if (Se)
      return t
        ? new Promise((e) => {
            const t = document.createElement('iframe')
            t.style.display = 'none'
            t.setAttribute('nonce', oe)
            function cb({ data: r }) {
              const s = Array.isArray(r) && r[0] === 'esms'
              if (s) {
                Ce = r[1]
                Ue = r[2]
                xe = r[3]
                Le = r[4]
                e()
                document.head.removeChild(t)
                window.removeEventListener('message', cb, false)
              }
            }
            window.addEventListener('message', cb, false)
            const r = `<script nonce=${oe || ''}>b=(s,type='text/javascript')=>URL.createObjectURL(new Blob([s],{type}));document.head.appendChild(Object.assign(document.createElement('script'),{type:'importmap',nonce:"${oe}",innerText:\`{"imports":{"x":"\${b('')}"}}\`}));Promise.all([${Ce ? 'true,true' : `'x',b('${Ee}')`}, ${me ? `b('${Pe}'.replace('x',b('','text/css')))` : 'false'}, ${be ? `b('${Ie}'.replace('x',b('{}','text/json')))` : 'false'}].map(x =>typeof x==='string'?import(x).then(x =>!!x,()=>false):x)).then(a=>parent.postMessage(['esms'].concat(a),'*'))<\/script>`
            let s = false,
              n = false
            function doOnload() {
              if (!s) {
                n = true
                return
              }
              const e = t.contentDocument
              if (e && e.head.childNodes.length === 0) {
                const t = e.createElement('script')
                oe && t.setAttribute('nonce', oe)
                t.innerHTML = r.slice(15 + (oe ? oe.length : 0), -9)
                e.head.appendChild(t)
              }
            }
            t.onload = doOnload
            document.head.appendChild(t)
            s = true
            'srcdoc' in t ? (t.srcdoc = r) : t.contentDocument.write(r)
            n && doOnload()
          })
        : Promise.all([
            Ce || $e(createBlob(Ee)).then(() => (Ue = true), noop),
            me && $e(createBlob(Pe.replace('x', createBlob('', 'text/css')))).then(() => (xe = true), noop),
            be &&
              $e(createBlob(jsonModulescheck.replace('x', createBlob('{}', 'text/json')))).then(() => (Le = true), noop)
          ])
  })
  let je,
    Re,
    Te,
    _e = 2 << 19
  const Ne =
      1 === new Uint8Array(new Uint16Array([1]).buffer)[0]
        ? function (e, t) {
            const r = e.length
            let s = 0
            for (; s < r; ) t[s] = e.charCodeAt(s++)
          }
        : function (e, t) {
            const r = e.length
            let s = 0
            for (; s < r; ) {
              const r = e.charCodeAt(s)
              t[s++] = ((255 & r) << 8) | (r >>> 8)
            }
          },
    He = 'xportmportlassetaromsyncunctionssertvoyiedelecontininstantybreareturdebuggeawaithrwhileforifcatcfinallels'
  let qe, Fe, Je
  function parse(e, t = '@') {
    ;(qe = e), (Fe = t)
    const r = 2 * qe.length + (2 << 18)
    if (r > _e || !je) {
      for (; r > _e; ) _e *= 2
      ;(Re = new ArrayBuffer(_e)),
        Ne(He, new Uint16Array(Re, 16, 105)),
        (je = (function (e, t, r) {
          'use asm'
          var s = new e.Int8Array(r),
            n = new e.Int16Array(r),
            a = new e.Int32Array(r),
            i = new e.Uint8Array(r),
            c = new e.Uint16Array(r),
            f = 1024
          function b() {
            var e = 0,
              t = 0,
              r = 0,
              i = 0,
              ne = 0,
              oe = 0
            oe = f
            f = (f + 10240) | 0
            s[796] = 1
            s[795] = 0
            n[395] = 0
            n[396] = 0
            a[67] = a[2]
            s[797] = 0
            a[66] = 0
            s[794] = 0
            a[68] = oe + 2048
            a[69] = oe
            s[798] = 0
            e = ((a[3] | 0) + -2) | 0
            a[70] = e
            t = (e + (a[64] << 1)) | 0
            a[71] = t
            e: while (1) {
              r = (e + 2) | 0
              a[70] = r
              if (e >>> 0 >= t >>> 0) {
                ne = 18
                break
              }
              t: do {
                switch (n[r >> 1] | 0) {
                  case 9:
                  case 10:
                  case 11:
                  case 12:
                  case 13:
                  case 32:
                    break
                  case 101: {
                    if (
                      (((n[396] | 0) == 0 ? H(r) | 0 : 0) ? (m((e + 4) | 0, 16, 10) | 0) == 0 : 0)
                        ? (l(), (s[796] | 0) == 0)
                        : 0
                    ) {
                      ne = 9
                      break e
                    } else ne = 17
                    break
                  }
                  case 105: {
                    if (H(r) | 0 ? (m((e + 4) | 0, 26, 10) | 0) == 0 : 0) {
                      k()
                      ne = 17
                    } else ne = 17
                    break
                  }
                  case 59: {
                    ne = 17
                    break
                  }
                  case 47:
                    switch (n[(e + 4) >> 1] | 0) {
                      case 47: {
                        P()
                        break t
                      }
                      case 42: {
                        y(1)
                        break t
                      }
                      default: {
                        ne = 16
                        break e
                      }
                    }
                  default: {
                    ne = 16
                    break e
                  }
                }
              } while (0)
              if ((ne | 0) == 17) {
                ne = 0
                a[67] = a[70]
              }
              e = a[70] | 0
              t = a[71] | 0
            }
            if ((ne | 0) == 9) {
              e = a[70] | 0
              a[67] = e
              ne = 19
            } else if ((ne | 0) == 16) {
              s[796] = 0
              a[70] = e
              ne = 19
            } else if ((ne | 0) == 18)
              if (!(s[794] | 0)) {
                e = r
                ne = 19
              } else e = 0
            do {
              if ((ne | 0) == 19) {
                e: while (1) {
                  t = (e + 2) | 0
                  a[70] = t
                  if (e >>> 0 >= (a[71] | 0) >>> 0) {
                    ne = 82
                    break
                  }
                  t: do {
                    switch (n[t >> 1] | 0) {
                      case 9:
                      case 10:
                      case 11:
                      case 12:
                      case 13:
                      case 32:
                        break
                      case 101: {
                        if (((n[396] | 0) == 0 ? H(t) | 0 : 0) ? (m((e + 4) | 0, 16, 10) | 0) == 0 : 0) {
                          l()
                          ne = 81
                        } else ne = 81
                        break
                      }
                      case 105: {
                        if (H(t) | 0 ? (m((e + 4) | 0, 26, 10) | 0) == 0 : 0) {
                          k()
                          ne = 81
                        } else ne = 81
                        break
                      }
                      case 99: {
                        if ((H(t) | 0 ? (m((e + 4) | 0, 36, 8) | 0) == 0 : 0) ? V(n[(e + 12) >> 1] | 0) | 0 : 0) {
                          s[798] = 1
                          ne = 81
                        } else ne = 81
                        break
                      }
                      case 40: {
                        i = a[68] | 0
                        t = n[396] | 0
                        ne = t & 65535
                        a[(i + (ne << 3)) >> 2] = 1
                        r = a[67] | 0
                        n[396] = ((t + 1) << 16) >> 16
                        a[(i + (ne << 3) + 4) >> 2] = r
                        ne = 81
                        break
                      }
                      case 41: {
                        t = n[396] | 0
                        if (!((t << 16) >> 16)) {
                          ne = 36
                          break e
                        }
                        ne = ((t + -1) << 16) >> 16
                        n[396] = ne
                        i = n[395] | 0
                        t = i & 65535
                        if ((i << 16) >> 16 != 0 ? (a[((a[68] | 0) + ((ne & 65535) << 3)) >> 2] | 0) == 5 : 0) {
                          t = a[((a[69] | 0) + ((t + -1) << 2)) >> 2] | 0
                          r = (t + 4) | 0
                          if (!(a[r >> 2] | 0)) a[r >> 2] = (a[67] | 0) + 2
                          a[(t + 12) >> 2] = e + 4
                          n[395] = ((i + -1) << 16) >> 16
                          ne = 81
                        } else ne = 81
                        break
                      }
                      case 123: {
                        ne = a[67] | 0
                        i = a[61] | 0
                        e = ne
                        do {
                          if (((n[ne >> 1] | 0) == 41) & ((i | 0) != 0) ? (a[(i + 4) >> 2] | 0) == (ne | 0) : 0) {
                            t = a[62] | 0
                            a[61] = t
                            if (!t) {
                              a[57] = 0
                              break
                            } else {
                              a[(t + 28) >> 2] = 0
                              break
                            }
                          }
                        } while (0)
                        i = a[68] | 0
                        r = n[396] | 0
                        ne = r & 65535
                        a[(i + (ne << 3)) >> 2] = (s[798] | 0) == 0 ? 2 : 6
                        n[396] = ((r + 1) << 16) >> 16
                        a[(i + (ne << 3) + 4) >> 2] = e
                        s[798] = 0
                        ne = 81
                        break
                      }
                      case 125: {
                        e = n[396] | 0
                        if (!((e << 16) >> 16)) {
                          ne = 49
                          break e
                        }
                        i = a[68] | 0
                        ne = ((e + -1) << 16) >> 16
                        n[396] = ne
                        if ((a[(i + ((ne & 65535) << 3)) >> 2] | 0) == 4) {
                          h()
                          ne = 81
                        } else ne = 81
                        break
                      }
                      case 39: {
                        d(39)
                        ne = 81
                        break
                      }
                      case 34: {
                        d(34)
                        ne = 81
                        break
                      }
                      case 47:
                        switch (n[(e + 4) >> 1] | 0) {
                          case 47: {
                            P()
                            break t
                          }
                          case 42: {
                            y(1)
                            break t
                          }
                          default: {
                            e = a[67] | 0
                            i = n[e >> 1] | 0
                            r: do {
                              if (!(U(i) | 0)) {
                                switch ((i << 16) >> 16) {
                                  case 41:
                                    if (D(a[((a[68] | 0) + (c[396] << 3) + 4) >> 2] | 0) | 0) {
                                      ne = 69
                                      break r
                                    } else {
                                      ne = 66
                                      break r
                                    }
                                  case 125:
                                    break
                                  default: {
                                    ne = 66
                                    break r
                                  }
                                }
                                t = a[68] | 0
                                r = c[396] | 0
                                if (!(p(a[(t + (r << 3) + 4) >> 2] | 0) | 0) ? (a[(t + (r << 3)) >> 2] | 0) != 6 : 0)
                                  ne = 66
                                else ne = 69
                              } else
                                switch ((i << 16) >> 16) {
                                  case 46:
                                    if ((((n[(e + -2) >> 1] | 0) + -48) & 65535) < 10) {
                                      ne = 66
                                      break r
                                    } else {
                                      ne = 69
                                      break r
                                    }
                                  case 43:
                                    if ((n[(e + -2) >> 1] | 0) == 43) {
                                      ne = 66
                                      break r
                                    } else {
                                      ne = 69
                                      break r
                                    }
                                  case 45:
                                    if ((n[(e + -2) >> 1] | 0) == 45) {
                                      ne = 66
                                      break r
                                    } else {
                                      ne = 69
                                      break r
                                    }
                                  default: {
                                    ne = 69
                                    break r
                                  }
                                }
                            } while (0)
                            r: do {
                              if ((ne | 0) == 66) {
                                ne = 0
                                if (!(o(e) | 0)) {
                                  switch ((i << 16) >> 16) {
                                    case 0: {
                                      ne = 69
                                      break r
                                    }
                                    case 47: {
                                      if (s[797] | 0) {
                                        ne = 69
                                        break r
                                      }
                                      break
                                    }
                                    default: {
                                    }
                                  }
                                  r = a[3] | 0
                                  t = i
                                  do {
                                    if (e >>> 0 <= r >>> 0) break
                                    e = (e + -2) | 0
                                    a[67] = e
                                    t = n[e >> 1] | 0
                                  } while (!(E(t) | 0))
                                  if (F(t) | 0) {
                                    do {
                                      if (e >>> 0 <= r >>> 0) break
                                      e = (e + -2) | 0
                                      a[67] = e
                                    } while (F(n[e >> 1] | 0) | 0)
                                    if (j(e) | 0) {
                                      g()
                                      s[797] = 0
                                      ne = 81
                                      break t
                                    } else e = 1
                                  } else e = 1
                                } else ne = 69
                              }
                            } while (0)
                            if ((ne | 0) == 69) {
                              g()
                              e = 0
                            }
                            s[797] = e
                            ne = 81
                            break t
                          }
                        }
                      case 96: {
                        i = a[68] | 0
                        r = n[396] | 0
                        ne = r & 65535
                        a[(i + (ne << 3) + 4) >> 2] = a[67]
                        n[396] = ((r + 1) << 16) >> 16
                        a[(i + (ne << 3)) >> 2] = 3
                        h()
                        ne = 81
                        break
                      }
                      default:
                        ne = 81
                    }
                  } while (0)
                  if ((ne | 0) == 81) {
                    ne = 0
                    a[67] = a[70]
                  }
                  e = a[70] | 0
                }
                if ((ne | 0) == 36) {
                  T()
                  e = 0
                  break
                } else if ((ne | 0) == 49) {
                  T()
                  e = 0
                  break
                } else if ((ne | 0) == 82) {
                  e = (s[794] | 0) == 0 ? ((n[395] | n[396]) << 16) >> 16 == 0 : 0
                  break
                }
              }
            } while (0)
            f = oe
            return e | 0
          }
          function l() {
            var e = 0,
              t = 0,
              r = 0,
              i = 0,
              c = 0,
              f = 0,
              ne = 0,
              oe = 0,
              ce = 0,
              le = 0,
              ue = 0,
              de = 0,
              pe = 0,
              he = 0
            oe = a[70] | 0
            ce = a[63] | 0
            he = (oe + 12) | 0
            a[70] = he
            r = w(1) | 0
            e = a[70] | 0
            if (!((e | 0) == (he | 0) ? !(I(r) | 0) : 0)) pe = 3
            e: do {
              if ((pe | 0) == 3) {
                t: do {
                  switch ((r << 16) >> 16) {
                    case 123: {
                      a[70] = e + 2
                      e = w(1) | 0
                      t = a[70] | 0
                      while (1) {
                        if (W(e) | 0) {
                          d(e)
                          e = ((a[70] | 0) + 2) | 0
                          a[70] = e
                        } else {
                          q(e) | 0
                          e = a[70] | 0
                        }
                        w(1) | 0
                        e = A(t, e) | 0
                        if ((e << 16) >> 16 == 44) {
                          a[70] = (a[70] | 0) + 2
                          e = w(1) | 0
                        }
                        if ((e << 16) >> 16 == 125) {
                          pe = 15
                          break
                        }
                        he = t
                        t = a[70] | 0
                        if ((t | 0) == (he | 0)) {
                          pe = 12
                          break
                        }
                        if (t >>> 0 > (a[71] | 0) >>> 0) {
                          pe = 14
                          break
                        }
                      }
                      if ((pe | 0) == 12) {
                        T()
                        break e
                      } else if ((pe | 0) == 14) {
                        T()
                        break e
                      } else if ((pe | 0) == 15) {
                        s[795] = 1
                        a[70] = (a[70] | 0) + 2
                        break t
                      }
                      break
                    }
                    case 42: {
                      a[70] = e + 2
                      w(1) | 0
                      he = a[70] | 0
                      A(he, he) | 0
                      break
                    }
                    default: {
                      s[796] = 0
                      switch ((r << 16) >> 16) {
                        case 100: {
                          oe = (e + 14) | 0
                          a[70] = oe
                          switch (((w(1) | 0) << 16) >> 16) {
                            case 97: {
                              t = a[70] | 0
                              if ((m((t + 2) | 0, 56, 8) | 0) == 0 ? ((c = (t + 10) | 0), F(n[c >> 1] | 0) | 0) : 0) {
                                a[70] = c
                                w(0) | 0
                                pe = 22
                              }
                              break
                            }
                            case 102: {
                              pe = 22
                              break
                            }
                            case 99: {
                              t = a[70] | 0
                              if (
                                (
                                  (m((t + 2) | 0, 36, 8) | 0) == 0
                                    ? ((i = (t + 10) | 0), (he = n[i >> 1] | 0), V(he) | 0 | ((he << 16) >> 16 == 123))
                                    : 0
                                )
                                  ? ((a[70] = i), (f = w(1) | 0), (f << 16) >> 16 != 123)
                                  : 0
                              ) {
                                de = f
                                pe = 31
                              }
                              break
                            }
                            default: {
                            }
                          }
                          r: do {
                            if ((pe | 0) == 22 ? ((ne = a[70] | 0), (m((ne + 2) | 0, 64, 14) | 0) == 0) : 0) {
                              r = (ne + 16) | 0
                              t = n[r >> 1] | 0
                              if (!(V(t) | 0))
                                switch ((t << 16) >> 16) {
                                  case 40:
                                  case 42:
                                    break
                                  default:
                                    break r
                                }
                              a[70] = r
                              t = w(1) | 0
                              if ((t << 16) >> 16 == 42) {
                                a[70] = (a[70] | 0) + 2
                                t = w(1) | 0
                              }
                              if ((t << 16) >> 16 != 40) {
                                de = t
                                pe = 31
                              }
                            }
                          } while (0)
                          if (
                            (pe | 0) == 31 ? ((le = a[70] | 0), q(de) | 0, (ue = a[70] | 0), ue >>> 0 > le >>> 0) : 0
                          ) {
                            O(e, oe, le, ue)
                            a[70] = (a[70] | 0) + -2
                            break e
                          }
                          O(e, oe, 0, 0)
                          a[70] = e + 12
                          break e
                        }
                        case 97: {
                          a[70] = e + 10
                          w(0) | 0
                          e = a[70] | 0
                          pe = 35
                          break
                        }
                        case 102: {
                          pe = 35
                          break
                        }
                        case 99: {
                          if ((m((e + 2) | 0, 36, 8) | 0) == 0 ? ((t = (e + 10) | 0), E(n[t >> 1] | 0) | 0) : 0) {
                            a[70] = t
                            he = w(1) | 0
                            pe = a[70] | 0
                            q(he) | 0
                            he = a[70] | 0
                            O(pe, he, pe, he)
                            a[70] = (a[70] | 0) + -2
                            break e
                          }
                          e = (e + 4) | 0
                          a[70] = e
                          break
                        }
                        case 108:
                        case 118:
                          break
                        default:
                          break e
                      }
                      if ((pe | 0) == 35) {
                        a[70] = e + 16
                        e = w(1) | 0
                        if ((e << 16) >> 16 == 42) {
                          a[70] = (a[70] | 0) + 2
                          e = w(1) | 0
                        }
                        pe = a[70] | 0
                        q(e) | 0
                        he = a[70] | 0
                        O(pe, he, pe, he)
                        a[70] = (a[70] | 0) + -2
                        break e
                      }
                      a[70] = e + 6
                      s[796] = 0
                      r = w(1) | 0
                      e = a[70] | 0
                      r = ((q(r) | 0 | 32) << 16) >> 16 == 123
                      i = a[70] | 0
                      if (r) {
                        a[70] = i + 2
                        he = w(1) | 0
                        e = a[70] | 0
                        q(he) | 0
                      }
                      r: while (1) {
                        t = a[70] | 0
                        if ((t | 0) == (e | 0)) break
                        O(e, t, e, t)
                        t = w(1) | 0
                        if (r)
                          switch ((t << 16) >> 16) {
                            case 93:
                            case 125:
                              break e
                            default: {
                            }
                          }
                        e = a[70] | 0
                        if ((t << 16) >> 16 != 44) {
                          pe = 51
                          break
                        }
                        a[70] = e + 2
                        t = w(1) | 0
                        e = a[70] | 0
                        switch ((t << 16) >> 16) {
                          case 91:
                          case 123: {
                            pe = 51
                            break r
                          }
                          default: {
                          }
                        }
                        q(t) | 0
                      }
                      if ((pe | 0) == 51) a[70] = e + -2
                      if (!r) break e
                      a[70] = i + -2
                      break e
                    }
                  }
                } while (0)
                he = ((w(1) | 0) << 16) >> 16 == 102
                e = a[70] | 0
                if (he ? (m((e + 2) | 0, 50, 6) | 0) == 0 : 0) {
                  a[70] = e + 8
                  u(oe, w(1) | 0)
                  e = (ce | 0) == 0 ? 232 : (ce + 16) | 0
                  while (1) {
                    e = a[e >> 2] | 0
                    if (!e) break e
                    a[(e + 12) >> 2] = 0
                    a[(e + 8) >> 2] = 0
                    e = (e + 16) | 0
                  }
                }
                a[70] = e + -2
              }
            } while (0)
            return
          }
          function k() {
            var e = 0,
              t = 0,
              r = 0,
              i = 0,
              c = 0,
              f = 0
            c = a[70] | 0
            e = (c + 12) | 0
            a[70] = e
            e: do {
              switch (((w(1) | 0) << 16) >> 16) {
                case 40: {
                  t = a[68] | 0
                  f = n[396] | 0
                  r = f & 65535
                  a[(t + (r << 3)) >> 2] = 5
                  e = a[70] | 0
                  n[396] = ((f + 1) << 16) >> 16
                  a[(t + (r << 3) + 4) >> 2] = e
                  if ((n[a[67] >> 1] | 0) != 46) {
                    a[70] = e + 2
                    f = w(1) | 0
                    v(c, a[70] | 0, 0, e)
                    t = a[61] | 0
                    r = a[69] | 0
                    c = n[395] | 0
                    n[395] = ((c + 1) << 16) >> 16
                    a[(r + ((c & 65535) << 2)) >> 2] = t
                    switch ((f << 16) >> 16) {
                      case 39: {
                        d(39)
                        break
                      }
                      case 34: {
                        d(34)
                        break
                      }
                      default: {
                        a[70] = (a[70] | 0) + -2
                        break e
                      }
                    }
                    e = ((a[70] | 0) + 2) | 0
                    a[70] = e
                    switch (((w(1) | 0) << 16) >> 16) {
                      case 44: {
                        a[70] = (a[70] | 0) + 2
                        w(1) | 0
                        c = a[61] | 0
                        a[(c + 4) >> 2] = e
                        f = a[70] | 0
                        a[(c + 16) >> 2] = f
                        s[(c + 24) >> 0] = 1
                        a[70] = f + -2
                        break e
                      }
                      case 41: {
                        n[396] = (((n[396] | 0) + -1) << 16) >> 16
                        f = a[61] | 0
                        a[(f + 4) >> 2] = e
                        a[(f + 12) >> 2] = (a[70] | 0) + 2
                        s[(f + 24) >> 0] = 1
                        n[395] = (((n[395] | 0) + -1) << 16) >> 16
                        break e
                      }
                      default: {
                        a[70] = (a[70] | 0) + -2
                        break e
                      }
                    }
                  }
                  break
                }
                case 46: {
                  a[70] = (a[70] | 0) + 2
                  if (((w(1) | 0) << 16) >> 16 == 109 ? ((t = a[70] | 0), (m((t + 2) | 0, 44, 6) | 0) == 0) : 0) {
                    e = a[67] | 0
                    if (!(G(e) | 0) ? (n[e >> 1] | 0) == 46 : 0) break e
                    v(c, c, (t + 8) | 0, 2)
                  }
                  break
                }
                case 42:
                case 39:
                case 34: {
                  i = 18
                  break
                }
                case 123: {
                  e = a[70] | 0
                  if (n[396] | 0) {
                    a[70] = e + -2
                    break e
                  }
                  while (1) {
                    if (e >>> 0 >= (a[71] | 0) >>> 0) break
                    e = w(1) | 0
                    if (!(W(e) | 0)) {
                      if ((e << 16) >> 16 == 125) {
                        i = 33
                        break
                      }
                    } else d(e)
                    e = ((a[70] | 0) + 2) | 0
                    a[70] = e
                  }
                  if ((i | 0) == 33) a[70] = (a[70] | 0) + 2
                  f = ((w(1) | 0) << 16) >> 16 == 102
                  e = a[70] | 0
                  if (f ? m((e + 2) | 0, 50, 6) | 0 : 0) {
                    T()
                    break e
                  }
                  a[70] = e + 8
                  e = w(1) | 0
                  if (W(e) | 0) {
                    u(c, e)
                    break e
                  } else {
                    T()
                    break e
                  }
                }
                default:
                  if ((a[70] | 0) == (e | 0)) a[70] = c + 10
                  else i = 18
              }
            } while (0)
            do {
              if ((i | 0) == 18) {
                if (n[396] | 0) {
                  a[70] = (a[70] | 0) + -2
                  break
                }
                e = a[71] | 0
                t = a[70] | 0
                while (1) {
                  if (t >>> 0 >= e >>> 0) {
                    i = 25
                    break
                  }
                  r = n[t >> 1] | 0
                  if (W(r) | 0) {
                    i = 23
                    break
                  }
                  f = (t + 2) | 0
                  a[70] = f
                  t = f
                }
                if ((i | 0) == 23) {
                  u(c, r)
                  break
                } else if ((i | 0) == 25) {
                  T()
                  break
                }
              }
            } while (0)
            return
          }
          function u(e, t) {
            e = e | 0
            t = t | 0
            var r = 0,
              s = 0
            r = ((a[70] | 0) + 2) | 0
            switch ((t << 16) >> 16) {
              case 39: {
                d(39)
                s = 5
                break
              }
              case 34: {
                d(34)
                s = 5
                break
              }
              default:
                T()
            }
            do {
              if ((s | 0) == 5) {
                v(e, r, a[70] | 0, 1)
                a[70] = (a[70] | 0) + 2
                t = w(0) | 0
                e = (t << 16) >> 16 == 97
                if (e) {
                  r = a[70] | 0
                  if (m((r + 2) | 0, 78, 10) | 0) s = 11
                } else {
                  r = a[70] | 0
                  if (
                    !((((t << 16) >> 16 == 119 ? (n[(r + 2) >> 1] | 0) == 105 : 0) ? (n[(r + 4) >> 1] | 0) == 116 : 0)
                      ? (n[(r + 6) >> 1] | 0) == 104
                      : 0)
                  )
                    s = 11
                }
                if ((s | 0) == 11) {
                  a[70] = r + -2
                  break
                }
                a[70] = r + ((e ? 6 : 4) << 1)
                if (((w(1) | 0) << 16) >> 16 != 123) {
                  a[70] = r
                  break
                }
                e = a[70] | 0
                t = e
                e: while (1) {
                  a[70] = t + 2
                  t = w(1) | 0
                  switch ((t << 16) >> 16) {
                    case 39: {
                      d(39)
                      a[70] = (a[70] | 0) + 2
                      t = w(1) | 0
                      break
                    }
                    case 34: {
                      d(34)
                      a[70] = (a[70] | 0) + 2
                      t = w(1) | 0
                      break
                    }
                    default:
                      t = q(t) | 0
                  }
                  if ((t << 16) >> 16 != 58) {
                    s = 20
                    break
                  }
                  a[70] = (a[70] | 0) + 2
                  switch (((w(1) | 0) << 16) >> 16) {
                    case 39: {
                      d(39)
                      break
                    }
                    case 34: {
                      d(34)
                      break
                    }
                    default: {
                      s = 24
                      break e
                    }
                  }
                  a[70] = (a[70] | 0) + 2
                  switch (((w(1) | 0) << 16) >> 16) {
                    case 125: {
                      s = 29
                      break e
                    }
                    case 44:
                      break
                    default: {
                      s = 28
                      break e
                    }
                  }
                  a[70] = (a[70] | 0) + 2
                  if (((w(1) | 0) << 16) >> 16 == 125) {
                    s = 29
                    break
                  }
                  t = a[70] | 0
                }
                if ((s | 0) == 20) {
                  a[70] = r
                  break
                } else if ((s | 0) == 24) {
                  a[70] = r
                  break
                } else if ((s | 0) == 28) {
                  a[70] = r
                  break
                } else if ((s | 0) == 29) {
                  s = a[61] | 0
                  a[(s + 16) >> 2] = e
                  a[(s + 12) >> 2] = (a[70] | 0) + 2
                  break
                }
              }
            } while (0)
            return
          }
          function o(e) {
            e = e | 0
            e: do {
              switch (n[e >> 1] | 0) {
                case 100:
                  switch (n[(e + -2) >> 1] | 0) {
                    case 105: {
                      e = $((e + -4) | 0, 88, 2) | 0
                      break e
                    }
                    case 108: {
                      e = $((e + -4) | 0, 92, 3) | 0
                      break e
                    }
                    default: {
                      e = 0
                      break e
                    }
                  }
                case 101:
                  switch (n[(e + -2) >> 1] | 0) {
                    case 115:
                      switch (n[(e + -4) >> 1] | 0) {
                        case 108: {
                          e = B((e + -6) | 0, 101) | 0
                          break e
                        }
                        case 97: {
                          e = B((e + -6) | 0, 99) | 0
                          break e
                        }
                        default: {
                          e = 0
                          break e
                        }
                      }
                    case 116: {
                      e = $((e + -4) | 0, 98, 4) | 0
                      break e
                    }
                    case 117: {
                      e = $((e + -4) | 0, 106, 6) | 0
                      break e
                    }
                    default: {
                      e = 0
                      break e
                    }
                  }
                case 102: {
                  if ((n[(e + -2) >> 1] | 0) == 111 ? (n[(e + -4) >> 1] | 0) == 101 : 0)
                    switch (n[(e + -6) >> 1] | 0) {
                      case 99: {
                        e = $((e + -8) | 0, 118, 6) | 0
                        break e
                      }
                      case 112: {
                        e = $((e + -8) | 0, 130, 2) | 0
                        break e
                      }
                      default: {
                        e = 0
                        break e
                      }
                    }
                  else e = 0
                  break
                }
                case 107: {
                  e = $((e + -2) | 0, 134, 4) | 0
                  break
                }
                case 110: {
                  e = (e + -2) | 0
                  if (B(e, 105) | 0) e = 1
                  else e = $(e, 142, 5) | 0
                  break
                }
                case 111: {
                  e = B((e + -2) | 0, 100) | 0
                  break
                }
                case 114: {
                  e = $((e + -2) | 0, 152, 7) | 0
                  break
                }
                case 116: {
                  e = $((e + -2) | 0, 166, 4) | 0
                  break
                }
                case 119:
                  switch (n[(e + -2) >> 1] | 0) {
                    case 101: {
                      e = B((e + -4) | 0, 110) | 0
                      break e
                    }
                    case 111: {
                      e = $((e + -4) | 0, 174, 3) | 0
                      break e
                    }
                    default: {
                      e = 0
                      break e
                    }
                  }
                default:
                  e = 0
              }
            } while (0)
            return e | 0
          }
          function h() {
            var e = 0,
              t = 0,
              r = 0,
              s = 0
            t = a[71] | 0
            r = a[70] | 0
            e: while (1) {
              e = (r + 2) | 0
              if (r >>> 0 >= t >>> 0) {
                t = 10
                break
              }
              switch (n[e >> 1] | 0) {
                case 96: {
                  t = 7
                  break e
                }
                case 36: {
                  if ((n[(r + 4) >> 1] | 0) == 123) {
                    t = 6
                    break e
                  }
                  break
                }
                case 92: {
                  e = (r + 4) | 0
                  break
                }
                default: {
                }
              }
              r = e
            }
            if ((t | 0) == 6) {
              e = (r + 4) | 0
              a[70] = e
              t = a[68] | 0
              s = n[396] | 0
              r = s & 65535
              a[(t + (r << 3)) >> 2] = 4
              n[396] = ((s + 1) << 16) >> 16
              a[(t + (r << 3) + 4) >> 2] = e
            } else if ((t | 0) == 7) {
              a[70] = e
              r = a[68] | 0
              s = (((n[396] | 0) + -1) << 16) >> 16
              n[396] = s
              if ((a[(r + ((s & 65535) << 3)) >> 2] | 0) != 3) T()
            } else if ((t | 0) == 10) {
              a[70] = e
              T()
            }
            return
          }
          function w(e) {
            e = e | 0
            var t = 0,
              r = 0,
              s = 0
            r = a[70] | 0
            e: do {
              t = n[r >> 1] | 0
              t: do {
                if ((t << 16) >> 16 != 47)
                  if (e)
                    if (V(t) | 0) break
                    else break e
                  else if (F(t) | 0) break
                  else break e
                else
                  switch (n[(r + 2) >> 1] | 0) {
                    case 47: {
                      P()
                      break t
                    }
                    case 42: {
                      y(e)
                      break t
                    }
                    default: {
                      t = 47
                      break e
                    }
                  }
              } while (0)
              s = a[70] | 0
              r = (s + 2) | 0
              a[70] = r
            } while (s >>> 0 < (a[71] | 0) >>> 0)
            return t | 0
          }
          function d(e) {
            e = e | 0
            var t = 0,
              r = 0,
              s = 0,
              i = 0
            i = a[71] | 0
            t = a[70] | 0
            while (1) {
              s = (t + 2) | 0
              if (t >>> 0 >= i >>> 0) {
                t = 9
                break
              }
              r = n[s >> 1] | 0
              if ((r << 16) >> 16 == (e << 16) >> 16) {
                t = 10
                break
              }
              if ((r << 16) >> 16 == 92) {
                r = (t + 4) | 0
                if ((n[r >> 1] | 0) == 13) {
                  t = (t + 6) | 0
                  t = (n[t >> 1] | 0) == 10 ? t : r
                } else t = r
              } else if (Z(r) | 0) {
                t = 9
                break
              } else t = s
            }
            if ((t | 0) == 9) {
              a[70] = s
              T()
            } else if ((t | 0) == 10) a[70] = s
            return
          }
          function v(e, t, r, n) {
            e = e | 0
            t = t | 0
            r = r | 0
            n = n | 0
            var i = 0,
              c = 0
            i = a[65] | 0
            a[65] = i + 32
            c = a[61] | 0
            a[((c | 0) == 0 ? 228 : (c + 28) | 0) >> 2] = i
            a[62] = c
            a[61] = i
            a[(i + 8) >> 2] = e
            if (2 == (n | 0)) e = r
            else e = 1 == (n | 0) ? (r + 2) | 0 : 0
            a[(i + 12) >> 2] = e
            a[i >> 2] = t
            a[(i + 4) >> 2] = r
            a[(i + 16) >> 2] = 0
            a[(i + 20) >> 2] = n
            c = 1 == (n | 0)
            s[(i + 24) >> 0] = c & 1
            a[(i + 28) >> 2] = 0
            if (c | (2 == (n | 0))) s[795] = 1
            return
          }
          function A(e, t) {
            e = e | 0
            t = t | 0
            var r = 0,
              s = 0,
              i = 0,
              c = 0
            r = a[70] | 0
            s = n[r >> 1] | 0
            c = (e | 0) == (t | 0)
            i = c ? 0 : e
            c = c ? 0 : t
            if ((s << 16) >> 16 == 97) {
              a[70] = r + 4
              r = w(1) | 0
              e = a[70] | 0
              if (W(r) | 0) {
                d(r)
                t = ((a[70] | 0) + 2) | 0
                a[70] = t
              } else {
                q(r) | 0
                t = a[70] | 0
              }
              s = w(1) | 0
              r = a[70] | 0
            }
            if ((r | 0) != (e | 0)) O(e, t, i, c)
            return s | 0
          }
          function C() {
            var e = 0,
              t = 0,
              r = 0
            r = a[71] | 0
            t = a[70] | 0
            e: while (1) {
              e = (t + 2) | 0
              if (t >>> 0 >= r >>> 0) {
                t = 6
                break
              }
              switch (n[e >> 1] | 0) {
                case 13:
                case 10: {
                  t = 6
                  break e
                }
                case 93: {
                  t = 7
                  break e
                }
                case 92: {
                  e = (t + 4) | 0
                  break
                }
                default: {
                }
              }
              t = e
            }
            if ((t | 0) == 6) {
              a[70] = e
              T()
              e = 0
            } else if ((t | 0) == 7) {
              a[70] = e
              e = 93
            }
            return e | 0
          }
          function g() {
            var e = 0,
              t = 0,
              r = 0
            e: while (1) {
              e = a[70] | 0
              t = (e + 2) | 0
              a[70] = t
              if (e >>> 0 >= (a[71] | 0) >>> 0) {
                r = 7
                break
              }
              switch (n[t >> 1] | 0) {
                case 13:
                case 10: {
                  r = 7
                  break e
                }
                case 47:
                  break e
                case 91: {
                  C() | 0
                  break
                }
                case 92: {
                  a[70] = e + 4
                  break
                }
                default: {
                }
              }
            }
            if ((r | 0) == 7) T()
            return
          }
          function p(e) {
            e = e | 0
            switch (n[e >> 1] | 0) {
              case 62: {
                e = (n[(e + -2) >> 1] | 0) == 61
                break
              }
              case 41:
              case 59: {
                e = 1
                break
              }
              case 104: {
                e = $((e + -2) | 0, 200, 4) | 0
                break
              }
              case 121: {
                e = $((e + -2) | 0, 208, 6) | 0
                break
              }
              case 101: {
                e = $((e + -2) | 0, 220, 3) | 0
                break
              }
              default:
                e = 0
            }
            return e | 0
          }
          function y(e) {
            e = e | 0
            var t = 0,
              r = 0,
              s = 0,
              i = 0,
              c = 0
            i = ((a[70] | 0) + 2) | 0
            a[70] = i
            r = a[71] | 0
            while (1) {
              t = (i + 2) | 0
              if (i >>> 0 >= r >>> 0) break
              s = n[t >> 1] | 0
              if (!e ? Z(s) | 0 : 0) break
              if ((s << 16) >> 16 == 42 ? (n[(i + 4) >> 1] | 0) == 47 : 0) {
                c = 8
                break
              }
              i = t
            }
            if ((c | 0) == 8) {
              a[70] = t
              t = (i + 4) | 0
            }
            a[70] = t
            return
          }
          function m(e, t, r) {
            e = e | 0
            t = t | 0
            r = r | 0
            var n = 0,
              a = 0
            e: do {
              if (!r) e = 0
              else {
                while (1) {
                  n = s[e >> 0] | 0
                  a = s[t >> 0] | 0
                  if ((n << 24) >> 24 != (a << 24) >> 24) break
                  r = (r + -1) | 0
                  if (!r) {
                    e = 0
                    break e
                  } else {
                    e = (e + 1) | 0
                    t = (t + 1) | 0
                  }
                }
                e = ((n & 255) - (a & 255)) | 0
              }
            } while (0)
            return e | 0
          }
          function I(e) {
            e = e | 0
            e: do {
              switch ((e << 16) >> 16) {
                case 38:
                case 37:
                case 33: {
                  e = 1
                  break
                }
                default:
                  if ((((e & -8) << 16) >> 16 == 40) | (((e + -58) & 65535) < 6)) e = 1
                  else {
                    switch ((e << 16) >> 16) {
                      case 91:
                      case 93:
                      case 94: {
                        e = 1
                        break e
                      }
                      default: {
                      }
                    }
                    e = ((e + -123) & 65535) < 4
                  }
              }
            } while (0)
            return e | 0
          }
          function U(e) {
            e = e | 0
            e: do {
              switch ((e << 16) >> 16) {
                case 38:
                case 37:
                case 33:
                  break
                default:
                  if (!((((e + -58) & 65535) < 6) | ((((e + -40) & 65535) < 7) & ((e << 16) >> 16 != 41)))) {
                    switch ((e << 16) >> 16) {
                      case 91:
                      case 94:
                        break e
                      default: {
                      }
                    }
                    return (((e << 16) >> 16 != 125) & (((e + -123) & 65535) < 4)) | 0
                  }
              }
            } while (0)
            return 1
          }
          function x(e) {
            e = e | 0
            var t = 0
            t = n[e >> 1] | 0
            e: do {
              if (((t + -9) & 65535) >= 5) {
                switch ((t << 16) >> 16) {
                  case 160:
                  case 32: {
                    t = 1
                    break e
                  }
                  default: {
                  }
                }
                if (I(t) | 0) return ((t << 16) >> 16 != 46) | (G(e) | 0) | 0
                else t = 0
              } else t = 1
            } while (0)
            return t | 0
          }
          function S(e) {
            e = e | 0
            var t = 0,
              r = 0,
              s = 0,
              i = 0
            r = f
            f = (f + 16) | 0
            s = r
            a[s >> 2] = 0
            a[64] = e
            t = a[3] | 0
            i = (t + (e << 1)) | 0
            e = (i + 2) | 0
            n[i >> 1] = 0
            a[s >> 2] = e
            a[65] = e
            a[57] = 0
            a[61] = 0
            a[59] = 0
            a[58] = 0
            a[63] = 0
            a[60] = 0
            f = r
            return t | 0
          }
          function O(e, t, r, n) {
            e = e | 0
            t = t | 0
            r = r | 0
            n = n | 0
            var i = 0,
              c = 0
            i = a[65] | 0
            a[65] = i + 20
            c = a[63] | 0
            a[((c | 0) == 0 ? 232 : (c + 16) | 0) >> 2] = i
            a[63] = i
            a[i >> 2] = e
            a[(i + 4) >> 2] = t
            a[(i + 8) >> 2] = r
            a[(i + 12) >> 2] = n
            a[(i + 16) >> 2] = 0
            s[795] = 1
            return
          }
          function $(e, t, r) {
            e = e | 0
            t = t | 0
            r = r | 0
            var s = 0,
              n = 0
            s = (e + ((0 - r) << 1)) | 0
            n = (s + 2) | 0
            e = a[3] | 0
            if (n >>> 0 >= e >>> 0 ? (m(n, t, r << 1) | 0) == 0 : 0)
              if ((n | 0) == (e | 0)) e = 1
              else e = x(s) | 0
            else e = 0
            return e | 0
          }
          function j(e) {
            e = e | 0
            switch (n[e >> 1] | 0) {
              case 107: {
                e = $((e + -2) | 0, 134, 4) | 0
                break
              }
              case 101: {
                if ((n[(e + -2) >> 1] | 0) == 117) e = $((e + -4) | 0, 106, 6) | 0
                else e = 0
                break
              }
              default:
                e = 0
            }
            return e | 0
          }
          function B(e, t) {
            e = e | 0
            t = t | 0
            var r = 0
            r = a[3] | 0
            if (r >>> 0 <= e >>> 0 ? (n[e >> 1] | 0) == (t << 16) >> 16 : 0)
              if ((r | 0) == (e | 0)) r = 1
              else r = E(n[(e + -2) >> 1] | 0) | 0
            else r = 0
            return r | 0
          }
          function E(e) {
            e = e | 0
            e: do {
              if (((e + -9) & 65535) < 5) e = 1
              else {
                switch ((e << 16) >> 16) {
                  case 32:
                  case 160: {
                    e = 1
                    break e
                  }
                  default: {
                  }
                }
                e = ((e << 16) >> 16 != 46) & (I(e) | 0)
              }
            } while (0)
            return e | 0
          }
          function P() {
            var e = 0,
              t = 0,
              r = 0
            e = a[71] | 0
            r = a[70] | 0
            e: while (1) {
              t = (r + 2) | 0
              if (r >>> 0 >= e >>> 0) break
              switch (n[t >> 1] | 0) {
                case 13:
                case 10:
                  break e
                default:
                  r = t
              }
            }
            a[70] = t
            return
          }
          function q(e) {
            e = e | 0
            while (1) {
              if (V(e) | 0) break
              if (I(e) | 0) break
              e = ((a[70] | 0) + 2) | 0
              a[70] = e
              e = n[e >> 1] | 0
              if (!((e << 16) >> 16)) {
                e = 0
                break
              }
            }
            return e | 0
          }
          function z() {
            var e = 0
            e = a[((a[59] | 0) + 20) >> 2] | 0
            switch (e | 0) {
              case 1: {
                e = -1
                break
              }
              case 2: {
                e = -2
                break
              }
              default:
                e = (e - (a[3] | 0)) >> 1
            }
            return e | 0
          }
          function D(e) {
            e = e | 0
            if (!($(e, 180, 5) | 0) ? !($(e, 190, 3) | 0) : 0) e = $(e, 196, 2) | 0
            else e = 1
            return e | 0
          }
          function F(e) {
            e = e | 0
            switch ((e << 16) >> 16) {
              case 160:
              case 32:
              case 12:
              case 11:
              case 9: {
                e = 1
                break
              }
              default:
                e = 0
            }
            return e | 0
          }
          function G(e) {
            e = e | 0
            if ((n[e >> 1] | 0) == 46 ? (n[(e + -2) >> 1] | 0) == 46 : 0) e = (n[(e + -4) >> 1] | 0) == 46
            else e = 0
            return e | 0
          }
          function H(e) {
            e = e | 0
            if ((a[3] | 0) == (e | 0)) e = 1
            else e = x((e + -2) | 0) | 0
            return e | 0
          }
          function J() {
            var e = 0
            e = a[((a[60] | 0) + 12) >> 2] | 0
            if (!e) e = -1
            else e = (e - (a[3] | 0)) >> 1
            return e | 0
          }
          function K() {
            var e = 0
            e = a[((a[59] | 0) + 12) >> 2] | 0
            if (!e) e = -1
            else e = (e - (a[3] | 0)) >> 1
            return e | 0
          }
          function L() {
            var e = 0
            e = a[((a[60] | 0) + 8) >> 2] | 0
            if (!e) e = -1
            else e = (e - (a[3] | 0)) >> 1
            return e | 0
          }
          function M() {
            var e = 0
            e = a[((a[59] | 0) + 16) >> 2] | 0
            if (!e) e = -1
            else e = (e - (a[3] | 0)) >> 1
            return e | 0
          }
          function N() {
            var e = 0
            e = a[((a[59] | 0) + 4) >> 2] | 0
            if (!e) e = -1
            else e = (e - (a[3] | 0)) >> 1
            return e | 0
          }
          function Q() {
            var e = 0
            e = a[59] | 0
            e = a[((e | 0) == 0 ? 228 : (e + 28) | 0) >> 2] | 0
            a[59] = e
            return ((e | 0) != 0) | 0
          }
          function R() {
            var e = 0
            e = a[60] | 0
            e = a[((e | 0) == 0 ? 232 : (e + 16) | 0) >> 2] | 0
            a[60] = e
            return ((e | 0) != 0) | 0
          }
          function T() {
            s[794] = 1
            a[66] = ((a[70] | 0) - (a[3] | 0)) >> 1
            a[70] = (a[71] | 0) + 2
            return
          }
          function V(e) {
            e = e | 0
            return (((e | 128) << 16) >> 16 == 160) | (((e + -9) & 65535) < 5) | 0
          }
          function W(e) {
            e = e | 0
            return ((e << 16) >> 16 == 39) | ((e << 16) >> 16 == 34) | 0
          }
          function X() {
            return (((a[((a[59] | 0) + 8) >> 2] | 0) - (a[3] | 0)) >> 1) | 0
          }
          function Y() {
            return (((a[((a[60] | 0) + 4) >> 2] | 0) - (a[3] | 0)) >> 1) | 0
          }
          function Z(e) {
            e = e | 0
            return ((e << 16) >> 16 == 13) | ((e << 16) >> 16 == 10) | 0
          }
          function _() {
            return (((a[a[59] >> 2] | 0) - (a[3] | 0)) >> 1) | 0
          }
          function ee() {
            return (((a[a[60] >> 2] | 0) - (a[3] | 0)) >> 1) | 0
          }
          function ae() {
            return i[((a[59] | 0) + 24) >> 0] | 0 | 0
          }
          function re(e) {
            e = e | 0
            a[3] = e
            return
          }
          function ie() {
            return ((s[795] | 0) != 0) | 0
          }
          function se() {
            return ((s[796] | 0) != 0) | 0
          }
          function te() {
            return a[66] | 0
          }
          function fe(e) {
            e = e | 0
            f = (e + 992 + 15) & -16
            return 992
          }
          return {
            su: fe,
            ai: M,
            e: te,
            ee: Y,
            ele: J,
            els: L,
            es: ee,
            f: se,
            id: z,
            ie: N,
            ip: ae,
            is: _,
            ms: ie,
            p: b,
            re: R,
            ri: Q,
            sa: S,
            se: K,
            ses: re,
            ss: X
          }
        })('undefined' != typeof self ? self : global, {}, Re)),
        (Te = je.su(_e - (2 << 17)))
    }
    const s = qe.length + 1
    je.ses(Te), je.sa(s - 1), Ne(qe, new Uint16Array(Re, Te, s)), je.p() || ((Je = je.e()), o())
    const n = [],
      a = []
    for (; je.ri(); ) {
      const e = je.is(),
        t = je.ie(),
        r = je.ai(),
        s = je.id(),
        a = je.ss(),
        i = je.se()
      let c
      je.ip() && (c = b(-1 === s ? e : e + 1, qe.charCodeAt(-1 === s ? e - 1 : e))),
        n.push({ n: c, s: e, e: t, ss: a, se: i, d: s, a: r })
    }
    for (; je.re(); ) {
      const e = je.es(),
        t = je.ee(),
        r = je.els(),
        s = je.ele(),
        n = qe.charCodeAt(e),
        i = r >= 0 ? qe.charCodeAt(r) : -1
      a.push({
        s: e,
        e: t,
        ls: r,
        le: s,
        n: 34 === n || 39 === n ? b(e + 1, n) : qe.slice(e, t),
        ln: r < 0 ? void 0 : 34 === i || 39 === i ? b(r + 1, i) : qe.slice(r, s)
      })
    }
    return [n, a, !!je.f(), !!je.ms()]
  }
  function b(e, t) {
    Je = e
    let r = '',
      s = Je
    for (;;) {
      Je >= qe.length && o()
      const e = qe.charCodeAt(Je)
      if (e === t) break
      92 === e ? ((r += qe.slice(s, Je)), (r += l()), (s = Je)) : (8232 === e || 8233 === e || (u(e) && o()), ++Je)
    }
    return (r += qe.slice(s, Je++)), r
  }
  function l() {
    let e = qe.charCodeAt(++Je)
    switch ((++Je, e)) {
      case 110:
        return '\n'
      case 114:
        return '\r'
      case 120:
        return String.fromCharCode(k(2))
      case 117:
        return (function () {
          const e = qe.charCodeAt(Je)
          let t
          123 === e ? (++Je, (t = k(qe.indexOf('}', Je) - Je)), ++Je, t > 1114111 && o()) : (t = k(4))
          return t <= 65535
            ? String.fromCharCode(t)
            : ((t -= 65536), String.fromCharCode(55296 + (t >> 10), 56320 + (1023 & t)))
        })()
      case 116:
        return '\t'
      case 98:
        return '\b'
      case 118:
        return '\v'
      case 102:
        return '\f'
      case 13:
        10 === qe.charCodeAt(Je) && ++Je
      case 10:
        return ''
      case 56:
      case 57:
        o()
      default:
        if (e >= 48 && e <= 55) {
          let t = qe.substr(Je - 1, 3).match(/^[0-7]+/)[0],
            r = parseInt(t, 8)
          return (
            r > 255 && ((t = t.slice(0, -1)), (r = parseInt(t, 8))),
            (Je += t.length - 1),
            (e = qe.charCodeAt(Je)),
            ('0' === t && 56 !== e && 57 !== e) || o(),
            String.fromCharCode(r)
          )
        }
        return u(e) ? '' : String.fromCharCode(e)
    }
  }
  function k(e) {
    const t = Je
    let r = 0,
      s = 0
    for (let t = 0; t < e; ++t, ++Je) {
      let e,
        n = qe.charCodeAt(Je)
      if (95 !== n) {
        if (n >= 97) e = n - 97 + 10
        else if (n >= 65) e = n - 65 + 10
        else {
          if (!(n >= 48 && n <= 57)) break
          e = n - 48
        }
        if (e >= 16) break
        ;(s = n), (r = 16 * r + e)
      } else (95 !== s && 0 !== t) || o(), (s = n)
    }
    return (95 !== s && Je - t === e) || o(), r
  }
  function u(e) {
    return 13 === e || 10 === e
  }
  function o() {
    throw Object.assign(
      Error(`Parse error ${Fe}:${qe.slice(0, Je).split('\n').length}:${Je - qe.lastIndexOf('\n', Je - 1)}`),
      { idx: Je }
    )
  }
  async function _resolve(e, t) {
    const r = resolveIfNotPlainOrUrl(e, t) || asURL(e)
    return { r: resolveImportMap(De, r || e, t) || throwUnresolved(e, t), b: !r && !asURL(e) }
  }
  const We = i
    ? async (e, t) => {
        let r = i(e, t, defaultResolve)
        r && r.then && (r = await r)
        return r ? { r: r, b: !resolveIfNotPlainOrUrl(e, t) && !asURL(e) } : _resolve(e, t)
      }
    : _resolve
  async function importShim(e, ...r) {
    let s = r[r.length - 1]
    typeof s !== 'string' && (s = we)
    await ze
    a && (await a(e, typeof r[1] !== 'string' ? r[1] : {}, s))
    if (Ze || n || !Ve) {
      t && processScriptsAndPreloads(true)
      n || (Ze = false)
    }
    await Ge
    return topLevelLoad((await We(e, s)).r, { credentials: 'same-origin' })
  }
  self.importShim = importShim
  function defaultResolve(e, t) {
    return resolveImportMap(De, resolveIfNotPlainOrUrl(e, t) || e, t) || throwUnresolved(e, t)
  }
  function throwUnresolved(e, t) {
    throw Error(`Unable to resolve specifier '${e}'${fromParent(t)}`)
  }
  const resolveSync = (e, t = we) => {
    t = `${t}`
    const r = i && i(e, t, defaultResolve)
    return r && !r.then ? r : defaultResolve(e, t)
  }
  function metaResolve(e, t = this.url) {
    return resolveSync(e, t)
  }
  importShim.resolve = resolveSync
  importShim.getImportMap = () => JSON.parse(JSON.stringify(De))
  importShim.addImportMap = (e) => {
    if (!n) throw new Error('Unsupported in polyfill mode.')
    De = resolveAndComposeImportMap(e, we, De)
  }
  const Be = (importShim._r = {})
  importShim._w = {}
  async function loadAll(e, t) {
    if (!e.b && !t[e.u]) {
      t[e.u] = 1
      await e.L
      await Promise.all(e.d.map((e) => loadAll(e, t)))
      e.n || (e.n = e.d.some((e) => e.n))
    }
  }
  let De = { imports: {}, scopes: {} }
  let Ve
  const ze = Me.then(() => {
    Ve = s.polyfillEnable !== true && Se && Ue && Ce && (!be || Le) && (!me || xe) && !ve
    if (t) {
      if (!Ce) {
        const e = HTMLScriptElement.supports || ((e) => e === 'classic' || e === 'module')
        HTMLScriptElement.supports = (t) => t === 'importmap' || e(t)
      }
      if (n || !Ve) {
        new MutationObserver((e) => {
          for (const t of e)
            if (t.type === 'childList')
              for (const e of t.addedNodes)
                if (e.tagName === 'SCRIPT') {
                  e.type === (n ? 'module-shim' : 'module') && processScript(e, true)
                  e.type === (n ? 'importmap-shim' : 'importmap') && processImportMap(e, true)
                } else
                  e.tagName === 'LINK' && e.rel === (n ? 'modulepreload-shim' : 'modulepreload') && processPreload(e)
        }).observe(document, { childList: true, subtree: true })
        processScriptsAndPreloads()
        if (document.readyState === 'complete') readyStateCompleteCheck()
        else {
          async function readyListener() {
            await ze
            processScriptsAndPreloads()
            if (document.readyState === 'complete') {
              readyStateCompleteCheck()
              document.removeEventListener('readystatechange', readyListener)
            }
          }
          document.addEventListener('readystatechange', readyListener)
        }
      }
    }
  })
  let Ge = ze
  let Ke = true
  let Ze = true
  async function topLevelLoad(e, t, r, s, i) {
    n || (Ze = false)
    await ze
    await Ge
    a && (await a(e, typeof t !== 'string' ? t : {}, ''))
    if (!n && Ve) {
      if (s) return null
      await i
      return $e(r ? createBlob(r) : e, { errUrl: e || r })
    }
    const c = getOrCreateLoad(e, t, null, r)
    const f = {}
    await loadAll(c, f)
    Qe = void 0
    resolveDeps(c, f)
    await i
    if (r && !n && !c.n) {
      if (s) return
      ue && revokeObjectURLs(Object.keys(f))
      return await $e(createBlob(r), { errUrl: r })
    }
    if (Ke && !n && c.n && s) {
      le()
      Ke = false
    }
    const ne = await $e(n || c.n || !s ? c.b : c.u, { errUrl: c.u })
    c.s && (await $e(c.s)).u$_(ne)
    ue && revokeObjectURLs(Object.keys(f))
    return ne
  }
  function revokeObjectURLs(e) {
    let t = 0
    const r = e.length
    const s = self.requestIdleCallback ? self.requestIdleCallback : self.requestAnimationFrame
    s(cleanup)
    function cleanup() {
      const n = t * 100
      if (!(n > r)) {
        for (const t of e.slice(n, n + 100)) {
          const e = Be[t]
          e && URL.revokeObjectURL(e.b)
        }
        t++
        s(cleanup)
      }
    }
  }
  function urlJsString(e) {
    return `'${e.replace(/'/g, "\\'")}'`
  }
  let Qe
  function resolveDeps(e, t) {
    if (e.b || !t[e.u]) return
    t[e.u] = 0
    for (const r of e.d) resolveDeps(r, t)
    const [r, s] = e.a
    const n = e.S
    let a = ke && Qe ? `import '${Qe}';` : ''
    let i = 0,
      c = 0,
      ne = []
    function pushStringTo(t) {
      while (ne[ne.length - 1] < t) {
        const t = ne.pop()
        a += `${n.slice(i, t)}, ${urlJsString(e.r)}`
        i = t
      }
      a += n.slice(i, t)
      i = t
    }
    for (const { s: t, ss: s, se: oe, d: ce } of r)
      if (ce === -1) {
        let r = e.d[c++],
          s = r.b,
          f = !s
        f &&
          ((s = r.s) ||
            (s = r.s =
              createBlob(
                `export function u$_(m){${r.a[1]
                  .map(({ s: e, e: t }, s) => {
                    const n = r.S[e] === '"' || r.S[e] === "'"
                    return `e$_${s}=m${n ? '[' : '.'}${r.S.slice(e, t)}${n ? ']' : ''}`
                  })
                  .join(
                    ','
                  )}}${r.a[1].length ? `let ${r.a[1].map((e, t) => `e$_${t}`).join(',')};` : ''}export {${r.a[1].map(({ s: e, e: t }, s) => `e$_${s} as ${r.S.slice(e, t)}`).join(',')}}\n//# sourceURL=${r.r}?cycle`
              )))
        pushStringTo(t - 1)
        a += `/*${n.slice(t - 1, oe)}*/${urlJsString(s)}`
        if (!f && r.s) {
          a += `;import*as m$_${c} from'${r.b}';import{u$_ as u$_${c}}from'${r.s}';u$_${c}(m$_${c})`
          r.s = void 0
        }
        i = oe
      } else if (ce === -2) {
        e.m = { url: e.r, resolve: metaResolve }
        f(e.m, e.u)
        pushStringTo(t)
        a += `importShim._r[${urlJsString(e.u)}].m`
        i = oe
      } else {
        pushStringTo(s + 6)
        a += 'Shim('
        ne.push(oe - 1)
        i = t
      }
    e.s &&
      (a += `\n;import{u$_}from'${e.s}';try{u$_({${s
        .filter((e) => e.ln)
        .map(({ s: e, e: t, ln: r }) => `${n.slice(e, t)}:${r}`)
        .join(',')}})}catch(_){};\n`)
    function pushSourceURL(t, r) {
      const s = r + t.length
      const c = n.indexOf('\n', s)
      const f = c !== -1 ? c : n.length
      pushStringTo(s)
      a += new URL(n.slice(s, f), e.r).href
      i = f
    }
    let oe = n.lastIndexOf(Xe)
    let ce = n.lastIndexOf(Ye)
    oe < i && (oe = -1)
    ce < i && (ce = -1)
    oe !== -1 && (ce === -1 || ce > oe) && pushSourceURL(Xe, oe)
    if (ce !== -1) {
      pushSourceURL(Ye, ce)
      oe !== -1 && oe > ce && pushSourceURL(Xe, oe)
    }
    pushStringTo(n.length)
    oe === -1 && (a += Xe + e.r)
    e.b = Qe = createBlob(a)
    e.S = void 0
  }
  const Xe = '\n//# sourceURL='
  const Ye = '\n//# sourceMappingURL='
  const et = /^(text|application)\/(x-)?javascript(;|$)/
  const tt = /^(application)\/wasm(;|$)/
  const rt = /^(text|application)\/json(;|$)/
  const st = /^(text|application)\/css(;|$)/
  const nt = /url\(\s*(?:(["'])((?:\\.|[^\n\\"'])+)\1|((?:\\.|[^\s,"'()\\])+))\s*\)/g
  let at = []
  let it = 0
  function pushFetchPool() {
    if (++it > 100) return new Promise((e) => at.push(e))
  }
  function popFetchPool() {
    it--
    at.length && at.shift()()
  }
  async function doFetch(e, t, r) {
    if (pe && !t.integrity) throw Error(`No integrity for ${e}${fromParent(r)}.`)
    const s = pushFetchPool()
    s && (await s)
    try {
      var n = await c(e, t)
    } catch (t) {
      t.message = `Unable to fetch ${e}${fromParent(r)} - see network log for details.\n` + t.message
      throw t
    } finally {
      popFetchPool()
    }
    if (!n.ok) {
      const e = new TypeError(`${n.status} ${n.statusText} ${n.url}${fromParent(r)}`)
      e.response = n
      throw e
    }
    return n
  }
  async function fetchModule(e, t, r) {
    const s = await doFetch(e, t, r)
    const n = s.headers.get('content-type')
    if (et.test(n)) return { r: s.url, s: await s.text(), t: 'js' }
    if (tt.test(n)) {
      const t = (importShim._w[e] = await WebAssembly.compileStreaming(s))
      let r = '',
        n = 0,
        a = ''
      for (const e of WebAssembly.Module.imports(t)) {
        r += `import * as impt${n} from '${e.module}';\n`
        a += `'${e.module}':impt${n++},`
      }
      n = 0
      r += `const instance = await WebAssembly.instantiate(importShim._w['${e}'], {${a}});\n`
      for (const e of WebAssembly.Module.exports(t)) {
        r += `const expt${n} = instance['${e.name}'];\n`
        r += `export { expt${n++} as "${e.name}" };\n`
      }
      return { r: s.url, s: r, t: 'wasm' }
    }
    if (rt.test(n)) return { r: s.url, s: `export default ${await s.text()}`, t: 'json' }
    if (st.test(n))
      return {
        r: s.url,
        s: `var s=new CSSStyleSheet();s.replaceSync(${JSON.stringify((await s.text()).replace(nt, (t, r = '', s, n) => `url(${r}${resolveUrl(s || n, e)}${r})`))});export default s;`,
        t: 'css'
      }
    throw Error(
      `Unsupported Content-Type "${n}" loading ${e}${fromParent(r)}. Modules must be served with a valid MIME type like application/javascript.`
    )
  }
  function getOrCreateLoad(e, t, r, s) {
    let a = Be[e]
    if (a && !s) return a
    a = {
      u: e,
      r: s ? e : void 0,
      f: void 0,
      S: void 0,
      L: void 0,
      a: void 0,
      d: void 0,
      b: void 0,
      s: void 0,
      n: false,
      t: null,
      m: null
    }
    if (Be[e]) {
      let e = 0
      while (Be[a.u + ++e]);
      a.u += e
    }
    Be[a.u] = a
    a.f = (async () => {
      if (!s) {
        let i
        ;({ r: a.r, s: s, t: i } = await (ft[e] || fetchModule(e, t, r)))
        if (i && !n) {
          if ((i === 'css' && !me) || (i === 'json' && !be))
            throw Error(
              `${i}-modules require <script type="esms-options">{ "polyfillEnable": ["${i}-modules"] }<\/script>`
            )
          ;((i === 'css' && !xe) || (i === 'json' && !Le)) && (a.n = true)
        }
      }
      try {
        a.a = parse(s, a.u)
      } catch (e) {
        throwError(e)
        a.a = [[], [], false]
      }
      a.S = s
      return a
    })()
    a.L = a.f.then(async () => {
      let e = t
      a.d = (
        await Promise.all(
          a.a[0].map(async ({ n: t, d: r }) => {
            ;((r >= 0 && !Se) || (r === -2 && !Ue)) && (a.n = true)
            if (r !== -1 || !t) return
            const { r: s, b: n } = await We(t, a.r || a.u)
            !n || (Ce && !ve) || (a.n = true)
            if (r === -1) {
              if (ge && ge(s)) return { b: s }
              e.integrity && (e = Object.assign({}, e, { integrity: void 0 }))
              return getOrCreateLoad(s, e, a.r).f
            }
          })
        )
      ).filter((e) => e)
    })
    return a
  }
  function processScriptsAndPreloads(e = false) {
    if (!e)
      for (const e of document.querySelectorAll(n ? 'link[rel=modulepreload-shim]' : 'link[rel=modulepreload]'))
        processPreload(e)
    for (const e of document.querySelectorAll(n ? 'script[type=importmap-shim]' : 'script[type=importmap]'))
      processImportMap(e)
    if (!e)
      for (const e of document.querySelectorAll(n ? 'script[type=module-shim]' : 'script[type=module]'))
        processScript(e)
  }
  function getFetchOpts(e) {
    const t = {}
    e.integrity && (t.integrity = e.integrity)
    e.referrerPolicy && (t.referrerPolicy = e.referrerPolicy)
    e.crossOrigin === 'use-credentials'
      ? (t.credentials = 'include')
      : e.crossOrigin === 'anonymous'
        ? (t.credentials = 'omit')
        : (t.credentials = 'same-origin')
    return t
  }
  let ot = Promise.resolve()
  let ct = 1
  function domContentLoadedCheck() {
    --ct !== 0 || de || (!n && Ve) || document.dispatchEvent(new Event('DOMContentLoaded'))
  }
  t &&
    document.addEventListener('DOMContentLoaded', async () => {
      await ze
      domContentLoadedCheck()
    })
  let lt = 1
  function readyStateCompleteCheck() {
    --lt !== 0 || de || (!n && Ve) || document.dispatchEvent(new Event('readystatechange'))
  }
  const hasNext = (e) => e.nextSibling || (e.parentNode && hasNext(e.parentNode))
  const epCheck = (e, t) =>
    e.ep || (!t && ((!e.src && !e.innerHTML) || !hasNext(e))) || e.getAttribute('noshim') !== null || !(e.ep = true)
  function processImportMap(e, t = lt > 0) {
    if (!epCheck(e, t)) {
      if (e.src) {
        if (!n) return
        setImportMapSrcOrLazy()
      }
      if (Ze) {
        Ge = Ge.then(async () => {
          De = resolveAndComposeImportMap(
            e.src ? await (await doFetch(e.src, getFetchOpts(e))).json() : JSON.parse(e.innerHTML),
            e.src || we,
            De
          )
        }).catch((t) => {
          console.log(t)
          t instanceof SyntaxError &&
            (t = new Error(`Unable to parse import map ${t.message} in: ${e.src || e.innerHTML}`))
          throwError(t)
        })
        n || (Ze = false)
      }
    }
  }
  function processScript(e, t = lt > 0) {
    if (epCheck(e, t)) return
    const r = e.getAttribute('async') === null && lt > 0
    const s = ct > 0
    r && lt++
    s && ct++
    const a = topLevelLoad(e.src || we, getFetchOpts(e), !e.src && e.innerHTML, !n, r && ot)
      .then(() => {
        n && e.dispatchEvent(new Event('load'))
      })
      .catch(throwError)
    r && (ot = a.then(readyStateCompleteCheck))
    s && a.then(domContentLoadedCheck)
  }
  const ft = {}
  function processPreload(e) {
    if (!e.ep) {
      e.ep = true
      ft[e.href] || (ft[e.href] = fetchModule(e.href, getFetchOpts(e)))
    }
  }
})()
//# sourceMappingURL=es-module-shims.js.map
