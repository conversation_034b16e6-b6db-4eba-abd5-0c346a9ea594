class ColorSwatches extends HTMLElement{connectedCallback(){this.selectors={colorSwatchImage:'.grid-product__color-image',colorSwatch:'.color-swatch--with-image',gridItemLink:'.grid-item__link',gridProductImageWrap:'.grid-product__image-wrap'}
this.gridItemLink=this.closest(this.selectors.gridItemLink)
this.gridProductImageWrap=this.gridItemLink.querySelector(this.selectors.gridProductImageWrap)
this.colorImages=this.gridProductImageWrap.querySelectorAll(this.selectors.colorSwatchImage)
if(this.colorImages.length){this.swatches=this.querySelectorAll(this.selectors.colorSwatch)
this.colorSwatchHovering()}}
colorSwatchHovering(){this.swatches.forEach((swatch)=>{swatch.addEventListener('mouseenter',()=>this.setActiveColorImage(swatch))
swatch.addEventListener('touchstart',(evt)=>{evt.preventDefault()
this.setActiveColorImage(swatch)},{passive:!0})
swatch.addEventListener('mouseleave',()=>this.removeActiveColorImage(swatch))})}
setActiveColorImage(swatch){const id=swatch.dataset.variantId
const image=swatch.dataset.variantImage
this.colorImages.forEach((el)=>{el.classList.remove('is-active')})
this.swatches.forEach((el)=>{el.classList.remove('is-active')})
const imageEl=this.gridProductImageWrap.querySelector('.grid-product__color-image--'+id)
imageEl.style.backgroundImage='url('+image+')'
imageEl.classList.add('is-active')
swatch.classList.add('is-active')
const variantUrl=swatch.dataset.url
const gridItem=swatch.closest('.grid-item__link')
gridItem.setAttribute('href',variantUrl)}
removeActiveColorImage(swatch){const id=swatch.dataset.variantId
this.gridProductImageWrap.querySelector(`.grid-product__color-image--${id}`).classList.remove('is-active')
swatch.classList.remove('is-active')}}
customElements.define('color-swatches',ColorSwatches)