import{executeJSmodules}from '@archetype-themes/utils/utils'
import{EVENTS}from '@archetype-themes/utils/events'
class QuickAdd extends HTMLElement{constructor(){super()
this.selectors={quickAddBtn:'[data-single-variant-quick-add]',quickAddHolder:'[data-tool-tip-content]'}
this.init()}
init(){const quickAddBtn=this.querySelector(this.selectors.quickAddBtn)
if(quickAddBtn){quickAddBtn.addEventListener('click',this.addToCart.bind(this))}else{this.addEventListener('tooltip:interact',async(e)=>{if(e.detail.context==='QuickAdd'){if(!this.quickAddData){this.quickAddData=await this.loadQuickAddForm(e)}}})
this.addEventListener('tooltip:open',async(e)=>{if(e.detail.context==='QuickAdd'){if(!this.quickAddData){this.quickAddData=await this.loadQuickAddForm(e)}
const quickAddContainer=document.querySelector(this.selectors.quickAddHolder)
quickAddContainer.innerHTML=this.quickAddData.outerHTML
this.dispatchEvent(new CustomEvent('quickshop:opened'),{bubbles:!0})
if(Shopify&&Shopify.PaymentButton){Shopify.PaymentButton.init()}
const scripts=document.querySelectorAll(`tool-tip [data-product-id="${this.prodId}"] script[type="module"]`)
executeJSmodules(scripts)}})}}
addToCart(evt){const btn=evt.currentTarget
const visibleBtn=btn.querySelector('.btn')
const id=btn.dataset.id
visibleBtn.classList.add('btn--loading')
const data={items:[{id:id,quantity:1}],sections:['cart-ajax']}
const endpoint='cart/add.js'
fetch(window.Shopify.routes.root+endpoint,{method:'POST',body:JSON.stringify(data),credentials:'same-origin',headers:{'Content-Type':'application/json'}}).then((response)=>response.json()).then((data)=>{if(data.status===422||data.status==='bad_request'){if(data.description){alert(data.description)}}else{this.dispatchEvent(new CustomEvent(EVENTS.ajaxProductAdded,{bubbles:!0,detail:{product:data,addToCartBtn:btn}}))}
visibleBtn.classList.remove('btn--loading')})}
async loadQuickAddForm(evt){const gridItem=evt.currentTarget.closest('.grid-product')
const handle=gridItem.firstElementChild.getAttribute('data-product-handle')
this.prodId=gridItem.firstElementChild.getAttribute('data-product-id')
let url=`${window.Shopify.routes.root}/products/${handle}?view=form`
url=url.replace('//','/')
try{const response=await fetch(url)
const html=await response.text()
const parser=new DOMParser()
const doc=parser.parseFromString(html,'text/html')
const div=doc.querySelector(`.page-content[data-product-id="${this.prodId}"]`)
this.processHTML(div)
window.dispatchEvent(new CustomEvent(`quickadd:loaded:-${this.prodId}`))
return div}catch(error){console.error('Error:',error)}}
processHTML(productElement){this.removeBreadcrumbs(productElement)
this.preventVariantURLSwitching(productElement)}
removeBreadcrumbs(productElement){const breadcrumbs=productElement.querySelector('.breadcrumb')
if(!breadcrumbs)return
breadcrumbs.remove()}
preventVariantURLSwitching(productElement){const variantPicker=productElement.querySelector('block-variant-picker')
if(!variantPicker)return
variantPicker.removeAttribute('data-update-url')}}
customElements.define('quick-add',QuickAdd)