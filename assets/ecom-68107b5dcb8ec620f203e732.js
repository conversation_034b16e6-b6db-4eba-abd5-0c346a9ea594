!function(){const e=function(){"use strict";if(window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-liaxzs6akyg"]=window.__ectimmers["ecom-liaxzs6akyg"]||{},!this.$el)return;const e=this.$el,t=e.querySelector(".ecom-text_view-more-btn"),o=e.querySelector(".ecom-text_view-less-btn"),c=e.querySelector(".text-content.ecom-html");!c||(t&&t.addEventListener("click",()=>{c.classList.remove("ecom-text--is-mark"),c.style.maxHeight="",t.style.display="none",o.style.display=""}),o&&o.addEventListener("click",()=>{c.classList.add("ecom-text--is-mark"),c.style.maxHeight="var(--ecom-text-height)",o.style.display="none",t.style.display=""}))};document.querySelectorAll(".ecom-liaxzs6akyg").forEach((function(t){e.call({$el:t,id:"ecom-liaxzs6akyg",settings:{},isLive:!0})}))}(),function(){const e=function(){"use strict";if(window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-5myogl0bn6y"]=window.__ectimmers["ecom-5myogl0bn6y"]||{},!this.$el)return!1;const e=this.$el;this.settings.animation&&function(t){if(!e)return;const o=e.querySelector(".ecom__element--button");if(!o)return;let c=1e3*parseInt(t.settings.animation_loop_time)||6e3;window.__ectimmers["ecom-5myogl0bn6y"].u8p5r6zn6=setInterval((function(){o.classList.add("animated"),setTimeout((function(){o.classList.remove("animated")}),1e3)}),c)}(this);var t=e.querySelector(".ecom__element--button");this.isLive&&t&&t.dataset.ecTrackingId&&t.addEventListener("click",(function(e){if(window.Shopify.analytics){e.preventDefault();let o=document.createElement("div");document.body.appendChild(o),o.click();let c=window.EComposer.PAGE||window.EComposer.TEMPLATE||window.EComposer.SECTION||window.EComposer.BLOCK||{};const i=Object.assign({button_id:t.id,tracking_id:t.dataset.ecTrackingId},c);Shopify.analytics.publish("ec_custom_events",i),t.cloneNode(!0).click()}}),{once:!0}),this.isLive&&t&&t.dataset.eventTrackingFb&&t.addEventListener("click",(function(e){window.fbq&&window.fbq("track",""+t.dataset.eventTrackingFb)}),{once:!0})};document.querySelectorAll(".ecom-5myogl0bn6y").forEach((function(t){e.call({$el:t,id:"ecom-5myogl0bn6y",settings:{animation:!1},isLive:!0})})),document.querySelectorAll(".ecom-j6vl5vj1jue").forEach((function(t){e.call({$el:t,id:"ecom-j6vl5vj1jue",settings:{animation:!1},isLive:!0})})),document.querySelectorAll(".ecom-r0se2rp0zgs").forEach((function(t){e.call({$el:t,id:"ecom-r0se2rp0zgs",settings:{animation:!1},isLive:!0})})),document.querySelectorAll(".ecom-niwrtfloizs").forEach((function(t){e.call({$el:t,id:"ecom-niwrtfloizs",settings:{animation:!1},isLive:!0})})),document.querySelectorAll(".ecom-8hy6luod42").forEach((function(t){e.call({$el:t,id:"ecom-8hy6luod42",settings:{animation:!1},isLive:!0})})),document.querySelectorAll(".ecom-6b0elm12swx").forEach((function(t){e.call({$el:t,id:"ecom-6b0elm12swx",settings:{animation:!1},isLive:!0})})),document.querySelectorAll(".ecom-v1bsmvte6xa").forEach((function(t){e.call({$el:t,id:"ecom-v1bsmvte6xa",settings:{animation:!1},isLive:!0})}))}(),function(){const e=function(){"use strict";window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-27hppmvlaul"]=window.__ectimmers["ecom-27hppmvlaul"]||{};let e=this.$el;if(!e)return;let t=e.querySelectorAll(":scope > .tabs__wrapper > .tabs__navs > .tabs__navs--items > .tabs__nav"),o=e.querySelectorAll(":scope > .tabs__wrapper > .core__group--items > .tab__item"),c=this.settings.action;function i(){let t=window.location.hash;if(t){let o=e.querySelector(`[data-target="${t}"]`);if(o){o.click();let e=new MouseEvent("mouseover",{bubbles:!0,cancelable:!0,view:window});o.dispatchEvent(e);let t=o.getBoundingClientRect().top+window.pageYOffset-window.innerHeight/2;window.scrollTo(0,t)}}}function n(){window.dispatchEvent(new window.Event("resize")),setTimeout(()=>{window.dispatchEvent(new window.Event("resize"))},500)}t.forEach((e,i)=>{"click"===c||window.screen.width<1025?e.onclick=function(){this.classList&&this.classList.contains("ecom-item-active")?(t.forEach(e=>e.classList.remove("ecom-item-active")),o.forEach(e=>e.classList.remove("ecom-item-active"))):(t.forEach(e=>e.classList.remove("ecom-item-active")),o.forEach(e=>e.classList.remove("ecom-item-active")),e.classList.add("ecom-item-active"),o[i].classList.add("ecom-item-active")),n()}:e.onmouseover=function(){this.classList&&this.classList.contains("ecom-item-active")?(t.forEach(e=>e.classList.remove("ecom-item-active")),o.forEach(e=>e.classList.remove("ecom-item-active"))):(t.forEach(e=>e.classList.remove("ecom-item-active")),o.forEach(e=>e.classList.remove("ecom-item-active")),e.classList.add("ecom-item-active"),o[i].classList.add("ecom-item-active")),n()}}),setTimeout(()=>{i()},300),window.addEventListener("hashchange",i,!1)};document.querySelectorAll(".ecom-27hppmvlaul").forEach((function(t){e.call({$el:t,id:"ecom-27hppmvlaul",settings:{action:"click"},isLive:!0})}))}(),function(){const e=function(){"use strict";var e,t,o;window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-codw20hf0w4"]=window.__ectimmers["ecom-codw20hf0w4"]||{};let c=this.$el;if(!c)return;let i=!0;const n=this.id;let s=c.querySelectorAll(".ecom-collection__product-variants"),r=this.isLive,l=null!=(e=this.settings.show_featured_media)&&e,a=null!=(t=this.settings.bage_sale)?t:"",d=null!=(o=this.settings.enable_progress_pagination)&&o,m=this.settings.price_type,u="bullets";const p=this.settings.slider_center,_=this.settings.slider_center__tablet,w=this.settings.slider_center__mobile;"progress"===this.settings.slider_pagination_style&&(u="progressbar");const f=this.settings.sale_badge_type;let y=this.settings.slider_speed,h=this.settings.slider_speed__tablet,v=this.settings.slider_speed__mobile;const g=function(e,t={},o=""){return window.innerWidth>1024&&e[0]&&(t[""+o]=e[0]),window.innerWidth<=1024&&window.innerWidth>768&&e[1]?t[""+o]=e[1]:e[0]&&(t[""+o]=e[0]),window.innerWidth<768&&e[2]?t[""+o]=e[2]:e[1]?t[""+o]=e[1]:e[0]&&(t[""+o]=e[0]),t};let b=c.querySelectorAll(".ecom-collection__product-item");function S(e){e.forEach((function(e){e.setAttribute("data-init-quantity","true");let t=e.querySelector(".ecom-collection__product-quantity-input"),o=e.querySelector(".ecom-collection__quantity-controls-plus"),c=e.querySelector(".ecom-collection__quantity-controls-minus");c&&c.addEventListener("click",(function(){t.stepDown(),t.dispatchEvent(new Event("change"))})),o&&o.addEventListener("click",(function(){t.stepUp(),t.dispatchEvent(new Event("change"))})),t&&t.addEventListener("change",(function(t){let o=e.querySelector("a.ecom-collection__product-submit");if(t.target.value>parseInt(t.target.max)&&(t.target.value=parseInt(t.target.max)),o){let e=o.getAttribute("href");o.setAttribute("href",e.replace(/quantity=(\d*)/gm,"quantity="+t.target.value))}}))}))}function q(e=!1,t){const o=c.querySelector(".ecom-paginate__progress-bar--outner"),i=c.querySelector(".ecom-paginate__progress-bar--inner"),n=c.querySelector(".ecom-paginate__progress-text");if(!(d&&r&&o&&i&&n))return;let{total:s,initProduct:l}=o&&o.dataset,a=n&&n.dataset.text,m=0,u=1,p=0,_=0;l=parseInt(l),e?(u=1,p=l*t):(window.location.href.match(/page=\d*/gm)&&(m=new URL(window.location.href).searchParams.get("page"),u=1===m?1:l*(m-1)+1),p=u+l-1),p>s&&(p=s),_=Math.round(p/s*100),i.style.width=_+"%",a=a.replace("{_start}",u),a=a.replace("{_end}",p),a=a.replace("{_total}",s),n.innerText=a}function E(e,t){var o=t.variantIdField.closest(".ecom-collection__product-item");let i=o.querySelector(".ecom-collection__product-submit"),n=o.querySelector(".ecom-collection__product-quantity-input"),s=o.querySelector(".ecom-collection__product-price"),d=o.querySelector(".ecom-collection__product-price--regular"),u=o.querySelector(".ecom-unit-price");d&&d.classList.add("ecom-collection__product--compare-at-price");let p=o.querySelector(".ecom-collection__product-price--bage-sale"),_=o.querySelector(".ecom-collection__product-badge--sale"),w=o.querySelector(".ecom-collection__product-badge--sold-out"),y=o.querySelector(".ecom-collection__product-item-sku-element"),h="";if(null===e||o.hasAttribute("ec-variant-init")&&"first_price"===m){let t=o.querySelector('select[name="variant_id"]'),c=o.querySelector(".product-json"),i=null;try{i=JSON.parse(c.innerHTML)}catch(e){return 1}if(o.hasAttribute("ec-variant-init")&&"first_price"===m)o.removeAttribute("ec-variant-init"),null==(e=i.variants.find(e=>e.available))&&(e=i.variants[0]);else{let c=o.querySelector("select#"+t.id+"-option-0");if(!c)return;const n=c.value;n&&i.variants.forEach((function(t){t.options.includes(n)&&(e=t)}))}}if(e){if(s&&(s.innerHTML=window.EComposer.formatMoney(e.price)),d&&(d.innerHTML=window.EComposer.formatMoney(e.compare_at_price)),u){e.unit_price?u.style.display="block":u.style.display="none";const t=u.querySelector(".ecom-ground-price_unit-price");t&&(t.innerHTML=window.EComposer.formatMoney(e.unit_price))}if(e.compare_at_price>e.price){d&&(d.style.display="inherit");let t="";t=c.querySelector(".ecom-collection__product-main").dataset.sale,"false"==c.querySelector(".ecom-collection__product-main").dataset.translate&&(t=a),_&&w&&(_.style.display="block",w.style.display="none"),"amount"===f?(h=e.compare_at_price-e.price,p&&(p.style.display="inherit",p.innerHTML=t.replace(/\{{.*\}}/g,window.EComposer.formatMoney(h)))):(h=100*(e.compare_at_price-e.price)/e.compare_at_price,p&&(p.style.display="inherit",p.innerHTML=t.replace(/\{{.*\}}/g,Math.round(h))))}else d&&(d.style.display="none"),_&&w&&(_.style.display="none",w.style.display="none"),p&&(p.style.display="none",p.innerHTML="");if(y&&(e.sku?(y.querySelector(".ecom-collection__product-item-sku").innerHTML=e.sku,y.style.display="flex"):y.style.display="none"),e.featured_image){let t=o.querySelector(".ecom-collection__product-media img");if(!l){let o=t.closest("div");o.classList.add("ecom-product-image-loading"),t.setAttribute("src",e.featured_image.src),t.removeAttribute("srcset"),t.addEventListener("load",(function(){o.classList.remove("ecom-product-image-loading")}))}}if(e.options.length,o.querySelector(".ecom-collection__product-submit"))if(e.available){const t=i.closest(".ecom-collection__product--wrapper-items");if(t.dataset.iconAdd&&i.querySelector(".ecom-collection__product-add-cart-icon")&&(i.querySelector(".ecom-collection__product-add-cart-icon").innerHTML=t.dataset.iconAdd),!e.inventory_management||e.inventory_management&&e.inventory_quantity>0){if(i.removeAttribute("disabled"),n){let t=n.closest(".ecom-collection__product-quantity--wrapper");t&&(t.style.display="flex"),n.style.display="flex",e.inventory_management?n.max=e.inventory_quantity:n.max=9999}i.classList.add("ecom-collection__product-form__actions--add"),i.classList.remove("ecom-collection__product-form__actions--soldout"),i.classList.remove("ecom-collection__product-form__actions--unavailable"),i.querySelector(".ecom-add-to-cart-text").innerHTML=i.getAttribute("data-text-add-cart")}else if("continue"==e.inventory_policy&&e.inventory_quantity<=0){if(i.removeAttribute("disabled"),n){let e=n.closest(".ecom-collection__product-quantity--wrapper");e&&(e.style.display="flex"),n.max=9999,n.style.display="flex"}i.classList.add("ecom-collection__product-form__actions--add"),i.classList.remove("ecom-collection__product-form__actions--soldout"),i.classList.remove("ecom-collection__product-form__actions--unavailable"),i.querySelector(".ecom-add-to-cart-text").innerHTML=i.getAttribute("data-text-pre-order")}i.dataset.childName="add_to_cart_button",i.dataset.childTitle="Add to cart button"}else{if(_&&w&&(_.style.display="none",w.style.display="block"),r&&i.setAttribute("disabled","disabled"),n){let e=n.closest(".ecom-collection__product-quantity--wrapper");e&&(e.style.display="none"),n.style.display="none"}const e=i.closest(".ecom-collection__product--wrapper-items");e.dataset.iconSoldout&&i.querySelector(".ecom-collection__product-add-cart-icon")&&(i.querySelector(".ecom-collection__product-add-cart-icon").innerHTML=e.dataset.iconSoldout),i.classList.add("ecom-collection__product-form__actions--soldout"),i.classList.remove("ecom-collection__product-form__actions--add"),i.classList.remove("ecom-collection__product-form__actions--unavailable"),i.querySelector(".ecom-add-to-cart-text").innerHTML=i.getAttribute("data-text-sold-out"),i.dataset.childName="sold_out_button",i.dataset.childTitle="Sold out button"}}else s.html=window.EComposer.formatMoney(0),d&&(d.innerHTML=window.EComposer.formatMoney(0),d.style.display="none"),i&&(i.setAttribute("disabled","disabled"),i.classList.add("ecom-collection__product-form__actions--unavailable"),i.classList.remove("ecom-collection__product-form__actions--add"),i.classList.remove("ecom-collection__product-form__actions--soldout"),i.querySelector(".ecom-add-to-cart-text").innerHTML=i.getAttribute("data-text-unavailable"))}function L(e){e.classList.add("ecom-swatch-init");let t=e.querySelector(".ecom-collection__product-form");if(!t)return;let o=t.querySelector('select[name="variant_id"]'),c=e.querySelector(".product-json"),i=null;try{i=JSON.parse(c.innerHTML)}catch(e){return 1}window.EComposer&&window.EComposer.OptionSelectors&&new window.EComposer.OptionSelectors(o.id,{product:i,onVariantSelected:E,enableHistoryState:!1}),e.querySelectorAll(".ecom-collection__product-swatch-item").forEach((function(t){t.addEventListener("click",(function(){l=!1;var t=this.closest("li");if(t.classList.contains("ecom-product-swatch-item--active"))return!1;t.parentNode.querySelectorAll(".ecom-product-swatch-item--active").forEach((function(e){e.classList.remove("ecom-product-swatch-item--active")})),t.classList.add("ecom-product-swatch-item--active");var c=t.getAttribute("data-option-index"),i=t.getAttribute("data-value");let n=e.querySelector("select#"+o.id+"-option-"+c);n.value=i,n.dispatchEvent(new Event("change"))}))})),e.querySelectorAll("select.ecom-collection__product-swatch-select").forEach((function(t){t.addEventListener("change",(function(){var t=this.getAttribute("data-option-index"),c=this.value;e.querySelectorAll("select#"+o.id+"-option-"+t).forEach((function(e){e.value=c,e.dispatchEvent(new Event("change"))}))}))}))}if(b.length&&S(b),q(!1,1),"slider"===this.settings.layout){let e=function(e){let t=e.querySelector(".ecom-swiper-container"),o=t&&t.dataset.optionSwiper;if(!o)return;o=JSON.parse(o),o.pagination={el:e.querySelector(".ecom-swiper-pagination"),type:u,clickable:!0},o.navigation={nextEl:e.querySelector(".ecom-swiper-button-next"),prevEl:e.querySelector(".ecom-swiper-button-prev")},o.autoHeight=!1,o.on={init:function(){this.el.classList.add("ecom-swiper-initialized")}};let c=[y,h,v];if(r){o=g(c,o,"speed"),o=g([p,_,w],o,"centeredSlides");const e=new window.EComSwiper(t,o);o.autoplay.enabled&&(e.on("touchStart",(function(e,t){e.params.speed=300,e.autoplay.stop()})),e.on("touchEnd",(function(e,t){window.innerWidth>1024&&y&&(e.params.speed=y),window.innerWidth<=1024&&window.innerWidth>768&&h?e.params.speed=h:y&&(e.params.speed=y),window.innerWidth<768&&v?e.params.speed=v:h?e.params.speed=h:y&&(e.params.speed=y),e.autoplay.start()})))}else setTimeout((function(){o=g(c,o,"speed"),o=g([p,_,w],o,"centeredSlides"),new window.EComSwiper(t,o)}),200)},t=this.$el,o=t.querySelector(".ecom-collection__product-container");e(t),o.addEventListener("ecom-products-init-slider",(function(t){e(t.detail.wrapper)}))}s.forEach(L);const A=function(e){e.querySelectorAll(".ecom-collection__product-form__actions--quickshop").forEach((function(e){e.addEventListener("click",(function(e){this.style.display="none";let t=this.closest(".ecom-collection__product-item");t.querySelectorAll(".ecom-collection__product-variants").forEach((function(e){e.classList.add("ecom-active")})),t.querySelectorAll(".ecom-collection__product-quick-shop-wrapper").forEach((function(e){e.style.display="inherit"}))}))})),e.querySelectorAll(".ecom-collection__product-close").forEach((function(e){e.addEventListener("click",(function(e){let t=this.closest(".ecom-collection__product-item");t.querySelectorAll(".ecom-collection__product-variants").forEach((function(e){e.classList.remove("ecom-active")})),t.querySelectorAll(".ecom-collection__product-quick-shop-wrapper").forEach((function(e){e.style.display="none"})),t.querySelectorAll(".ecom-collection__product-form__actions--quickshop").forEach((function(e){e.style.display="inherit"}))}))}))};A(c);const k=c.querySelector(".ecom-collection__product-main");let x=k.dataset,T=k.dataset.countdownShows;const $=/\[([^\]]+)\]/gm;var C="";if(T.indexOf("week")>=0&&x.week){let e="",t=x.week.replace($,(...t)=>(e=t[1],""));C+=`\n                            <div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--week">\n                                <span class="ecom-collection__product-time--number">\n                                    ${e}\n                                </span>\n                                <span class="ecom-collection__product-time--label">\n                                    ${t}\n                                </span>\n                            </div>`}if(T.indexOf("day")>=0&&x.day){let e="",t=x.day.replace($,(...t)=>(e=t[1],""));C+=`<div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--day">\n                                    <span class="ecom-collection__product-time--number">\n                                        ${e}\n                                    </span>\n                                    <span class="ecom-collection__product-time--label">\n                                        ${t}\n                                    </span>\n                                </div> `}if(T.indexOf("hour")>=0&&x.hour){let e="",t=x.hour.replace($,(...t)=>(e=t[1],""));C+=`\n                            <div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--hour">\n                                <span class="ecom-collection__product-time--number">\n                                    ${e}\n                                </span>\n                                <span class="ecom-collection__product-time--label">\n                                    ${t}\n                                </span>\n                            </div>\n                        `}if(T.indexOf("minute")>=0&&x.minute){let e="",t=x.minute.replace($,(...t)=>(e=t[1],""));C+=`<div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--minute">\n                                    <span class="ecom-collection__product-time--number">\n                                        ${e}\n                                    </span>\n                                    <span class="ecom-collection__product-time--label">\n                                        ${t}\n                                    </span>\n                                </div>\n                            `}if(T.indexOf("second")>=0&&x.second){let e="",t=x.second.replace($,(...t)=>(e=t[1],""));C+=`<div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--second">\n                                    <span class="ecom-collection__product-time--number">\n                                        ${e}\n                                    </span>\n                                    <span class="ecom-collection__product-time--label">\n                                        ${t}\n                                    </span>\n                                </div>`}function M(e){let t=this.closest(".ecom-collection__product-countdown-wrapper"),o=t.querySelector(".ecom-collection__product-countdown-progress-bar"),c=t.querySelector(".ecom-collection__product-countdown-progress-bar--timer"),i=this.getAttribute("data-ecom-countdown-from")||0;if(this.innerHTML=e.strftime(C),o&&i){let t=(new Date).getTime(),n=new Date(i).getTime(),s=e.finalDate.getTime();if(n<t&&s>n){o.style.removeProperty("display");let e=s-n,i=s-t,r=Math.round(100*i/e)+"%";c.style.width=r}else o.style.display="none"}}function H(e){if(e.dataset.ecomCountdown){if(e.dataset.ecomCountdownFrom&&(new Date).getTime()>new Date(e.dataset.ecomCountdown).getTime()&&r)return e.closest(".ecom-collection__product-countdown-wrapper").style.display="none",!1;window.EComCountdown&&window.EComCountdown(e,new Date(e.dataset.ecomCountdown),M),e.addEventListener("stoped.ecom.countdown",()=>{e.closest(".ecom-collection__product-countdown-wrapper").style.display="none"})}}if(c.querySelectorAll(".ecom-collection__product-countdown-time").forEach((function(e){H(e)})),r){const e=c.querySelector(".ecom-collection__product-main");let t=1;const o=function(e){e.preventDefault();const o=this.dataset.get,i=this.closest(".ecom-sections[data-section-id]"),n=c.closest(".ecom-row.ecom-section");if(!o||!i||!i.dataset.sectionId)return;const r=`${o}&section_id=${i.dataset.sectionId}`;t++,q(!0,t),this.classList.add("ecom-loading"),s(r,i,this,"loadmore",n)},n=function(e){var t,o;t=e,o={},new IntersectionObserver((e,n)=>{e.forEach(e=>{e.isIntersecting&&(o.cb?o.cb(t):function(e){const t=e.dataset.get,o=e.closest(".ecom-sections[data-section-id]"),n=e.closest(".ecom-row.ecom-section");if(!t||!o||!o.dataset.sectionId)return;const r=o.dataset.sectionId,l=`${t}&section_id=${r}`;i&&(c.classList.add("ecom-doing-scroll"),s(l,o,e,"infinite",n))}(e.target),n.unobserve(e.target))})},o).observe(t)},s=function(t,o,s,r,l){i=!1,async function(e){return(await fetch(e,{method:"GET",cache:"no-cache",headers:{"Content-Type":"text/html"}})).text()}(t).then((function(t){const o=document.createElement("div");o.innerHTML=t;const c=o.querySelector(".ecom-collection__product-main.ecom-collection_product_template_collection .ecom-collection__product--wrapper-items");if(!c)return;const i=l.querySelector(".ecom-collection__product--wrapper-items"),a=l.querySelector(".ecom-products-pagination-loadmore");for(;c.firstChild;)i.appendChild(c.firstChild);if(c.parentNode.removeChild(c),"loadmore"===r){const e=o.querySelector(".ecom-products-pagination-loadmore");e?a.innerHTML=e.innerHTML:a.remove()}else{s.remove();const e=o.querySelector(".ecom-products-pagination-infinite");e&&(i.after(e),n(e))}e.dispatchEvent(new CustomEvent("ecom-products-init",{detail:{wrapper:e}}))})).finally((function(){window.EComposer&&window.EComposer.initQuickview(),i=!0,c.classList.remove("ecom-doing-scroll"),s.classList.remove("ecom-loading")}))};if(e&&e.dataset.pagination){const t=e.dataset.pagination;if("loadmore"===t)c.querySelector(".ecom-products-pagination-loadmore-btn")&&c.querySelector(".ecom-products-pagination-loadmore-btn").addEventListener("click",o);else if("infinit"===t){const e=c.querySelector(".ecom-products-pagination-infinite");e&&n(e)}}e.addEventListener("ecom-products-init",(function(t){const i=t.detail.wrapper;if(!i)return;if(e&&e.dataset.pagination){const t=e.dataset.pagination;if("loadmore"===t)c.querySelector(".ecom-products-pagination-loadmore-btn")&&c.querySelector(".ecom-products-pagination-loadmore-btn").addEventListener("click",o);else if("infinit"===t){const e=c.querySelector(".ecom-products-pagination-infinite");e&&n(e)}}i.querySelectorAll(".ecom-collection__product-variants:not(.ecom-swatch-init)").length&&i.querySelectorAll(".ecom-collection__product-variants:not(.ecom-swatch-init)").forEach(L),i.querySelectorAll(".ecom-collection__product-countdown-time").length&&i.querySelectorAll(".ecom-collection__product-countdown-time").forEach((function(e){H(e)})),A(i);let s=i.querySelectorAll(".ecom-collection__product-item:not([data-init-quantity='true'])");s.length&&S(s),i.querySelector(".ecom-products-pagination-loadmore-btn")&&i.querySelector(".ecom-products-pagination-loadmore-btn").addEventListener("click",o),window.EComposer&&"function"==typeof window.EComposer.init&&window.EComposer.init(),I(i);P(i.querySelector(".ecom-collection__product--wishlist-wrapper"))}))}function I(e){if(e&&e.dataset.reviewPlatform)switch(e.dataset.reviewPlatform){case"product-reviews":if(window.SPR)try{window.SPR.$=window.jQuery,window.SPR.initDomEls(),window.SPR.loadBadges()}catch(e){console.info(e.message)}break;case"judgeme":if(window.jdgm){try{window.jdgm.batchRenderBadges()}catch(e){console.info(e.message)}c.querySelectorAll('[data-average-rating="0.00"]').forEach((function(e){e.style.display="block !important"}))}break;case"product-reviews-addon":window.StampedFn&&window.StampedFn.loadBadges();break;case"lai-reviews":void 0!==window.SMARTIFYAPPS&&window.SMARTIFYAPPS.rv.installed&&window.SMARTIFYAPPS.rv.scmReviewsRate.actionCreateReviews();break;case"air-reviews":"function"==typeof window.avadaAirReviewRerender&&window.avadaAirReviewRerender()}}function P(e){if(e)switch(e.dataset.wishlistApp){case"swym-relay":window._swat&&window._swat.initializeActionButtons(".ecom-collection__product-wishlist-button");break;case"wishlist-hero":c.querySelectorAll(".wishlist-hero-custom-button").forEach((function(e){var t=new CustomEvent("wishlist-hero-add-to-custom-element",{detail:e});document.dispatchEvent(t)}))}}if(!r){I(c.querySelector(".ecom-collection__product-main"));P(c.querySelector(".ecom-collection__product--wishlist-wrapper"))}this.settings.enable_preload&&c.querySelectorAll(".ecom-collection__product-item").forEach((function(e){e.addEventListener("mouseenter",(function(){let e=document.createElement("link");e.rel="prefetch",document.head.appendChild(e);var t=this.querySelector("a.ecom-collection__product-item-information-title").getAttribute("href");e.href=t}),{once:!0})}));this.settings.show_compare&&!r&&c.querySelectorAll(".ecom-product__compare-link").forEach((function(e){e.addEventListener("click",(function(){this.classList.contains("ecom-product__compare-link-added")?this.classList.remove("ecom-product__compare-link-added","ecom-button-active"):this.classList.add("ecom-product__compare-link-added","ecom-button-active")}))}));this.settings.show_wishlist&&!r&&c.querySelectorAll(".ecom-product__wishlist-link").forEach((function(e){e.addEventListener("click",(function(){this.classList.contains("ecom-product__wishlist-link-added")?this.classList.remove("ecom-product__wishlist-link-added","ecom-button-active"):this.classList.add("ecom-product__wishlist-link-added","ecom-button-active")}))}));if("recommendations"===this.settings.show_product_by&&r){let e=c.closest(".ecom-builder");if(e){let t=e.querySelector(".ecom-sections").dataset.sectionId,o=e.querySelector('input[name="product-id"]')?e.querySelector('input[name="product-id"]').value:"",i=8,s=c.querySelector(".ecom-collection__product-container"),r=c.querySelector(".ecom-collection__product-main");r.classList.contains("ecom-collection_product_template_product")&&"recommendations"===this.settings.show_product_by&&(i=this.settings.limit_recommended_products),fetch(`${window.Shopify.routes.root}recommendations/products?product_id=${o}&limit=${i}&section_id=${t}`).then(e=>e.text()).then(e=>{const o=document.createElement("div");o.innerHTML=e;const c=o.querySelector(`[data-section-id="${t}"]`),i=c.querySelector(".ecom-block."+n);if(!i)return void console.warn(`Block with ID ${n} not found in recommendations.`);const l=i.querySelector(".ecom-collection__product-main");c.innerHTML.trim().length&&r&&(r.innerHTML=l.innerHTML,r.querySelector(".ecom-collection__product--wrapper-items")&&r.dispatchEvent(new CustomEvent("ecom-products-init",{detail:{wrapper:r}})),s.dispatchEvent(new CustomEvent("ecom-products-init-slider",{detail:{wrapper:s}})))}).catch(e=>{console.error(e)})}}};document.querySelectorAll(".ecom-codw20hf0w4").forEach((function(t){e.call({$el:t,id:"ecom-codw20hf0w4",settings:{show_featured_media:!0,bage_sale:"Save {{sale}}",price_type:"first_price",sale_badge_type:"amount",slider_speed:200,layout:"slider",enable_preload:!1,show_compare:!1,show_wishlist:!1},isLive:!0})})),document.querySelectorAll(".ecom-tddmbs31vqq").forEach((function(t){e.call({$el:t,id:"ecom-tddmbs31vqq",settings:{show_featured_media:!0,bage_sale:"Save {{sale}}",price_type:"first_price",sale_badge_type:"amount",slider_speed:200,layout:"slider",enable_preload:!1,show_compare:!1,show_wishlist:!1},isLive:!0})})),document.querySelectorAll(".ecom-u6zslnflk8h").forEach((function(t){e.call({$el:t,id:"ecom-u6zslnflk8h",settings:{show_featured_media:!0,bage_sale:"Save {{sale}}",price_type:"first_price",sale_badge_type:"amount",slider_speed:200,layout:"slider",enable_preload:!1,show_compare:!1,show_wishlist:!1},isLive:!0})})),document.querySelectorAll(".ecom-7jls8nlkouu").forEach((function(t){e.call({$el:t,id:"ecom-7jls8nlkouu",settings:{show_featured_media:!0,bage_sale:"Save {{sale}}",price_type:"first_price",sale_badge_type:"amount",slider_speed:200,layout:"slider",enable_preload:!1,show_compare:!1,show_wishlist:!1,show_product_by:"condition",limit_recommended_products:8},isLive:!0})})),document.querySelectorAll(".ecom-fkeu614q7uv").forEach((function(t){e.call({$el:t,id:"ecom-fkeu614q7uv",settings:{show_featured_media:!0,bage_sale:"Save {{sale}}",price_type:"first_price",sale_badge_type:"amount",slider_speed:200,layout:"slider",enable_preload:!1,show_compare:!1,show_wishlist:!1,show_product_by:"condition",limit_recommended_products:8},isLive:!0})})),document.querySelectorAll(".ecom-5qdsg2bx02v").forEach((function(t){e.call({$el:t,id:"ecom-5qdsg2bx02v",settings:{show_featured_media:!0,bage_sale:"Save {{sale}}",price_type:"first_price",sale_badge_type:"amount",slider_speed:200,layout:"slider",enable_preload:!1,show_compare:!1,show_wishlist:!1,show_product_by:"condition",limit_recommended_products:8},isLive:!0})})),document.querySelectorAll(".ecom-x1dvih34h2").forEach((function(t){e.call({$el:t,id:"ecom-x1dvih34h2",settings:{show_featured_media:!0,bage_sale:"Save {{sale}}",price_type:"first_price",sale_badge_type:"amount",slider_speed:200,layout:"slider",enable_preload:!1,show_compare:!1,show_wishlist:!1,show_product_by:"condition",limit_recommended_products:8},isLive:!0})}))}();