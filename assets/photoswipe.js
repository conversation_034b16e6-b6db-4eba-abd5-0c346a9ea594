import '@archetype-themes/vendors/photoswipe.min'
import '@archetype-themes/vendors/photoswipe-ui-default.min'
let selectors={trigger:'.js-photoswipe__zoom',images:'.photoswipe__image',slideshowTrack:'.flickity-viewport ',activeImage:'.is-selected'}
export default class Photoswipe{constructor(container,sectionId){this.container=container
this.sectionId=sectionId
this.namespace='.photoswipe-'+this.sectionId
this.gallery
this.images
this.items
this.inSlideshow=!1
if(!container||container.dataset.zoom==='false'){return}
this.init()}
init(){this.container.querySelectorAll(selectors.trigger).forEach((trigger)=>{trigger.addEventListener('click',this.triggerClick.bind(this))})}
triggerClick(evt){if(this.container.dataset&&this.container.dataset.hasSlideshow==='true'){this.inSlideshow=!0}else{this.inSlideshow=!1}
this.items=this.getImageData()
let image=this.inSlideshow?this.container.querySelector(selectors.activeImage):evt.currentTarget
let index=this.inSlideshow?this.getChildIndex(image):image.dataset.index
this.initGallery(this.items,index)}
getChildIndex(el){let i=0
while((el=el.previousSibling)!=null){i++}
return i+1}
getImageData(){this.images=this.inSlideshow?this.container.querySelectorAll(selectors.slideshowTrack+selectors.images):this.container.querySelectorAll(selectors.images)
let items=[]
let options={}
this.images.forEach((el)=>{let item={msrc:el.currentSrc||el.src,src:el.getAttribute('data-photoswipe-src'),w:el.getAttribute('data-photoswipe-width'),h:el.getAttribute('data-photoswipe-height'),el:el,initialZoomLevel:0.5}
items.push(item)})
return items}
initGallery(items,index){document.body.classList.add('photoswipe-open')
let pswpElement=document.querySelectorAll('.pswp')[0]
let options={allowPanToNext:!1,captionEl:!1,closeOnScroll:!1,counterEl:!1,history:!1,index:index-1,pinchToClose:!1,preloaderEl:!1,scaleMode:'zoom',shareEl:!1,tapToToggleControls:!1,getThumbBoundsFn:function(index){let pageYScroll=window.pageYOffset||document.documentElement.scrollTop
let thumbnail=items[index].el
let rect=thumbnail.getBoundingClientRect()
let imageAspectRatio=thumbnail.naturalWidth/thumbnail.naturalHeight
let containerAspectRatio=rect.width/rect.height
let displayedWidth,displayedHeight,offsetX,offsetY
if(imageAspectRatio>containerAspectRatio){displayedWidth=rect.width
displayedHeight=rect.width/imageAspectRatio
offsetX=0
offsetY=(rect.height-displayedHeight)/2}else{displayedHeight=rect.height
displayedWidth=rect.height*imageAspectRatio
offsetX=(rect.width-displayedWidth)/2
offsetY=0}
return{x:rect.left+offsetX,y:rect.top+offsetY+pageYScroll,w:displayedWidth}}}
this.gallery=new PhotoSwipe(pswpElement,PhotoSwipeUI_Default,items,options)
this.gallery.listen('afterChange',this.afterChange.bind(this))
this.gallery.listen('afterInit',this.afterInit.bind(this))
this.gallery.init()
this.preventiOS15Scrolling()}
afterChange(){let index=this.gallery.getCurrentIndex()
this.container.dispatchEvent(new CustomEvent('photoswipe:afterChange',{detail:{index:index}}))}
afterInit(){this.container.dispatchEvent(new CustomEvent('photoswipe:afterInit'))}
syncHeight(){document.documentElement.style.setProperty('--window-inner-height',`${window.innerHeight}px`)}
preventiOS15Scrolling(){let initialScrollPos
if(!/iPhone|iPad|iPod/i.test(window.navigator.userAgent))return
this.syncHeight()
initialScrollPos=window.scrollY
document.documentElement.classList.add('pswp-open-in-ios')
window.addEventListener('resize',this.syncHeight)
this.gallery.listen('destroy',()=>{document.documentElement.classList.remove('pswp-open-in-ios')
window.scrollTo(0,initialScrollPos)})}}