import{Slideshow}from '@archetype-themes/modules/slideshow'
import{HTMLThemeElement}from '@archetype-themes/custom-elements/theme-element'
class Testimonials extends HTMLThemeElement{connectedCallback(){super.connectedCallback()
this.defaults={adaptiveHeight:!0,avoidReflow:!0,pageDots:!0,prevNextButtons:!1}
this.timeout
this.slideshow=this.querySelector(`#Testimonials-${this.sectionId}`)
this.namespace=`.testimonial-${this.sectionId}`
if(!this.slideshow){return}
this.init()}
init(){if(this.slideshow.dataset.count<=3){this.defaults.wrapAround=!1}
this.flickity=new Slideshow(this.slideshow,this.defaults)
if(this.slideshow.dataset.count>2){this.timeout=setTimeout(function(){this.flickity.goToSlide(1)}.bind(this),1000)}}
disconnectedCallback(){super.disconnectedCallback()
if(this.flickity&&typeof this.flickity.destroy==='function'){this.flickity.destroy()}}
onSectionDeselect(){if(this.flickity&&typeof this.flickity.play==='function'){this.flickity.play()}}
onBlockSelect({detail:{blockId}}){const slide=this.slideshow.querySelector(`.testimonials-slide--${blockId}`)
const index=parseInt(slide.dataset.index)
clearTimeout(this.timeout)
if(this.flickity&&typeof this.flickity.pause==='function'){this.flickity.goToSlide(index)
this.flickity.pause()}}
onBlockDeselect(){if(this.flickity&&typeof this.flickity.play==='function'){this.flickity.play()}}}
customElements.define('testimonials-component',Testimonials)