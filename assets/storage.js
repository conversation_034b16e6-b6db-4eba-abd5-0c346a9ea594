export function hasLocalStorage(){const testKey='localStorageTestKey'
const testValue='test'
try{localStorage.setItem(testKey,testValue)
localStorage.removeItem(testKey)
return!0}catch(error){return!1}}
export function setLocalStorage(key,value,expiryDays){if(!hasLocalStorage())return!1
const now=new Date()
const item={value:value,expiry:now.getTime()+expiryDays*24*60*60*1000,}
try{localStorage.setItem(key,JSON.stringify(item))
return!0}catch(error){return!1}}
export function getLocalStorage(key){if(!hasLocalStorage())return null
try{const itemStr=localStorage.getItem(key)
if(!itemStr)return null
const item=JSON.parse(itemStr)
if(item&&typeof item==='object'&&'value' in item&&'expiry' in item){if(Date.now()>item.expiry){localStorage.removeItem(key)
return null}
return item.value}
return item}catch(error){return null}}