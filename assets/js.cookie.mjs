function assign(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}var e={read:function(e){'"'===e[0]&&(e=e.slice(1,-1));return e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function init(e,t){function set(n,r,i){if("undefined"!==typeof document){i=assign({},t,i);"number"===typeof i.expires&&(i.expires=new Date(Date.now()+864e5*i.expires));i.expires&&(i.expires=i.expires.toUTCString());n=encodeURIComponent(n).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var o="";for(var c in i)if(i[c]){o+="; "+c;true!==i[c]&&(o+="="+i[c].split(";")[0])}return document.cookie=n+"="+e.write(r,n)+o}}function get(t){if("undefined"!==typeof document&&(!arguments.length||t)){var n=document.cookie?document.cookie.split("; "):[];var r={};for(var i=0;i<n.length;i++){var o=n[i].split("=");var c=o.slice(1).join("=");try{var a=decodeURIComponent(o[0]);r[a]=e.read(c,a);if(t===a)break}catch(e){}}return t?r[t]:r}}return Object.create({set:set,get:get,remove:function(e,t){set(e,"",assign({},t,{expires:-1}))},withAttributes:function(e){return init(this.converter,assign({},this.attributes,e))},withConverter:function(e){return init(assign({},this.converter,e),this.attributes)}},{attributes:{value:Object.freeze(t)},converter:{value:Object.freeze(e)}})}var t=init(e,{path:"/"});export{t as default};

//# sourceMappingURL=js.cookie.mjs.map