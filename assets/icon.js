import '@archetype-themes/scripts/config'
import '@archetype-themes/scripts/helpers/sections'
class AtIcon extends HTMLElement{constructor(){super()
this.src=this.getAttribute('src')
this.name=this.getAttribute('data-name')}
connectedCallback(){if(!this.src)return
fetch(this.src).then((response)=>response.text()).then((svg)=>{if(svg.indexOf('<!DOCTYPE')>-1){return}
this.innerHTML=svg
this.querySelector('svg').classList.add('icon',`icon-${this.name}`)}).catch((error)=>{console.log('Error:',error)})}}
customElements.define('at-icon',AtIcon)