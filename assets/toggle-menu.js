import{EVENTS}from '@archetype-themes/utils/events'
class ToggleMenu extends HTMLElement{connectedCallback(){this.abortController=new AbortController()
this.isMobileNavOpen=!1
this.openTrigger=this.querySelector('[aria-controls="MobileNav"]')
if(!this.openTrigger)return
this.openTrigger.addEventListener('click',this.handleClick.bind(this),{signal:this.abortController.signal})
document.addEventListener(EVENTS.headerDrawerClosed,this.handleDrawerClosed.bind(this),{signal:this.abortController.signal})}
handleClick(evt){evt.preventDefault()
if(this.isMobileNavOpen){evt.target.dispatchEvent(new CustomEvent(EVENTS.mobileNavClose,{bubbles:!0}))
this.openTrigger.classList.remove('is-active')
this.isMobileNavOpen=!1}else{evt.target.dispatchEvent(new CustomEvent(EVENTS.mobileNavOpen,{bubbles:!0}))
this.openTrigger.classList.add('is-active')
this.isMobileNavOpen=!0}}
handleDrawerClosed(){this.isMobileNavOpen=!1
this.openTrigger.classList.remove('is-active')}
disconnectedCallback(){this.abortController.abort()}}
customElements.define('toggle-menu',ToggleMenu)