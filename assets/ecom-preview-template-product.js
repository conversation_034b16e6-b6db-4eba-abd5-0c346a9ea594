!function(){const e=function(){"use strict";window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-3ipkpkrm5y9"]=window.__ectimmers["ecom-3ipkpkrm5y9"]||{};let e=this.$el;if(e&&(!this.isLive||e.closest(".ecom-quickview"))){let t=e.querySelector(".ecom-product-single__rating-wrapper");if(t&&t.dataset.reviewPlatform)switch(t.dataset.reviewPlatform.trim()){case"product-reviews":if(!window.$){let e=document.createElement("script");e.src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js",e.setAttribute("integrity","sha512-3gJwYpMe3QewGELv8k/BX9vcqhryRdzRMxVfq6ngyWXwo03GFEzjsUm8Q7RZcHPHksttq7/GFoxjCVUjkjvPdw=="),e.setAttribute("crossorigin","anonymous"),e.setAttribute("referrerpolicy","no-referrer"),document.querySelector("body").appendChild(e),setTimeout((function(){if(window.SPR)try{window.SPR.$=window.jQuery,window.SPR.initDomEls(),window.SPR.loadBadges()}catch(e){console.info(e.message)}}),300);break}if(window.SPR)try{window.SPR.$=window.jQuery,window.SPR.initDomEls(),window.SPR.loadBadges()}catch(e){console.info(e.message)}break;case"judgeme":if(window.jdgm){try{window.jdgm.batchRenderBadges()}catch(e){console.info(e.message)}e.querySelectorAll('[data-average-rating="0.00"]').forEach((function(e){e.style.display="block !important"}))}break;case"product-reviews-addon":window.StampedFn&&window.StampedFn.loadBadges()}}};document.querySelectorAll(".ecom-3ipkpkrm5y9").forEach((function(t){e.call({$el:t,id:"ecom-3ipkpkrm5y9",settings:{},isLive:!0})}))}(),function(){const e=function(){"use strict";var e,t;window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-pd5pb8cfgwo"]=window.__ectimmers["ecom-pd5pb8cfgwo"]||{};let o=this.$el;if(!o)return;let i=o.querySelector("afterpay-placement.ecom-afterpay-integrate"),n=this.$el.closest(".ecom-product-form--single");if(n){let e=n.querySelector('[name="id"]');if(e&&e.dispatchEvent(new window.Event("ecomUpdate")),this.settings.show_total_price){const e=n.querySelector(".ecom-product-single__quantity-input"),t=o.querySelector(".ecom-product-single__price--sale");if(!t)return;e&&e.addEventListener("change",(function(){t.innerHTML=window.EComposer.formatMoney(parseInt(t.getAttribute("data-price"))*parseInt(this.value))})),n.addEventListener("ecomVariantChange",(function(o){if(o.detail.variant){const i=o.detail.variant,n=e?parseInt(e.value):1;t.setAttribute("data-price",i.price),n>1&&(t.innerHTML=window.EComposer.formatMoney(i.price*parseInt(n)))}}))}}if(this.settings.enable_afterpay){let o=window.document.querySelector("#ecom-after-pay-script");if(window.afterpay_min=this.settings.lower_limit||1,o)o.dataset.analyticsEnabled=!0,o.dataset.min=null!=(e=this.settings.lower_limit)?e:1,o.dataset.max=null!=(t=this.settings.upper_limit)?t:1e3;else{let e=window.document.createElement("script");e.id="ecom-after-pay-script",e.dataset.analyticsEnabled=!0,e.dataset.min=this.settings.lower_limit||1,e.dataset.max=this.settings.upper_limit||1e3,e.src="https://js.afterpay.com/afterpay-1.x.js",window.document.head.appendChild(e)}i&&this.isLive&&window.jQuery&&window.Afterpay&&window.Afterpay.initProductPage&&window.Afterpay.initProductPage(window.jQuery)}let c=o.querySelector(".ecom-product-single__price--badges-sale");if(c&&"true"==c.dataset.haveSale){let e=c.dataset.sale,t=c.dataset.text;t=t.replace(/\{.*\}/g,e),c.innerHTML=t,c.style.display="block"}};document.querySelectorAll(".ecom-pd5pb8cfgwo").forEach((function(t){e.call({$el:t,id:"ecom-pd5pb8cfgwo",settings:{enable_afterpay:!1,lower_limit:"100",upper_limit:"600"},isLive:!0})}))}(),function(){const e=function(){"use strict";if(window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-unwd1epxyr"]=window.__ectimmers["ecom-unwd1epxyr"]||{},!this.$el)return;const e=this.$el,t=e.querySelector(".ecom-text_view-more-btn"),o=e.querySelector(".ecom-text_view-less-btn"),i=e.querySelector(".text-content.ecom-html");!i||(t&&t.addEventListener("click",()=>{i.classList.remove("ecom-text--is-mark"),i.style.maxHeight="",t.style.display="none",o.style.display=""}),o&&o.addEventListener("click",()=>{i.classList.add("ecom-text--is-mark"),i.style.maxHeight="var(--ecom-text-height)",o.style.display="none",t.style.display=""}))};document.querySelectorAll(".ecom-unwd1epxyr").forEach((function(t){e.call({$el:t,id:"ecom-unwd1epxyr",settings:{},isLive:!0})})),document.querySelectorAll(".ecom-wz6ba4unx5").forEach((function(t){e.call({$el:t,id:"ecom-wz6ba4unx5",settings:{},isLive:!0})})),document.querySelectorAll(".ecom-nzijpe9f4p").forEach((function(t){e.call({$el:t,id:"ecom-nzijpe9f4p",settings:{},isLive:!0})})),document.querySelectorAll(".ecom-5b7yzmzigia").forEach((function(t){e.call({$el:t,id:"ecom-5b7yzmzigia",settings:{},isLive:!0})})),document.querySelectorAll(".ecom-5qtejsfswj").forEach((function(t){e.call({$el:t,id:"ecom-5qtejsfswj",settings:{},isLive:!0})}))}(),function(){const e=function(){"use strict";if(window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-hieo3l4qaf"]=window.__ectimmers["ecom-hieo3l4qaf"]||{},!this.$el)return!1;const e=this.$el,t=e.closest(".ecom-product-form--single");if(!t)return!1;if(t&&this.settings.show_total_price){let o=function(e){n.innerHTML=window.EComposer.formatMoney(parseInt(n.getAttribute("data-price"))*parseInt(e.value))},i=function(e){setTimeout(()=>{o(e)},100),e.addEventListener("change",(function(){o(e)}))};const n=e.querySelector(".ecom-product-single__price--sale");let c=t.querySelector("#quantity");if(!n||!c)return;i(c),t.addEventListener("ecomQuantityType",(function(){c=t.querySelector("#quantity"),c&&i(c)})),t.addEventListener("ecomVariantChange",(function(e){if(e.detail.variant){const t=e.detail.variant,o=c?parseInt(c.value):1;n.setAttribute("data-price",t.price),o>1&&(n.innerHTML=window.EComposer.formatMoney(t.price*parseInt(o)))}}))}const o=t.querySelector('select[name="id"]'),i=e.querySelector(".ecom-product-single__add-to-cart--submit"),n=e.closest("form");if(!n)return;function c(){let t=null;const o=n.dataset.product_id;let c=null;if(c=o?n.querySelector("[id^=product-json-"+o+"]"):e.querySelector("[id^=addtocart-product-json]"),!c)return;try{t=JSON.parse(c.innerHTML)}catch(e){return}let r=t.variants[0];if(r){const e=i.querySelector(".ecom-add-to-cart-text");if(!e)return;0==r.available?(i.setAttribute("disabled","disabled"),e.innerText=i.dataset.textOutstock,i.classList.remove("ecom-product-single__pre-order")):r.inventory_quantity<=0&&"continue"==r.inventory_policy&&r.inventory_management&&(e.innerText=i.dataset.textPreOrder,i.classList.add("ecom-product-single__pre-order"))}}if(this.isLive){let r=function(e){const o=t.querySelectorAll(".ecom-required--checkbox");o.length>0&&o.forEach((function(e){let t=e.querySelectorAll("input[type=checkbox]");if(0==t.length)return;let o=!1;t.forEach((function(e){e.checked&&(o=!0)})),o?t.forEach((function(e){e.required=!1})):t.forEach((function(e){e.required=!0}))}));const i=t.querySelectorAll(".ecom-product-single__property-file");let n,c=!1;i.length&&i.forEach((function(e){if(e.required&&(!e.value||""==e.value))return e.parentNode.parentNode.querySelector(".ecom-product-single-form--text-error").style.display="block",c=!0,void(n=e);c=!1})),c&&(e.preventDefault(),e.stopPropagation(),n.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"}))};if((!n.querySelector("select[name=id]")||n.querySelector("select[name=id]")&&o.classList.contains("ecom-product-single__picker-default-variant"))&&c(),!o&&i&&i.dataset.variant_id){const t=document.createElement("input");t.type="hidden",t.value=i.dataset.variant_id,e.appendChild(t)}i.addEventListener("click",r)}else t&&(o&&!o.classList.contains("ecom-product-single__picker-default-variant")?o.dispatchEvent(new window.Event("ecomUpdate")):c());this.settings.animation&&function(t){if(!e)return;const o=e.querySelector(".ecom-product-single__add-to-cart--submit");if(!o)return;let i=1e3*parseInt(t.settings.animation_loop_time)||6e3;window.__ectimmers["ecom-hieo3l4qaf"]["3zj51a5kp"]=setInterval((function(){o.classList.add("animated"),setTimeout((function(){o.classList.remove("animated")}),1e3)}),i)}(this)};document.querySelectorAll(".ecom-hieo3l4qaf").forEach((function(t){e.call({$el:t,id:"ecom-hieo3l4qaf",settings:{animation:!1},isLive:!0})}))}(),function(){const e=function(){"use strict";if(window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-x0ozz60h1ii"]=window.__ectimmers["ecom-x0ozz60h1ii"]||{},!this.$el)return!1;const e=this.$el;let t=e.closest(".ecom-product-form--single");if(!t){const o=e.closest(".ecom-builder");o&&(t=o.querySelector(".ecom-product-form--single"))}if(!t)return!1;if(this.isLive){let o=t.querySelector("select[name=id]");const i=e.querySelector(".ecom-product-single__buy_it_now_btn--checkout"),n=t.dataset.product_id;if(!n)return;let c=null;const r=t.querySelector("[id^=product-json-"+n+"]");if(!r)return;try{c=JSON.parse(r.innerHTML)}catch(e){return}i&&i.addEventListener("click",(function(e){e.preventDefault();let o=1;t.querySelector("[name=quantity]")&&(o=t.querySelector("[name=quantity]").value);let i=t.querySelector("select[name=id]")||t.querySelector("input[name=id]");i||console.warn("EC error: Select id or input id not found!");let n=i.value,r=c.variants.find(e=>e.id==n);if(!r||!1===r.available)return;window.Shopify.analytics&&Shopify.analytics.publish("ec_buy_now",{cartLine:{cost:{totalAmount:{amount:r.price*o,currencyCode:window.Shopify.currency.active}},merchandise:{id:r.id,image:r.image,price:{amount:r.price,currencyCode:window.Shopify.currency.active},product:{id:c.id,title:c.title,vendor:c.vendor},sku:r.sku,title:r.title},quantity:o}});let s=`${window.EComposer.routes.cart_url}/${n}:${o}`;if(window.location.search.includes("selling_plan")){const e=/selling_plan=(\d+)/,t=window.location.search.match(e);if(t){const e=t[1];s=`${window.EComposer.routes.cart_url}/clear?return_to=/cart/add?items[][id]=${n}%26items[][quantity]=${o}%26items[][selling_plan]=${e}%26return_to=/checkout`}}location.href=s})),c.available||(i.classList.add("ecom-disable-buy"),i.disabled=!0),o&&o.addEventListener("change",(function(){let e=this.value;e&&(!1===c.variants.find(t=>t.id==e).available?(i.classList.add("ecom-disable-buy"),i.disabled=!0):(i.classList.remove("ecom-disable-buy"),i.disabled=!1))}))}this.settings.animation&&function(t){if(!e)return;const o=e.querySelector(".ecom-product-single__buy_it_now_btn--checkout");if(!o)return;let i=1e3*parseInt(t.settings.animation_loop_time)||6e3;window.__ectimmers["ecom-x0ozz60h1ii"].oiy5x3xc3=setInterval((function(){o.classList.add("animated"),setTimeout((function(){o.classList.remove("animated")}),1e3)}),i)}(this)};document.querySelectorAll(".ecom-x0ozz60h1ii").forEach((function(t){e.call({$el:t,id:"ecom-x0ozz60h1ii",settings:{animation:!1},isLive:!0})}))}(),function(){const e=function(){"use strict";var e,t,o,i;if(window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-8zx3hp8buzx"]=window.__ectimmers["ecom-8zx3hp8buzx"]||{},!this.$el)return;const n=this.$el.querySelector(".ecom-product-single__description-view-more-btn");if("tab"===this.settings.type){const e=this.$el.querySelectorAll(".ecom-product-description-tab__item"),t=this.$el.querySelectorAll(".ecom-product-description-tab__content");e.length&&e.forEach((o,i)=>{o.onclick=function(){this.classList&&!this.classList.contains("ecom-item-active")&&(e.forEach(e=>e.classList.remove("ecom-item-active")),t.forEach(e=>e.classList.remove("ecom-item-active")),o.classList.add("ecom-item-active"),t[i].classList.add("ecom-item-active"))}})}if("accordion"===this.settings.type){let n=function(e){e.classList.remove("ecom-item-active"),e.querySelector(".ecom-accordion__body").style.height="0"},c=function(e){e.forEach((e,t)=>{let o=e.parentNode,i=o.querySelector(".ecom-accordion__body");!a&&i&&i.classList.add("ecom-effect-accodion"),0==t&&o.querySelector(".ecom-item-active")?i.style.height=a?"auto":i.clientHeight+"px":i.style.height=0,e.onclick=function(t){t.preventDefault();let i=this.parentNode,c=o.parentNode,d=c.querySelectorAll(".ecom-product-description__accordion-item"),u=c.querySelectorAll(".ecom-product-description__accordion-title");if(this.classList&&this.classList.contains("ecom-item-active"))l&&innerWidth<768||s&&innerWidth>767&&innerWidth<1025||r&&innerWidth>1024?(this.classList.remove("ecom-item-active"),o.querySelector(".ecom-accordion__body").style.height="0px",o.classList.remove("ecom-item-active")):(d.forEach(e=>n(e)),u.forEach(e=>e.classList.remove("ecom-item-active")));else{l&&innerWidth<768||s&&innerWidth>767&&innerWidth<1025||r&&innerWidth>1024||(d.forEach(e=>n(e)),u.forEach(e=>e.classList.remove("ecom-item-active"))),e.classList.add("ecom-item-active"),i.classList.add("ecom-item-active");let t=e.parentNode.querySelector(".ecom-accordion__body"),o="auto";a||(o=t.scrollHeight+"px",t.classList.add("ecom-effect-accodion")),setTimeout(()=>{t.style.height=o},20)}}})};const r=null!=(e=this.settings.disable_auto_close)&&e,s=null!=(t=this.settings.disable_auto_close__tablet)&&t,l=null!=(o=this.settings.disable_auto_close__mobile)&&o,a=null!=(i=this.settings.disable_effect)&&i;c(this.$el.querySelectorAll(".ecom-product-description__accordion-item > .ecom-product-description__accordion-title"))}const c=this.settings.content_type,r=this.$el.querySelector(".ecom-product-single__description--full"),s=this.$el.querySelector(".ecom-product-single__description-view-less-btn"),l=this.$el.querySelector(".ecom-product-single__description--paragraph .ecom-text-des"),a=this.$el.querySelector(".ecom-product-single__description--paragraph .ecom-html-des");n&&(n&&n.addEventListener("click",(function(){"text"===c&&r?(r.style.display="block",l.style.display="none"):a.style.maxHeight=null,this.style.display="none",s&&(s.style.display="flex")})),s&&s.addEventListener("click",(function(){n.style.display="flex",this.style.display="none","text"===c&&r?(r&&(r.style.display="none"),l.style.display="block"):a.style.maxHeight="var(--ecom-description-height)"})))};document.querySelectorAll(".ecom-8zx3hp8buzx").forEach((function(t){e.call({$el:t,id:"ecom-8zx3hp8buzx",settings:{type:"full",disable_effect:!1,content_type:"html"},isLive:!0})}))}(),function(){const e=function(){"use strict";window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-z9oxkn0r0y"]=window.__ectimmers["ecom-z9oxkn0r0y"]||{};let e=this.$el.querySelector(".ecom-product-single__review-wrapper");if(e&&e.dataset.reviewPlatform&&!this.isLive)switch(e.dataset.reviewPlatform){case"product-reviews":if(window.SPR)try{window.SPR.$=window.jQuery,window.SPR.initDomEls(),window.SPR.loadProducts()}catch(e){console.info(e.message)}break;case"judgeme":window.jdgm&&"function"==typeof window.jdgm.initializeWidgets&&window.jdgm.initializeWidgets();break;case"product-reviews-addon":window.StampedFn&&window.StampedFn.init();break;case"yotpo-social-reviews":break;case"lai-reviews":"undefined"!=typeof SMARTIFYAPPS&&window.SMARTIFYAPPS.rv.installed&&window.SMARTIFYAPPS.rv.scmReviewsRate.actionCreateReviews()}};document.querySelectorAll(".ecom-z9oxkn0r0y").forEach((function(t){e.call({$el:t,id:"ecom-z9oxkn0r0y",settings:{},isLive:!0})}))}(),function(){const e=function(){"use strict";window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-ji68axy0gmj"]=window.__ectimmers["ecom-ji68axy0gmj"]||{};let e=this.$el;if(!e)return;let t=e.querySelectorAll(":scope > .tabs__wrapper > .tabs__navs > .tabs__navs--items > .tabs__nav"),o=e.querySelectorAll(":scope > .tabs__wrapper > .core__group--items > .tab__item"),i=this.settings.action;function n(){let t=window.location.hash;if(t){let o=e.querySelector(`[data-target="${t}"]`);if(o){o.click();let e=new MouseEvent("mouseover",{bubbles:!0,cancelable:!0,view:window});o.dispatchEvent(e);let t=o.getBoundingClientRect().top+window.pageYOffset-window.innerHeight/2;window.scrollTo(0,t)}}}function c(){window.dispatchEvent(new window.Event("resize")),setTimeout(()=>{window.dispatchEvent(new window.Event("resize"))},500)}t.forEach((e,n)=>{"click"===i||window.screen.width<1025?e.onclick=function(){this.classList&&this.classList.contains("ecom-item-active")?(t.forEach(e=>e.classList.remove("ecom-item-active")),o.forEach(e=>e.classList.remove("ecom-item-active"))):(t.forEach(e=>e.classList.remove("ecom-item-active")),o.forEach(e=>e.classList.remove("ecom-item-active")),e.classList.add("ecom-item-active"),o[n].classList.add("ecom-item-active")),c()}:e.onmouseover=function(){this.classList&&this.classList.contains("ecom-item-active")?(t.forEach(e=>e.classList.remove("ecom-item-active")),o.forEach(e=>e.classList.remove("ecom-item-active"))):(t.forEach(e=>e.classList.remove("ecom-item-active")),o.forEach(e=>e.classList.remove("ecom-item-active")),e.classList.add("ecom-item-active"),o[n].classList.add("ecom-item-active")),c()}}),setTimeout(()=>{n()},300),window.addEventListener("hashchange",n,!1)};document.querySelectorAll(".ecom-ji68axy0gmj").forEach((function(t){e.call({$el:t,id:"ecom-ji68axy0gmj",settings:{action:"click"},isLive:!0})}))}(),function(){const e=function(){"use strict";if(window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-pn2svqepxv"]=window.__ectimmers["ecom-pn2svqepxv"]||{},"lightbox"===this.settings.link&&"yes"===this.settings.lightbox&&window.EComModal&&this.$el){var e=this.$el.querySelector("[ecom-modal]");new window.EComModal(e,{cssClass:["ecom-container-lightbox-"+this.id]})}let t=this.$el;function o(){let e=t.querySelector(".ecom-element.ecom-base-image"),o=t.closest(".core__row--columns");e&&(function(e){const t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom-e.offsetHeight/2<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)}(e)?(e.classList.add("image-highlight"),o.setAttribute("style","z-index: unset")):(e.classList.remove("image-highlight"),o.setAttribute("style","z-index: 1")))}t&&this.settings.highligh_on_viewport&&window.addEventListener("scroll",(function(){o()}))};document.querySelectorAll(".ecom-pn2svqepxv").forEach((function(t){e.call({$el:t,id:"ecom-pn2svqepxv",settings:{link:"none",lightbox:"no",highligh_on_viewport:!1},isLive:!0})}))}(),function(){const e=function(){"use strict";window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-p1udaa47up8"]=window.__ectimmers["ecom-p1udaa47up8"]||{};const e=this.$el;if(!e)return;const t=this.isLive,o=!!this.settings.show_option_selected&&this.settings.show_option_selected,i=!!this.settings.history_state&&this.settings.history_state,n=this.settings.auto_variant_disable,c=this.settings.hide_soldout_variant,r=this.settings.hide_unavaiable_variant,s=this.settings.type,l=e.querySelector('[name="id"]'),a=e.closest(".ecom-product-form--single");if(!a)return;n&&"dropdown"!==s&&a.classList.add("ecom_auto_variant_disable");const d=e.querySelector(".ecom-product-single__variant-picker-container");let u=null;if(!l)return;const m=this.$el.querySelector("#"+l.dataset.jsonProduct);if(!m)return;let p=null;try{p=JSON.parse(m.innerHTML)}catch(e){return}function _(i){(function(e){if(a.classList.contains("ecom_auto_variant_disable")&&n&&null===e)return;const t=a.querySelector(".ecom-product-single__price--badges");if(t&&t.querySelectorAll("span").forEach((function(e){e.style.display="none"})),e)if(e.available&&e.price<e.compare_at_price){if(t&&t.querySelector(".ecom-product-single__price--badges-sale")){const o=t.querySelector(".ecom-product-single__price--badges-sale");o.style.display="block";let i=0;i=Math.round(100*(e.compare_at_price-e.price)/e.compare_at_price),"amount"===o.dataset.type&&(i=window.EComposer.formatMoney(e.compare_at_price-e.price));let n=o.dataset.text;n=n.replace(/\{.*\}/g,i),o.innerHTML=n}}else e.available||t&&(t.querySelector(".ecom-product-single__price--badges-sold-out").style.display="block")})(i),function(e){const t=a.querySelectorAll(".ecom-product-single__media--slider");if(t.length&&e)t.forEach((function(t){const o=t.querySelector(".ecom-product-single__media--featured");if(o){const t=o.querySelectorAll(".ecom-product-single__media--image");t&&t.length&&t.forEach(t=>{var i,n,c;if("featured"===o.getAttribute("data-priority"))return;const r=null==(i=t.dataset.variant_id)?void 0:i.split(",");if((null==r?void 0:r.length)&&r.includes(String(e.id))){const e=parseInt(t.dataset.index);e!=(null!=(c=null==(n=null==o?void 0:o.ec_splide)?void 0:n.index)?c:0)&&o&&o.ec_splide&&o.ec_splide.go(e)}})}}));else if(e&&e.featured_image){const t=a.querySelector(".ecom-product-single__media--single");if(t){const o=t.querySelector("img");o&&(o.setAttribute("src",e.featured_image.src),o.setAttribute("alt",e.featured_image.alt),o.setAttribute("srcset",e.featured_image.src))}}}(i),function(e){const t=a.querySelectorAll(".ecom-product-single__add-to-cart--submit");t.length&&t.forEach((function(t){if(a.classList.contains("ecom_auto_variant_disable")&&n)t.setAttribute("disabled","disabled");else if(e)e.available||null===e.inventory_management?(t.removeAttribute("disabled"),t.querySelector(".ecom-add-to-cart-text")&&(!e.inventory_management||e.inventory_management&&e.inventory_quantity>0?(t.querySelector(".ecom-add-to-cart-text").innerHTML=t.dataset.textAddCart,t.classList.remove("ecom-product-single__pre-order")):e.inventory_quantity<=0&&"continue"==e.inventory_policy&&(t.querySelector(".ecom-add-to-cart-text").innerHTML=t.dataset.textPreOrder,t.classList.add("ecom-product-single__pre-order")))):(t.setAttribute("disabled","disabled"),t.querySelector(".ecom-add-to-cart-text")&&(t.querySelector(".ecom-add-to-cart-text").innerHTML=t.dataset.textOutstock,t.classList.remove("ecom-product-single__pre-order")));else if(t.setAttribute("disabled","disabled"),t.querySelector(".ecom-add-to-cart-text")){let e=!1;d.querySelectorAll(".single-option-selector").forEach((function(t){""!==t.value||(e=!0)})),t.querySelector(".ecom-add-to-cart-text").innerHTML=e?t.dataset.textAddCart:t.dataset.textUnavailable}}))}(i),function(e){const t=a.querySelector(".ecom-product-single__quantity-input");if(t){const o=t&&t.dataset.minValue?parseInt(t.dataset.minValue):"",i=t&&t.dataset.maxValue?parseInt(t.dataset.maxValue):"";if(!e)return t.value=o&&o>0?o:1,void t.setAttribute("disabled","disabled");e.available?((!t.value||o&&o>0&&t.value<o)&&(t.value=o),t.removeAttribute("disabled","disabled")):(o&&o>0&&(t.value=o),t.setAttribute("disabled","disabled"));const n=e.inventory_quantity,c=e.inventory_policy;let r=i&&i>0?i:9999;e.inventory_management&&"deny"===c&&(r=i&&i>0&&i<n?i:n,(n<o||!t.value||o&&o>0&&t.value<o)&&(t.value=o)),n<1&&"continue"==c&&((!t.value||o&&o>0&&t.value<o)&&(t.value=o),r=i&&i>0?i:999999),(e&&n&&n>o||e&&"continue"==c)&&(e.inventory_management&&"deny"===c?r=i&&i>0&&i<n?i:n:e.inventory_management&&"continue"===c&&(r=i&&i>0?i:999999),t.value<o&&(t.value=o)),r<0&&(r=0);let s=parseInt(t.value);!o&&s>r&&(s=r),s=isNaN(s)||!s?1:s,!o&&!e.available&&(s=0),s=s>=0?s:1,t.value=s,t.setAttribute("max",r)}}(i),function(e){if((!a.classList.contains("ecom_auto_variant_disable")||!n)&&e&&e.options.length)for(let t=0;t<e.options.length;t++)a.querySelectorAll(`.ecom-product-single__swatch-item[data-option-index="${t}"][data-value="${e.options[t].replace(/'/g,"'").replace(/"/g,'\\"')}"]`).forEach(e=>{e.parentNode.childNodes.forEach((function(e){e.classList&&(e.classList.remove("ecom-box-active"),e.classList.remove("ecom-button-active"),e.classList.remove("ecom-image-active"))})),e.classList.add("ecom-box-active"),e.classList.add("ecom-button-active"),e.classList.add("ecom-image-active")}),a.querySelectorAll(`select.ecom-product-single__swatch-select[data-option-index="${t}"]`).forEach((function(o){o.value=e.options[t]}))}(i),function(e){const t=a.querySelectorAll(".ecom-product-single__price--regular"),o=a.querySelectorAll(".ecom-product-single__price--sale"),i=a.querySelectorAll(".ecom-product-single__price--badges-pecent-wrapper"),n=a.querySelectorAll(".ecom-product_ground-price"),c=a.querySelector(".ecom-unit-price"),r=a.querySelectorAll(".ecom-ground-price_unit-price-measurement");var s;e&&(a.querySelector("shopify-payment-terms")&&a.querySelector("shopify-payment-terms").setAttribute("variant-id",e.id),o.length&&(s=e,o.forEach((function(e){!s.compare_at_price||s.compare_at_price<s.price?e.classList.add("ecom-product-single__price-normal"):e.classList.remove("ecom-product-single__price-normal"),e.innerHTML=window.EComposer.formatMoney(s.price)}))),i.length&&i.forEach((function(t){const o=t.dataset.labelType;if(e.compare_at_price&&e.compare_at_price>e.price){let i=Math.round((e.compare_at_price-e.price)/e.compare_at_price*100);"amount"===o&&(i=window.EComposer.formatMoney(e.compare_at_price-e.price)),t.querySelector("span")&&(t.style.display="block",t.querySelector("span").innerText=`-${i}%`)}else t.style.display="none"})),t.length&&t.forEach((function(t){t.innerHTML=window.EComposer.formatMoney(e.compare_at_price),e.compare_at_price>e.price?t.style.display="inherit":t.style.display="none"})),n.length&&(n.forEach((function(t){e.unit_price?(t.style.display="block",c&&(c.style.display="block")):(t.style.display="none",c&&(c.style.display="none"));const o=t.querySelector(".ecom-ground-price_unit-price");o&&(o.innerHTML=window.EComposer.formatMoney(e.unit_price))})),r.length&&r.forEach((function(t){1!=e.unit_price_measurement.reference_value?t.innerHTML=e.unit_price_measurement.reference_value+e.unit_price_measurement.reference_unit:t.innerHTML=e.unit_price_measurement.reference_unit}))))}(i),function(e){const o=a.querySelector(".ecom-product-single__countdown");o&&e&&(t||(o.firstElementChild.style.display=""),"true"===o.dataset.showOnSale?e.compare_at_price>e.price&&e.available?(o.style.display="inherit",o.classList.remove("ecom-placeholder-on-builder-mode")):(t&&(o.style.display="none"),o.classList.add("ecom-placeholder-on-builder-mode"),o.classList.add("ecom-force-show"),o.dataset.ecomPlaceholder="This feature not match with your condition",t||(o.firstElementChild.style.display="none")):(o.classList.remove("ecom-placeholder-on-builder-mode"),o.style.display="inherit"))}(i),function(e){const t=a.querySelector(".ecom-product-single__variant-attributes--barcode"),o=a.querySelector(".ecom-product-single__variant-attributes--sku");e?(t&&(t.style.removeProperty("display"),t.querySelector(".ecom-product-single__variant-attributes--text").innerHTML=""+(e.barcode?e.barcode:"N/A")),o&&(o.style.removeProperty("display"),o.querySelector(".ecom-product-single__variant-attributes--text").innerHTML=""+(e.sku?e.sku:"N/A"))):(t&&(t.style.display="none"),o&&(o.style.display="none"))}(i),function(t){if(a.classList.contains("ecom_auto_variant_disable")&&n)return;const i=a.querySelectorAll(".ecom-product-single__variant-picker-container");if(!i.length||!t)return!1;l.dispatchEvent(new Event("change")),i.forEach(i=>{i.querySelectorAll(".ecom-product-single__variant-picker--selected-value").forEach((function(e){e.remove()})),n&&a.classList.contains("ecom_auto_variant_disable")&&(a.classList.remove("ecom_auto_variant_disable"),a.querySelectorAll(".ecom-product-single__add-to-cart--submit").forEach((function(e){e.removeAttribute("disabled")})));const c=e.querySelectorAll('.selector-wrapper label[for*="ecom-variant-selector"');if(c.length>0&&c.forEach(e=>{const t=e.textContent;e.childNodes.length&&e.childNodes[0].remove();const o=document.createElement("span");o.className="ecom-product-variant--option-label-text",o.innerText=`${t}${t.endsWith(":")?"":":"}`,e.prepend(o)}),!o)return 1;const r=t.options.length,s=i.querySelectorAll(".selector-wrapper");for(let e=0;e<r;e++)s[e]&&s[e].querySelectorAll("label").forEach(o=>{const i=document.createElement("span");i.className="ecom-product-single__variant-picker--selected-value",i.innerHTML=t.options[e],o.appendChild(i)}),i.querySelectorAll(`.ecom-product-single__picker--option-label[data-option-index="${e}"]`).forEach((function(o){let i=document.createElement("span");i.classList.add("ecom-product-single__variant-picker--selected-value"),i.innerHTML=t.options[e],o.appendChild(i)}))})}(i),function(e){const t=a.querySelectorAll(".ecom-product-single__media-label");e&&t.length&&t.forEach((function(t){const o=t.querySelector("span.ecom-product-single__media-label-sale");o&&(o.style.display=e.available&&e.compare_at_price&&e.compare_at_price>e.price?"block":"none");const i=t.querySelector(".ecom-product-single__media-label-sold-out");i&&(i.style.display=e.available?"none":"block");const n=t.querySelector(".ecom-product-single__media-label--bage-sale");if(n){const t=n.dataset.labelType;if(e.compare_at_price>e.price){let o=n.dataset.sale,i="";"amount"===t?(i=e.compare_at_price-e.price,n.style.display="inherit",n.innerHTML=o.replace(/\[.*\]/g,window.EComposer.formatMoney(i))):(i=Math.round(100*(e.compare_at_price-e.price)/e.compare_at_price),n.style.display="inherit",n.innerHTML=o.replace(/\[.*\]/g,Math.floor(i))),n.style.display=e.available?"inherit":"none"}else n.style.display="none"}}))}(i),a.dispatchEvent(new CustomEvent("ecomVariantChange",{detail:{variant:i}}))}if((!l||!l.classList.contains("ecom-product-single__picker-default-variant"))&&window.EComposer&&window.EComposer.OptionSelectors){let e=function(){const e=o.product.getVariantById(l.value);e&&_(e)},t=function(e,t){let o=null;if(2===p.options.length){const i=0===e?1:0,n=a.querySelector(`.ecom-product-single__swatch-item.ecom-button-active[data-option-index="${i}"]`),c=n?n.dataset.value:null;if(!c)return;const r=0===i?p.variants.find(e=>e.option1===c&&e.option2===t):p.variants.find(e=>e.option2===c&&e.option1===t);o=r&&r.featured_image?r.featured_image.src:null}return o};const o=new window.EComposer.OptionSelectors(l.id,{product:p,onVariantSelected:function(e,t){_(e),e&&e.id&&a.querySelectorAll('select[name="id"]').forEach(t=>{t.value=e.id})},enableHistoryState:i,autoVariantDisabled:n});l.addEventListener("swatch",(function(e){o.selectVariant(e.target.value)})),l.addEventListener("ecomUpdate",(function(){clearTimeout(u),u=setTimeout(e,1e3)}));const m={};let f=null;const w=function(e,o=!0){switch(e){case 0:var i="root",n=d.querySelectorAll(".single-option-selector")[0];break;case 1:i=d.querySelectorAll(".single-option-selector")[0].value,n=d.querySelectorAll(".single-option-selector")[1];break;case 2:i=d.querySelectorAll(".single-option-selector")[0].value;i+=" / "+d.querySelectorAll(".single-option-selector")[1].value;n=d.querySelectorAll(".single-option-selector")[2]}if(!o){const t=a.querySelector(`select[data-option-index="${e}"]`);if(t&&t.classList.contains("ecom-product-single__picker-dropdown-list"))return}if(n){var l=n.value;n.innerHTML="";var u=m[i]||[];if(u){for(var p=0;p<u.length;p++){var _=u[p],h=document.createElement("option");h.value=_,h.innerHTML=_,n.append(h)}var w=a.querySelector('.ecom-product-single__swatch-select[data-option-index="'+e+'"]');w&&(w.innerHTML=n.innerHTML),a.querySelectorAll('.ecom-product-single__swatch-item[data-option-index="'+e+'"]').forEach(o=>{var i=o.dataset.value;if("image"===s&&o.querySelector("img")){const n=t(e,i);n&&o.querySelector("img").setAttribute("src",n)}(c||r)&&(i&&-1!==u.indexOf(i)?o.classList.remove("ecom-variant-disable"):o.classList.add("ecom-variant-disable"))}),-1!==u.indexOf(l)&&(n.value=l),clearTimeout(f),f=setTimeout(()=>{n.value&&n.value!==l&&o&&n.dispatchEvent(new Event("change"))},50)}}},y=function(e){for(var t=0;t<e.variants.length;t++){var o=e.variants[t];if(!c||o.available){if(m.root=m.root||[],m.root.push(o.option1),m.root=EComposer.uniq(m.root),e.options.length>1){var i=o.option1;m[i]=m[i]||[],m[i].push(o.option2),m[i]=EComposer.uniq(m[i])}if(3===e.options.length){i=o.option1+" / "+o.option2;m[i]=m[i]||[],m[i].push(o.option3),m[i]=EComposer.uniq(m[i])}}}w(0,o),e.options.length>1&&w(1),3===e.options.length&&w(2);var n=d.querySelectorAll(".single-option-selector")[0];n&&n.addEventListener("change",(function(t){return e.options.length>1&&w(1),3===e.options.length&&w(2),!0}));var r=d.querySelectorAll(".single-option-selector")[1];r&&r.addEventListener("change",(function(t){return w(0,!1),3===e.options.length&&w(2),!0}))};if(window.MutationObserver&&a&&("image"===s||c||r)){"object"==typeof h&&"function"==typeof h.disconnect&&h.disconnect();var h=new MutationObserver((function(){y(p),h.disconnect()}));h.observe(a,{childList:!0,subtree:!0})}}if(!this.settings.hasOwnProperty("show_option_selected")&&!this.settings.show_option_selected){const t=e.querySelectorAll('.selector-wrapper label[for*="ecom-variant-selector"');t.length>0&&t.forEach(e=>{const t=e.textContent;e.childNodes.length&&e.childNodes[0].remove();const o=document.createElement("span");o.className="ecom-product-variant--option-label-text",o.innerText=t+":",e.prepend(o)})}a.querySelectorAll(".ecom-product-single__swatch-item[data-option-index]").forEach(e=>{e.addEventListener("click",(function(e){e.preventDefault();const o=a.querySelectorAll(".ecom-product-single__media--featured");let i=null;if(!o)return;o.length>1?o.forEach((function(e,o){i||(t?(window.screen.width>1024&&!e.closest(".hide-on-desktop")||window.screen.width>767&&window.screen.width<=1024&&!e.closest(".hide-on-tablet")||window.screen.width<=767&&!e.closest(".hide-on-mobile"))&&(i=e):(window.innerWidth>1024&&!e.closest(".hide-on-desktop")||window.innerWidth>767&&window.innerWidth<=1024&&!e.closest(".hide-on-tablet")||window.innerWidth<=767&&!e.closest(".hide-on-mobile"))&&(i=e))})):i=o[0],i&&i.removeAttribute("data-priority");const n=this;if(this.classList.contains("ecom-button-active")&&this.classList.contains("ecom-image-button"))return;this.parentNode.childNodes.forEach((function(e){e.classList&&(e.classList.remove("ecom-button-active"),e.classList.remove("ecom-image-button"))})),this.classList.add("ecom-button-active"),this.classList.add("ecom-image-button");const c=this.dataset.optionIndex;a.classList.remove("ecom_auto_variant_disable"),a.querySelectorAll("select#"+l.id+"-option-"+c).forEach((function(e){e.value=n.dataset.value,e.dispatchEvent(new Event("change"))}))}))}),a.querySelectorAll(".ecom-product-single__swatch-select").length?a.querySelectorAll(".ecom-product-single__swatch-select").forEach((function(e){e.addEventListener("change",(function(e){const t=a.querySelectorAll(".ecom-product-single__media--featured");let o=null;if(!t)return;t.length>1?t.forEach((function(e,t){o||(window.screen.width>1024&&!e.closest(".hide-on-desktop")||window.screen.width>767&&window.screen.width<=1024&&!e.closest(".hide-on-tablet")||window.screen.width<=767&&!e.closest(".hide-on-mobile"))&&(o=e)})):o=t[0],o&&o.removeAttribute("data-priority");let i=e.target.getAttribute("data-option-index"),n=e.target.value;a.classList.remove("ecom_auto_variant_disable"),a.querySelectorAll("select#"+l.id+"-option-"+i).forEach((function(e){e.value=n,e.dispatchEvent(new Event("change"))}))}))})):setTimeout((function(){const e=a.querySelectorAll(".ecom-product-single__media--featured");let t=null;!e||(e.length>1?e.forEach((function(e,o){t||(window.screen.width>1024&&!e.closest(".hide-on-desktop")||window.screen.width>767&&window.screen.width<=1024&&!e.closest(".hide-on-tablet")||window.screen.width<=767&&!e.closest(".hide-on-mobile"))&&(t=e)})):t=e[0],t&&t.removeAttribute("data-priority"))}),t?500:2500)};document.querySelectorAll(".ecom-p1udaa47up8").forEach((function(t){e.call({$el:t,id:"ecom-p1udaa47up8",settings:{show_option_selected:!0,type:"image"},isLive:!0})}))}(),function(){const e=function(){"use strict";var e,t,o;window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-ibo8s94zj5g"]=window.__ectimmers["ecom-ibo8s94zj5g"]||{};let i=this.$el;if(!i)return;let n=!0;const c=this.id;let r=i.querySelectorAll(".ecom-collection__product-variants"),s=this.isLive,l=null!=(e=this.settings.show_featured_media)&&e,a=null!=(t=this.settings.bage_sale)?t:"",d=null!=(o=this.settings.enable_progress_pagination)&&o,u=this.settings.price_type,m="bullets";const p=this.settings.slider_center,_=this.settings.slider_center__tablet,h=this.settings.slider_center__mobile;"progress"===this.settings.slider_pagination_style&&(m="progressbar");const f=this.settings.sale_badge_type;let w=this.settings.slider_speed,y=this.settings.slider_speed__tablet,g=this.settings.slider_speed__mobile;const v=function(e,t={},o=""){return window.innerWidth>1024&&e[0]&&(t[""+o]=e[0]),window.innerWidth<=1024&&window.innerWidth>768&&e[1]?t[""+o]=e[1]:e[0]&&(t[""+o]=e[0]),window.innerWidth<768&&e[2]?t[""+o]=e[2]:e[1]?t[""+o]=e[1]:e[0]&&(t[""+o]=e[0]),t};let S=i.querySelectorAll(".ecom-collection__product-item");function b(e){e.forEach((function(e){e.setAttribute("data-init-quantity","true");let t=e.querySelector(".ecom-collection__product-quantity-input"),o=e.querySelector(".ecom-collection__quantity-controls-plus"),i=e.querySelector(".ecom-collection__quantity-controls-minus");i&&i.addEventListener("click",(function(){t.stepDown(),t.dispatchEvent(new Event("change"))})),o&&o.addEventListener("click",(function(){t.stepUp(),t.dispatchEvent(new Event("change"))})),t&&t.addEventListener("change",(function(t){let o=e.querySelector("a.ecom-collection__product-submit");if(t.target.value>parseInt(t.target.max)&&(t.target.value=parseInt(t.target.max)),o){let e=o.getAttribute("href");o.setAttribute("href",e.replace(/quantity=(\d*)/gm,"quantity="+t.target.value))}}))}))}function q(e=!1,t){const o=i.querySelector(".ecom-paginate__progress-bar--outner"),n=i.querySelector(".ecom-paginate__progress-bar--inner"),c=i.querySelector(".ecom-paginate__progress-text");if(!(d&&s&&o&&n&&c))return;let{total:r,initProduct:l}=o&&o.dataset,a=c&&c.dataset.text,u=0,m=1,p=0,_=0;l=parseInt(l),e?(m=1,p=l*t):(window.location.href.match(/page=\d*/gm)&&(u=new URL(window.location.href).searchParams.get("page"),m=1===u?1:l*(u-1)+1),p=m+l-1),p>r&&(p=r),_=Math.round(p/r*100),n.style.width=_+"%",a=a.replace("{_start}",m),a=a.replace("{_end}",p),a=a.replace("{_total}",r),c.innerText=a}function E(e,t){var o=t.variantIdField.closest(".ecom-collection__product-item");let n=o.querySelector(".ecom-collection__product-submit"),c=o.querySelector(".ecom-collection__product-quantity-input"),r=o.querySelector(".ecom-collection__product-price"),d=o.querySelector(".ecom-collection__product-price--regular"),m=o.querySelector(".ecom-unit-price");d&&d.classList.add("ecom-collection__product--compare-at-price");let p=o.querySelector(".ecom-collection__product-price--bage-sale"),_=o.querySelector(".ecom-collection__product-badge--sale"),h=o.querySelector(".ecom-collection__product-badge--sold-out"),w=o.querySelector(".ecom-collection__product-item-sku-element"),y="";if(null===e||o.hasAttribute("ec-variant-init")&&"first_price"===u){let t=o.querySelector('select[name="variant_id"]'),i=o.querySelector(".product-json"),n=null;try{n=JSON.parse(i.innerHTML)}catch(e){return 1}if(o.hasAttribute("ec-variant-init")&&"first_price"===u)o.removeAttribute("ec-variant-init"),null==(e=n.variants.find(e=>e.available))&&(e=n.variants[0]);else{let i=o.querySelector("select#"+t.id+"-option-0");if(!i)return;const c=i.value;c&&n.variants.forEach((function(t){t.options.includes(c)&&(e=t)}))}}if(e){if(r&&(r.innerHTML=window.EComposer.formatMoney(e.price)),d&&(d.innerHTML=window.EComposer.formatMoney(e.compare_at_price)),m){e.unit_price?m.style.display="block":m.style.display="none";const t=m.querySelector(".ecom-ground-price_unit-price");t&&(t.innerHTML=window.EComposer.formatMoney(e.unit_price))}if(e.compare_at_price>e.price){d&&(d.style.display="inherit");let t="";t=i.querySelector(".ecom-collection__product-main").dataset.sale,"false"==i.querySelector(".ecom-collection__product-main").dataset.translate&&(t=a),_&&h&&(_.style.display="block",h.style.display="none"),"amount"===f?(y=e.compare_at_price-e.price,p&&(p.style.display="inherit",p.innerHTML=t.replace(/\{{.*\}}/g,window.EComposer.formatMoney(y)))):(y=100*(e.compare_at_price-e.price)/e.compare_at_price,p&&(p.style.display="inherit",p.innerHTML=t.replace(/\{{.*\}}/g,Math.round(y))))}else d&&(d.style.display="none"),_&&h&&(_.style.display="none",h.style.display="none"),p&&(p.style.display="none",p.innerHTML="");if(w&&(e.sku?(w.querySelector(".ecom-collection__product-item-sku").innerHTML=e.sku,w.style.display="flex"):w.style.display="none"),e.featured_image){let t=o.querySelector(".ecom-collection__product-media img");if(!l){let o=t.closest("div");o.classList.add("ecom-product-image-loading"),t.setAttribute("src",e.featured_image.src),t.removeAttribute("srcset"),t.addEventListener("load",(function(){o.classList.remove("ecom-product-image-loading")}))}}if(e.options.length,o.querySelector(".ecom-collection__product-submit"))if(e.available){const t=n.closest(".ecom-collection__product--wrapper-items");if(t.dataset.iconAdd&&n.querySelector(".ecom-collection__product-add-cart-icon")&&(n.querySelector(".ecom-collection__product-add-cart-icon").innerHTML=t.dataset.iconAdd),!e.inventory_management||e.inventory_management&&e.inventory_quantity>0){if(n.removeAttribute("disabled"),c){let t=c.closest(".ecom-collection__product-quantity--wrapper");t&&(t.style.display="flex"),c.style.display="flex",e.inventory_management?c.max=e.inventory_quantity:c.max=9999}n.classList.add("ecom-collection__product-form__actions--add"),n.classList.remove("ecom-collection__product-form__actions--soldout"),n.classList.remove("ecom-collection__product-form__actions--unavailable"),n.querySelector(".ecom-add-to-cart-text").innerHTML=n.getAttribute("data-text-add-cart")}else if("continue"==e.inventory_policy&&e.inventory_quantity<=0){if(n.removeAttribute("disabled"),c){let e=c.closest(".ecom-collection__product-quantity--wrapper");e&&(e.style.display="flex"),c.max=9999,c.style.display="flex"}n.classList.add("ecom-collection__product-form__actions--add"),n.classList.remove("ecom-collection__product-form__actions--soldout"),n.classList.remove("ecom-collection__product-form__actions--unavailable"),n.querySelector(".ecom-add-to-cart-text").innerHTML=n.getAttribute("data-text-pre-order")}n.dataset.childName="add_to_cart_button",n.dataset.childTitle="Add to cart button"}else{if(_&&h&&(_.style.display="none",h.style.display="block"),s&&n.setAttribute("disabled","disabled"),c){let e=c.closest(".ecom-collection__product-quantity--wrapper");e&&(e.style.display="none"),c.style.display="none"}const e=n.closest(".ecom-collection__product--wrapper-items");e.dataset.iconSoldout&&n.querySelector(".ecom-collection__product-add-cart-icon")&&(n.querySelector(".ecom-collection__product-add-cart-icon").innerHTML=e.dataset.iconSoldout),n.classList.add("ecom-collection__product-form__actions--soldout"),n.classList.remove("ecom-collection__product-form__actions--add"),n.classList.remove("ecom-collection__product-form__actions--unavailable"),n.querySelector(".ecom-add-to-cart-text").innerHTML=n.getAttribute("data-text-sold-out"),n.dataset.childName="sold_out_button",n.dataset.childTitle="Sold out button"}}else r.html=window.EComposer.formatMoney(0),d&&(d.innerHTML=window.EComposer.formatMoney(0),d.style.display="none"),n&&(n.setAttribute("disabled","disabled"),n.classList.add("ecom-collection__product-form__actions--unavailable"),n.classList.remove("ecom-collection__product-form__actions--add"),n.classList.remove("ecom-collection__product-form__actions--soldout"),n.querySelector(".ecom-add-to-cart-text").innerHTML=n.getAttribute("data-text-unavailable"))}function L(e){e.classList.add("ecom-swatch-init");let t=e.querySelector(".ecom-collection__product-form");if(!t)return;let o=t.querySelector('select[name="variant_id"]'),i=e.querySelector(".product-json"),n=null;try{n=JSON.parse(i.innerHTML)}catch(e){return 1}window.EComposer&&window.EComposer.OptionSelectors&&new window.EComposer.OptionSelectors(o.id,{product:n,onVariantSelected:E,enableHistoryState:!1}),e.querySelectorAll(".ecom-collection__product-swatch-item").forEach((function(t){t.addEventListener("click",(function(){l=!1;var t=this.closest("li");if(t.classList.contains("ecom-product-swatch-item--active"))return!1;t.parentNode.querySelectorAll(".ecom-product-swatch-item--active").forEach((function(e){e.classList.remove("ecom-product-swatch-item--active")})),t.classList.add("ecom-product-swatch-item--active");var i=t.getAttribute("data-option-index"),n=t.getAttribute("data-value");let c=e.querySelector("select#"+o.id+"-option-"+i);c.value=n,c.dispatchEvent(new Event("change"))}))})),e.querySelectorAll("select.ecom-collection__product-swatch-select").forEach((function(t){t.addEventListener("change",(function(){var t=this.getAttribute("data-option-index"),i=this.value;e.querySelectorAll("select#"+o.id+"-option-"+t).forEach((function(e){e.value=i,e.dispatchEvent(new Event("change"))}))}))}))}if(S.length&&b(S),q(!1,1),"slider"===this.settings.layout){let e=function(e){let t=e.querySelector(".ecom-swiper-container"),o=t&&t.dataset.optionSwiper;if(!o)return;o=JSON.parse(o),o.pagination={el:e.querySelector(".ecom-swiper-pagination"),type:m,clickable:!0},o.navigation={nextEl:e.querySelector(".ecom-swiper-button-next"),prevEl:e.querySelector(".ecom-swiper-button-prev")},o.autoHeight=!1,o.on={init:function(){this.el.classList.add("ecom-swiper-initialized")}};let i=[w,y,g];if(s){o=v(i,o,"speed"),o=v([p,_,h],o,"centeredSlides");const e=new window.EComSwiper(t,o);o.autoplay.enabled&&(e.on("touchStart",(function(e,t){e.params.speed=300,e.autoplay.stop()})),e.on("touchEnd",(function(e,t){window.innerWidth>1024&&w&&(e.params.speed=w),window.innerWidth<=1024&&window.innerWidth>768&&y?e.params.speed=y:w&&(e.params.speed=w),window.innerWidth<768&&g?e.params.speed=g:y?e.params.speed=y:w&&(e.params.speed=w),e.autoplay.start()})))}else setTimeout((function(){o=v(i,o,"speed"),o=v([p,_,h],o,"centeredSlides"),new window.EComSwiper(t,o)}),200)},t=this.$el,o=t.querySelector(".ecom-collection__product-container");e(t),o.addEventListener("ecom-products-init-slider",(function(t){e(t.detail.wrapper)}))}r.forEach(L);const A=function(e){e.querySelectorAll(".ecom-collection__product-form__actions--quickshop").forEach((function(e){e.addEventListener("click",(function(e){this.style.display="none";let t=this.closest(".ecom-collection__product-item");t.querySelectorAll(".ecom-collection__product-variants").forEach((function(e){e.classList.add("ecom-active")})),t.querySelectorAll(".ecom-collection__product-quick-shop-wrapper").forEach((function(e){e.style.display="inherit"}))}))})),e.querySelectorAll(".ecom-collection__product-close").forEach((function(e){e.addEventListener("click",(function(e){let t=this.closest(".ecom-collection__product-item");t.querySelectorAll(".ecom-collection__product-variants").forEach((function(e){e.classList.remove("ecom-active")})),t.querySelectorAll(".ecom-collection__product-quick-shop-wrapper").forEach((function(e){e.style.display="none"})),t.querySelectorAll(".ecom-collection__product-form__actions--quickshop").forEach((function(e){e.style.display="inherit"}))}))}))};A(i);const x=i.querySelector(".ecom-collection__product-main");let k=x.dataset,C=x.dataset.countdownShows;const T=/\[([^\]]+)\]/gm;var M="";if(C.indexOf("week")>=0&&k.week){let e="",t=k.week.replace(T,(...t)=>(e=t[1],""));M+=`\n                            <div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--week">\n                                <span class="ecom-collection__product-time--number">\n                                    ${e}\n                                </span>\n                                <span class="ecom-collection__product-time--label">\n                                    ${t}\n                                </span>\n                            </div>`}if(C.indexOf("day")>=0&&k.day){let e="",t=k.day.replace(T,(...t)=>(e=t[1],""));M+=`<div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--day">\n                                    <span class="ecom-collection__product-time--number">\n                                        ${e}\n                                    </span>\n                                    <span class="ecom-collection__product-time--label">\n                                        ${t}\n                                    </span>\n                                </div> `}if(C.indexOf("hour")>=0&&k.hour){let e="",t=k.hour.replace(T,(...t)=>(e=t[1],""));M+=`\n                            <div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--hour">\n                                <span class="ecom-collection__product-time--number">\n                                    ${e}\n                                </span>\n                                <span class="ecom-collection__product-time--label">\n                                    ${t}\n                                </span>\n                            </div>\n                        `}if(C.indexOf("minute")>=0&&k.minute){let e="",t=k.minute.replace(T,(...t)=>(e=t[1],""));M+=`<div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--minute">\n                                    <span class="ecom-collection__product-time--number">\n                                        ${e}\n                                    </span>\n                                    <span class="ecom-collection__product-time--label">\n                                        ${t}\n                                    </span>\n                                </div>\n                            `}if(C.indexOf("second")>=0&&k.second){let e="",t=k.second.replace(T,(...t)=>(e=t[1],""));M+=`<div class="ecom-collection__product-time--item ecom-d-flex ecom-collection__product-time--second">\n                                    <span class="ecom-collection__product-time--number">\n                                        ${e}\n                                    </span>\n                                    <span class="ecom-collection__product-time--label">\n                                        ${t}\n                                    </span>\n                                </div>`}function $(e){let t=this.closest(".ecom-collection__product-countdown-wrapper"),o=t.querySelector(".ecom-collection__product-countdown-progress-bar"),i=t.querySelector(".ecom-collection__product-countdown-progress-bar--timer"),n=this.getAttribute("data-ecom-countdown-from")||0;if(this.innerHTML=e.strftime(M),o&&n){let t=(new Date).getTime(),c=new Date(n).getTime(),r=e.finalDate.getTime();if(c<t&&r>c){o.style.removeProperty("display");let e=r-c,n=r-t,s=Math.round(100*n/e)+"%";i.style.width=s}else o.style.display="none"}}function H(e){if(e.dataset.ecomCountdown){if(e.dataset.ecomCountdownFrom&&(new Date).getTime()>new Date(e.dataset.ecomCountdown).getTime()&&s)return e.closest(".ecom-collection__product-countdown-wrapper").style.display="none",!1;window.EComCountdown&&window.EComCountdown(e,new Date(e.dataset.ecomCountdown),$),e.addEventListener("stoped.ecom.countdown",()=>{e.closest(".ecom-collection__product-countdown-wrapper").style.display="none"})}}if(i.querySelectorAll(".ecom-collection__product-countdown-time").forEach((function(e){H(e)})),s){const e=i.querySelector(".ecom-collection__product-main");let t=1;const o=function(e){e.preventDefault();const o=this.dataset.get,n=this.closest(".ecom-sections[data-section-id]"),c=i.closest(".ecom-row.ecom-section");if(!o||!n||!n.dataset.sectionId)return;const s=`${o}&section_id=${n.dataset.sectionId}`;t++,q(!0,t),this.classList.add("ecom-loading"),r(s,n,this,"loadmore",c)},c=function(e){var t,o;t=e,o={},new IntersectionObserver((e,c)=>{e.forEach(e=>{e.isIntersecting&&(o.cb?o.cb(t):function(e){const t=e.dataset.get,o=e.closest(".ecom-sections[data-section-id]"),c=e.closest(".ecom-row.ecom-section");if(!t||!o||!o.dataset.sectionId)return;const s=o.dataset.sectionId,l=`${t}&section_id=${s}`;n&&(i.classList.add("ecom-doing-scroll"),r(l,o,e,"infinite",c))}(e.target),c.unobserve(e.target))})},o).observe(t)},r=function(t,o,r,s,l){n=!1,async function(e){return(await fetch(e,{method:"GET",cache:"no-cache",headers:{"Content-Type":"text/html"}})).text()}(t).then((function(t){const o=document.createElement("div");o.innerHTML=t;const i=o.querySelector(".ecom-collection__product-main.ecom-collection_product_template_collection .ecom-collection__product--wrapper-items");if(!i)return;const n=l.querySelector(".ecom-collection__product--wrapper-items"),a=l.querySelector(".ecom-products-pagination-loadmore");for(;i.firstChild;)n.appendChild(i.firstChild);if(i.parentNode.removeChild(i),"loadmore"===s){const e=o.querySelector(".ecom-products-pagination-loadmore");e?a.innerHTML=e.innerHTML:a.remove()}else{r.remove();const e=o.querySelector(".ecom-products-pagination-infinite");e&&(n.after(e),c(e))}e.dispatchEvent(new CustomEvent("ecom-products-init",{detail:{wrapper:e}}))})).finally((function(){window.EComposer&&window.EComposer.initQuickview(),n=!0,i.classList.remove("ecom-doing-scroll"),r.classList.remove("ecom-loading")}))};if(e&&e.dataset.pagination){const t=e.dataset.pagination;if("loadmore"===t)i.querySelector(".ecom-products-pagination-loadmore-btn")&&i.querySelector(".ecom-products-pagination-loadmore-btn").addEventListener("click",o);else if("infinit"===t){const e=i.querySelector(".ecom-products-pagination-infinite");e&&c(e)}}e.addEventListener("ecom-products-init",(function(t){const n=t.detail.wrapper;if(!n)return;if(e&&e.dataset.pagination){const t=e.dataset.pagination;if("loadmore"===t)i.querySelector(".ecom-products-pagination-loadmore-btn")&&i.querySelector(".ecom-products-pagination-loadmore-btn").addEventListener("click",o);else if("infinit"===t){const e=i.querySelector(".ecom-products-pagination-infinite");e&&c(e)}}n.querySelectorAll(".ecom-collection__product-variants:not(.ecom-swatch-init)").length&&n.querySelectorAll(".ecom-collection__product-variants:not(.ecom-swatch-init)").forEach(L),n.querySelectorAll(".ecom-collection__product-countdown-time").length&&n.querySelectorAll(".ecom-collection__product-countdown-time").forEach((function(e){H(e)})),A(n);let r=n.querySelectorAll(".ecom-collection__product-item:not([data-init-quantity='true'])");r.length&&b(r),n.querySelector(".ecom-products-pagination-loadmore-btn")&&n.querySelector(".ecom-products-pagination-loadmore-btn").addEventListener("click",o),window.EComposer&&"function"==typeof window.EComposer.init&&window.EComposer.init(),z(n);j(n.querySelector(".ecom-collection__product--wishlist-wrapper"))}))}function z(e){if(e&&e.dataset.reviewPlatform)switch(e.dataset.reviewPlatform){case"product-reviews":if(window.SPR)try{window.SPR.$=window.jQuery,window.SPR.initDomEls(),window.SPR.loadBadges()}catch(e){console.info(e.message)}break;case"judgeme":if(window.jdgm){try{window.jdgm.batchRenderBadges()}catch(e){console.info(e.message)}i.querySelectorAll('[data-average-rating="0.00"]').forEach((function(e){e.style.display="block !important"}))}break;case"product-reviews-addon":window.StampedFn&&window.StampedFn.loadBadges();break;case"lai-reviews":void 0!==window.SMARTIFYAPPS&&window.SMARTIFYAPPS.rv.installed&&window.SMARTIFYAPPS.rv.scmReviewsRate.actionCreateReviews();break;case"air-reviews":"function"==typeof window.avadaAirReviewRerender&&window.avadaAirReviewRerender()}}function j(e){if(e)switch(e.dataset.wishlistApp){case"swym-relay":window._swat&&window._swat.initializeActionButtons(".ecom-collection__product-wishlist-button");break;case"wishlist-hero":i.querySelectorAll(".wishlist-hero-custom-button").forEach((function(e){var t=new CustomEvent("wishlist-hero-add-to-custom-element",{detail:e});document.dispatchEvent(t)}))}}if(!s){z(i.querySelector(".ecom-collection__product-main"));j(i.querySelector(".ecom-collection__product--wishlist-wrapper"))}this.settings.enable_preload&&i.querySelectorAll(".ecom-collection__product-item").forEach((function(e){e.addEventListener("mouseenter",(function(){let e=document.createElement("link");e.rel="prefetch",document.head.appendChild(e);var t=this.querySelector("a.ecom-collection__product-item-information-title").getAttribute("href");e.href=t}),{once:!0})}));this.settings.show_compare&&!s&&i.querySelectorAll(".ecom-product__compare-link").forEach((function(e){e.addEventListener("click",(function(){this.classList.contains("ecom-product__compare-link-added")?this.classList.remove("ecom-product__compare-link-added","ecom-button-active"):this.classList.add("ecom-product__compare-link-added","ecom-button-active")}))}));this.settings.show_wishlist&&!s&&i.querySelectorAll(".ecom-product__wishlist-link").forEach((function(e){e.addEventListener("click",(function(){this.classList.contains("ecom-product__wishlist-link-added")?this.classList.remove("ecom-product__wishlist-link-added","ecom-button-active"):this.classList.add("ecom-product__wishlist-link-added","ecom-button-active")}))}));if("recommendations"===this.settings.show_product_by&&s){let e=i.closest(".ecom-builder");if(e){let t=e.querySelector(".ecom-sections").dataset.sectionId,o=e.querySelector('input[name="product-id"]')?e.querySelector('input[name="product-id"]').value:"",n=8,r=i.querySelector(".ecom-collection__product-container"),s=i.querySelector(".ecom-collection__product-main");s.classList.contains("ecom-collection_product_template_product")&&"recommendations"===this.settings.show_product_by&&(n=this.settings.limit_recommended_products),fetch(`${window.Shopify.routes.root}recommendations/products?product_id=${o}&limit=${n}&section_id=${t}`).then(e=>e.text()).then(e=>{const o=document.createElement("div");o.innerHTML=e;const i=o.querySelector(`[data-section-id="${t}"]`),n=i.querySelector(".ecom-block."+c);if(!n)return void console.warn(`Block with ID ${c} not found in recommendations.`);const l=n.querySelector(".ecom-collection__product-main");i.innerHTML.trim().length&&s&&(s.innerHTML=l.innerHTML,s.querySelector(".ecom-collection__product--wrapper-items")&&s.dispatchEvent(new CustomEvent("ecom-products-init",{detail:{wrapper:s}})),r.dispatchEvent(new CustomEvent("ecom-products-init-slider",{detail:{wrapper:r}})))}).catch(e=>{console.error(e)})}}};document.querySelectorAll(".ecom-ibo8s94zj5g").forEach((function(t){e.call({$el:t,id:"ecom-ibo8s94zj5g",settings:{show_featured_media:!1,bage_sale:"-{{sale}}%",price_type:"first_price",sale_badge_type:"percent",slider_speed:200,layout:"slider",enable_preload:!1,show_compare:!1,show_wishlist:!1,show_product_by:"condition"},isLive:!0})})),document.querySelectorAll(".ecom-vuh7xl98gmq").forEach((function(t){e.call({$el:t,id:"ecom-vuh7xl98gmq",settings:{show_featured_media:!1,bage_sale:"-{{sale}}%",price_type:"first_price",sale_badge_type:"percent",slider_speed:800,layout:"slider",enable_preload:!1},isLive:!0})}))}(),function(){const e=function(){"use strict";var e,t,o,i,n;if(window.__ectimmers=window.__ectimmers||{},window.__ectimmers["ecom-8fvlz46y338"]=window.__ectimmers["ecom-8fvlz46y338"]||{},!this.$el)return!1;const c=this,r=this.id,s=this.$el,l=this.isLive,a={width:this.settings.zoom_width,height:this.settings.zoom_height},d=s.closest(".ecom-product-form--single"),u=!!this.settings.show_thumbnails&&this.settings.show_thumbnails,m=this.settings.layout?this.settings.layout:"slider",p=!!this.settings.enable_zoom&&this.settings.enable_zoom,_=this.settings.image_action&&"lightbox"===this.settings.image_action;var h,f,w=this.settings.thumbnail_position,y=this.settings.thumbnail_position__tablet,g=this.settings.thumbnail_position__mobile,v=!!this.settings.show_pagination,S=!!this.settings.sliderControls,b=!!this.settings.sliderControlsThumb,q=null!=(e=this.settings.enable_gallery)&&e,E=null!=(t=this.settings.gallery_name)&&t,L=null!=(o=this.settings.centeredSlides)&&o,A=null!=(i=this.settings.slide_loop)&&i,x=this.settings.disable_auto_height,k=null!=(n=this.settings.video_auto_play)&&n;function C(){var e,t;if("slider"===m)try{let i=function(e,t,o){var i,n;let c=0;const r=[],s=f.options.perPage;let l=0,a=0;if(o!==t){l=o,a=o+s,a>f.Components.Slides.getLength(!0)&&(l=f.Components.Slides.getLength(!0)-s,a=f.Components.Slides.getLength(!0));for(let e=l;e<a;e++)f.Components.Slides.getAt(e)&&r.push(null==(i=f.Components.Slides.getAt(e))?void 0:i.slide)}else{const e=f.index;l=e,a=e+s,a>f.Components.Slides.getLength(!0)&&(l=f.Components.Slides.getLength(!0)-s,a=f.Components.Slides.getLength(!0));for(let e=l;e<a;e++)f.Components.Slides.getAt(e)&&r.push(null==(n=f.Components.Slides.getAt(e))?void 0:n.slide)}r.length>0&&(c=r.reduce((e,t)=>{const o=t.offsetHeight;return o>e?o:e},0)),r.forEach(e=>{const t=e.querySelector("video");if(t)try{t.pause(),k&&t.play()}catch(e){}});const d=f.Components.Elements.list;d&&c>0?setTimeout(()=>{d.style.height=c+"px"},100):d.style.height=Math.round(9*d.offsetWidth/16)+"px"};if(u){const t=c.$el.querySelector(".ecom-product-single__media--thumbs"),o="rtl"===(null==(e=t.dataset)?void 0:e.direction);let i=JSON.parse(t.dataset.breakpoints);if(Object.keys(i).forEach(e=>{i[e].direction=["row","row-reverse"].includes(i[e].thumbnail_position)?"ttb":o?"rtl":"ltr"}),t.hasChildNodes()){const e={rewind:A,isNavigation:!0,arrows:b,pagination:!1,autoHeight:!!(["row","row-reverse"].includes(w)&&window.screen.width>1024||["row","row-reverse"].includes(y)&&(window.screen.width>=768||window.screen.width<=1024)||["row","row-reverse"].includes(g)&&window.screen.width<768),drag:!!l,mediaQuery:"min",isThumb:!0,rewind:!0,breakpoints:i,direction:o?"rtl":"ltr"};(["row","row-reverse"].includes(w)&&window.screen.width>1024||["row","row-reverse"].includes(y)&&(window.screen.width>=768||window.screen.width<=1024)||["row","row-reverse"].includes(g)&&window.screen.width<768)&&(e.height="auto"),(h=new EcSplide(t,e)).on("updated",(function(){setTimeout(()=>window.dispatchEvent(new window.Event("resize")),500)})),h.on("mounted",(function(){setTimeout(()=>{t&&t.classList.remove("ecom-product-single__init-thumb-hidden")},50)}))}let n=null;t.querySelectorAll("img").forEach((function(e){e.addEventListener("load",(function(){clearTimeout(n),n=setTimeout(()=>window.dispatchEvent(new window.Event("resize")),500)}))}))}const n=s.querySelector(".ecom-product-single__media--featured"),r="rtl"===(null==(t=n.dataset)?void 0:t.direction);var o=n.dataset.breakpoints;o=o?JSON.parse(o):{0:{perPage:1,gap:20}},(f=new EcSplide(n,{type:"slide",autoHeight:!!x,lazyload:!0,pagination:v,arrows:S,drag:!!l,flickPower:600,type:A?"loop":"slide",focus:L?"center":"",rewind:!0,mediaQuery:"min",trimSpace:!0,breakpoints:o,dynamicBullets:!0,direction:r?"rtl":"ltr",paginationDirection:r?"rtl":"ltr"})).on("ready",(function(){var e,t,o;const i=null==(o=null==(t=null==(e=f.Components)?void 0:e.Slides)?void 0:t.getAt(f.index))?void 0:o.slide;if(!i)return;x||f.Components.Elements.root.classList.add("ec_splide--disable-autoheight");const n=i.querySelector("video");n&&n.hasAttribute("autoplay")&&n.play(),i&&p&&M(i)})),f.on("updated",(function(){setTimeout(()=>window.dispatchEvent(new window.Event("resize")),500),f.refresh();let e=f.index;f.go(0),f.go(e)})),f.on("move",(function(e,t,o){var i;if(q)return;e!==t&&(f.lastIndex=e+"");const n=d&&d.querySelector('[name="id"]');if(n){let t=null;const o=f.Components.Slides.getAt(e);if(!o)return;if(t=null==(i=o.slide.dataset)?void 0:i.variant_id,t){t+="";const e=n.value;(!e||!t.includes(e.toString()))&&(n.value=t.split(",")[0],n.dispatchEvent(new Event("swatch")))}}if(p){const t=f.Components.Slides.getAt(e);t&&M(t.slide)}})),f.on("moved",(function(e,t,o){var i,n,c,r,s,a;if(!f.Components.Slides)return;const d=null==(c=null==(n=null==(i=f.Components)?void 0:i.Slides)?void 0:n.getAt(e))?void 0:c.slide,u=null==(a=null==(s=null==(r=f.Components)?void 0:r.Slides)?void 0:s.getAt(t))?void 0:a.slide;if(!d||!u)return;const m=u.querySelector("iframe, video");m&&("IFRAME"===m.nodeName?m.src=m.src:m.pause());const p=d.querySelector("video");if(p&&p.hasAttribute("autoplay")&&p.play(),l){const t=f.Components.Slides.getAt(e).slide;f.options.drag=!t.classList.contains("ecom-swiper-no-swiping")}})),x&&f.on("ready move",i);try{h&&f.sync(h),n.ec_splide?(f.refresh(),h&&h.refresh()):(n.ec_splide=f,f.mount(),h&&h.mount())}catch(e){console.log(e)}let a=null;n.querySelectorAll("img").forEach((function(e){e.addEventListener("load",(function(){clearTimeout(a),a=setTimeout(()=>window.dispatchEvent(new window.Event("resize")),500)}))})),l||setTimeout(()=>{n.classList.remove("ecom-before-init")},200)}catch(e){console.info(e.message)}}async function T(e){const t=await window.fetch(e,{method:"GET",headers:{Accept:"application/json","Content-Type":"application/json"}});if(t.ok){const e=await t.json();if(e)return e.product}return!1}if(C(),q&&async function(){const e=s.querySelectorAll(".ecom-product-single__media--image img"),t=s&&s.querySelector(".ecom-product-single__media--featured .ec_splide__track .ec_splide__list"),o=t&&t.querySelectorAll(".ecom-product-single__media--image"),i=s&&s.querySelector(".ecom-product-single__media--featured .ec_splide__track"),n=s&&s.querySelector(".ecom-product-single__media--thumbs .ec_splide__track"),c=s&&s.querySelector(".ecom-product-single__media--slider .ecom-product-single__media--thumbs .ec_splide__track .ec_splide__list"),r=n&&n.querySelectorAll(".ecom-product-single__media--thumbnail"),a=s&&s.querySelector(".ecom-product-single__media--grid .ecom-product-single__media--images-layout__grid"),u=a&&a.querySelectorAll(".ecom-product-single__media--image");let m=!0;if(e&&e.forEach((function(e,t){e&&e.alt&&e.alt.includes("ecomposer-")&&(m=!1)})),m)return;let _=null,h=d&&d.querySelector(".ecom-product-single-select-id[name=id]");if(!h)return;const f=d&&d.querySelector("#"+h.dataset.jsonProduct);if(!f)return;try{_=JSON.parse(f.innerHTML)}catch(e){return}let w=null;if(l&&window.Shopify&&"/"!=window.Shopify.routes.root){let e=window.location.origin+"/products/"+_.handle+".json";w=await T(e),w||(e=window.location.origin+window.Shopify.routes.root+"products/"+_.handle+".json",w=await T(e)),_.options_with_values=w.options,_.variants=w.variants}let y={detail:{variant:null}};y.detail.variant=_.variants.find((function(e){if(e.id==h.value)return e}));let g=s.querySelector("#ecom-single-product-default-variant"),v=g&&g.innerText,S=g.dataset.dontSetAlt;function b(e){if(e.detail.variant&&(e.target&&e.target.querySelector(".ecom-product-single__variant-picker-container"),q&&E)){let m=function(e){let t=e.options_with_values,o=[];E.includes(",")?E.split(",").forEach((e,i)=>{t&&t.forEach((function(t){t.name.trim().toLowerCase()===e.trim().toLowerCase()&&(o=o.concat({key:t.name.trim(),value:t.values}))}))}):t&&t.forEach((function(e){e.name.trim().toLowerCase()!==E.toLowerCase()||(o=o.concat({key:e.name.trim(),value:e.values}))}));let i=[];return o&&o.forEach((function(e,t){e.value.forEach(t=>{S.option1==t&&i.push(`ecomposer-${e.key.toLowerCase()}-${S.option1.replaceAll(" ","-").toLowerCase()}`),S.option2==t&&i.push(`ecomposer-${e.key.toLowerCase()}-${S.option2.replaceAll(" ","-").toLowerCase()}`),S.option3==t&&i.push(`ecomposer-${e.key.toLowerCase()}-${S.option3.replaceAll(" ","-").toLowerCase()}`)})})),i},h=function(e,t,o,i,n){if(i&&t&&_&&o.length){t.innerHTML="",o.forEach((function(e){let o=e.querySelector("img")&&e.querySelector("img").alt;if(o)if(o.includes(",")){o=o.split(","),o=o.map((function(e){return e.trim().toLowerCase()}));let i=m(_).filter(e=>-1!==o.indexOf(e));(f(i,o)||w(i,o)&&i.length===y(o))&&(e.querySelector("img").removeAttribute("loading"),t.appendChild(e))}else m(_).includes(o.toLowerCase())&&(e.querySelector("img").removeAttribute("loading"),t.appendChild(e))})),t.style=n,e.prepend(t);s.querySelector(".ecom-product-single__media--featured").ec_splide=null,C()}},f=function(e,t){return e.sort().join()===t.sort().join()},w=function(e,t){return e.every(e=>t.includes(e))},y=function(e){const t=new Set;let o=0;for(const i of e){const e=i.indexOf("-",i.indexOf("-")+1),n=i.substring(10,e);t.has(n)||(t.add(n),o++)}return o},g=function(e){const t=d&&d.querySelector(".ecom-product-single__media--grid_default");if(!t||!e||!_)return;let o=t&&t.querySelectorAll(".ecom-product-single__media--image");o.length&&(E&&q?o.forEach((function(e){e.style.display="none";let t=e.querySelector("img").alt;if(t.includes(",")){t=t.split(","),t=t.map((function(e){return e.trim().toLowerCase()}));let o=m(_).filter(e=>-1!==t.indexOf(e));(f(o,t)||w(o,t)&&o.length===y(t))&&(e.style.display="block")}else m(_).includes(t.toLowerCase())&&(e.style.display="block")})):o.forEach((function(e){e.style.display="flex"})))},v=function(e){if(e&&a&&_){a.innerHTML="";let e=[];u.forEach((function(t,o){let i=t.querySelector("img").alt;if(i.includes(",")){i=i.split(","),i=i.map((function(e){return e.trim().toLowerCase()}));let o=m(_).filter(e=>-1!==i.indexOf(e));(f(o,i)||w(o,i)&&o.length===y(i))&&e.push(t)}else m(_).includes(i.toLowerCase())&&e.push(t)})),e.forEach((function(t,o){0==o||o>=5&&o%5==0||o%5!=0&&(o+1)%2==0&&o==e.length-1||1==o&&2==e.length?t.style.paddingTop=t.dataset.fullWidth+"%":t.style.paddingTop=t.dataset.halfWidth+"%",a.appendChild(t)}))}},S=e.detail.variant;l&&window.Shopify&&"/"!=window.Shopify.routes.root&&(S=_.variants.find((function(e){if(e.id==d.querySelector(".ecom-product-single-select-id[name=id]").value)return e})));const b=c&&c.style,L=t&&t.style;if(h(i,t,o,S,L),h(n,c,r,S,b),g(S),v(S),p){let e=s.querySelectorAll(".ecom-image-zoom");if(0==e.length)return;M(e),l&&e.forEach((function(e){e.querySelector("a")&&e.querySelector("a").addEventListener("click",(function(e){e.preventDefault()}))}))}}}S&&"true"==S||y&&"false"===v&&(b(y),d&&d.addEventListener("ecomVariantChange",b))}(),s.querySelectorAll(".ecom-product-single__media--play-control").forEach((function(e){e.addEventListener("click",(function(e){this.style.display="none",this.parentNode.querySelector("video").play()}))})),!this.isLive)try{c.$el.querySelectorAll("model-viewer").forEach((function(e){const t=element.outerHTML;e.replaceWith(t)}))}catch(e){console.info(e.message)}if(document.querySelector("model-viewer")&&!document.getElementById("ModelViewerStyle")){let e=document.createElement("link");e.id="ModelViewerStyle",e.rel="stylesheet",e.href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css",e.media="print",e.onload=function(){this.media="all"},document.head.appendChild(e)}if(window.Shopify&&window.Shopify.loadFeatures&&window.Shopify.loadFeatures([{name:"shopify-xr",version:"1.0",onLoad:function e(){if(window.ShopifyXR){try{const e=c.$el.querySelector('[id^="Product-model-"]');e&&(window.ShopifyXR.addModels(JSON.parse(e.textContent)),e.remove())}catch(e){console.log(e.message)}window.ShopifyXR.setupXRElements()}else document.addEventListener("shopify_xr_initialized",(function(){e()}))}},{name:"model-viewer-ui",version:"1.0",onLoad:function(e){if(e)return;const t=s.querySelectorAll("model-viewer");t&&t.forEach(e=>{if(e)try{new window.Shopify.ModelViewerUI(e)}catch(e){console.warn(e.message)}}),s.querySelectorAll("button").forEach((function(e){e.setAttribute("type","button")}))}}]),function(){if(_&&l){let e=s.querySelectorAll("[ecom-modal]");e.length&&window.EComModal&&new window.EComModal(e,{gallery:!0,cssClass:["ecom-container-lightbox-"+r]})}}(),this.settings.position_sticky&&window.innerWidth>1024&&s.parentElement&&(this.isLive?s.style.height="100%":s.parentElement.style.height="100%"),p){let e=s.querySelectorAll(".ecom-image-zoom");if(0==e.length)return;"slider"!==m&&M(e),l&&e.forEach((function(e){e.querySelector("a")&&e.querySelector("a").addEventListener("click",(function(e){e.preventDefault()}))}))}function M(e){if(l&&!(window.innerWidth<768)&&window.EcomImgZoom)if(e.length>0)for(var t=0,o=e.length;t<o;t++)new window.EcomImgZoom(e[t],a);else new window.EcomImgZoom(e,a)}};document.querySelectorAll(".ecom-8fvlz46y338").forEach((function(t){e.call({$el:t,id:"ecom-8fvlz46y338",settings:{zoom_width:"500px",zoom_height:"500px",show_thumbnails:!0,layout:"slider",image_action:"nothing",thumbnail_position:"row-reverse",thumbnail_position__tablet:"column",thumbnail_position__mobile:"column",show_pagination:!1,sliderControls:!0,sliderControlsThumb:!0,enable_gallery:!1,slide_loop:!1,disable_auto_height:!0,video_auto_play:!1,position_sticky:!0},isLive:!0})}))}(),window.location.search.indexOf("ecom-token=")<0&&(document.querySelector(".ecom-builder").innerHTML='<h3 style="width:100%;display:block;text-align:center">This template for preview purposes only</h3>',document.querySelector("body").style.opacity="0.7");