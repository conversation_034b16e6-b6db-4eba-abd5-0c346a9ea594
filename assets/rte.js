import{wrap}from '@archetype-themes/utils/utils'
class AtRte extends HTMLElement{connectedCallback(){this.querySelectorAll('table').forEach((table)=>{var wrapWith=document.createElement('div')
wrapWith.classList.add('table-wrapper')
wrap(table,wrapWith)})
this.querySelectorAll('iframe[src*="youtube.com/embed"]').forEach((iframe)=>{this.wrapVideo(iframe)})
this.querySelectorAll('iframe[src*="player.vimeo"]').forEach((iframe)=>{this.wrapVideo(iframe)})
this.querySelectorAll('a img').forEach((img)=>{img.parentNode.classList.add('rte__image')})}
wrapVideo(iframe){iframe.src=iframe.src
var wrapWith=document.createElement('div')
wrapWith.classList.add('video-wrapper')
wrap(iframe,wrapWith)}}
customElements.define('at-rte',AtRte)