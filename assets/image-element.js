import '@archetype-themes/scripts/config'
import '@archetype-themes/scripts/helpers/sections'
class ImageElement extends HTMLElement{constructor(){super()}
connectedCallback(){const handleIntersection=(entries,observer)=>{if(!entries[0].isIntersecting)return
this.removeAnimations()
observer.unobserve(this)}
new IntersectionObserver(handleIntersection.bind(this),{rootMargin:'0px 0px 400px 0px'}).observe(this)}
removeAnimations(){const imageWrap=this.closest('.image-wrap')
const skrimWrap=this.closest('.skrim__link')
if(imageWrap){imageWrap.classList.add('loaded')}
if(skrimWrap){skrimWrap.classList.add('loaded')}}}
customElements.define('image-element',ImageElement)