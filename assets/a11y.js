export function forceFocus(element,options={}){let savedTabIndex=element.tabIndex
element.tabIndex=-1
element.dataset.tabIndex=savedTabIndex
element.focus()
if(typeof options.className!=='undefined'){element.classList.add(options.className)}
element.addEventListener('blur',callback)
function callback(event){event.target.removeEventListener(event.type,callback)
element.tabIndex=savedTabIndex
delete element.dataset.tabIndex
if(typeof options.className!=='undefined'){element.classList.remove(options.className)}}}
export function focusable(container){return Array.from(container.querySelectorAll("summary, a[href], button:enabled, [tabindex]:not([tabindex^='-']), [draggable], area, input:not([type=hidden]):enabled, select:enabled, textarea:enabled, object, iframe"))}
let trapFocusHandlers={}
export function trapFocus(container,options={}){let elements=focusable(container)
let elementToFocus=options.elementToFocus||container
let first=elements[0]
let last=elements[elements.length-1]
removeTrapFocus()
trapFocusHandlers.focusin=function(event){if(container!==event.target&&!container.contains(event.target)){first.focus()}
if(event.target!==container&&event.target!==last&&event.target!==first)return
document.addEventListener('keydown',trapFocusHandlers.keydown)}
trapFocusHandlers.focusout=function(){document.removeEventListener('keydown',trapFocusHandlers.keydown)}
trapFocusHandlers.keydown=function(event){if(event.keyCode!==9)return
if(event.target===last&&!event.shiftKey){event.preventDefault()
first.focus()}
if((event.target===container||event.target===first)&&event.shiftKey){event.preventDefault()
last.focus()}}
document.addEventListener('focusout',trapFocusHandlers.focusout)
document.addEventListener('focusin',trapFocusHandlers.focusin)
forceFocus(elementToFocus,options)}
export function removeTrapFocus(elementToFocus=null){document.removeEventListener('focusin',trapFocusHandlers.focusin)
document.removeEventListener('focusout',trapFocusHandlers.focusout)
document.removeEventListener('keydown',trapFocusHandlers.keydown)
if(elementToFocus)elementToFocus.focus()}
let _handleTouchmove=()=>!0
export function lockMobileScrolling(element){let el=element?element:document.documentElement
document.documentElement.classList.add('lock-scroll')
el.addEventListener('touchmove',_handleTouchmove)}
export function unlockMobileScrolling(element){document.documentElement.classList.remove('lock-scroll')
let el=element?element:document.documentElement
el.removeEventListener('touchmove',_handleTouchmove)}
let scrollPosition=0
export function lockScroll(){scrollPosition=window.pageYOffset
document.body.style.overflow='hidden'
document.body.style.position='fixed'
document.body.style.top=`-${scrollPosition}px`
document.body.style.width='100%'}
export function unlockScroll(){document.body.style.removeProperty('overflow')
document.body.style.removeProperty('position')
document.body.style.removeProperty('top')
document.body.style.removeProperty('width')
window.scrollTo(0,scrollPosition)}