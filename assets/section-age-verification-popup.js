import Modals from '@archetype-themes/modules/modal'
import{lockMobileScrolling,unlockMobileScrolling}from '@archetype-themes/utils/a11y'
import{HTMLThemeElement}from '@archetype-themes/custom-elements/theme-element'
import{setLocalStorage,getLocalStorage}from '@archetype-themes/utils/storage'
class AgeVerificationPopup extends HTMLThemeElement{connectedCallback(){this.storageKey=this.id
this.storageValue=getLocalStorage(this.storageKey)
this.classes={activeContent:'age-verification-popup__content--active',inactiveContent:'age-verification-popup__content--inactive',inactiveDeclineContent:'age-verification-popup__decline-content--inactive',activeDeclineContent:'age-verification-popup__decline-content--active'}
this.declineButton=this.querySelector('[data-age-verification-popup-decline-button]')
this.declineContent=this.querySelector('[data-age-verification-popup-decline-content]')
this.content=this.querySelector('[data-age-verification-popup-content]')
this.returnButton=this.querySelector('[data-age-verification-popup-return-button]')
this.exitButton=this.querySelector('[data-age-verification-popup-exit-button]')
this.backgroundImage=this.querySelector('[data-background-image]')
this.mobileBackgroundImage=this.querySelector('[data-mobile-background-image]')
if(this.storageValue&&this.dataset.testMode==='false')return
this.init()}
init(){this.modal=new Modals(this.id,'age-verification-popup-modal',{closeOffContentClick:!1})
if(this.backgroundImage){this.backgroundImage.style.display='block'}
if(matchMedia('(max-width: 768px)').matches&&this.mobileBackgroundImage){this.mobileBackgroundImage.style.display='block'}
this.modal.open()
lockMobileScrolling(document.querySelector('#MainContent'))
if(this.declineButton){this.declineButton.addEventListener('click',(e)=>{e.preventDefault()
this.showDeclineContent()
if(Shopify.designMode){sessionStorage.setItem(this.id,'second-view')}})}
if(this.returnButton){this.returnButton.addEventListener('click',(e)=>{e.preventDefault()
this.hideDeclineContent()
const secondViewVisited=sessionStorage.getItem(this.id)
if(Shopify.designMode&&secondViewVisited){sessionStorage.removeItem(this.id)}})}
if(this.exitButton){this.exitButton.addEventListener('click',(e)=>{e.preventDefault()
if(this.dataset.testMode==='false'){setLocalStorage(this.storageKey,'entered',30)}
if(this.backgroundImage){this.backgroundImage.style.display='none'}
if(matchMedia('(max-width: 768px)').matches&&this.mobileBackgroundImage){this.mobileBackgroundImage.style.display='none'}
this.modal.close()
unlockMobileScrolling(document.querySelector('#MainContent'))})}}
showDeclineContent(){this.declineContent.classList.remove(this.classes.inactiveDeclineContent)
this.declineContent.classList.add(this.classes.activeDeclineContent)
this.content.classList.add(this.classes.inactiveContent)
this.content.classList.remove(this.classes.activeContent)}
hideDeclineContent(){this.declineContent.classList.add(this.classes.inactiveDeclineContent)
this.declineContent.classList.remove(this.classes.activeDeclineContent)
this.content.classList.remove(this.classes.inactiveContent)
this.content.classList.add(this.classes.activeContent)}
onSectionLoad(){this.init()
if(this.dataset.testMode==='true'&&this.storageValue){localStorage.removeItem(this.storageKey)}
const secondViewVisited=sessionStorage.getItem(this.id)
if(!secondViewVisited)return
this.showDeclineContent()}
onSectionUnload(){this.modal.close()}
onSectionSelect(){this.init()}}
customElements.define('age-verification-popup',AgeVerificationPopup)