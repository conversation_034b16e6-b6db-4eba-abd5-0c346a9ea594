import{EVENTS}from '@archetype-themes/utils/events'
import{unlockMobileScrolling}from '@archetype-themes/utils/a11y'
let selectors={sidebarId:'CollectionSidebar',trigger:'.collection-filter__btn'}
let config={isOpen:!1,namespace:'.collection-filters'}
export default class CollectionSidebar{constructor(){if(!document.getElementById(selectors.sidebarId)){return}
this.init()}
init(){config.isOpen=!1
unlockMobileScrolling()
this.trigger=document.querySelector(selectors.trigger)
this.trigger.removeEventListener('click',this._handleClick)
this._handleClick=this.handleClick.bind(this)
this.trigger.addEventListener('click',this._handleClick)}
handleClick(){this.isOpen=!this.isOpen
this.trigger.classList.toggle('is-active',this.isOpen)
document.dispatchEvent(new CustomEvent(EVENTS.toggleMobileFilters,{detail:{isOpen:this.isOpen}}))}}