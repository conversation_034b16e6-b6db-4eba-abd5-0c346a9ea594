import{EVENTS}from '@archetype-themes/utils/events'
let selectors={nav:'.slide-nav',childList:'.slide-nav__dropdown',allLinks:'a.slide-nav__link',subNavToggleBtn:'.js-toggle-submenu'}
let classes={isActive:'is-active'}
let defaults={menuLevel:1,inHeader:!1}
class MobileNav extends HTMLElement{constructor(){super()
this.config=Object.assign({},defaults)
this.config.inHeader=this.getAttribute('inHeader')==='true'}
connectedCallback(){this.abortController=new AbortController()
this.nav=this.querySelector(selectors.nav)
this.init()}
init(){this.nav.querySelectorAll(selectors.subNavToggleBtn).forEach((btn)=>{btn.addEventListener('click',this.toggleSubNav.bind(this),{signal:this.abortController.signal})})
this.nav.querySelectorAll(selectors.allLinks).forEach((link)=>{this.dispatchEvent(new CustomEvent(EVENTS.mobileNavClose,{bubbles:!0}))})}
toggleSubNav(evt){let btn=evt.currentTarget
this.goToSubnav(btn.dataset.target)}
goToSubnav(target){let targetMenu=this.nav.querySelector(selectors.childList+'[data-parent="'+target+'"]')
if(targetMenu){this.config.menuLevel=targetMenu.dataset.level
if(this.config.menuLevel==2){this.nav.querySelectorAll(selectors.childList+'[data-level="3"]').forEach((list)=>{list.classList.remove(classes.isActive)})}
targetMenu.classList.add(classes.isActive)
this.setWrapperHeight(targetMenu.offsetHeight)}else{this.config.menuLevel=1
this.removeAttribute('style')
this.nav.querySelectorAll(selectors.childList).forEach((list)=>{list.classList.remove(classes.isActive)})}
this.dataset.level=this.config.menuLevel}
setWrapperHeight(h){this.style.height=h+'px'}}
customElements.define('mobile-nav',MobileNav)