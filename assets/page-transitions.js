import '@archetype-themes/scripts/config'
window.onpageshow=function(evt){if(evt.persisted){document.body.classList.remove('unloading')
document.querySelectorAll('.cart__checkout').forEach((el)=>{el.classList.remove('btn--loading')})}}
theme.pageTransitions=function(){if(document.body.dataset.transitions==='true'){if(!!navigator.userAgent.match(/Version\/[\d\.]+.*Safari/)){document.querySelectorAll('a').forEach((a)=>{window.setTimeout(function(){document.body.classList.remove('unloading')},1200)})}
document.querySelectorAll('a.hero__slide-link, a[href^="mailto:"], a[href^="#"], a[target="_blank"], a[href*="youtube.com/watch"], a[href*="youtu.be/"], a[href*="player.vimeo.com/video/"], a[href*="vimeo.com/"], a[download]').forEach((el)=>{el.classList.add('js-no-transition')})
document.querySelectorAll('a:not(.js-no-transition)').forEach((el)=>{el.addEventListener('click',function(evt){if(evt.metaKey)return!0
evt.preventDefault()
document.body.classList.add('unloading')
var src=el.getAttribute('href')
window.setTimeout(function(){location.href=src},50)})})
document.querySelectorAll('a.mobile-nav__link').forEach((el)=>{el.addEventListener('click',function(){theme.NavDrawer.close()})})}}