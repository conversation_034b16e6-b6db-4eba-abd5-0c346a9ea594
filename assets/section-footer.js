class FooterSection extends HTMLElement{connectedCallback(){this.ids={mobileNav:'MobileNav',footerNavWrap:'FooterMobileNavWrap',footerNav:'FooterMobileNav'}
this.init()}
init(){const newsletterInput=document.querySelector('.footer__newsletter-input')
if(newsletterInput){newsletterInput.addEventListener('keyup',function(){newsletterInput.classList.add('footer__newsletter-input--active')})}
this.mobileMediaQuery=window.matchMedia('(max-width: 768px)')
if(this.mobileMediaQuery.matches){this.initDoubleMobileNav()}
this.mobileMediaQuery.addListener(this.handleMediaQueryChange.bind(this))}
handleMediaQueryChange(mql){if(mql.matches){this.initDoubleMobileNav()}}
initDoubleMobileNav(){if(this.hasDoubleMobileNav){return}
const menuPlaceholder=document.getElementById(this.ids.footerNavWrap)
if(!menuPlaceholder){return}
const mobileNav=document.querySelector(`mobile-nav[container="${this.ids.mobileNav}"]`)
const footerNav=document.getElementById(this.ids.footerNav)
const clone=mobileNav.cloneNode(!0)
clone.setAttribute('container',this.ids.footerNav)
clone.setAttribute('inHeader','false')
footerNav.appendChild(clone)
menuPlaceholder.classList.remove('hide')
this.hasDoubleMobileNav=!0}}
customElements.define('footer-section',FooterSection)