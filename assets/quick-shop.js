import{executeJSmodules}from '@archetype-themes/utils/utils'
class QuickShop extends HTMLElement{constructor(){super()
this.selectors={quickShopContainer:'[data-tool-tip-content]',blocksHolder:'[data-blocks-holder]',blocks:'[data-product-blocks]',form:'.product-single__form'}
if(matchMedia('(max-width: 768px)').matches){return}
this.addEventListener('tooltip:interact',async(e)=>{if(e.detail.context==='QuickShop'){if(!this.quickShopData){this.quickShopData=await this.loadQuickShopData(e)}}})
this.addEventListener('tooltip:open',async(e)=>{if(e.detail.context==='QuickShop'){if(!this.quickShopData){this.quickShopData=await this.loadQuickShopData(e)}
const quickShopContainer=document.querySelector(this.selectors.quickShopContainer)
const clonedQuickShopData=this.quickShopData.cloneNode(!0)
quickShopContainer.innerHTML=''
quickShopContainer.appendChild(clonedQuickShopData)
this.dispatchEvent(new CustomEvent('quickshop:opened',{bubbles:!0}))
if(Shopify&&Shopify.PaymentButton){Shopify.PaymentButton.init()}
const scripts=document.querySelectorAll(`tool-tip [data-product-id="${this.prodId}"] script[type="module"]`)
executeJSmodules(scripts)}})
this.addEventListener('quickshop:opened',async()=>{if(Shopify&&Shopify.PaymentButton){Shopify.PaymentButton.init()}})}
async loadQuickShopData(evt){const gridItem=evt.currentTarget.closest('.grid-product')
this.handle=gridItem.firstElementChild.getAttribute('data-product-handle')
this.prodId=gridItem.firstElementChild.getAttribute('data-product-id')
if(!gridItem||!this.handle||!this.prodId)return
let url=`${window.Shopify.routes.root}/products/${this.handle}`
url=url.replace('//','/')
try{const response=await fetch(url)
const text=await response.text()
const responseHTML=new DOMParser().parseFromString(text,'text/html')
const fragment=document.createDocumentFragment()
const div=responseHTML.querySelector(`.page-content[data-product-id="${this.prodId}"]`)
this.processHTML(div)
if(div){div.dataset.modal=!0
fragment.appendChild(div.cloneNode(!0))}
window.dispatchEvent(new CustomEvent(`quickshop:loaded-${this.prodId}`))
return fragment}catch(error){console.error('Error:',error)}}
processHTML(productElement){this.removeBreadcrumbs(productElement)
this.preventVariantURLSwitching(productElement)}
removeBreadcrumbs(productElement){const breadcrumbs=productElement.querySelector('.breadcrumb')
if(!breadcrumbs)return
breadcrumbs.remove()}
preventVariantURLSwitching(productElement){const variantPicker=productElement.querySelector('block-variant-picker')
if(!variantPicker)return
variantPicker.removeAttribute('data-update-url')}}
customElements.define('quick-shop',QuickShop)