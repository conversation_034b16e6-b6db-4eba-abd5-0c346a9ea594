export const config={bpSmall:!1,youTubeReady:!1,vimeoReady:!1,vimeoLoading:!1,mediaQuerySmall:'screen and (max-width: '+769+'px)',isTouch:'ontouchstart' in window||(window.DocumentTouch&&window.document instanceof DocumentTouch)||window.navigator.maxTouchPoints||window.navigator.msMaxTouchPoints?!0:!1,rtl:document.documentElement.getAttribute('dir')=='rtl'?!0:!1,stickyHeader:!1,hasSessionStorage:!0,hasLocalStorage:!0,filtersPrime:null,overlayHeader:!1}
config.bpSmall=matchMedia(config.mediaQuerySmall).matches
matchMedia(config.mediaQuerySmall).addListener(function(mql){if(mql.matches){config.bpSmall=!0
document.dispatchEvent(new CustomEvent('matchSmall'))}else{config.bpSmall=!1
document.dispatchEvent(new CustomEvent('unmatchSmall'))}})
config.hasSessionStorage=isStorageSupported('session')
config.hasLocalStorage=isStorageSupported('local')
function isStorageSupported(type){if(window.self!==window.top){return!1}
var testKey='test'
var storage
if(type==='session'){storage=window.sessionStorage}
if(type==='local'){storage=window.localStorage}
try{storage.setItem(testKey,'1')
storage.removeItem(testKey)
return!0}catch(error){return!1}}
if(config.isTouch){document.documentElement.className+=' supports-touch'}