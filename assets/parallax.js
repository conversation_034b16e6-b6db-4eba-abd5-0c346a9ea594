class ParallaxImage extends HTMLElement{constructor(){super()
this.parallaxImage=this.querySelector('[data-parallax-image]')
this.windowInnerHeight=window.innerHeight
this.isActive=!1
this.timeout=null
this.directionMap={right:0,top:90,left:180,bottom:270}
this.directionMultipliers={0:[1,0],90:[0,-1],180:[-1,0],270:[0,1]}
this.init()
window.addEventListener('scroll',()=>this.scrollHandler())}
getParallaxInfo(){const{width,height,top}=this.parallaxImage.getBoundingClientRect()
let element=this.parallaxImage
let multipliers
let{angle,movement}=element.dataset
let movementPixels=angle==='top'?Math.ceil(height*(parseFloat(movement)/100)):Math.ceil(width*(parseFloat(movement)/100))
angle=this.directionMap[angle]??parseFloat(angle)
if(angle!==angle)angle=270
if(movementPixels!==movementPixels)movementPixels=100
angle%=360
if(angle<0)angle+=360
const toLeft=angle>90&&angle<270
const toTop=angle<180
element.style[toLeft?'left':'right']=0
element.style[toTop?'top':'bottom']=0
if(angle%90){const radians=(angle*Math.PI)/180
multipliers=[Math.cos(radians),Math.sin(radians)*-1]}else{multipliers=this.directionMultipliers[angle]}
if(multipliers[0])element.style.width=`calc(100% + ${movementPixels * Math.abs(multipliers[0])}px)`
if(multipliers[1])element.style.height=`calc(100% + ${movementPixels * Math.abs(multipliers[1])}px)`
return{element,movementPixels,multipliers,top,height}}
init(){const{element,movementPixels,multipliers,top,height}=this.getParallaxInfo()
const scrolledInContainer=this.windowInnerHeight-top
const scrollArea=this.windowInnerHeight+height
const progress=scrolledInContainer/scrollArea
if(progress>-0.1&&progress<1.1){const position=Math.min(Math.max(progress,0),1)*movementPixels
element.style.transform=`translate3d(${position * multipliers[0]}px, ${position * multipliers[1]}px, 0)`}
if(this.isActive)requestAnimationFrame(this.init.bind(this))}
scrollHandler(){if(this.isActive){clearTimeout(this.timeout)}else{this.isActive=!0
requestAnimationFrame(this.init.bind(this))}
this.timeout=setTimeout(()=>(this.isActive=!1),20)}}
customElements.define('parallax-image',ParallaxImage)